﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using WeifenLuo.WinFormsUI;
using AssistPro.navtex;
using System.IO;
using Microsoft.WindowsAPICodePack.Dialogs;
using AssistPro.navtex.communication;
using static System.Windows.Forms.VisualStyles.VisualStyleElement;

namespace AssistPro.navtex.monitor
{
    public partial class Nmea_Monitor_Form : DockContent
    {
        static readonly Nmea_Monitor_Form instance = new Nmea_Monitor_Form();
        public static Nmea_Monitor_Form Instance
        {
            get
            {
                return instance;
            }
        }

        CommProcess m_CommProc = CommProcess.Instance;
        bool isProcLogging = false;

        public Nmea_Monitor_Form()
        {
            InitializeComponent();

            m_CommProc.NMEA_Received_INS = NMEA_Received_INS;
        }

        private string GetPath()
        {
            DateTime now = DateTime.Now;

            string s = Application.StartupPath + "\\NMEA_Log";
            DirectoryInfo di = new DirectoryInfo(s);
            if (di.Exists == false)
            {
                di.Create();
            }

            s += string.Format("\\{0}-{1}-{2}", now.Year, now.Month, now.Day);
            DirectoryInfo di_2nd = new DirectoryInfo(s);
            if (di_2nd.Exists == false)
            {
                di_2nd.Create();
            }

            return s;
        }

        private void button_Log_Start_Click(object sender, EventArgs e)
        {
            if(isProcLogging == false)
            {
                var dlg = new CommonOpenFileDialog();
                dlg.IsFolderPicker = true;
                dlg.DefaultDirectory = "C:\\";

                if (dlg.ShowDialog() == CommonFileDialogResult.Ok)
                {
                    //dlg.FileName
                    isProcLogging = true;
                    button_Log_Start.Text = "Logging";
                    button_Log_Start.BackColor = Color.FromArgb(0, 255, 0);
                }
            }
            else
            {
                isProcLogging = false;
                button_Log_Start.Text = "Log Start";
                button_Log_Start.BackColor = Color.Silver;
            }
        }

        private void button_Analyze_NRX_Click(object sender, EventArgs e)
        {
            var dlg = new CommonOpenFileDialog();
            dlg.IsFolderPicker = true;
            dlg.DefaultDirectory = "C:\\";

            if (dlg.ShowDialog() == CommonFileDialogResult.Ok)
            {
                //dlg.FileName
            }
        }

        internal delegate void StringDelegate(string str);
        private void NMEA_Received_INS(string str)
        {
            // Handle multi-threading
            if (this.IsHandleCreated && !this.IsDisposed)
            {
                if (InvokeRequired)
                {
                    Invoke(new StringDelegate(NMEA_Received_INS), new object[] { str });
                    return;
                }

                textBox_INS.AppendText(str);
            }
        }
        private void textBox_INS_TextChanged(object sender, EventArgs e)
        {
            long len = textBox_INS.Text.Trim().Length;

            textBox_INS.SelectionStart = textBox_INS.Text.Length;
            textBox_INS.ScrollToCaret();

            if(len >= 220000)
            {
                textBox_INS.Text = string.Empty;
            }
        }

        private void NMEA_Received_Mon_1(string str)
        {
            // Handle multi-threading
            if (this.IsHandleCreated && !this.IsDisposed)
            {
                if (InvokeRequired)
                {
                    Invoke(new StringDelegate(NMEA_Received_Mon_1), new object[] { str });
                    return;
                }

                textBox_Mon_1.AppendText(str);
            }
        }
        private void textBox_Mon_1_TextChanged(object sender, EventArgs e)
        {
            long len = textBox_Mon_1.Text.Trim().Length;

            textBox_Mon_1.SelectionStart = textBox_Mon_1.Text.Length;
            textBox_Mon_1.ScrollToCaret();

            if (len >= 220000)
            {
                textBox_Mon_1.Text = string.Empty;
            }
        }

        private void NMEA_Received_Mon_2(string str)
        {
            // Handle multi-threading
            if (this.IsHandleCreated && !this.IsDisposed)
            {
                if (InvokeRequired)
                {
                    Invoke(new StringDelegate(NMEA_Received_Mon_2), new object[] { str });
                    return;
                }

                textBox_Mon_2.AppendText(str);
            }
        }
        private void textBox_Mon_2_TextChanged(object sender, EventArgs e)
        {
            long len = textBox_Mon_2.Text.Trim().Length;

            textBox_Mon_2.SelectionStart = textBox_Mon_2.Text.Length;
            textBox_Mon_2.ScrollToCaret();

            if (len >= 220000)
            {
                textBox_Mon_2.Text = string.Empty;
            }
        }

        private void NMEA_Received_Mon_3(string str)
        {
            // Handle multi-threading
            if (this.IsHandleCreated && !this.IsDisposed)
            {
                if (InvokeRequired)
                {
                    Invoke(new StringDelegate(NMEA_Received_Mon_3), new object[] { str });
                    return;
                }

                textBox_Mon_3.AppendText(str);
            }
        }
        private void textBox_Mon_3_TextChanged(object sender, EventArgs e)
        {
            long len = textBox_Mon_3.Text.Trim().Length;

            textBox_Mon_3.SelectionStart = textBox_Mon_3.Text.Length;
            textBox_Mon_3.ScrollToCaret();

            if (len >= 220000)
            {
                textBox_Mon_3.Text = string.Empty;
            }
        }

        private void Nmea_Monitor_Form_FormClosing(object sender, FormClosingEventArgs e)
        {
            this.Dispose();
        }
    }
}
