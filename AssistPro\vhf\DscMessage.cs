﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace AssistPro.vhf
{
    internal class DscMessage
    {
        public const string PARAM_RT = "RT";
        public const string PARAM_RT_ACK = "RT ACK";
        public const string PARAM_RT_UNABLE = "UNABLE";
        public const string PARAM_POS_REQ = "POS REQ";
        public const string PARAM_POS_ACK = "POS ACK";
        public const string PARAM_TEST = "TEST";
        public const string PARAM_TEST_ACK = "TEST ACK";
        public const string PARAM_POLL_ACK = "POLL ACK";

        public const string TYPE_DIST = "DISTRESS";
        public const string TYPE_DIST_ACK = "DISTRESS ACK";
        public const string TYPE_DIST_RLY = "DISTRESS RELAY";
        public const string TYPE_DIST_RLY_ACK = "DISTRESS RELAY ACK";
        public const string TYPE_ALLSHIPS = "ALLSHIPS";
        public const string TYPE_INDIVIDUAL = "INDIVIDUAL";
        public const string TYPE_GROUP_CALL = "GROUP";
        public const string TYPE_SEMI_AUTO = "SEMI AUTO";

        public const string FMT_102 = "GEO AREA(102)";
        public const string FMT_112 = "DISTRESS(112)";
        public const string FMT_114 = "GROUP(114)";
        public const string FMT_116 = "ALLSHIPS(116)";
        public const string FMT_120 = "INDIVIDUAL(120)";
        public const string FMT_123 = "SEMI AUTO(123)";

        public const string CAT_100 = "ROUTINE(100)";
        public const string CAT_108 = "SAFETY(108)";
        public const string CAT_110 = "URGENCY(110)";
        public const string CAT_112 = "DISTRESS(112)";

        public const string TELCOM1_100 = "All TP(100)";
        public const string TELCOM1_101 = "DUPLEX TP(101)";
        public const string TELCOM1_103 = "POLLING(103)";
        public const string TELCOM1_104 = "UNABLE(104)";
        public const string TELCOM1_105 = "END OF CALL(105)";
        public const string TELCOM1_106 = "DATA(106)";
        public const string TELCOM1_110 = "DIST ACK(110)";
        public const string TELCOM1_112 = "DIST RLY(112)";
        public const string TELCOM1_118 = "TEST(118)";
        public const string TELCOM1_121 = "SHIP POSITION(121)";
        public const string TELCOM1_126 = "NO INFO(126)";

        public const string TELCOM2_100 = "NO REASON(100)";
        public const string TELCOM2_101 = "CONGESTION(101)";
        public const string TELCOM2_102 = "BUSY(102)";
        public const string TELCOM2_103 = "QUEUE(103)";
        public const string TELCOM2_104 = "BARRED(104)";
        public const string TELCOM2_105 = "NO OPER(105)";
        public const string TELCOM2_106 = "TEMP NO OPER(106)";
        public const string TELCOM2_107 = "EQP DISABLED(107)";
        public const string TELCOM2_108 = "UNABLE CH(108)";
        public const string TELCOM2_109 = "UNABLE MODE(109)";

        public const string TELCOM2_110 = "AIRCRAFT(110)";
        public const string TELCOM2_111 = "MEDICAL(111)";
        public const string TELCOM2_126 = "NO INFO(126)";

        public const string EOS_117 = "ACK.RQ(117)";
        public const string EOS_122 = "ACK.BQ(122)";
        public const string EOS_127 = "EOS(127)";

        public const string EXPAN_1 = "expan1";
        public const string EXPAN_2 = "expan2";
        public const string EXPAN_3 = "expan3";

        public byte ConvertFormatToNum(string format)
        {
            byte ret = 0;

            if (format.Equals(FMT_102))
                ret = 102;
            else if (format.Equals(FMT_112))
                ret = 112;
            else if (format.Equals(FMT_114))
                ret = 114;
            else if (format.Equals(FMT_116))
                ret = 116;
            else if (format.Equals(FMT_120))
                ret = 120;
            else if (format.Equals(FMT_123))
                ret = 123;           
            //else
                //MessageBox.Show(format + " not in format list");

            return ret;
        }

        public byte ConvertNatureToNum(string Nature)
        {
            byte ret = 0;

            if (Nature.Equals("FIRE(100)"))
                ret = 100;
            else if (Nature.Equals("FLODDING(101)"))
                ret = 101;
            else if (Nature.Equals("COLLISION(102)"))
                ret = 102;
            else if (Nature.Equals("GROUNDING(103)"))
                ret = 103;
            else if (Nature.Equals("LISTING(104)"))
                ret = 104;
            else if (Nature.Equals("SINGKING(105)"))
                ret = 105;
            else if (Nature.Equals("DISABLED(106)"))
                ret = 106;
            else if (Nature.Equals("UNDESIGNATED(107)"))
                ret = 107;
            else if (Nature.Equals("ABANDONING(108)"))
                ret = 108;
            else if (Nature.Equals("PIRACY ATTACK(109)"))
                ret = 109;
            else if (Nature.Equals("MAN OVERBOARD(110)"))
                ret = 110;
            else if (Nature.Equals("EPIRB(112)"))
                ret = 112;
            else if (Nature.Equals("BEACON(112)"))
                ret = 112;
            //else
                //MessageBox.Show(Nature + " not in nature list");

            return ret;
        }

        public string ConvertNumToNatureString(byte Nature)
        {
            string ret = string.Empty;

            if (Nature == 100)
                ret = "FIRE(100)";
            else if (Nature == 101)
                ret = "FLODDING(101)";
            else if (Nature == 102)
                ret = "COLLISION(102)";
            else if (Nature == 103)
                ret = "GROUNDING(103)";
            else if (Nature == 104)
                ret = "LISTING(104)";
            else if (Nature == 105)
                ret = "SINGKING(105)";
            else if (Nature == 106)
                ret = "DISABLED(106)";
            else if (Nature == 107)
                ret = "UNDESIGNATED(107)";
            else if (Nature == 108)
                ret = "ABANDONING(108)";
            else if (Nature == 109)
                ret = "PIRACY ATTACK(109)";
            else if (Nature == 110)
                ret = "MAN OVERBOARD(110)";
            else if (Nature == 112)
                ret = "EPIRB(112)";
            //else
                //MessageBox.Show(Nature + " not in nature list");
            return ret;
        }

        public string ConvertNumToFormatString(byte format)
        {
            string ret = string.Empty;

            if (format == 102)
                ret = FMT_102;
            else if (format == 112)
                ret = FMT_112;
            else if (format == 114)
                ret = FMT_114;
            else if (format == 116)
                ret = FMT_116;
            else if (format == 120)
                ret = FMT_120;
            else if (format == 123)
                ret = FMT_123;
            //else
                //MessageBox.Show(format + " not in format list");
            return ret;
        }

        public string ConvertNumToCategoryString(byte Category)
        {
            string ret = string.Empty;

            if (Category == 100)
                ret = CAT_100;
            else if (Category == 108)
                ret = CAT_108;
            else if (Category == 110)
                ret = CAT_110;
            else if (Category == 112)
                ret = CAT_112;
            //else
                //MessageBox.Show(Category + " not in category list");
            return ret;
        }

        public byte ConvertCategoryStringToNum(string Category)
        {
            byte ret = 0;

            if (Category == CAT_100)
                ret = 100;
            else if (Category == CAT_108)
                ret = 108;
            else if (Category == CAT_110)
                ret = 110;
            else if (Category == CAT_112)
                ret = 112;
            //else
                //MessageBox.Show(Category + " not in category list");
            return ret;
        }


        public string ConvertNumToTelCom1String(byte TelCom1)
        {
            string ret = string.Empty;

            if (TelCom1 == 100)
                ret = TELCOM1_100;
            else if (TelCom1 == 103)
                ret = TELCOM1_103;
            else if (TelCom1 == 104)
                ret = TELCOM1_104;
            else if (TelCom1 == 105)
                ret = TELCOM1_105;
            else if (TelCom1 == 106)
                ret = TELCOM1_106;
            else if (TelCom1 == 110)
                ret = TELCOM1_110;
            else if (TelCom1 == 112)
                ret = TELCOM1_112;
            else if (TelCom1 == 118)
                ret = TELCOM1_118;
            else if (TelCom1 == 121)
                ret = TELCOM1_121;
            //else
                //MessageBox.Show(TelCom1 + " not in telcom1 list");
            return ret;
        }

        public byte ConvertTelCom1StringToNum(string TelCom1)
        {
            byte ret = 0;

            if (TelCom1 == TELCOM1_100)
                ret = 100;
            else if (TelCom1 == TELCOM1_101)
                ret = 101;
            else if (TelCom1 == TELCOM1_103)
                ret = 103;
            else if (TelCom1 == TELCOM1_104)
                ret = 104;
            else if (TelCom1 == TELCOM1_105)
                ret = 105;
            else if (TelCom1 == TELCOM1_106)
                ret = 106;
            else if (TelCom1 == TELCOM1_110)
                ret = 110;
            else if (TelCom1 == TELCOM1_112)
                ret = 112;
            else if (TelCom1 == TELCOM1_118)
                ret = 118;
            else if (TelCom1 == TELCOM1_121)
                ret = 121;
            else if (TelCom1 == TELCOM1_126)
                ret = 126;
            //else
                //MessageBox.Show(TelCom1 + " not in telcom1 list");
            return ret;
        }


        public string ConvertNumToTelCom2String(byte TelCom2)
        {
            string ret = string.Empty;

            if (TelCom2 == 100)
                ret = TELCOM2_100;
            else if (TelCom2 == 101)
                ret = TELCOM2_101;
            else if (TelCom2 == 102)
                ret = TELCOM2_102;
            else if (TelCom2 == 103)
                ret = TELCOM2_103;
            else if (TelCom2 == 104)
                ret = TELCOM2_104;
            else if (TelCom2 == 105)
                ret = TELCOM2_105;
            else if (TelCom2 == 106)
                ret = TELCOM2_106;
            else if (TelCom2 == 107)
                ret = TELCOM2_107;
            else if (TelCom2 == 108)
                ret = TELCOM2_108;
            else if (TelCom2 == 109)
                ret = TELCOM2_109;
            else if (TelCom2 == 110)
                ret = TELCOM2_110;
            else if (TelCom2 == 111)
                ret = TELCOM2_111;
            else if (TelCom2 == 126)
                ret = TELCOM2_126;
            //else
                //MessageBox.Show(TelCom2 + " not in telcom2 list");
            return ret;
        }

        public byte ConvertTelCom2StringToNum(string TelCom2)
        {
            byte ret = 0;

            if (TelCom2 == TELCOM2_100)
                ret = 100;
            else if (TelCom2 == TELCOM2_101)
                ret = 101;
            else if (TelCom2 == TELCOM2_102)
                ret = 102;
            else if (TelCom2 == TELCOM2_103)
                ret = 103;
            else if (TelCom2 == TELCOM2_104)
                ret = 104;
            else if (TelCom2 == TELCOM2_105)
                ret = 105;
            else if (TelCom2 == TELCOM2_106)
                ret = 106;
            else if (TelCom2 == TELCOM2_107)
                ret = 107;
            else if (TelCom2 == TELCOM2_108)
                ret = 108;
            else if (TelCom2 == TELCOM2_109)
                ret = 109;
            else if (TelCom2 == TELCOM2_110)
                ret = 110;
            else if (TelCom2 == TELCOM2_111)
                ret = 111;
            else if (TelCom2 == TELCOM2_126)
                ret = 126;
            //else
                //MessageBox.Show(TelCom2 + " not in telcom2 list");
            return ret;
        }


        public string ConvertNumToEosString(byte eos)
        {
            string ret = string.Empty;

            if (eos == 117)
                ret = EOS_117;
            else if (eos == 122)
                ret = EOS_122;
            else if (eos == 127)
                ret = EOS_127;
            //else
                //MessageBox.Show(eos + " not in EOS list");
            return ret;
        }

        public byte ConvertEosStringToNum(string eos)
        {
            byte ret = 0;

            if (eos == EOS_117)
                ret = 117;
            else if (eos == EOS_122)
                ret = 122;
            else if (eos == EOS_127)
                ret = 127;
            //else
                //MessageBox.Show(eos + " not in EOS list");
            return ret;
        }

        public byte ConvertExpnStringToNum(string expn)
        {
            byte ret = 0;

            if (expn == EXPAN_1)
                ret = 127;
            else if (expn == EXPAN_2)
                ret = 117;
            else if (expn == EXPAN_3)
                ret = 122;
            //else
                //MessageBox.Show(expn + " not in expand mode list");
            return ret;
        }



    }

}
