﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Security.AccessControl</name>
  </assembly>
  <members>
    <member name="T:System.Security.AccessControl.AccessControlActions">
      <summary>Gibt die Aktionen an, die für sicherungsfähige Objekte zulässig sind.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlActions.Change">
      <summary>Gibt lesegeschützten Zugriff an.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlActions.None">
      <summary>Gibt keinen Zugriff an.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlActions.View">
      <summary>Gibt schreibgeschützten Zugriff an.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AccessControlModification">
      <summary>Gibt den Typ der auszuführenden Zugriffssteuerungsänderung an.Diese Enumeration wird von Methoden der <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Klasse und ihrer untergeordneten Klassen verwendet.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlModification.Add">
      <summary>Fügen Sie der Zugriffssteuerungsliste (ACL) die angegebene Autorisierungsregel hinzu.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlModification.Remove">
      <summary>Entfernen Sie die Autorisierungsregeln, die die gleiche Sicherheits-ID (SID) und die gleiche Zugriffsmaske wie die angegebene Autorisierungsregel aus der Zugriffssteuerungsliste enthalten.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlModification.RemoveAll">
      <summary>Entfernen Sie Autorisierungsregeln, die die gleiche SID wie die angegebene Autorisierungsregel aus der Zugriffssteuerungsliste enthalten.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlModification.RemoveSpecific">
      <summary>Entfernen Sie Autorisierungsregeln, die genau mit der angegebenen Autorisierungsregel aus der Zugriffssteuerungsliste übereinstimmen.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlModification.Reset">
      <summary>Entfernen Sie Autorisierungsregeln, die die gleiche SID wie die angegebene Autorisierungsregel aus der Zugriffssteuerungsliste enthalten, und fügen Sie anschließend der Zugriffssteuerungsliste die angegebene Autorisierungsregel hinzu.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlModification.Set">
      <summary>Entfernen Sie alle Autorisierungsregeln aus der Zugriffssteuerungsliste. Fügen Sie danach der Zugriffssteuerungsliste die angegebene Autorisierungsregel hinzu.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AccessControlSections">
      <summary>Gibt an, welche Abschnitte einer Sicherheitsbeschreibung gespeichert oder geladen werden sollen.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlSections.Access">
      <summary>Die DACL (Discretionary Access Control List).</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlSections.All">
      <summary>Die vollständige Sicherheitsbeschreibung.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlSections.Audit">
      <summary>Die SACL (System Access Control List).</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlSections.Group">
      <summary>Die primäre Gruppe.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlSections.None">
      <summary>Keine Abschnitte.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlSections.Owner">
      <summary>Der Besitzer.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AccessControlType">
      <summary>Gibt an, ob zum Gewähren oder Verweigern des Zugriffs ein <see cref="T:System.Security.AccessControl.AccessRule" />-Objekt verwendet wird.Diese Werte sind keine Flags, und sie können nicht kombiniert werden.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlType.Allow">
      <summary>Mithilfe des <see cref="T:System.Security.AccessControl.AccessRule" />-Objekts wird der Zugriff auf ein gesichertes Objekt zugelassen.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlType.Deny">
      <summary>Mithilfe des <see cref="T:System.Security.AccessControl.AccessRule" />-Objekts wird der Zugriff auf ein gesichertes Objekt verweigert.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AccessRule">
      <summary>Stellt eine Kombination der Identität eines Benutzers, einer Zugriffsmaske und eines Zugriffssteuerungstyps (gewähren oder verweigern) dar.Ein <see cref="T:System.Security.AccessControl.AccessRule" />-Objekt enthält auch Informationen darüber, wie die Regel von untergeordneten Objekten geerbt wird und wie die Vererbung weitergegeben wird.</summary>
    </member>
    <member name="M:System.Security.AccessControl.AccessRule.#ctor(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.AccessControl.AccessRule" />-Klasse unter Verwendung der angegebenen Werte.</summary>
      <param name="identity">Die Identität, für die die Zugriffsregel gilt.Dabei muss es sich um ein Objekt handeln, das in einen <see cref="T:System.Security.Principal.SecurityIdentifier" /> umgewandelt werden kann.</param>
      <param name="accessMask">Die Zugriffsmaske dieser Regel.Die Zugriffsmaske ist eine 32-Bit-Auflistung anonymer Bits, deren Bedeutung von den einzelnen Integratoren definiert wird.</param>
      <param name="isInherited">true, wenn diese Regel von einem übergeordneten Container geerbt wird.</param>
      <param name="inheritanceFlags">Die Vererbungseigenschaften der Zugriffsregel.</param>
      <param name="propagationFlags">Gibt an, ob geerbte Zugriffsregeln automatisch weitergegeben werden.Die Weitergabeflags werden ignoriert, wenn <paramref name="inheritanceFlags" /> auf <see cref="F:System.Security.AccessControl.InheritanceFlags.None" /> festgelegt ist.</param>
      <param name="type">Der gültige Zugriffssteuerungstyp.</param>
      <exception cref="T:System.ArgumentException">Der Wert des <paramref name="identity" />-Parameters kann nicht in einen <see cref="T:System.Security.Principal.SecurityIdentifier" /> umgewandelt werden, oder der <paramref name="type" />-Parameter enthält einen ungültigen Wert.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der Wert des <paramref name="accessMask" />-Parameters ist 0 (null), oder der <paramref name="inheritanceFlags" />-Parameter bzw. der <paramref name="propagationFlags" />-Parameter enthalten nicht erkannte Flagwerte.</exception>
    </member>
    <member name="P:System.Security.AccessControl.AccessRule.AccessControlType">
      <summary>Ruft den <see cref="T:System.Security.AccessControl.AccessControlType" />-Wert ab, der diesem <see cref="T:System.Security.AccessControl.AccessRule" />-Objekt zugeordnet ist.</summary>
      <returns>Der <see cref="T:System.Security.AccessControl.AccessControlType" />, der diesem <see cref="T:System.Security.AccessControl.AccessRule" />-Objekt zugeordnet sind.</returns>
    </member>
    <member name="T:System.Security.AccessControl.AccessRule`1">
      <summary>Stellt eine Kombination der Identität eines Benutzers, einer Zugriffsmaske und eines Zugriffssteuerungstyps (gewähren oder verweigern) dar.Ein AccessRule`1 Objekt enthält auch Informationen darüber, wie die Regel von untergeordneten Objekten geerbt und wie die Vererbung weitergegeben wird.</summary>
      <typeparam name="T">Der Zugriffsrechtetyp für die Zugriffsregel.</typeparam>
    </member>
    <member name="M:System.Security.AccessControl.AccessRule`1.#ctor(System.Security.Principal.IdentityReference,`0,System.Security.AccessControl.AccessControlType)">
      <summary>Initialisiert eine neue Instanz der AccessRule’1-Klasse unter Verwendung der angegebenen Werte.</summary>
      <param name="identity">Die Identität, für die die Zugriffsregel gilt.</param>
      <param name="rights">Die Rechte der Zugriffsregel.</param>
      <param name="type">Der gültige Zugriffssteuerungstyp.</param>
    </member>
    <member name="M:System.Security.AccessControl.AccessRule`1.#ctor(System.Security.Principal.IdentityReference,`0,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
      <summary>Initialisiert eine neue Instanz der AccessRule’1-Klasse unter Verwendung der angegebenen Werte.</summary>
      <param name="identity">Die Identität, für die die Zugriffsregel gilt.</param>
      <param name="rights">Die Rechte der Zugriffsregel.</param>
      <param name="inheritanceFlags">Die Vererbungseigenschaften der Zugriffsregel.</param>
      <param name="propagationFlags">Gibt an, ob geerbte Zugriffsregeln automatisch weitergegeben werden.Die Weitergabeflags werden ignoriert, wenn <paramref name="inheritanceFlags" /> auf <see cref="F:System.Security.AccessControl.InheritanceFlags.None" /> festgelegt ist.</param>
      <param name="type">Der gültige Zugriffssteuerungstyp.</param>
    </member>
    <member name="M:System.Security.AccessControl.AccessRule`1.#ctor(System.String,`0,System.Security.AccessControl.AccessControlType)">
      <summary>Initialisiert eine neue Instanz der AccessRule’1-Klasse unter Verwendung der angegebenen Werte.</summary>
      <param name="identity">Die Identität, für die die Zugriffsregel gilt.</param>
      <param name="rights">Die Rechte der Zugriffsregel.</param>
      <param name="type">Der gültige Zugriffssteuerungstyp.</param>
    </member>
    <member name="M:System.Security.AccessControl.AccessRule`1.#ctor(System.String,`0,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
      <summary>Initialisiert eine neue Instanz der AccessRule’1-Klasse unter Verwendung der angegebenen Werte.</summary>
      <param name="identity">Die Identität, für die die Zugriffsregel gilt.</param>
      <param name="rights">Die Rechte der Zugriffsregel.</param>
      <param name="inheritanceFlags">Die Vererbungseigenschaften der Zugriffsregel.</param>
      <param name="propagationFlags">Gibt an, ob geerbte Zugriffsregeln automatisch weitergegeben werden.Die Weitergabeflags werden ignoriert, wenn <paramref name="inheritanceFlags" /> auf <see cref="F:System.Security.AccessControl.InheritanceFlags.None" /> festgelegt ist.</param>
      <param name="type">Der gültige Zugriffssteuerungstyp.</param>
    </member>
    <member name="P:System.Security.AccessControl.AccessRule`1.Rights">
      <summary>Ruft die Rechte der aktuellen Instanz ab.</summary>
      <returns>Die Berechtigungen, Umwandlung als Typ &lt;T&gt;, der aktuellen Instanz.</returns>
    </member>
    <member name="T:System.Security.AccessControl.AceEnumerator">
      <summary>Bietet die Möglichkeit, die Access Control Entries (ACEs – Zugriffssteuerungseinträge) in einer Access Control List (ACL – Zugriffssteuerungsliste) zu durchlaufen. </summary>
    </member>
    <member name="P:System.Security.AccessControl.AceEnumerator.Current">
      <summary>Ruft das aktuelle Element in der <see cref="T:System.Security.AccessControl.GenericAce" />-Auflistung ab.Diese Eigenschaft ruft die typfreundliche Version des Objekts ab.</summary>
      <returns>Das aktuelle Element in der <see cref="T:System.Security.AccessControl.GenericAce" />-Auflistung.</returns>
    </member>
    <member name="M:System.Security.AccessControl.AceEnumerator.MoveNext">
      <summary>Setzt den Enumerator auf das nächste Element der <see cref="T:System.Security.AccessControl.GenericAce" />-Auflistung.</summary>
      <returns>true, wenn der Enumerator erfolgreich auf das nächste Element gesetzt wurde, false, wenn der Enumerator das Ende der Auflistung überschritten hat.</returns>
      <exception cref="T:System.InvalidOperationException">Die Auflistung wurde nach dem Erstellen des Enumerators geändert.</exception>
    </member>
    <member name="M:System.Security.AccessControl.AceEnumerator.Reset">
      <summary>Legt den Enumerator auf seine anfängliche Position vor dem ersten Element in der <see cref="T:System.Security.AccessControl.GenericAce" />-Auflistung fest.</summary>
      <exception cref="T:System.InvalidOperationException">Die Auflistung wurde nach dem Erstellen des Enumerators geändert.</exception>
    </member>
    <member name="P:System.Security.AccessControl.AceEnumerator.System#Collections#IEnumerator#Current"></member>
    <member name="T:System.Security.AccessControl.AceFlags">
      <summary>Gibt das Vererbungs- und Überwachungsverhalten eines Zugriffssteuerungseintrags (ACE) an.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.AuditFlags">
      <summary>Alle Zugriffsversuche werden überwacht.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.ContainerInherit">
      <summary>Die Zugriffsmaske wird an untergeordnete Containerobjekte weitergegeben.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.FailedAccess">
      <summary>Fehlgeschlagene Zugriffsversuche werden überwacht.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.InheritanceFlags">
      <summary>Ein logische OR-Verknüpfung von <see cref="F:System.Security.AccessControl.AceFlags.ObjectInherit" />, <see cref="F:System.Security.AccessControl.AceFlags.ContainerInherit" />, <see cref="F:System.Security.AccessControl.AceFlags.NoPropagateInherit" /> und <see cref="F:System.Security.AccessControl.AceFlags.InheritOnly" />.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.Inherited">
      <summary>Ein ACE wird von einem übergeordneten Container geerbt und nicht explizit für ein Objekt festgelegt.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.InheritOnly">
      <summary>Die Zugriffsmaske wird nur an untergeordnete Objekte weitergegeben.Dies schließt untergeordnete Container- und Endobjekte ein.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.None">
      <summary>Es sind keine ACE-Flags festgelegt.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.NoPropagateInherit">
      <summary>Die Zugriffsprüfungen gelten nicht für das Objekt. Sie gelten nur für dessen untergeordnete Elemente.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.ObjectInherit">
      <summary>Die Zugriffsmaske wird an untergeordnete Endobjekte weitergegeben.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.SuccessfulAccess">
      <summary>Erfolgreiche Zugriffsversuche werden überwacht.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AceQualifier">
      <summary>Gibt die Funktion eines Zugriffssteuerungseintrags (Access Control Entry, ACE) an.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceQualifier.AccessAllowed">
      <summary>Zugriff zulassen.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceQualifier.AccessDenied">
      <summary>Zugriff verweigern.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceQualifier.SystemAlarm">
      <summary>Systemwarnsignal auslösen.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceQualifier.SystemAudit">
      <summary>Systemüberwachung veranlassen.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AceType">
      <summary>Definiert die verfügbaren Typen von ACEs (Access Control Entries, Zugriffssteuerungseinträge).</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessAllowed">
      <summary>Gewährt einem bestimmten durch ein <see cref="T:System.Security.Principal.IdentityReference" />-Objekt identifizierten Vertrauensnehmer den Zugriff auf ein Objekt.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessAllowedCallback">
      <summary>Gewährt einem bestimmten durch ein <see cref="T:System.Security.Principal.IdentityReference" />-Objekt identifizierten Vertrauensnehmer den Zugriff auf ein Objekt.Dieser ACE-Typ enthält möglicherweise optionale Rückrufdaten.Bei den Rückrufdaten handelt es sich um ein spezifisches BLOB des Ressourcen-Managers, das nicht interpretiert wird.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessAllowedCallbackObject">
      <summary>Gewährt den Zugriff auf ein Objekt, eine Gruppe von Eigenschaften oder eine Eigenschaft.Der ACE enthält eine Gruppe von Zugriffsrechten, eine GUID, die den Typ des Objekts identifiziert, und ein <see cref="T:System.Security.Principal.IdentityReference" />-Objekt, das den Vertrauensnehmer identifiziert, dem das System den Zugriff gewährt.Der ACE enthält außerdem eine GUID und verschiedene Flags, die die Vererbung des ACE an untergeordnete Objekte steuern.Dieser ACE-Typ enthält möglicherweise optionale Rückrufdaten.Bei den Rückrufdaten handelt es sich um ein spezifisches BLOB des Ressourcen-Managers, das nicht interpretiert wird.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessAllowedCompound">
      <summary>Definiert, aber nicht verwendet.Wird hier nur aus Gründen der Vollständigkeit aufgeführt.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessAllowedObject">
      <summary>Gewährt den Zugriff auf ein Objekt, eine Gruppe von Eigenschaften oder eine Eigenschaft.Der ACE enthält eine Gruppe von Zugriffsrechten, eine GUID, die den Typ des Objekts identifiziert, und ein <see cref="T:System.Security.Principal.IdentityReference" />-Objekt, das den Vertrauensnehmer identifiziert, dem das System den Zugriff gewährt.Der ACE enthält außerdem eine GUID und verschiedene Flags, die die Vererbung des ACE an untergeordnete Objekte steuern.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessDenied">
      <summary>Verweigert einem bestimmten durch ein <see cref="T:System.Security.Principal.IdentityReference" />-Objekt identifizierten Vertrauensnehmer den Zugriff auf ein Objekt.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessDeniedCallback">
      <summary>Verweigert einem bestimmten durch ein <see cref="T:System.Security.Principal.IdentityReference" />-Objekt identifizierten Vertrauensnehmer den Zugriff auf ein Objekt.Dieser ACE-Typ enthält möglicherweise optionale Rückrufdaten.Bei den Rückrufdaten handelt es sich um ein spezifisches BLOB des Ressourcen-Managers, das nicht interpretiert wird.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessDeniedCallbackObject">
      <summary>Verweigert den Zugriff auf ein Objekt, eine Gruppe von Eigenschaften oder eine Eigenschaft.Der ACE enthält eine Gruppe von Zugriffsrechten, eine GUID, die den Typ des Objekts identifiziert, und ein <see cref="T:System.Security.Principal.IdentityReference" />-Objekt, das den Vertrauensnehmer identifiziert, dem das System den Zugriff gewährt.Der ACE enthält außerdem eine GUID und verschiedene Flags, die die Vererbung des ACE an untergeordnete Objekte steuern.Dieser ACE-Typ enthält möglicherweise optionale Rückrufdaten.Bei den Rückrufdaten handelt es sich um ein spezifisches BLOB des Ressourcen-Managers, das nicht interpretiert wird.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessDeniedObject">
      <summary>Verweigert den Zugriff auf ein Objekt, eine Gruppe von Eigenschaften oder eine Eigenschaft.Der ACE enthält eine Gruppe von Zugriffsrechten, eine GUID, die den Typ des Objekts identifiziert, und ein <see cref="T:System.Security.Principal.IdentityReference" />-Objekt, das den Vertrauensnehmer identifiziert, dem das System den Zugriff gewährt.Der ACE enthält außerdem eine GUID und verschiedene Flags, die die Vererbung des ACE an untergeordnete Objekte steuern.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.MaxDefinedAceType">
      <summary>Verfolgt den maximalen definierten ACE-Typ in der Enumeration nach.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAlarm">
      <summary>Für zukünftige Verwendung reserviert.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAlarmCallback">
      <summary>Für zukünftige Verwendung reserviert.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAlarmCallbackObject">
      <summary>Für zukünftige Verwendung reserviert.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAlarmObject">
      <summary>Für zukünftige Verwendung reserviert.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAudit">
      <summary>Bewirkt, dass eine Überwachungsmeldung protokolliert wird, wenn ein angegebener Vertrauensnehmer versucht, den Zugriff auf ein Objekt zu erhalten.Der Vertrauensnehmer wird durch ein <see cref="T:System.Security.Principal.IdentityReference" />-Objekt identifiziert.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAuditCallback">
      <summary>Bewirkt, dass eine Überwachungsmeldung protokolliert wird, wenn ein angegebener Vertrauensnehmer versucht, den Zugriff auf ein Objekt zu erhalten.Der Vertrauensnehmer wird durch ein <see cref="T:System.Security.Principal.IdentityReference" />-Objekt identifiziert.Dieser ACE-Typ enthält möglicherweise optionale Rückrufdaten.Bei den Rückrufdaten handelt es sich um ein spezifisches BLOB des Ressourcen-Managers, das nicht interpretiert wird.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAuditCallbackObject">
      <summary>Bewirkt, dass eine Überwachungsmeldung protokolliert wird, wenn ein angegebener Vertrauensnehmer versucht, den Zugriff auf ein Objekt oder Unterobjekte wie Eigenschaften oder Gruppen von Eigenschaften zu erhalten.Der ACE enthält eine Gruppe von Zugriffsrechten, eine GUID, die den Typ des Objekts oder Unterobjekts identifiziert, und ein <see cref="T:System.Security.Principal.IdentityReference" />-Objekt, das den Vertrauensnehmer identifiziert, dessen Zugriffe das System überwacht.Der ACE enthält außerdem eine GUID und verschiedene Flags, die die Vererbung des ACE an untergeordnete Objekte steuern.Dieser ACE-Typ enthält möglicherweise optionale Rückrufdaten.Bei den Rückrufdaten handelt es sich um ein spezifisches BLOB des Ressourcen-Managers, das nicht interpretiert wird.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAuditObject">
      <summary>Bewirkt, dass eine Überwachungsmeldung protokolliert wird, wenn ein angegebener Vertrauensnehmer versucht, den Zugriff auf ein Objekt oder Unterobjekte wie Eigenschaften oder Gruppen von Eigenschaften zu erhalten.Der ACE enthält eine Gruppe von Zugriffsrechten, eine GUID, die den Typ des Objekts oder Unterobjekts identifiziert, und ein <see cref="T:System.Security.Principal.IdentityReference" />-Objekt, das den Vertrauensnehmer identifiziert, dessen Zugriffe das System überwacht.Der ACE enthält außerdem eine GUID und verschiedene Flags, die die Vererbung des ACE an untergeordnete Objekte steuern.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AuditFlags">
      <summary>Gibt die Bedingungen für das Überwachen von Zugriffsversuchen auf ein sicherungsfähiges Objekt an.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AuditFlags.Failure">
      <summary>Fehlgeschlagene Zugriffsversuche sollen überwacht werden.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AuditFlags.None">
      <summary>Es sollen keine Zugriffsversuche überwacht werden.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AuditFlags.Success">
      <summary>Erfolgreiche Zugriffsversuche sollen überwacht werden.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AuditRule">
      <summary>Stellt eine Kombination aus der Identität eines Benutzers und einer Zugriffsmaske dar.Ein <see cref="T:System.Security.AccessControl.AuditRule" />-Objekt enthält darüber hinaus Informationen zum Erben der Regel durch untergeordnete Objekte, zur Weitergabe der Vererbung sowie zu den Überwachungsbedingungen.</summary>
    </member>
    <member name="M:System.Security.AccessControl.AuditRule.#ctor(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.AccessControl.AuditRule" />-Klasse unter Verwendung der angegebenen Werte.</summary>
      <param name="identity">Die Identität, auf die die Überwachungsregel angewendet wird.Dabei muss es sich um ein Objekt handeln, das in eine <see cref="T:System.Security.Principal.SecurityIdentifier" /> umgewandelt werden kann.</param>
      <param name="accessMask">Die Zugriffsmaske dieser Regel.Die Zugriffsmaske ist eine 32-Bit-Auflistung anonymer Bits, deren Bedeutung von den einzelnen Integratoren definiert wird.</param>
      <param name="isInherited">true, um diese Regel von einem übergeordneten Container zu erben.</param>
      <param name="inheritanceFlags">Die Vererbungseigenschaften der Überwachungsregel.</param>
      <param name="propagationFlags">Gibt an, ob geerbte Überwachungsregeln automatisch weitergegeben werden.Die Weitergabeflags werden ignoriert, wenn <paramref name="inheritanceFlags" /> auf <see cref="F:System.Security.AccessControl.InheritanceFlags.None" /> festgelegt ist.</param>
      <param name="auditFlags">Die Bedingungen, für die die Regel überwacht wird.</param>
      <exception cref="T:System.ArgumentException">Der Wert des <paramref name="identity" />-Parameters kann nicht in eine <see cref="T:System.Security.Principal.SecurityIdentifier" /> umgewandelt werden, oder der <paramref name="auditFlags" />-Parameter enthält einen ungültigen Wert.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der Wert des <paramref name="accessMask" />-Parameters ist 0 (null), oder der <paramref name="inheritanceFlags" />-Parameter bzw. der <paramref name="propagationFlags" />-Parameter enthalten nicht erkannte Flagwerte.</exception>
    </member>
    <member name="P:System.Security.AccessControl.AuditRule.AuditFlags">
      <summary>Ruft die Überwachungsflags für diese Überwachungsregel ab.</summary>
      <returns>Eine bitweise Kombination der Enumerationswerte.Diese Kombination gibt die Überwachungsbedingungen für diese Überwachungsregel an.</returns>
    </member>
    <member name="T:System.Security.AccessControl.AuditRule`1">
      <summary>Stellt eine Kombination aus der Identität eines Benutzers und einer Zugriffsmaske dar.</summary>
      <typeparam name="T"></typeparam>
    </member>
    <member name="M:System.Security.AccessControl.AuditRule`1.#ctor(System.Security.Principal.IdentityReference,`0,System.Security.AccessControl.AuditFlags)">
      <summary>Initialisiert eine neue Instanz der AccessRule’1-Klasse unter Verwendung der angegebenen Werte.</summary>
      <param name="identity">Die Identität, für die diese Überwachungsregel gilt.</param>
      <param name="rights">Die Rechte der Überwachungsregel.</param>
      <param name="flags">Die Bedingungen, für die die Regel überwacht wird.</param>
    </member>
    <member name="M:System.Security.AccessControl.AuditRule`1.#ctor(System.Security.Principal.IdentityReference,`0,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
      <summary>Initialisiert eine neue Instanz der AccessRule’1-Klasse unter Verwendung der angegebenen Werte.</summary>
      <param name="identity">Die Identität, auf die die Überwachungsregel angewendet wird. </param>
      <param name="rights">Die Rechte der Überwachungsregel.</param>
      <param name="inheritanceFlags">Die Vererbungseigenschaften der Überwachungsregel.</param>
      <param name="propagationFlags">Gibt an, ob geerbte Überwachungsregeln automatisch weitergegeben werden.</param>
      <param name="flags">Die Bedingungen, für die die Regel überwacht wird.</param>
    </member>
    <member name="M:System.Security.AccessControl.AuditRule`1.#ctor(System.String,`0,System.Security.AccessControl.AuditFlags)">
      <summary>Initialisiert eine neue Instanz der AccessRule’1-Klasse unter Verwendung der angegebenen Werte.</summary>
      <param name="identity">Die Identität, auf die die Überwachungsregel angewendet wird.</param>
      <param name="rights">Die Rechte der Überwachungsregel.</param>
      <param name="flags">Die Eigenschaften der Überwachungsregel.</param>
    </member>
    <member name="M:System.Security.AccessControl.AuditRule`1.#ctor(System.String,`0,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
      <summary>Initialisiert eine neue Instanz der AccessRule’1-Klasse unter Verwendung der angegebenen Werte.</summary>
      <param name="identity">Die Identität, auf die die Überwachungsregel angewendet wird.</param>
      <param name="rights">Die Rechte der Überwachungsregel.</param>
      <param name="inheritanceFlags">Die Vererbungseigenschaften der Überwachungsregel.</param>
      <param name="propagationFlags">Gibt an, ob geerbte Überwachungsregeln automatisch weitergegeben werden.</param>
      <param name="flags">Die Bedingungen, für die die Regel überwacht wird.</param>
    </member>
    <member name="P:System.Security.AccessControl.AuditRule`1.Rights">
      <summary>Die Rechte der Überwachungsregel.</summary>
      <returns>Gibt <see cref="{0}" /> zurück.</returns>
    </member>
    <member name="T:System.Security.AccessControl.AuthorizationRule">
      <summary>Bestimmt den Zugriff auf sicherungsfähige Objekte.Die abgeleiteten Klassen <see cref="T:System.Security.AccessControl.AccessRule" /> und <see cref="T:System.Security.AccessControl.AuditRule" /> bieten Spezialisierungen für Zugriffs- und Überwachungsfunktionen.</summary>
    </member>
    <member name="M:System.Security.AccessControl.AuthorizationRule.#ctor(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.AuthorizationControl.AccessRule" />-Klasse unter Verwendung der angegebenen Werte.</summary>
      <param name="identity">Die Identität, für die die Zugriffsregel gilt.  Dabei muss es sich um ein Objekt handeln, das in einen <see cref="T:System.Security.Principal.SecurityIdentifier" /> umgewandelt werden kann.</param>
      <param name="accessMask">Die Zugriffsmaske dieser Regel.Die Zugriffsmaske ist eine 32-Bit-Auflistung anonymer Bits, deren Bedeutung von den einzelnen Integratoren definiert wird.</param>
      <param name="isInherited">true, um diese Regel von einem übergeordneten Container zu erben.</param>
      <param name="inheritanceFlags">Die Vererbungseigenschaften der Zugriffsregel.</param>
      <param name="propagationFlags">Gibt an, ob geerbte Zugriffsregeln automatisch weitergegeben werden.Die Weitergabeflags werden ignoriert, wenn <paramref name="inheritanceFlags" /> auf <see cref="F:System.Security.AccessControl.InheritanceFlags.None" /> festgelegt ist.</param>
      <exception cref="T:System.ArgumentException">Der Wert des <paramref name="identity" />-Parameters kann nicht in einen <see cref="T:System.Security.Principal.SecurityIdentifier" /> umgewandelt werden.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der Wert des <paramref name="accessMask" />-Parameters ist 0 (null), oder der <paramref name="inheritanceFlags" />-Parameter bzw. der <paramref name="propagationFlags" />-Parameter enthalten nicht erkannte Flagwerte.</exception>
    </member>
    <member name="P:System.Security.AccessControl.AuthorizationRule.AccessMask">
      <summary>Ruft die Zugriffsmaske für diese Regel ab.</summary>
      <returns>Die Zugriffsmaske für diese Regel.</returns>
    </member>
    <member name="P:System.Security.AccessControl.AuthorizationRule.IdentityReference">
      <summary>Ruft die <see cref="T:System.Security.Principal.IdentityReference" /> ab, für die diese Regel gilt.</summary>
      <returns>Die <see cref="T:System.Security.Principal.IdentityReference" />, für die diese Regel gilt.</returns>
    </member>
    <member name="P:System.Security.AccessControl.AuthorizationRule.InheritanceFlags">
      <summary>Ruft den Wert von Flags ab, mit denen bestimmt wird, wie diese Regel von untergeordneten Objekten geerbt wird.</summary>
      <returns>Eine bitweise Kombination der Enumerationswerte.</returns>
    </member>
    <member name="P:System.Security.AccessControl.AuthorizationRule.IsInherited">
      <summary>Ruft einen Wert ab, der angibt, ob diese Regel explizit festgelegt oder von einem übergeordneten Containerobjekt geerbt wird.</summary>
      <returns>true, wenn diese Regel nicht explizit festgelegt wird, sondern von einem übergeordneten Container geerbt wird.</returns>
    </member>
    <member name="P:System.Security.AccessControl.AuthorizationRule.PropagationFlags">
      <summary>Ruft den Wert der Weitergabeflags ab, die bestimmen, wie die Vererbung dieser Regel an untergeordnete Objekte weitergegeben wird.Diese Eigenschaft ist nur dann relevant, wenn der Wert der <see cref="T:System.Security.AccessControl.InheritanceFlags" />-Enumeration nicht <see cref="F:System.Security.AccessControl.InheritanceFlags.None" /> ist.</summary>
      <returns>Eine bitweise Kombination der Enumerationswerte.</returns>
    </member>
    <member name="T:System.Security.AccessControl.AuthorizationRuleCollection">
      <summary>Stellt eine Auflistung von<see cref="T:System.Security.AccessControl.AuthorizationRule" />-Objekten dar.</summary>
    </member>
    <member name="M:System.Security.AccessControl.AuthorizationRuleCollection.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.AccessControl.AuthorizationRuleCollection" />-Klasse.</summary>
    </member>
    <member name="M:System.Security.AccessControl.AuthorizationRuleCollection.AddRule(System.Security.AccessControl.AuthorizationRule)">
      <summary>Fügt der Auflistung ein <see cref="T:System.Web.Configuration.AuthorizationRule" />-Objekt hinzu.</summary>
      <param name="rule">Das <see cref="T:System.Web.Configuration.AuthorizationRule" />-Objekt, das der Auflistung hinzugefügt werden soll.</param>
    </member>
    <member name="M:System.Security.AccessControl.AuthorizationRuleCollection.CopyTo(System.Security.AccessControl.AuthorizationRule[],System.Int32)">
      <summary>Kopiert den Inhalt der Auflistung in ein Array.</summary>
      <param name="rules">Ein Array, in das der Inhalt der Auflistung kopiert werden soll.</param>
      <param name="index">Der auf 0 (null) basierende Index, ab dem kopiert wird.</param>
    </member>
    <member name="P:System.Security.AccessControl.AuthorizationRuleCollection.Count"></member>
    <member name="P:System.Security.AccessControl.AuthorizationRuleCollection.Item(System.Int32)">
      <summary>Ruft das <see cref="T:System.Security.AccessControl.AuthorizationRule" />-Objekt am angegebenen Index der Auflistung ab.</summary>
      <returns>Das <see cref="T:System.Security.AccessControl.AuthorizationRule" />-Objekt am angegebenen Index.</returns>
      <param name="index">Der nullbasierte Index des abzurufenden <see cref="T:System.Security.AccessControl.AuthorizationRule" />-Objekts.</param>
    </member>
    <member name="M:System.Security.AccessControl.AuthorizationRuleCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)"></member>
    <member name="P:System.Security.AccessControl.AuthorizationRuleCollection.System#Collections#ICollection#IsSynchronized"></member>
    <member name="P:System.Security.AccessControl.AuthorizationRuleCollection.System#Collections#ICollection#SyncRoot"></member>
    <member name="M:System.Security.AccessControl.AuthorizationRuleCollection.System#Collections#IEnumerable#GetEnumerator"></member>
    <member name="T:System.Security.AccessControl.CommonAce">
      <summary>Stellt einen Zugriffssteuerungseintrag (ACE) dar.</summary>
    </member>
    <member name="M:System.Security.AccessControl.CommonAce.#ctor(System.Security.AccessControl.AceFlags,System.Security.AccessControl.AceQualifier,System.Int32,System.Security.Principal.SecurityIdentifier,System.Boolean,System.Byte[])">
      <summary>Initialisiert eine neue Instanz der<see cref="T:System.Security.AccessControl.CommonAce" />-Klasse.</summary>
      <param name="flags">Flags, die Informationen zu Vererbung, Vererbungsweitergabe und Überwachungsbedingungen für den neuen Zugriffssteuerungseintrag (ACE) angeben.</param>
      <param name="qualifier">Die Verwendung des neuen ACE.</param>
      <param name="accessMask">Die Zugriffsmaske für den ACE.</param>
      <param name="sid">Der dem neuen ACE zugeordnete <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <param name="isCallback">true, um anzugeben, dass der neue ACE ein Rückruftyp-ACE ist.</param>
      <param name="opaque">Dem neuen ACE zugeordnete opake Daten.Opake Daten sind nur für Rückruf-ACE-Typen zugelassen.Die Länge dieses Arrays darf nicht größer sein als der Rückgabewert der <see cref="M:System.Security.AccessControl.CommonAce.MaxOpaqueLength(System.Boolean)" />-Methode.</param>
    </member>
    <member name="P:System.Security.AccessControl.CommonAce.BinaryLength">
      <summary>Ruft die Länge der binären Darstellung des aktuellen <see cref="T:System.Security.AccessControl.CommonAce" />-Objekts (in Bytes) ab.Verwenden Sie diese Länge in Verbindung mit der <see cref="M:System.Security.AccessControl.CommonAce.GetBinaryForm(System.Byte[],System.Int32)" />-Methode vor dem Marshallen der ACL in ein binäres Array.</summary>
      <returns>Die Länge der binären Darstellung des aktuellen <see cref="T:System.Security.AccessControl.CommonAce" />-Objekts (in Bytes).</returns>
    </member>
    <member name="M:System.Security.AccessControl.CommonAce.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Marshallt den Inhalt des <see cref="T:System.Security.AccessControl.CommonAce" />-Objekts in das angegebene Bytearray, wobei beim angegebenen Offset begonnen wird.</summary>
      <param name="binaryForm">Das Bytearray, in das der Inhalt des <see cref="T:System.Security.AccessControl.CommonAce" />-Objekts gemarshallt wird.</param>
      <param name="offset">Der Offset, bei dem das Marshallen begonnen werden soll.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> ist negativ oder zu hoch, um den ganzen <see cref="T:System.Security.AccessControl.CommonAce" /> in das <paramref name="binaryForm" />-Array zu kopieren.</exception>
    </member>
    <member name="M:System.Security.AccessControl.CommonAce.MaxOpaqueLength(System.Boolean)">
      <summary>Gibt die maximal zulässige Länge eines opaken Daten-BLOBs für Rückruf-ACEs (Access Control Entry – Zugriffssteuerungseintrag) zurück.</summary>
      <returns>Die zulässige Länge eines opaken Daten-BLOBs.</returns>
      <param name="isCallback">true, um anzugeben, dass das <see cref="T:System.Security.AccessControl.CommonAce" />-Objekt ein Rückruf-ACE-Typ ist.</param>
    </member>
    <member name="T:System.Security.AccessControl.CommonAcl">
      <summary>Stellt eine Zugriffssteuerungsliste (ACL) dar und ist die Basisklasse für die <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />-Klasse und die <see cref="T:System.Security.AccessControl.SystemAcl" />-Klasse.</summary>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.BinaryLength">
      <summary>Ruft die Länge der binären Darstellung des aktuellen <see cref="T:System.Security.AccessControl.CommonAcl" />-Objekts (in Bytes) ab.Diese Länge muss verwendet werden, bevor die Zugriffssteuerungsliste (ACL) mithilfe der <see cref="M:System.Security.AccessControl.CommonAcl.GetBinaryForm(System.Byte[],System.Int32)" />-Methode in ein binäres Array gemarshallt wird.</summary>
      <returns>Die Länge der binären Darstellung des aktuellen <see cref="T:System.Security.AccessControl.CommonAcl" />-Objekts (in Bytes).</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.Count">
      <summary>Ruft die Anzahl der ACEs im aktuellen <see cref="T:System.Security.AccessControl.CommonAcl" />-Objekt ab.</summary>
      <returns>Die Anzahl der ACEs im aktuellen <see cref="T:System.Security.AccessControl.CommonAcl" />-Objekt.</returns>
    </member>
    <member name="M:System.Security.AccessControl.CommonAcl.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Marshallt den Inhalt des <see cref="T:System.Security.AccessControl.CommonAcl" />-Objekts in das angegebene Bytearray, wobei beim angegebenen Offset begonnen wird.</summary>
      <param name="binaryForm">Das Bytearray, in das der Inhalt von <see cref="T:System.Security.AccessControl.CommonAcl" /> gemarshallt wird.</param>
      <param name="offset">Der Offset, bei dem das Marshallen begonnen werden soll.</param>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.IsCanonical">
      <summary>Ruft einen booleschen Wert ab, der angibt, ob sich die Zugriffssteuerungseinträge (ACEs) im aktuellen <see cref="T:System.Security.AccessControl.CommonAcl" />-Objekt in kanonischer Reihenfolge befinden.</summary>
      <returns>true, wenn sich die ACEs im aktuellen <see cref="T:System.Security.AccessControl.CommonAcl" />-Objekt in kanonischer Reihenfolge befinden, andernfalls false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.IsContainer">
      <summary>Legt fest, ob das <see cref="T:System.Security.AccessControl.CommonAcl" />-Objekt ein Container ist. </summary>
      <returns>true, wenn das aktuelle <see cref="T:System.Security.AccessControl.CommonAcl" />-Objekt ein Container ist.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.IsDS">
      <summary>Legt fest, ob das aktuelle <see cref="T:System.Security.AccessControl.CommonAcl" />-Objekt eine Zugriffssteuerungsliste (ACL) für ein Verzeichnisobjekt ist.</summary>
      <returns>true, wenn das aktuelle <see cref="T:System.Security.AccessControl.CommonAcl" />-Objekt eine ACL für ein Verzeichnisobjekt ist.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.Item(System.Int32)">
      <summary>Ruft die <see cref="T:System.Security.AccessControl.CommonAce" />-Klasse am angegebenen Index ab oder legt diese fest.</summary>
      <returns>Der <see cref="T:System.Security.AccessControl.CommonAce" /> am angegebenen Index.</returns>
      <param name="index">Der nullbasierte Index von <see cref="T:System.Security.AccessControl.CommonAce" />, der abgerufen oder festgelegt werden soll.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonAcl.Purge(System.Security.Principal.SecurityIdentifier)">
      <summary>Entfernt alle Zugriffssteuerungseinträge (ACEs) in diesem <see cref="T:System.Security.AccessControl.CommonAcl" />-Objekt, die dem angegebenen <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekt zugeordnet sind.</summary>
      <param name="sid">Das <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekt, für das eine Überprüfung erfolgen soll.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonAcl.RemoveInheritedAces">
      <summary>Entfernt alle geerbten Zugriffssteuerungseinträge (ACEs) aus diesem <see cref="T:System.Security.AccessControl.CommonAcl" />-Objekt.</summary>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.Revision">
      <summary>Ruft die Revisionsebene der <see cref="T:System.Security.AccessControl.CommonAcl" /> ab.</summary>
      <returns>Ein Bytewert, der die Revisionsebene der <see cref="T:System.Security.AccessControl.CommonAcl" /> angibt.</returns>
    </member>
    <member name="T:System.Security.AccessControl.CommonObjectSecurity">
      <summary>Steuert den Zugriff auf Objekte, ohne dass ACLs (Access Control Lists, Zugriffssteuerungslisten) dabei direkt bearbeitet werden.Diese Klasse ist die abstrakte Basisklasse für die <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />-Klasse.</summary>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.#ctor(System.Boolean)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />-Klasse.</summary>
      <param name="isContainer">true, wenn das neue Objekt ein Containerobjekt ist.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.AddAccessRule(System.Security.AccessControl.AccessRule)">
      <summary>Fügt der DACL (Discretionary Access Control List), die diesem <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />-Objekt zugeordnet ist, die angegebene Zugriffsregel hinzu.</summary>
      <param name="rule">Die hinzuzufügende Zugriffsregel.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.AddAuditRule(System.Security.AccessControl.AuditRule)">
      <summary>Fügt der SACL (System Access Control List), die diesem <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />-Objekt zugeordnet ist, die angegebene Überwachungsregel hinzu.</summary>
      <param name="rule">Die hinzuzufügende Überwachungsregel.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.GetAccessRules(System.Boolean,System.Boolean,System.Type)">
      <summary>Ruft eine Auflistung der Zugriffsregeln ab, die der angegebenen Sicherheits-ID zugeordnet sind.</summary>
      <returns>Die Auflistung der Zugriffsregeln, die dem angegebenen <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekt zugeordnet sind.</returns>
      <param name="includeExplicit">true, um explizit für das Objekt festgelegte Zugriffsregeln einzuschließen.</param>
      <param name="includeInherited">true, um geerbte Zugriffsregeln einzuschließen.</param>
      <param name="targetType">Gibt an, ob die Sicherheits-ID, für die Zugriffsregeln abgerufen werden sollen, vom Typ T:System.Security.Principal.SecurityIdentifier oder vom Typ T:System.Security.Principal.NTAccount ist.Der Wert dieses Parameters muss ein Typ sein, der in den <see cref="T:System.Security.Principal.SecurityIdentifier" />-Typ übersetzt werden soll.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.GetAuditRules(System.Boolean,System.Boolean,System.Type)">
      <summary>Ruft eine Auflistung der Überwachungsregeln ab, die der angegebenen Sicherheits-ID zugeordnet sind.</summary>
      <returns>Die Auflistung der Überwachungsregeln, die dem angegebenen <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekt zugeordnet sind.</returns>
      <param name="includeExplicit">true, um explizit für das Objekt festgelegte Überwachungsregeln einzuschließen.</param>
      <param name="includeInherited">true, um geerbte Überwachungsregeln einzuschließen.</param>
      <param name="targetType">Die Sicherheits-ID, für die Überwachungsregeln abgerufen werden sollen.Dabei muss es sich um ein Objekt handeln, das in ein <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekt umgewandelt werden kann.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.ModifyAccess(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AccessRule,System.Boolean@)">
      <summary>Wendet die angegebene Änderung auf die DACL an, die diesem <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />-Objekt zugeordnet ist.</summary>
      <returns>true, wenn die DACL erfolgreich geändert wird, andernfalls false.</returns>
      <param name="modification">Die Änderung, die auf die DACL angewendet werden soll.</param>
      <param name="rule">Die zu ändernde Zugriffsregel.</param>
      <param name="modified">true, wenn die DACL erfolgreich geändert wird, andernfalls false.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.ModifyAudit(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AuditRule,System.Boolean@)">
      <summary>Wendet die angegebene Änderung auf die SACL (System Access Control List) an, die diesem <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />-Objekt zugeordnet ist.</summary>
      <returns>true, wenn die SACL erfolgreich geändert wird, andernfalls false.</returns>
      <param name="modification">Die Änderung, die auf die SACL angewendet werden soll.</param>
      <param name="rule">Die zu ändernde Überwachungsregel.</param>
      <param name="modified">true, wenn die SACL erfolgreich geändert wird, andernfalls false.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAccessRule(System.Security.AccessControl.AccessRule)">
      <summary>Entfernt Zugriffsregeln, die dieselbe Sicherheits-ID und dieselbe Zugriffsmaske wie die angegebene Zugriffsregel enthalten, aus der DACL (Discretionary Access Control List), die diesem <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />-Objekt zugeordnet ist.</summary>
      <returns>true, wenn die Zugriffsregel erfolgreich entfernt wurde; andernfalls false.</returns>
      <param name="rule">Die zu entfernende Zugriffsregel.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAccessRuleAll(System.Security.AccessControl.AccessRule)">
      <summary>Entfernt alle Zugriffsregeln, die dieselbe Sicherheits-ID wie die angegebene Zugriffsregel enthalten, aus der DACL (Discretionary Access Control List), die diesem <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />-Objekt zugeordnet ist.</summary>
      <param name="rule">Die zu entfernende Zugriffsregel.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAccessRuleSpecific(System.Security.AccessControl.AccessRule)">
      <summary>Entfernt alle Zugriffsregeln, die der angegebenen Zugriffsregel genau entsprechen, aus der DACL (Discretionary Access Control List), die diesem <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />-Objekt zugeordnet ist.</summary>
      <param name="rule">Die zu entfernende Zugriffsregel.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAuditRule(System.Security.AccessControl.AuditRule)">
      <summary>Entfernt Überwachungsregeln, die dieselbe Sicherheits-ID und dieselbe Zugriffsmaske wie die angegebene Überwachungsregel enthalten, aus der SACL (System Access Control List), die diesem <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />-Objekt zugeordnet ist.</summary>
      <returns>true, wenn die Überwachungsregel erfolgreich entfernt wurde; andernfalls false.</returns>
      <param name="rule">Die zu entfernende Überwachungsregel.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAuditRuleAll(System.Security.AccessControl.AuditRule)">
      <summary>Entfernt alle Überwachungsregeln, die dieselbe Sicherheits-ID wie die angegebene Überwachungsregel enthalten, aus der SACL (System Access Control List), die diesem <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />-Objekt zugeordnet ist.</summary>
      <param name="rule">Die zu entfernende Überwachungsregel.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAuditRuleSpecific(System.Security.AccessControl.AuditRule)">
      <summary>Entfernt alle Überwachungsregeln, die der angegebenen Überwachungsregel genau entsprechen, aus der SACL, die diesem <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />-Objekt zugeordnet ist.</summary>
      <param name="rule">Die zu entfernende Überwachungsregel.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.ResetAccessRule(System.Security.AccessControl.AccessRule)">
      <summary>Entfernt alle Zugriffsregeln in der DACL, die diesem <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />-Objekt zugeordnet ist, und fügt anschließend die angegebene Zugriffsregel hinzu.</summary>
      <param name="rule">Die zurückzusetzende Zugriffsregel.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.SetAccessRule(System.Security.AccessControl.AccessRule)">
      <summary>Entfernt alle Zugriffsregeln, die dieselbe Sicherheits-ID und denselben Qualifizierer wie die angegebene Zugriffsregel in der DACL (Discretionary Access Control List) enthalten, die diesem <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />-Objekt zugeordnet ist, und fügt anschließend die angegebene Zugriffsregel hinzu.</summary>
      <param name="rule">Die festzulegende Zugriffsregel.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.SetAuditRule(System.Security.AccessControl.AuditRule)">
      <summary>Entfernt alle Überwachungsregeln, die dieselbe Sicherheits-ID und denselben Qualifizierer wie die angegebene Überwachungsregel in der SACL (System Access Control List) enthalten, die diesem <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />-Objekt zugeordnet ist, und fügt anschließend die angegebene Überwachungsregel hinzu.</summary>
      <param name="rule">Die festzulegende Überwachungsregel.</param>
    </member>
    <member name="T:System.Security.AccessControl.CommonSecurityDescriptor">
      <summary>Stellt eine Sicherheitsbeschreibung dar.Zu einer Sicherheitsbeschreibung gehören ein Besitzer, eine primäre Gruppe, eine besitzerverwaltete Zugriffssteuerungsliste (Discretionary Access Control List, DACL) und eine System-Zugriffssteuerungsliste (System Access Control List, SACL).</summary>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.#ctor(System.Boolean,System.Boolean,System.Byte[],System.Int32)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />-Klasse unter Verwendung des angegebenen Arrays von Bytewerten.</summary>
      <param name="isContainer">true, wenn die neue Sicherheitsbeschreibung einem Containerobjekt zugeordnet ist.</param>
      <param name="isDS">true, wenn die neue Sicherheitsbeschreibung einem Verzeichnisobjekt zugeordnet ist.</param>
      <param name="binaryForm">Das Array von Bytewerten, aus dem das neue <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />-Objekt erstellt werden soll.</param>
      <param name="offset">Der Offset im <paramref name="binaryForm" />-Array, an dem mit dem Kopieren begonnen werden soll.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.#ctor(System.Boolean,System.Boolean,System.Security.AccessControl.ControlFlags,System.Security.Principal.SecurityIdentifier,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.SystemAcl,System.Security.AccessControl.DiscretionaryAcl)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />-Klasse mit den angegebenen Informationen.</summary>
      <param name="isContainer">true, wenn die neue Sicherheitsbeschreibung einem Containerobjekt zugeordnet ist.</param>
      <param name="isDS">true, wenn die neue Sicherheitsbeschreibung einem Verzeichnisobjekt zugeordnet ist.</param>
      <param name="flags">Flags, die das Verhalten des neuen <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />-Objekts angeben.</param>
      <param name="owner">Der Besitzer des neuen <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />-Objekts.</param>
      <param name="group">Die primäre Gruppe für das neue <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />-Objekt.</param>
      <param name="systemAcl">Die SACL für das neue <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />-Objekt.</param>
      <param name="discretionaryAcl">Die DACL für das neue <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />-Objekt.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.#ctor(System.Boolean,System.Boolean,System.Security.AccessControl.RawSecurityDescriptor)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />-Klasse mit dem angegebenen <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />-Objekt.</summary>
      <param name="isContainer">true, wenn die neue Sicherheitsbeschreibung einem Containerobjekt zugeordnet ist.</param>
      <param name="isDS">true, wenn die neue Sicherheitsbeschreibung einem Verzeichnisobjekt zugeordnet ist.</param>
      <param name="rawSecurityDescriptor">Das <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />-Objekt, aus dem das neue <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />-Objekt erstellt werden soll.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.#ctor(System.Boolean,System.Boolean,System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />-Klasse mit der angegebenen SDDL-Zeichenfolge (Security Descriptor Definition Language).</summary>
      <param name="isContainer">true, wenn die neue Sicherheitsbeschreibung einem Containerobjekt zugeordnet ist.</param>
      <param name="isDS">true, wenn die neue Sicherheitsbeschreibung einem Verzeichnisobjekt zugeordnet ist.</param>
      <param name="sddlForm">Die SDDL-Zeichenfolge, aus der das neue <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />-Objekt erstellt werden soll.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.AddDiscretionaryAcl(System.Byte,System.Int32)">
      <summary>Legt die <see cref="P:System.Security.AccessControl.CommonSecurityDescriptor.DiscretionaryAcl" /> -Eigenschaft für diese <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> Instanz und setzt die <see cref="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclPresent" /> Flag.</summary>
      <param name="revision">Die Revisionsebene des neuen <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />-Objekts.</param>
      <param name="trusted">Die Anzahl der möglichen Zugriffssteuerungseinträge (ACEs – Access Control Entries), die dieses <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />-Objekt enthalten kann.Diese Zahl sollte nur als Anhaltspunkt verwendet werden.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.AddSystemAcl(System.Byte,System.Int32)">
      <summary>Legt die <see cref="P:System.Security.AccessControl.CommonSecurityDescriptor.SystemAcl" /> -Eigenschaft für diese <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> Instanz und setzt die <see cref="F:System.Security.AccessControl.ControlFlags.SystemAclPresent" /> Flag.</summary>
      <param name="revision">Die Revisionsebene des neuen <see cref="T:System.Security.AccessControl.SystemAcl" />-Objekts.</param>
      <param name="trusted">Die Anzahl der möglichen Zugriffssteuerungseinträge (ACEs – Access Control Entries), die dieses <see cref="T:System.Security.AccessControl.SystemAcl" />-Objekt enthalten kann.Diese Zahl sollte nur als Anhaltspunkt verwendet werden.</param>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.ControlFlags">
      <summary>Ruft Werte ab, die das Verhalten des <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />-Objekts angeben.</summary>
      <returns>Mindestens ein Wert der <see cref="T:System.Security.AccessControl.ControlFlags" />-Enumeration, kombiniert mit einem logischen Operator OR.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.DiscretionaryAcl">
      <summary>Ruft die DACL für dieses <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />-Objekt ab oder legt diese fest.Die DACL enthält Zugriffsregeln.</summary>
      <returns>Die DACL für dieses <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />-Objekt.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.Group">
      <summary>Ruft die primäre Gruppe für dieses <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />-Objekt ab oder legt diese fest.</summary>
      <returns>Die primäre Gruppe für dieses <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />-Objekt.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.IsContainer">
      <summary>Ruft einen booleschen Wert ab, der angibt, ob das diesem <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />-Objekt zugeordnete Objekt ein Containerobjekt ist.</summary>
      <returns>true, wenn das diesem <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />-Objekt zugeordnete Objekt ein Containerobjekt ist, andernfalls false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.IsDiscretionaryAclCanonical">
      <summary>Ruft einen booleschen Wert ab, der angibt, ob die diesem <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />-Objekt zugeordnete DACL eine kanonische Reihenfolge aufweist.</summary>
      <returns>true, wenn die diesem <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />-Objekt zugeordnete DACL eine kanonische Reihenfolge aufweist, andernfalls false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.IsDS">
      <summary>Ruft einen booleschen Wert ab, der angibt, ob das diesem <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />-Objekt zugeordnete Objekt ein Verzeichnisobjekt ist.</summary>
      <returns>true, wenn das diesem <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />-Objekt zugeordnete Objekt ein Verzeichnisobjekt ist, andernfalls false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.IsSystemAclCanonical">
      <summary>Ruft einen booleschen Wert ab, der angibt, ob die diesem <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />-Objekt zugeordnete SACL eine kanonische Reihenfolge aufweist.</summary>
      <returns>true, wenn die diesem <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />-Objekt zugeordnete SACL eine kanonische Reihenfolge aufweist, andernfalls false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.Owner">
      <summary>Ruft den Besitzer des Objekts ab, dem dieses <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />-Objekt zugeordnet ist.</summary>
      <returns>Der Besitzer des Objekts, dem dieses <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />-Objekt zugeordnet ist.</returns>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.PurgeAccessControl(System.Security.Principal.SecurityIdentifier)">
      <summary>Entfernt alle Zugriffsregeln für die angegebene Sicherheits-ID aus der diesem <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> Objekt zugeordneten DACL.</summary>
      <param name="sid">Die Sicherheits-ID, für die Zugriffsregeln entfernt werden sollen.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.PurgeAudit(System.Security.Principal.SecurityIdentifier)">
      <summary>Entfernt alle Überwachungsregeln für die angegebene Sicherheits-ID aus der diesem <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />-Objekt zugeordneten SACL.</summary>
      <param name="sid">Die Sicherheits-ID, für die Überwachungsregeln entfernt werden sollen.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.SetDiscretionaryAclProtection(System.Boolean,System.Boolean)">
      <summary>Legt den Vererbungsschutz für die DACL fest, die diesem <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />-Objekt zugeordnet ist.Geschützte DACLs erben keine Zugriffsregeln von übergeordneten Containern.</summary>
      <param name="isProtected">true, wenn die DACL vor Vererbung geschützt werden soll.</param>
      <param name="preserveInheritance">true, wenn geerbte Zugriffsregeln in der DACL beibehalten werden sollen, false, wenn geerbte Zugriffsregeln aus der DACL entfernt werden sollen.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.SetSystemAclProtection(System.Boolean,System.Boolean)">
      <summary>Legt den Vererbungsschutz für die SACL fest, die diesem <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />-Objekt zugeordnet ist.Geschützte SACLs erben keine Überwachungsregeln von übergeordneten Containern.</summary>
      <param name="isProtected">true, wenn die SACL vor Vererbung geschützt werden soll.</param>
      <param name="preserveInheritance">true, wenn geerbte Überwachungsregeln in der SACL beibehalten werden sollen, false, wenn geerbte Überwachungsregeln aus der SACL entfernt werden sollen.</param>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.SystemAcl">
      <summary>Ruft die SACL für dieses <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />-Objekt ab oder legt diese fest.Die SACL enthält Überwachungsregeln.</summary>
      <returns>Die SACL für dieses <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />-Objekt.</returns>
    </member>
    <member name="T:System.Security.AccessControl.CompoundAce">
      <summary>Stellt einen zusammengesetzten ACE (Access Control Entry, Zugriffssteuerungseintrag) dar.</summary>
    </member>
    <member name="M:System.Security.AccessControl.CompoundAce.#ctor(System.Security.AccessControl.AceFlags,System.Int32,System.Security.AccessControl.CompoundAceType,System.Security.Principal.SecurityIdentifier)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.AccessControl.CompoundAce" />-Klasse.</summary>
      <param name="flags">Enthält Flags, die Informationen zu Vererbung, Vererbungsweitergabe und Überwachungsbedingungen für den neuen ACE angeben.</param>
      <param name="accessMask">Die Zugriffsmaske für den ACE.</param>
      <param name="compoundAceType">Ein Wert aus der <see cref="T:System.Security.AccessControl.CompoundAceType" />-Enumeration.</param>
      <param name="sid">Der dem neuen ACE zugeordnete <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
    </member>
    <member name="P:System.Security.AccessControl.CompoundAce.BinaryLength">
      <summary>Ruft die Länge der binären Darstellung des aktuellen <see cref="T:System.Security.AccessControl.CompoundAce" />-Objekts (in Byte) ab.Diese Länge muss verwendet werden, bevor die ACL mithilfe der <see cref="M:System.Security.AccessControl.CompoundAce.GetBinaryForm" />-Methode in ein binäres Array gemarshallt wird.</summary>
      <returns>Die Länge der binären Darstellung des aktuellen <see cref="T:System.Security.AccessControl.CompoundAce" />-Objekts (in Byte).</returns>
    </member>
    <member name="P:System.Security.AccessControl.CompoundAce.CompoundAceType">
      <summary>Ruft den Typ dieses <see cref="T:System.Security.AccessControl.CompoundAce" />-Objekts ab oder legt diesen fest.</summary>
      <returns>Der Typ dieses <see cref="T:System.Security.AccessControl.CompoundAce" />-Objekts.</returns>
    </member>
    <member name="M:System.Security.AccessControl.CompoundAce.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Marshallt den Inhalt des <see cref="T:System.Security.AccessControl.CompoundAce" />-Objekts in das angegebene Bytearray, wobei beim angegebenen Offset begonnen wird.</summary>
      <param name="binaryForm">Das Bytearray, in das der Inhalt von <see cref="T:System.Security.AccessControl.CompoundAce" /> gemarshallt wird.</param>
      <param name="offset">Der Offset, bei dem das Marshallen begonnen werden soll.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> ist negativ oder zu hoch, um den gesamten <see cref="T:System.Security.AccessControl.CompoundAce" /> in <paramref name="array" /> zu kopieren.</exception>
    </member>
    <member name="T:System.Security.AccessControl.CompoundAceType">
      <summary>Gibt den Typ eines <see cref="T:System.Security.AccessControl.CompoundAce" />-Objekts an.</summary>
    </member>
    <member name="F:System.Security.AccessControl.CompoundAceType.Impersonation">
      <summary>Das <see cref="T:System.Security.AccessControl.CompoundAce" />-Objekt wird für Identitätswechsel verwendet.</summary>
    </member>
    <member name="T:System.Security.AccessControl.ControlFlags">
      <summary>Diese Flags wirken sich auf das Verhalten der Sicherheitsbeschreibung aus.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclAutoInherited">
      <summary>Gibt an, dass die freigegebene Zugriffssteuerungsliste (DACL – Discretionary Access Control List) automatisch vom übergeordneten Element geerbt wurde.Wird nur von Ressourcen-Managern festgelegt.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclAutoInheritRequired">
      <summary>Sie wird ignoriert.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclDefaulted">
      <summary>Gibt an, dass die DACL durch einen Standardmechanismus abgerufen wurde.Wird nur von Ressourcen-Managern festgelegt.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclPresent">
      <summary>Gibt an, dass die DACL nicht null ist.Wird von Ressourcen-Managern oder Benutzern festgelegt.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclProtected">
      <summary>Gibt an, dass der Ressourcen-Manager eine automatische Vererbung verhindert.Wird von Ressourcen-Managern oder Benutzern festgelegt.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclUntrusted">
      <summary>Sie wird ignoriert.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.GroupDefaulted">
      <summary>Gibt an, dass der <see cref="T:System.Security.Principal.SecurityIdentifier" /> der Gruppe durch einen Standardmechanismus abgerufen wurde.Wird nur von Ressourcen-Manager festgelegt und darf nicht durch Aufrufer festgelegt werden.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.None">
      <summary>Keine Steuerungsflags.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.OwnerDefaulted">
      <summary>Gibt an, dass der <see cref="T:System.Security.Principal.SecurityIdentifier" /> des Besitzers durch einen Standardmechnismus abgerufen wurde.Wird nur von Ressourcen-Manager festgelegt und darf nicht durch Aufrufer festgelegt werden.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.RMControlValid">
      <summary>Gibt an, dass der Inhalt des Felds "Reserved" gültig ist.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.SelfRelative">
      <summary>Gibt an, dass die binäre Darstellung der Sicherheitsbeschreibung im selbstbezogenen Format vorliegt.  Dieses Flag wird immer festgelegt.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.ServerSecurity">
      <summary>Sie wird ignoriert.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.SystemAclAutoInherited">
      <summary>Gibt an, dass die freigegebene Zugriffsliste (SACL – System Access Control List) automatisch vom übergeordneten Element geerbt wurde.Wird nur von Ressourcen-Managern festgelegt.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.SystemAclAutoInheritRequired">
      <summary>Sie wird ignoriert.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.SystemAclDefaulted">
      <summary>Gibt an, dass die SACL (System Access Control List, Systemzugriffssteuerungsliste) durch einen Standardmechanismus abgerufen wurde.Wird nur von Ressourcen-Managern festgelegt.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.SystemAclPresent">
      <summary>Gibt an, dass die SACL nicht null ist.Wird von Ressourcen-Managern oder Benutzern festgelegt.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.SystemAclProtected">
      <summary>Gibt an, dass der Ressourcen-Manager eine automatische Vererbung verhindert.Wird von Ressourcen-Managern oder Benutzern festgelegt.</summary>
    </member>
    <member name="T:System.Security.AccessControl.CustomAce">
      <summary>Stellt einen Zugriffssteuerungseintrag (ACE – Access Control Entry) dar, der nicht durch einen der Member der <see cref="T:System.Security.AccessControl.AceType" />-Enumeration definiert ist.</summary>
    </member>
    <member name="M:System.Security.AccessControl.CustomAce.#ctor(System.Security.AccessControl.AceType,System.Security.AccessControl.AceFlags,System.Byte[])">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.AccessControl.CustomAce" />-Klasse.</summary>
      <param name="type">Typ des neuen Zugriffssteuerungseintrags (ACE).Dieser Wert muss größer als <see cref="F:System.Security.AccessControl.AceType.MaxDefinedAceType" /> sein.</param>
      <param name="flags">Flags, die Informationen zu Vererbung, Weitergabe der Vererbung und Überwachungsbedingungen für den neuen ACE angeben.</param>
      <param name="opaque">Ein Array von Bytewerten, das die Daten für den neuen ACE enthält.Dieser Wert kann null sein.Die Länge dieses Arrays darf nicht größer als der Wert des <see cref="F:System.Security.AccessControl.CustomAce.MaxOpaqueLength" />-Felds sein und muss ein Vielfaches von vier sein.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Der Wert des <paramref name="type" />-Parameters ist nicht größer als <see cref="F:System.Security.AccessControl.AceType.MaxDefinedAceType" />, oder die Länge des <paramref name="opaque" />-Arrays ist größer als der Wert des <see cref="F:System.Security.AccessControl.CustomAce.MaxOpaqueLength" />-Felds oder kein Vielfaches von vier.</exception>
    </member>
    <member name="P:System.Security.AccessControl.CustomAce.BinaryLength">
      <summary>Ruft die Länge der binären Darstellung des aktuellen <see cref="T:System.Security.AccessControl.CustomAce" />-Objekts (in Byte) ab.Diese Länge muss verwendet werden, bevor die ACL mithilfe der <see cref="M:System.Security.AccessControl.CustomAce.GetBinaryForm" />-Methode in ein binäres Array gemarshallt wird.</summary>
      <returns>Die Länge der binären Darstellung des aktuellen <see cref="T:System.Security.AccessControl.CustomAce" />-Objekts (in Byte).</returns>
    </member>
    <member name="M:System.Security.AccessControl.CustomAce.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Marshallt den Inhalt des <see cref="T:System.Security.AccessControl.CustomAce" />-Objekts in das angegebene Bytearray, wobei beim angegebenen Offset begonnen wird.</summary>
      <param name="binaryForm">Das Bytearray, in das der Inhalt von <see cref="T:System.Security.AccessControl.CustomAce" /> gemarshallt wird.</param>
      <param name="offset">Der Offset, bei dem das Marshallen begonnen werden soll.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> ist negativ oder zu hoch, um den gesamten <see cref="T:System.Security.AccessControl.CustomAce" /> in <paramref name="array" /> zu kopieren.</exception>
    </member>
    <member name="M:System.Security.AccessControl.CustomAce.GetOpaque">
      <summary>Gibt die Länge der opaken Daten an, die diesem <see cref="T:System.Security.AccessControl.CustomAce" />-Objekt zugeordnet sind. </summary>
      <returns>Ein Array von Bytewerten, das die opaken Daten darstellt, die diesem <see cref="T:System.Security.AccessControl.CustomAce" />-Objekt zugeordnet sind.</returns>
    </member>
    <member name="F:System.Security.AccessControl.CustomAce.MaxOpaqueLength">
      <summary>Gibt die maximal zulässige Länge eines nicht opaken Daten-BLOBs für dieses<see cref="T:System.Security.AccessControl.CustomAce" />-Objekt zurück.</summary>
    </member>
    <member name="P:System.Security.AccessControl.CustomAce.OpaqueLength">
      <summary>Ruft die Länge der opaken Daten ab, die diesem <see cref="T:System.Security.AccessControl.CustomAce" />-Objekt zugeordnet sind.</summary>
      <returns>Die Länge der opaken Rückrufdaten.</returns>
    </member>
    <member name="M:System.Security.AccessControl.CustomAce.SetOpaque(System.Byte[])">
      <summary>Legt die opaken Rückrufdaten fest, die diesem <see cref="T:System.Security.AccessControl.CustomAce" />-Objekt zugeordnet sind.</summary>
      <param name="opaque">Ein Array von Bytewerten, das die opaken Rückrufdaten für dieses <see cref="T:System.Security.AccessControl.CustomAce" />-Objekt darstellt.</param>
    </member>
    <member name="T:System.Security.AccessControl.DiscretionaryAcl">
      <summary>Stellt eine DACL (Discretionary Access Control List, freigegebene Zugriffssteuerungsliste) dar.</summary>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.#ctor(System.Boolean,System.Boolean,System.Byte,System.Int32)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />-Klasse mit den angegebenen Werten.</summary>
      <param name="isContainer">true, wenn das neue <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />-Objekt ein Container ist.</param>
      <param name="isDS">true, wenn das neue <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />-Objekt eine Zugriffssteuerungsliste (ACL) für ein Verzeichnisobjekt ist.</param>
      <param name="revision">Die Revisionsebene des neuen <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />-Objekts.</param>
      <param name="capacity">Die Anzahl der möglichen Zugriffssteuerungseinträge (ACEs – Access Control Entries), die dieses <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />-Objekt enthalten kann.Diese Zahl sollte nur als Anhaltspunkt verwendet werden.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.#ctor(System.Boolean,System.Boolean,System.Int32)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />-Klasse mit den angegebenen Werten.</summary>
      <param name="isContainer">true, wenn das neue <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />-Objekt ein Container ist.</param>
      <param name="isDS">true, wenn das neue <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />-Objekt eine Zugriffssteuerungsliste (ACL) für ein Verzeichnisobjekt ist.</param>
      <param name="capacity">Die Anzahl der möglichen Zugriffssteuerungseinträge (ACEs – Access Control Entries), die dieses <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />-Objekt enthalten kann.Diese Zahl sollte nur als Anhaltspunkt verwendet werden.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.#ctor(System.Boolean,System.Boolean,System.Security.AccessControl.RawAcl)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />-Klasse mit den angegebenen Werten des angegebenen <see cref="T:System.Security.AccessControl.RawAcl" />-Objekts.</summary>
      <param name="isContainer">true, wenn das neue <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />-Objekt ein Container ist.</param>
      <param name="isDS">true, wenn das neue <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />-Objekt eine Zugriffssteuerungsliste (ACL) für ein Verzeichnisobjekt ist.</param>
      <param name="rawAcl">Das zugrunde liegende <see cref="T:System.Security.AccessControl.RawAcl" />-Objekt für das neue <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />-Objekt.Geben Sie null an, um eine leere ACL zu erstellen.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.AddAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Fügt dem aktuellen <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />-Objekt einen ACE mit den angegebenen Einstellungen hinzu.</summary>
      <param name="accessType">Der Typ der Zugriffssteuerung (gewähren oder verweigern), der hinzugefügt werden soll.</param>
      <param name="sid">Der <see cref="T:System.Security.Principal.SecurityIdentifier" />, dem ein ACE hinzugefügt werden soll.</param>
      <param name="accessMask">Die Zugriffsregel für den neuen ACE.</param>
      <param name="inheritanceFlags">Flags, die die Vererbungseigenschaften des neuen ACE angeben.</param>
      <param name="propagationFlags">Flags, die die Weitergabeeigenschaften der Vererbung für den neuen ACE angeben.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.AddAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>Fügt dem aktuellen <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />-Objekt einen ACE mit den angegebenen Einstellungen hinzu.Verwenden Sie diese Methode für ACLs für Verzeichnisobjekte, wenn Sie den Objekttyp oder den geerbten Objekttyp des neuen ACE angeben.</summary>
      <param name="accessType">Der Typ der Zugriffssteuerung (gewähren oder verweigern), der hinzugefügt werden soll.</param>
      <param name="sid">Der <see cref="T:System.Security.Principal.SecurityIdentifier" />, dem ein ACE hinzugefügt werden soll.</param>
      <param name="accessMask">Die Zugriffsregel für den neuen ACE.</param>
      <param name="inheritanceFlags">Flags, die die Vererbungseigenschaften des neuen ACE angeben.</param>
      <param name="propagationFlags">Flags, die die Weitergabeeigenschaften der Vererbung für den neuen ACE angeben.</param>
      <param name="objectFlags">Flags, die angeben, ob der <paramref name="objectType" />-Parameter und der <paramref name="inheritedObjectType" />-Parameter Nicht-null-Werte enthalten.</param>
      <param name="objectType">Die Identität der Klasse von Objekten, für die der neue ACE gilt.</param>
      <param name="inheritedObjectType">Die Identität der Klasse von untergeordneten Objekten, die den neuen ACE erben können.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.AddAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAccessRule)">
      <summary>Fügt dem aktuellen <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />-Objekt einen ACE mit den angegebenen Einstellungen hinzu.</summary>
      <param name="accessType">Der Typ der Zugriffssteuerung (gewähren oder verweigern), der hinzugefügt werden soll.</param>
      <param name="sid">Der <see cref="T:System.Security.Principal.SecurityIdentifier" />, dem ein ACE hinzugefügt werden soll.</param>
      <param name="rule">Die <see cref="T:System.Security.AccessControl.ObjectAccessRule" /> für den neuen Zugriff.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Entfernt die angegebene Zugriffssteuerungsregel aus dem aktuellen <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />-Objekt.</summary>
      <returns>true, wenn diese Methode den angegebenen Zugriff erfolgreich entfernt hat, andernfalls false.</returns>
      <param name="accessType">Der Typ der Zugriffssteuerung (gewähren oder verweigern), der entfernt werden soll.</param>
      <param name="sid">Der <see cref="T:System.Security.Principal.SecurityIdentifier" />, für den eine Zugriffssteuerungsregel entfernt werden soll.</param>
      <param name="accessMask">Die Zugriffsmaske für die Regel, die entfernt werden soll.</param>
      <param name="inheritanceFlags">Flags, die die Vererbungseigenschaften der Regel angeben, die entfernt werden sollen.</param>
      <param name="propagationFlags">Flags, die die Weitergabeeigenschaften der Vererbung für die Regel angeben, die entfernt werden sollen.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>Entfernt die angegebene Zugriffssteuerungsregel aus dem aktuellen <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />-Objekt.Verwenden Sie diese Methode für ACLs für Verzeichnisobjekte, wenn Sie den Objekttyp oder den geerbten Objekttyp angeben.</summary>
      <returns>true, wenn diese Methode den angegebenen Zugriff erfolgreich entfernt hat, andernfalls false.</returns>
      <param name="accessType">Der Typ der Zugriffssteuerung (gewähren oder verweigern), der entfernt werden soll.</param>
      <param name="sid">Der <see cref="T:System.Security.Principal.SecurityIdentifier" />, für den eine Zugriffssteuerungsregel entfernt werden soll.</param>
      <param name="accessMask">Die Zugriffsmaske für die Zugriffssteuerungsregel, die entfernt werden soll.</param>
      <param name="inheritanceFlags">Flags, die die Vererbungseigenschaften der Zugriffssteuerungsregel angeben, die entfernt werden sollen.</param>
      <param name="propagationFlags">Flags, die die Weitergabeeigenschaften der Vererbung für die Zugriffssteuerungsregel angeben, die entfernt werden sollen.</param>
      <param name="objectFlags">Flags, die angeben, ob der <paramref name="objectType" />-Parameter und der <paramref name="inheritedObjectType" />-Parameter Nicht-null-Werte enthalten.</param>
      <param name="objectType">Die Identität der Klasse von Objekten, für die die entfernte Zugriffssteuerungsregel gilt.</param>
      <param name="inheritedObjectType">Die Identität der Klasse von untergeordneten Objekten, die die entfernte Zugriffssteuerungsregel erben können.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAccessRule)">
      <summary>Entfernt die angegebene Zugriffssteuerungsregel aus dem aktuellen <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />-Objekt.</summary>
      <returns>Gibt <see cref="T:System.Boolean" />zurück.</returns>
      <param name="accessType">Der Typ der Zugriffssteuerung (gewähren oder verweigern), der entfernt werden soll.</param>
      <param name="sid">Der <see cref="T:System.Security.Principal.SecurityIdentifier" />, für den eine Zugriffssteuerungsregel entfernt werden soll.</param>
      <param name="rule">Die <see cref="T:System.Security.AccessControl.ObjectAccessRule" /> für den Zugriff zu entfernen.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccessSpecific(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Entfernt den angegebenen ACE aus dem aktuellen <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />-Objekt.</summary>
      <param name="accessType">Der Typ der Zugriffssteuerung (gewähren oder verweigern), der entfernt werden soll.</param>
      <param name="sid">Der <see cref="T:System.Security.Principal.SecurityIdentifier" />, für den ein ACE entfernt werden soll.</param>
      <param name="accessMask">Die Zugriffsmaske für den ACE, die entfernt werden soll.</param>
      <param name="inheritanceFlags">Flags, die die Vererbungseigenschaften des ACE angeben, die entfernt werden sollen.</param>
      <param name="propagationFlags">Flags, die die Weitergabeeigenschaften der Vererbung für den ACE angeben, die entfernt werden sollen.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccessSpecific(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>Entfernt den angegebenen ACE aus dem aktuellen <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />-Objekt.Verwenden Sie diese Methode für ACLs für Verzeichnisobjekte, wenn Sie den Objekttyp oder den geerbten Objekttyp für den ACE angeben, der entfernt werden soll.</summary>
      <param name="accessType">Der Typ der Zugriffssteuerung (gewähren oder verweigern), der entfernt werden soll.</param>
      <param name="sid">Der <see cref="T:System.Security.Principal.SecurityIdentifier" />, für den ein ACE entfernt werden soll.</param>
      <param name="accessMask">Die Zugriffsmaske für den ACE, die entfernt werden soll.</param>
      <param name="inheritanceFlags">Flags, die die Vererbungseigenschaften des ACE angeben, die entfernt werden sollen.</param>
      <param name="propagationFlags">Flags, die die Weitergabeeigenschaften der Vererbung für den ACE angeben, die entfernt werden sollen.</param>
      <param name="objectFlags">Flags, die angeben, ob der <paramref name="objectType" />-Parameter und der <paramref name="inheritedObjectType" />-Parameter Nicht-null-Werte enthalten.</param>
      <param name="objectType">Die Identität der Klasse von Objekten, für die der entfernte ACE gilt.</param>
      <param name="inheritedObjectType">Die Identität der Klasse von untergeordneten Objekten, die den entfernten ACE erben können.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccessSpecific(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAccessRule)">
      <summary>Entfernt den angegebenen ACE aus dem aktuellen <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />-Objekt.</summary>
      <param name="accessType">Der Typ der Zugriffssteuerung (gewähren oder verweigern), der entfernt werden soll.</param>
      <param name="sid">Der <see cref="T:System.Security.Principal.SecurityIdentifier" />, für den ein ACE entfernt werden soll.</param>
      <param name="rule">Die <see cref="T:System.Security.AccessControl.ObjectAccessRule" /> für den Zugriff zu entfernen.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.SetAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Legt die angegebene Zugriffssteuerung für das angegebene <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekt fest.</summary>
      <param name="accessType">Der Typ der Zugriffssteuerung (gewähren oder verweigern), der festgelegt werden soll.</param>
      <param name="sid">Der <see cref="T:System.Security.Principal.SecurityIdentifier" />, für den ein ACE festgelegt werden soll.</param>
      <param name="accessMask">Die Zugriffsregel für den neuen ACE.</param>
      <param name="inheritanceFlags">Flags, die die Vererbungseigenschaften des neuen ACE angeben.</param>
      <param name="propagationFlags">Flags, die die Weitergabeeigenschaften der Vererbung für den neuen ACE angeben.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.SetAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>Legt die angegebene Zugriffssteuerung für das angegebene <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekt fest.</summary>
      <param name="accessType">Der Typ der Zugriffssteuerung (gewähren oder verweigern), der festgelegt werden soll.</param>
      <param name="sid">Der <see cref="T:System.Security.Principal.SecurityIdentifier" />, für den ein ACE festgelegt werden soll.</param>
      <param name="accessMask">Die Zugriffsregel für den neuen ACE.</param>
      <param name="inheritanceFlags">Flags, die die Vererbungseigenschaften des neuen ACE angeben.</param>
      <param name="propagationFlags">Flags, die die Weitergabeeigenschaften der Vererbung für den neuen ACE angeben.</param>
      <param name="objectFlags">Flags, die angeben, ob der <paramref name="objectType" />-Parameter und der <paramref name="inheritedObjectType" />-Parameter Nicht-null-Werte enthalten.</param>
      <param name="objectType">Die Identität der Klasse von Objekten, für die der neue ACE gilt.</param>
      <param name="inheritedObjectType">Die Identität der Klasse von untergeordneten Objekten, die den neuen ACE erben können.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.SetAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAccessRule)">
      <summary>Legt die angegebene Zugriffssteuerung für das angegebene <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekt fest.</summary>
      <param name="accessType">Der Typ der Zugriffssteuerung (gewähren oder verweigern), der festgelegt werden soll.</param>
      <param name="sid">Der <see cref="T:System.Security.Principal.SecurityIdentifier" />, für den ein ACE festgelegt werden soll.</param>
      <param name="rule">Die <see cref="T:System.Security.AccessControl.ObjectAccessRule" /> für den Zugriff festgelegt.</param>
    </member>
    <member name="T:System.Security.AccessControl.GenericAce">
      <summary>Stellt einen ACE (Access Control Entry, Zugriffssteuerungseintrag) dar und ist die Basisklasse für alle anderen ACE-Klassen.</summary>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.AceFlags">
      <summary>Ruft die Instanz von <see cref="T:System.Security.AccessControl.AceFlags" /> ab, die diesem <see cref="T:System.Security.AccessControl.GenericAce" />-Objekt zugeordnet ist, oder legt diese fest.</summary>
      <returns>Die Instanz von <see cref="T:System.Security.AccessControl.AceFlags" />, die diesem <see cref="T:System.Security.AccessControl.GenericAce" />-Objekt zugeordnet ist.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.AceType">
      <summary>Ruft den Typ für diesen ACE (Access Control Entry, Zugriffssteuerungseintrag) ab.</summary>
      <returns>Der Typ für diesen ACE.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.AuditFlags">
      <summary>Ruft die diesem ACE (Access Control Entry, Zugriffssteuerungseintrag) zugeordneten Überwachungsinformationen ab.</summary>
      <returns>Die diesem ACE (Access Control Entry, Zugriffssteuerungseintrag) zugeordneten Überwachungsinformationen.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.BinaryLength">
      <summary>Ruft die Länge der binären Darstellung des aktuellen <see cref="T:System.Security.AccessControl.GenericAce" />-Objekts (in Byte) ab.Diese Länge muss verwendet werden, bevor die ACL mithilfe der <see cref="M:System.Security.AccessControl.GenericAce.GetBinaryForm" />-Methode in ein binäres Array gemarshallt wird.</summary>
      <returns>Die Länge der binären Darstellung des aktuellen <see cref="T:System.Security.AccessControl.GenericAce" />-Objekts (in Byte).</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.Copy">
      <summary>Erstellt eine Tiefenkopie von diesem ACE (Access Control Entry, Zugriffssteuerungseintrag).</summary>
      <returns>Das von dieser Methode erstellte <see cref="T:System.Security.AccessControl.GenericAce" />-Objekt.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.CreateFromBinaryForm(System.Byte[],System.Int32)">
      <summary>Erstellt ein <see cref="T:System.Security.AccessControl.GenericAce" />-Objekt aus den angegebenen binären Daten.</summary>
      <returns>Das von dieser Methode erstellte <see cref="T:System.Security.AccessControl.GenericAce" />-Objekt.</returns>
      <param name="binaryForm">Die binären Daten, aus denen das neue <see cref="T:System.Security.AccessControl.GenericAce" />-Objekt erstellt werden soll.</param>
      <param name="offset">Der Offset, bei dem mit dem Rückgängigmachen des Marshallens begonnen werden soll.</param>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.Equals(System.Object)">
      <summary>Bestimmt, ob das angegebene <see cref="T:System.Security.AccessControl.GenericAce" />-Objekt mit dem aktuellen <see cref="T:System.Security.AccessControl.GenericAce" />-Objekt identisch ist.</summary>
      <returns>true, wenn das angegebene <see cref="T:System.Security.AccessControl.GenericAce" />-Objekt gleich dem aktuellen <see cref="T:System.Security.AccessControl.GenericAce" />-Objekt ist, andernfalls false.</returns>
      <param name="o">Das <see cref="T:System.Security.AccessControl.GenericAce" />-Objekt, das mit dem aktuellen <see cref="T:System.Security.AccessControl.GenericAce" />-Objekt verglichen werden soll.</param>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Marshallt den Inhalt des <see cref="T:System.Security.AccessControl.GenericAce" />-Objekts in das angegebene Bytearray, wobei beim angegebenen Offset begonnen wird.</summary>
      <param name="binaryForm">Das Bytearray, in das der Inhalt von <see cref="T:System.Security.AccessControl.GenericAce" /> gemarshallt wird.</param>
      <param name="offset">Der Offset, bei dem das Marshallen begonnen werden soll.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> ist negativ oder zu hoch, um den gesamten <see cref="T:System.Security.AccessControl.GenericAcl" /> in <paramref name="array" /> zu kopieren.</exception>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.GetHashCode">
      <summary>Fungiert als eine Hashfunktion für die <see cref="T:System.Security.AccessControl.GenericAce" />-Klasse.Sie können die <see cref="M:System.Security.AccessControl.GenericAce.GetHashCode" />-Methode in Hashalgorithmen und Datenstrukturen wie Hashtabellen verwenden.</summary>
      <returns>Ein Hashcode für das aktuelle <see cref="T:System.Security.AccessControl.GenericAce" />-Objekt.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.InheritanceFlags">
      <summary>Ruft Flags ab, die die Vererbungseigenschaften für diesen ACE (Access Control Entry, Zugriffssteuerungseintrag) angeben.</summary>
      <returns>Flags, die die Vererbungseigenschaften für diesen ACE angeben.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.IsInherited">
      <summary>Ruft einen booleschen Wert ab, der angibt, ob dieser ACE (Access Control Entry, Zugriffssteuerungseintrag) geerbt oder explizit festgelegt wird.</summary>
      <returns>true, wenn dieser ACE geerbt wird, andernfalls false.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.op_Equality(System.Security.AccessControl.GenericAce,System.Security.AccessControl.GenericAce)">
      <summary>Bestimmt, ob die angegebenen <see cref="T:System.Security.AccessControl.GenericAce" />-Objekte als gleich betrachtet werden.</summary>
      <returns>true, wenn die beiden <see cref="T:System.Security.AccessControl.GenericAce" />-Objekte gleich sind, andernfalls false.</returns>
      <param name="left">Das erste zu vergleichende <see cref="T:System.Security.AccessControl.GenericAce" />-Objekt.</param>
      <param name="right">Das zweite zu vergleichende <see cref="T:System.Security.AccessControl.GenericAce" />.</param>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.op_Inequality(System.Security.AccessControl.GenericAce,System.Security.AccessControl.GenericAce)">
      <summary>Bestimmt, ob die angegebenen <see cref="T:System.Security.AccessControl.GenericAce" />-Objekte als ungleich betrachtet werden.</summary>
      <returns>true, wenn die beiden <see cref="T:System.Security.AccessControl.GenericAce" />-Objekte nicht identisch sind, andernfalls false.</returns>
      <param name="left">Das erste zu vergleichende <see cref="T:System.Security.AccessControl.GenericAce" />-Objekt.</param>
      <param name="right">Das zweite zu vergleichende <see cref="T:System.Security.AccessControl.GenericAce" />.</param>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.PropagationFlags">
      <summary>Ruft Flags ab, die die Eigenschaften der Vererbungsweitergabe für diesen ACE (Access Control Entry, Zugriffssteuerungseintrag) angeben.</summary>
      <returns>Flags, die die Eigenschaften der Vererbungsweitergabe für diesen ACE angeben.</returns>
    </member>
    <member name="T:System.Security.AccessControl.GenericAcl">
      <summary>Stellt eine Zugriffssteuerungsliste (ACL) dar und ist die Basisklasse für die Klassen <see cref="T:System.Security.AccessControl.CommonAcl" />, <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />, <see cref="T:System.Security.AccessControl.RawAcl" /> und <see cref="T:System.Security.AccessControl.SystemAcl" />.</summary>
    </member>
    <member name="M:System.Security.AccessControl.GenericAcl.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.AccessControl.GenericAcl" />-Klasse.</summary>
    </member>
    <member name="F:System.Security.AccessControl.GenericAcl.AclRevision">
      <summary>Die Revisionsebene der aktuellen <see cref="T:System.Security.AccessControl.GenericAcl" />.Dieser Wert wird von der <see cref="P:System.Security.AccessControl.GenericAcl.Revision" />-Eigenschaft für Zugriffssteuerungslisten (ACLs) zurückgegeben, die keinen Verzeichnisdienstobjekten zugeordnet sind.</summary>
    </member>
    <member name="F:System.Security.AccessControl.GenericAcl.AclRevisionDS">
      <summary>Die Revisionsebene der aktuellen <see cref="T:System.Security.AccessControl.GenericAcl" />.Dieser Wert wird von der <see cref="P:System.Security.AccessControl.GenericAcl.Revision" />-Eigenschaft für Zugriffssteuerungslisten (ACLs) zurückgegeben, die Verzeichnisdienstobjekten zugeordnet sind.</summary>
    </member>
    <member name="P:System.Security.AccessControl.GenericAcl.BinaryLength">
      <summary>Ruft die Länge der binären Darstellung des aktuellen <see cref="T:System.Security.AccessControl.GenericAcl" />-Objekts (in Byte) ab.Diese Länge muss verwendet werden, bevor die ACL mithilfe der <see cref="M:System.Security.AccessControl.GenericAcl.GetBinaryForm" />-Methode in ein binäres Array gemarshallt wird.</summary>
      <returns>Die Länge der binären Darstellung des aktuellen <see cref="T:System.Security.AccessControl.GenericAcl" />-Objekts (in Byte).</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericAcl.CopyTo(System.Security.AccessControl.GenericAce[],System.Int32)">
      <summary>Kopiert jeden <see cref="T:System.Security.AccessControl.GenericAce" /> der aktuellen <see cref="T:System.Security.AccessControl.GenericAcl" /> in das angegebene Array.</summary>
      <param name="array">Das Array, das Kopien der in der aktuellen <see cref="T:System.Security.AccessControl.GenericAcl" /> enthaltenen <see cref="T:System.Security.AccessControl.GenericAce" />-Objekte aufnimmt.</param>
      <param name="index">Der nullbasierte Index von <paramref name="array" />, an dem der Kopiervorgang beginnt.</param>
    </member>
    <member name="P:System.Security.AccessControl.GenericAcl.Count">
      <summary>Ruft die Anzahl der ACEs im aktuellen <see cref="T:System.Security.AccessControl.GenericAcl" />-Objekt ab.</summary>
      <returns>Die Anzahl der ACEs im aktuellen <see cref="T:System.Security.AccessControl.GenericAcl" />-Objekt.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericAcl.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Marshallt den Inhalt des <see cref="T:System.Security.AccessControl.GenericAcl" />-Objekts in das angegebene Bytearray, wobei beim angegebenen Offset begonnen wird.</summary>
      <param name="binaryForm">Das Bytearray, in das der Inhalt von <see cref="T:System.Security.AccessControl.GenericAcl" /> gemarshallt wird.</param>
      <param name="offset">Der Offset, bei dem das Marshallen begonnen werden soll.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> ist negativ oder zu hoch, um den gesamten <see cref="T:System.Security.AccessControl.GenericAcl" /> in <paramref name="array" /> zu kopieren.</exception>
    </member>
    <member name="M:System.Security.AccessControl.GenericAcl.GetEnumerator">
      <summary>Gibt eine neue Instanz der <see cref="T:System.Security.AccessControl.AceEnumerator" />-Klasse zurück.</summary>
      <returns>Der <see cref="T:Security.AccessControl.AceEnumerator" />, den diese Methode zurückgibt.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAcl.IsSynchronized">
      <summary>Diese Eigenschaft ist immer auf false festgelegt.Sie ist nur implementiert, weil sie für die Implementierung der <see cref="T:System.Collections.ICollection" />-Schnittstelle erforderlich ist.</summary>
      <returns>Immer false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAcl.Item(System.Int32)">
      <summary>Ruft den <see cref="T:System.Security.AccessControl.GenericAce" /> am angegebenen Index ab oder legt diesen fest.</summary>
      <returns>Der <see cref="T:System.Security.AccessControl.GenericAce" /> am angegebenen Index.</returns>
      <param name="index">Der nullbasierte Index von <see cref="T:System.Security.AccessControl.GenericAce" />, der abgerufen oder festgelegt werden soll.</param>
    </member>
    <member name="F:System.Security.AccessControl.GenericAcl.MaxBinaryLength">
      <summary>Die maximal zulässige binäre Länge eines <see cref="T:System.Security.AccessControl.GenericAcl" />-Objekts.</summary>
    </member>
    <member name="P:System.Security.AccessControl.GenericAcl.Revision">
      <summary>Ruft die Revisionsebene der <see cref="T:System.Security.AccessControl.GenericAcl" /> ab.</summary>
      <returns>Ein Bytewert, der die Revisionsebene der <see cref="T:System.Security.AccessControl.GenericAcl" /> angibt.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAcl.SyncRoot">
      <summary>Diese Eigenschaft gibt immer null zurück.Sie ist nur implementiert, weil sie für die Implementierung der <see cref="T:System.Collections.ICollection" />-Schnittstelle erforderlich ist.</summary>
      <returns>Gibt immer null zurück.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericAcl.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Kopiert jeden <see cref="T:System.Security.AccessControl.GenericAce" /> der aktuellen <see cref="T:System.Security.AccessControl.GenericAcl" /> in das angegebene Array.</summary>
      <param name="array">Das Array, das Kopien der in der aktuellen <see cref="T:System.Security.AccessControl.GenericAcl" /> enthaltenen <see cref="T:System.Security.AccessControl.GenericAce" />-Objekte aufnimmt.</param>
      <param name="index">Der nullbasierte Index von <paramref name="array" />, an dem der Kopiervorgang beginnt.</param>
    </member>
    <member name="M:System.Security.AccessControl.GenericAcl.System#Collections#IEnumerable#GetEnumerator">
      <summary>Gibt eine neue Instanz der <see cref="T:System.Security.AccessControl.AceEnumerator" />-Klasse zurück, die in eine Instanz der <see cref="T:System.Collections.IEnumerator" />-Schnittstelle konvertiert wurde.</summary>
      <returns>Ein neues <see cref="T:System.Security.AccessControl.AceEnumerator" />-Objekt, das in eine Instanz der <see cref="T:System.Collections.IEnumerator" />-Schnittstelle konvertiert wurde.</returns>
    </member>
    <member name="T:System.Security.AccessControl.GenericSecurityDescriptor">
      <summary>Stellt eine Sicherheitsbeschreibung dar.Zu einer Sicherheitsbeschreibung gehören ein Besitzer, eine primäre Gruppe, eine DACL und eine SACL.</summary>
    </member>
    <member name="M:System.Security.AccessControl.GenericSecurityDescriptor.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.AccessControl.GenericSecurity" />-Klasse.</summary>
    </member>
    <member name="P:System.Security.AccessControl.GenericSecurityDescriptor.BinaryLength">
      <summary>Ruft die Länge der binären Darstellung des aktuellen <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />-Objekts (in Byte) ab.Diese Länge muss verwendet werden, bevor die ACL mithilfe der <see cref="M:System.Security.AccessControl.GenericSecurityDescriptor.GetBinaryForm" />-Methode in ein binäres Array gemarshallt wird.</summary>
      <returns>Die Länge der binären Darstellung des aktuellen <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />-Objekts (in Byte).</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericSecurityDescriptor.ControlFlags">
      <summary>Ruft Werte ab, die das Verhalten des <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />-Objekts angeben.</summary>
      <returns>Mindestens ein Wert der <see cref="T:System.Security.AccessControl.ControlFlags" />-Enumeration, kombiniert mit einem logischen Operator OR.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericSecurityDescriptor.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Gibt ein Array von Bytewerten zurück, das die in diesem <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />-Objekt enthaltenen Informationen darstellt.</summary>
      <param name="binaryForm">Das Bytearray, in das der Inhalt von <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> gemarshallt wird.</param>
      <param name="offset">Der Offset, bei dem das Marshallen begonnen werden soll.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> ist negativ oder zu hoch, um den gesamten <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> in <paramref name="array" /> zu kopieren.</exception>
    </member>
    <member name="M:System.Security.AccessControl.GenericSecurityDescriptor.GetSddlForm(System.Security.AccessControl.AccessControlSections)">
      <summary>Gibt die SDDL-Darstellung (Security Descriptor Definition Language) der angegebenen Abschnitte der von diesem <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />-Objekt dargestellten Sicherheitsbeschreibung zurück.</summary>
      <returns>Die SDDL-Darstellung der angegebenen Abschnitte der Sicherheitsbeschreibung, die diesem <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />-Objekt zugeordnet ist.</returns>
      <param name="includeSections">Gibt an, welche Abschnitte (Zugriffsregeln, Überwachungsregeln, primäre Gruppe, Besitzer) der Sicherheitsbeschreibung abgerufen werden sollen.</param>
    </member>
    <member name="P:System.Security.AccessControl.GenericSecurityDescriptor.Group">
      <summary>Ruft die primäre Gruppe für dieses <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />-Objekt ab oder legt diese fest.</summary>
      <returns>Die primäre Gruppe für dieses <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />-Objekt.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericSecurityDescriptor.IsSddlConversionSupported">
      <summary>Gibt einen booleschen Wert zurück, der angibt, ob die diesem <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />-Objekt zugeordnete Sicherheitsbeschreibung in das SDDL-Format (Security Descriptor Definition Language) umgewandelt werden kann.</summary>
      <returns>true, wenn die diesem <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />-Objekt zugeordnete Sicherheitsbeschreibung in das SDDL-Format (Security Descriptor Definition Language) umgewandelt werden kann, andernfalls false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericSecurityDescriptor.Owner">
      <summary>Ruft den Besitzer des Objekts ab, dem dieses <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />-Objekt zugeordnet ist.</summary>
      <returns>Der Besitzer des Objekts, dem dieses <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />-Objekt zugeordnet ist.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericSecurityDescriptor.Revision">
      <summary>Ruft die Revisionsebene des <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />-Objekts ab.</summary>
      <returns>Ein Bytewert, der die Revisionsebene von <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> angibt.</returns>
    </member>
    <member name="T:System.Security.AccessControl.InheritanceFlags">
      <summary>Vererbungsflags geben die Semantik der Vererbung für ACEs (Access Control Entries, Zugriffssteuerungseinträge) an.</summary>
    </member>
    <member name="F:System.Security.AccessControl.InheritanceFlags.ContainerInherit">
      <summary>Der ACE wird von untergeordneten Containerobjekten geerbt.</summary>
    </member>
    <member name="F:System.Security.AccessControl.InheritanceFlags.None">
      <summary>Der ACE wird nicht von untergeordneten Objekten geerbt.</summary>
    </member>
    <member name="F:System.Security.AccessControl.InheritanceFlags.ObjectInherit">
      <summary>Der ACE wird von untergeordneten Endobjekten geerbt.</summary>
    </member>
    <member name="T:System.Security.AccessControl.KnownAce">
      <summary>Kapselt alle derzeit von Microsoft Corporation definierten ACE-Typen (Access Control Entry, Zugriffssteuerungseinträge).Alle <see cref="T:System.Security.AccessControl.KnownAce" />-Objekte enthalten eine 32-Bit-Zugriffsmaske und ein <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekt.</summary>
    </member>
    <member name="P:System.Security.AccessControl.KnownAce.AccessMask">
      <summary>Ruft die Zugriffsmaske für dieses <see cref="T:System.Security.AccessControl.KnownAce" />-Objekt ab oder legt diese fest.</summary>
      <returns>Die Zugriffsmaske für dieses <see cref="T:System.Security.AccessControl.KnownAce" />-Objekt.</returns>
    </member>
    <member name="P:System.Security.AccessControl.KnownAce.SecurityIdentifier">
      <summary>Ruft das <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekt ab, das diesem <see cref="T:System.Security.AccessControl.KnownAce" />-Objekt zugeordnet ist, oder legt dieses fest.</summary>
      <returns>Das diesem <see cref="T:System.Security.AccessControl.KnownAce" />-Objekt zugeordnete <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekt.</returns>
    </member>
    <member name="T:System.Security.AccessControl.NativeObjectSecurity">
      <summary>Bietet die Möglichkeit, Zugriff auf systemeigene Objekte ohne direkte Manipulation von Zugriffssteuerungslisten (Access Control Lists, ACLs) zu steuern.Systemeigene Objekttypen werden von der <see cref="T:System.Security.AccessControl.ResourceType" />-Enumeration definiert.</summary>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />-Klasse mit den angegebenen Werten.</summary>
      <param name="isContainer">true, wenn das neue <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />-Objekt ein Containerobjekt ist.</param>
      <param name="resourceType">Der Typ des sicherungsfähigen Objekts, dem das neue <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />-Objekt zugeordnet wird.</param>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />-Klasse mit den angegebenen Werten.Es wird empfohlen, dass die Werte der <paramref name="includeSections" />-Parameter, die an den Konstruktor übergeben werden, und die Persist-Methoden identisch sind.Weitere Informationen finden Sie in den Hinweisen.</summary>
      <param name="isContainer">true, wenn das neue <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />-Objekt ein Containerobjekt ist.</param>
      <param name="resourceType">Der Typ des sicherungsfähigen Objekts, dem das neue <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />-Objekt zugeordnet wird.</param>
      <param name="handle">Das Handle des sicherungsfähigen Objekts, dem das neue <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />-Objekt zugeordnet wird.</param>
      <param name="includeSections">Einer der <see cref="T:System.Security.AccessControl.AccessControlSections" />-Enumerationswerte, der die Abschnitte der Sicherheitsbeschreibung (Zugriffsregeln, Überwachungsregeln, Besitzer, primäre Gruppe) des sicherungsfähigen Objekts angibt, die in das <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />-Objekt eingefügt werden sollen.</param>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections,System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode,System.Object)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />-Klasse mit den angegebenen Werten.Es wird empfohlen, dass die Werte der <paramref name="includeSections" />-Parameter, die an den Konstruktor übergeben werden, und die Persist-Methoden identisch sind.Weitere Informationen finden Sie in den Hinweisen.</summary>
      <param name="isContainer">true, wenn das neue <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />-Objekt ein Containerobjekt ist.</param>
      <param name="resourceType">Der Typ des sicherungsfähigen Objekts, dem das neue <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />-Objekt zugeordnet wird.</param>
      <param name="handle">Das Handle des sicherungsfähigen Objekts, dem das neue <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />-Objekt zugeordnet wird.</param>
      <param name="includeSections">Einer der <see cref="T:System.Security.AccessControl.AccessControlSections" />-Enumerationswerte, der die Abschnitte der Sicherheitsbeschreibung (Zugriffsregeln, Überwachungsregeln, Besitzer, primäre Gruppe) des sicherungsfähigen Objekts angibt, die in das <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />-Objekt eingefügt werden sollen.</param>
      <param name="exceptionFromErrorCode">Ein von Integratoren implementierter Delegat, der benutzerdefinierte Ausnahmen bereitstellt. </param>
      <param name="exceptionContext">Ein Objekt, das Kontextinformationen über die Quelle oder das Ziel der Ausnahme bereitstellt.</param>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode,System.Object)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />-Klasse unter Verwendung der angegebenen Werte.</summary>
      <param name="isContainer">true, wenn das neue <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />-Objekt ein Containerobjekt ist.</param>
      <param name="resourceType">Der Typ des sicherungsfähigen Objekts, dem das neue <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />-Objekt zugeordnet wird.</param>
      <param name="exceptionFromErrorCode">Ein von Integratoren implementierter Delegat, der benutzerdefinierte Ausnahmen bereitstellt. </param>
      <param name="exceptionContext">Ein Objekt, das Kontextinformationen über die Quelle oder das Ziel der Ausnahme bereitstellt.</param>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />-Klasse mit den angegebenen Werten.Es wird empfohlen, dass die Werte der <paramref name="includeSections" />-Parameter, die an den Konstruktor übergeben werden, und die Persist-Methoden identisch sind.Weitere Informationen finden Sie in den Hinweisen.</summary>
      <param name="isContainer">true, wenn das neue <see cref="T:System.Security.AccessControl.NativObjectSecurity" />-Objekt ein Containerobjekt ist.</param>
      <param name="resourceType">Der Typ des sicherungsfähigen Objekts, dem das neue <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />-Objekt zugeordnet wird.</param>
      <param name="name">Der Name des sicherungsfähigen Objekts, dem das neue <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />-Objekt zugeordnet wird.</param>
      <param name="includeSections">Einer der <see cref="T:System.Security.AccessControl.AccessControlSections" />-Enumerationswerte, der die Abschnitte der Sicherheitsbeschreibung (Zugriffsregeln, Überwachungsregeln, Besitzer, primäre Gruppe) des sicherungsfähigen Objekts angibt, die in das <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />-Objekt eingefügt werden sollen.</param>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.String,System.Security.AccessControl.AccessControlSections,System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode,System.Object)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />-Klasse mit den angegebenen Werten.Es wird empfohlen, dass die Werte der <paramref name="includeSections" />-Parameter, die an den Konstruktor übergeben werden, und die Persist-Methoden identisch sind.Weitere Informationen finden Sie in den Hinweisen.</summary>
      <param name="isContainer">true, wenn das neue <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />-Objekt ein Containerobjekt ist.</param>
      <param name="resourceType">Der Typ des sicherungsfähigen Objekts, dem das neue <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />-Objekt zugeordnet wird.</param>
      <param name="name">Der Name des sicherungsfähigen Objekts, dem das neue <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />-Objekt zugeordnet wird.</param>
      <param name="includeSections">Einer der <see cref="T:System.Security.AccessControl.AccessControlSections" />-Enumerationswerte, der die Abschnitte der Sicherheitsbeschreibung (Zugriffsregeln, Überwachungsregeln, Besitzer, primäre Gruppe) des sicherungsfähigen Objekts angibt, die in das <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />-Objekt eingefügt werden sollen.</param>
      <param name="exceptionFromErrorCode">Ein von Integratoren implementierter Delegat, der benutzerdefinierte Ausnahmen bereitstellt. </param>
      <param name="exceptionContext">Ein Objekt, das Kontextinformationen über die Quelle oder das Ziel der Ausnahme bereitstellt.</param>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.Persist(System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections)">
      <summary>Führt eine dauerhafte Speicherung der angegebenen Abschnitte der Sicherheitsbeschreibung aus, die diesem <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />-Objekt zugeordnet sind.Es wird empfohlen, dass die Werte der <paramref name="includeSections" />-Parameter, die an den Konstruktor übergeben werden, und die Persist-Methoden identisch sind.Weitere Informationen finden Sie in den Hinweisen.</summary>
      <param name="handle">Das Handle des sicherungsfähigen Objekts, dem das <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />-Objekt zugeordnet wird.</param>
      <param name="includeSections">Einer der <see cref="T:System.Security.AccessControl.AccessControlSections" />-Enumerationswerte, der die Abschnitte der Sicherheitsbeschreibung (Zugriffsregeln, Überwachungsregeln, Besitzer, primäre Gruppe) des sicherungsfähigen Objekts angibt, die gespeichert werden sollen.</param>
      <exception cref="T:System.IO.FileNotFoundException">Das sicherungsfähige Objekt, dem das <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />-Objekt zugeordnet ist, ist ein Verzeichnis oder eine Datei und konnte nicht gefunden werden.</exception>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.Persist(System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections,System.Object)">
      <summary>Führt eine dauerhafte Speicherung der angegebenen Abschnitte der Sicherheitsbeschreibung aus, die diesem <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />-Objekt zugeordnet sind.Es wird empfohlen, dass die Werte der <paramref name="includeSections" />-Parameter, die an den Konstruktor übergeben werden, und die Persist-Methoden identisch sind.Weitere Informationen finden Sie in den Hinweisen.</summary>
      <param name="handle">Das Handle des sicherungsfähigen Objekts, dem das <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />-Objekt zugeordnet wird.</param>
      <param name="includeSections">Einer der <see cref="T:System.Security.AccessControl.AccessControlSections" />-Enumerationswerte, der die Abschnitte der Sicherheitsbeschreibung (Zugriffsregeln, Überwachungsregeln, Besitzer, primäre Gruppe) des sicherungsfähigen Objekts angibt, die gespeichert werden sollen.</param>
      <param name="exceptionContext">Ein Objekt, das Kontextinformationen über die Quelle oder das Ziel der Ausnahme bereitstellt.</param>
      <exception cref="T:System.IO.FileNotFoundException">Das sicherungsfähige Objekt, dem das <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />-Objekt zugeordnet ist, ist ein Verzeichnis oder eine Datei und konnte nicht gefunden werden.</exception>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.Persist(System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>Führt eine dauerhafte Speicherung der angegebenen Abschnitte der Sicherheitsbeschreibung aus, die diesem <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />-Objekt zugeordnet sind.Es wird empfohlen, dass die Werte der <paramref name="includeSections" />-Parameter, die an den Konstruktor übergeben werden, und die Persist-Methoden identisch sind.Weitere Informationen finden Sie in den Hinweisen.</summary>
      <param name="name">Der Name des sicherungsfähigen Objekts, dem das <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />-Objekt zugeordnet wird.</param>
      <param name="includeSections">Einer der <see cref="T:System.Security.AccessControl.AccessControlSections" />-Enumerationswerte, der die Abschnitte der Sicherheitsbeschreibung (Zugriffsregeln, Überwachungsregeln, Besitzer, primäre Gruppe) des sicherungsfähigen Objekts angibt, die gespeichert werden sollen.</param>
      <exception cref="T:System.IO.FileNotFoundException">Das sicherungsfähige Objekt, dem das <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />-Objekt zugeordnet ist, ist ein Verzeichnis oder eine Datei und konnte nicht gefunden werden.</exception>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.Persist(System.String,System.Security.AccessControl.AccessControlSections,System.Object)">
      <summary>Führt eine dauerhafte Speicherung der angegebenen Abschnitte der Sicherheitsbeschreibung aus, die diesem <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />-Objekt zugeordnet sind.Es wird empfohlen, dass die Werte der <paramref name="includeSections" />-Parameter, die an den Konstruktor übergeben werden, und die Persist-Methoden identisch sind.Weitere Informationen finden Sie in den Hinweisen.</summary>
      <param name="name">Der Name des sicherungsfähigen Objekts, dem das <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />-Objekt zugeordnet wird.</param>
      <param name="includeSections">Einer der <see cref="T:System.Security.AccessControl.AccessControlSections" />-Enumerationswerte, der die Abschnitte der Sicherheitsbeschreibung (Zugriffsregeln, Überwachungsregeln, Besitzer, primäre Gruppe) des sicherungsfähigen Objekts angibt, die gespeichert werden sollen.</param>
      <param name="exceptionContext">Ein Objekt, das Kontextinformationen über die Quelle oder das Ziel der Ausnahme bereitstellt.</param>
      <exception cref="T:System.IO.FileNotFoundException">Das sicherungsfähige Objekt, dem das <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />-Objekt zugeordnet ist, ist ein Verzeichnis oder eine Datei und konnte nicht gefunden werden.</exception>
    </member>
    <member name="T:System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode">
      <summary>Ermöglicht Integratoren, bestimmten von ihnen erstellen Ausnahmen numerische Fehlercodes zuzuordnen.</summary>
      <returns>Die von diesem Delegaten erstellte <see cref="T:System.Exception" />.</returns>
      <param name="errorCode">Der numerische Fehlercode.</param>
      <param name="name">Der Name des sicherungsfähigen Objekts, dem das <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />-Objekt zugeordnet wird.</param>
      <param name="handle">Das Handle des sicherungsfähigen Objekts, dem das <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />-Objekt zugeordnet wird.</param>
      <param name="context">Ein Objekt, das Kontextinformationen über die Quelle oder das Ziel der Ausnahme bereitstellt.</param>
    </member>
    <member name="T:System.Security.AccessControl.ObjectAccessRule">
      <summary>Stellt eine Kombination der Identität eines Benutzers, einer Zugriffsmaske und eines Zugriffssteuerungstyps (gewähren oder verweigern) dar.Ein <see cref="T:System.Security.AccessControl.ObjectAccessRule" />-Objekt enthält darüber hinaus Informationen zum Typ des Objekts, auf das die Regel angewendet wird, zum Typ des untergeordneten Objekts, das die Regel erben kann, zur Art und Weise, auf die die Regel von untergeordneten Objekten geerbt wird, sowie zur Übertragung der Vererbung.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectAccessRule.#ctor(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Guid,System.Guid,System.Security.AccessControl.AccessControlType)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.AccessControl.ObjectAccessRule" />-Klasse mit den angegebenen Werten.</summary>
      <param name="identity">Die Identität, für die die Zugriffsregel gilt.  Dabei muss es sich um ein Objekt handeln, das in eine <see cref="T:System.Security.Principal.SecurityIdentifier" /> umgewandelt werden kann.</param>
      <param name="accessMask">Die Zugriffsmaske dieser Regel.Die Zugriffsmaske ist eine 32-Bit-Auflistung anonymer Bits, deren Bedeutung von den einzelnen Integratoren definiert wird.</param>
      <param name="isInherited">true, wenn diese Regel von einem übergeordneten Container geerbt wird.</param>
      <param name="inheritanceFlags">Gibt die Vererbungseigenschaften der Zugriffsregel an.</param>
      <param name="propagationFlags">Gibt an, ob geerbte Zugriffsregeln automatisch weitergegeben werden.Die Weitergabeflags werden ignoriert, wenn <paramref name="inheritanceFlags" /> auf <see cref="F:System.Security.AccessControl.InheritanceFlags.None" /> festgelegt ist.</param>
      <param name="objectType">Der Objekttyp, auf den die Regel angewendet wird.</param>
      <param name="inheritedObjectType">Der Typ des untergeordneten Objekts, das die Regel erben kann.</param>
      <param name="type">Gibt an, ob diese Regel Zugriff gewährt oder Zugriff verweigert.</param>
      <exception cref="T:System.ArgumentException">Der Wert des <paramref name="identity" />-Parameters kann nicht in einen <see cref="T:System.Security.Principal.SecurityIdentifier" /> umgewandelt werden, oder der <paramref name="type" />-Parameter enthält einen ungültigen Wert.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der Wert des <paramref name="accessMask" />-Parameters ist 0 (null), oder der <paramref name="inheritanceFlags" />-Parameter bzw. der <paramref name="propagationFlags" />-Parameter enthält nicht erkannte Flagwerte.</exception>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAccessRule.InheritedObjectType">
      <summary>Ruft den Typ des untergeordneten Objekts ab, das das <see cref="System.Security.AccessControl.ObjectAccessRule" />-Objekt erben kann.</summary>
      <returns>Der Typ des untergeordneten Objekts, das das <see cref="System.Security.AccessControl.ObjectAccessRule" />-Objekt erben kann.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAccessRule.ObjectFlags">
      <summary>Ruft Flags ab, die angeben, ob die <see cref="P:System.Security.AccessControl.ObjectAccessRule.ObjectType" />-Eigenschaft und die <see cref="P:System.Security.AccessControl.ObjectAccessRule.InheritedObjectType" />-Eigenschaft des <see cref="System.Security.AccessControl.ObjectAccessRule" />-Objekts gültige Werte enthalten.</summary>
      <returns>
        <see cref="F:System.Security.AccessControl.ObjectAceFlags.ObjectAceTypePresent" /> gibt an, dass die <see cref="P:System.Security.AccessControl.ObjectAccessRule.ObjectType" />-Eigenschaft einen gültigen Wert enthält.<see cref="F:System.Security.AccessControl.ObjectAceFlags.InheritedObjectAceTypePresent" /> gibt an, dass die <see cref="P:System.Security.AccessControl.ObjectAccessRule.InheritedObjectType" />-Eigenschaft einen gültigen Wert enthält.Diese Werte können mit einem logischen OR kombiniert werden.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAccessRule.ObjectType">
      <summary>Ruft den Typ des Objekts ab, auf das die <see cref="System.Security.AccessControl.ObjectAccessRule" /> angewendet wird.</summary>
      <returns>Der Typ des Objekts, auf das die <see cref="System.Security.AccessControl.ObjectAccessRule" /> angewendet wird.</returns>
    </member>
    <member name="T:System.Security.AccessControl.ObjectAce">
      <summary>Steuert den Zugriff auf Verzeichnisdienste-Objekte.Diese Klasse stellt einen ACE (Access Control Entry, Zugriffssteuerungseintrag) dar, der einem Verzeichnisobjekt zugeordnet ist.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectAce.#ctor(System.Security.AccessControl.AceFlags,System.Security.AccessControl.AceQualifier,System.Int32,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid,System.Boolean,System.Byte[])">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.AccessControl.ObjectAce" />-Klasse.</summary>
      <param name="aceFlags">Informationen zu Vererbung, Vererbungsweitergabe und Überwachungsbedingungen für den neuen ACE (Access Control Entry, Zugriffssteuerungseintrag).</param>
      <param name="qualifier">Die Verwendung des neuen ACE.</param>
      <param name="accessMask">Die Zugriffsmaske für den ACE.</param>
      <param name="sid">Der dem neuen ACE zugeordnete <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <param name="flags">Gibt an, ob der <paramref name="type" />-Parameter und der <paramref name="inheritedType" />-Parameter gültige Objekt-GUIDs enthalten.</param>
      <param name="type">Eine GUID, die den Objekttyp bezeichnet, auf den der neue ACE angewendet wird.</param>
      <param name="inheritedType">Eine GUID, die den Objekttyp bezeichnet, der den neuen ACE erben kann.</param>
      <param name="isCallback">true, wenn der neue ACE vom Typ Rückruf-ACE ist.</param>
      <param name="opaque">Dem neuen ACE zugeordnete opake Daten.Dies ist nur für Rückruf-ACE-Typen zulässig.Die Länge dieses Arrays darf nicht größer sein als der Rückgabewert der <see cref="M:System.Security.AccessControl.ObjectAceMaxOpaqueLength" />-Methode.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Der Parameter des Qualifizierers enthält einen ungültigen Wert, oder die Länge der Werts für den nicht transparenten Parameter ist größer als der Rückgabewert der <see cref="M:System.Security.AccessControl.ObjectAceMaxOpaqueLength" />-Methode.</exception>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAce.BinaryLength">
      <summary>Ruft die Länge der binären Darstellung des aktuellen <see cref="T:System.Security.AccessControl.ObjectAce" />-Objekts (in Bytes) ab.Diese Länge muss verwendet werden, bevor die ACL mithilfe der <see cref="M:System.Security.AccessControl.ObjectAce.GetBinaryForm" />-Methode in ein binäres Array gemarshallt wird.</summary>
      <returns>Die Länge der binären Darstellung des aktuellen <see cref="T:System.Security.AccessControl.ObjectAce" />-Objekts (in Bytes).</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectAce.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Marshallt den Inhalt des <see cref="T:System.Security.AccessControl.ObjectAce" />-Objekts in das angegebene Bytearray, wobei beim angegebenen Offset begonnen wird.</summary>
      <param name="binaryForm">Das Bytearray, in das der Inhalt von <see cref="T:System.Security.AccessControl.ObjectAce" /> gemarshallt wird.</param>
      <param name="offset">Der Offset, bei dem das Marshallen begonnen werden soll.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> ist negativ oder zu hoch, um den gesamten <see cref="T:System.Security.AccessControl.ObjectAce" /> in <paramref name="array" /> zu kopieren.</exception>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAce.InheritedObjectAceType">
      <summary>Ruft die GUID des Objekttyps ab, der den ACE (Access Control Entry, Zugriffssteuerungseintrag) erben kann, den dieses <see cref="T:System.Security.AccessControl.ObjectAce" />-Objekt darstellt, oder legt diese fest.</summary>
      <returns>Die GUID des Objekttyps, der den ACE (Access Control Entry, Zugriffssteuerungseintrag) erben kann, den dieses <see cref="T:System.Security.AccessControl.ObjectAce" />-Objekt darstellt.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectAce.MaxOpaqueLength(System.Boolean)">
      <summary>Gibt die maximal zulässige Länge eines nicht transparenten Daten-BLOB für Rückruf-ACEs (Access Control Entry, Zugriffssteuerungseintrag) in Bytes zurück.</summary>
      <returns>Die maximal zulässige Länge eines nicht transparenten Daten-BLOB für Rückruf-ACEs (Access Control Entry, Zugriffssteuerungseintrag) in Bytes.</returns>
      <param name="isCallback">True, wenn <see cref="T:System.Security.AccessControl.ObjectAce" /> vom Typ Rückruf-ACE ist.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAce.ObjectAceFlags">
      <summary>Ruft Flags ab, die angeben, ob die <see cref="P:System.Security.AccessControl.ObjectAce.ObjectAceType" />-Eigenschaft und die <see cref="P:System.Security.AccessControl.ObjectAce.InheritedObjectAceType" />-Eigenschaft Werte enthalten, die gültige Objekttypen angeben, oder legt diese fest.</summary>
      <returns>Mindestens ein Member der <see cref="T:System.Security.AccessControl.ObjectAceFlags" />-Enumeration, kombiniert mit einer logischen OR-Operation.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAce.ObjectAceType">
      <summary>Ruft die GUID des Objekttyps ab, der diesem <see cref="T:System.Security.AccessControl.ObjectAce" />-Objekt zugeordnet ist, oder legt diese fest.</summary>
      <returns>Die GUID des Objekttyps, der diesem <see cref="T:System.Security.AccessControl.ObjectAce" />-Objekt zugeordnet ist.</returns>
    </member>
    <member name="T:System.Security.AccessControl.ObjectAceFlags">
      <summary>Gibt das Vorhandensein von Objekttypen für ACEs (Access Control Entries, Zugriffssteuerungseinträge) an.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ObjectAceFlags.InheritedObjectAceTypePresent">
      <summary>Der Objekttyp, der den ACE erben kann.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ObjectAceFlags.None">
      <summary>Es sind keine Objekttypen vorhanden.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ObjectAceFlags.ObjectAceTypePresent">
      <summary>Der Objekttyp, der dem ACE zugeordnet ist, ist vorhanden.</summary>
    </member>
    <member name="T:System.Security.AccessControl.ObjectAuditRule">
      <summary>Stellt eine Kombination aus der Identität eines Benutzers, einer Zugriffsmaske und Überwachungsbedingungen dar.Ein <see cref="T:System.Security.AccessControl.ObjectAuditRule" />-Objekt enthält darüber hinaus Informationen zum Typ des Objekts, auf das die Regel angewendet wird, zum Typ des untergeordneten Objekts, das die Regel erben kann, zur Art und Weise, auf die die Regel von untergeordneten Objekten geerbt wird, sowie zur Übertragung der Vererbung.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectAuditRule.#ctor(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Guid,System.Guid,System.Security.AccessControl.AuditFlags)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.AccessControl.ObjectAuditRule" />-Klasse.</summary>
      <param name="identity">Die Identität, für die die Zugriffsregel gilt.  Dabei muss es sich um ein Objekt handeln, das in eine <see cref="T:System.Security.Principal.SecurityIdentifier" /> umgewandelt werden kann.</param>
      <param name="accessMask">Die Zugriffsmaske dieser Regel.Die Zugriffsmaske ist eine 32-Bit-Auflistung anonymer Bits, deren Bedeutung von den einzelnen Integratoren definiert wird.</param>
      <param name="isInherited">true, wenn diese Regel von einem übergeordneten Container geerbt wird.</param>
      <param name="inheritanceFlags">Gibt die Vererbungseigenschaften der Zugriffsregel an.</param>
      <param name="propagationFlags">Gibt an, ob geerbte Zugriffsregeln automatisch weitergegeben werden.Die Weitergabeflags werden ignoriert, wenn <paramref name="inheritanceFlags" /> auf <see cref="F:System.Security.AccessControl.InheritanceFlags.None" /> festgelegt ist.</param>
      <param name="objectType">Der Objekttyp, auf den die Regel angewendet wird.</param>
      <param name="inheritedObjectType">Der Typ des untergeordneten Objekts, das die Regel erben kann.</param>
      <param name="auditFlags">Die Überwachungsbedingungen.</param>
      <exception cref="T:System.ArgumentException">Der Wert des <paramref name="identity" />-Parameters kann nicht in einen <see cref="T:System.Security.Principal.SecurityIdentifier" /> umgewandelt werden, oder der <paramref name="type" />-Parameter enthält einen ungültigen Wert.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der Wert des <paramref name="accessMask" />-Parameters ist 0 (null), oder der <paramref name="inheritanceFlags" />-Parameter bzw. der <paramref name="propagationFlags" />-Parameter enthält nicht erkannte Flagwerte.</exception>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAuditRule.InheritedObjectType">
      <summary>Ruft den Typ des untergeordneten Objekts ab, das das <see cref="System.Security.AccessControl.ObjectAuditRule" />-Objekt erben kann.</summary>
      <returns>Der Typ des untergeordneten Objekts, das das <see cref="System.Security.AccessControl.ObjectAuditRule" />-Objekt erben kann.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAuditRule.ObjectFlags">
      <summary>Die <see cref="P:System.Security.AccessControl.ObjectAuditRule.ObjectType" />-Eigenschaft und die <see cref="P:System.Security.AccessControl.ObjectAuditRule.InheritedObjectType" />-Eigenschaft des <see cref="System.Security.AccessControl.ObjectAuditRule" />-Objekts enthalten gültige Werte.</summary>
      <returns>
        <see cref="F:System.Security.AccessControl.ObjectAceFlags.ObjectAceTypePresent" /> gibt an, dass die <see cref="P:System.Security.AccessControl.ObjectAuditRule.ObjectType" />-Eigenschaft einen gültigen Wert enthält.<see cref="F:System.Security.AccessControl.ObjectAceFlags.InheritedObjectAceTypePresent" /> gibt an, dass die <see cref="P:System.Security.AccessControl.ObjectAuditRule.InheritedObjectType" />-Eigenschaft einen gültigen Wert enthält.Diese Werte können mit einem logischen OR kombiniert werden.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAuditRule.ObjectType">
      <summary>Ruft den Typ des Objekts ab, auf das die <see cref="System.Security.AccessControl.ObjectAuditRule" /> angewendet wird.</summary>
      <returns>Der Typ des Objekts, auf das die <see cref="System.Security.AccessControl.ObjectAuditRule" /> angewendet wird.</returns>
    </member>
    <member name="T:System.Security.AccessControl.ObjectSecurity">
      <summary>Bietet die Möglichkeit, den Zugriff auf Objekte ohne direktes Bearbeiten von Zugriffssteuerungslisten (ACLs – Access Control Lists) zu steuern.Diese Klasse ist die abstrakte Basisklasse der <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />-Klasse und der <see cref="T:System.Security.AccessControl.DirectoryObjectSecurity" />-Klasse.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Klasse.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.#ctor(System.Boolean,System.Boolean)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Klasse.</summary>
      <param name="isContainer">true, wenn das neue <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt ein Containerobjekt ist.</param>
      <param name="isDS">true, wenn das neue <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt ein Verzeichnisobjekt ist.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.#ctor(System.Security.AccessControl.CommonSecurityDescriptor)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Klasse.</summary>
      <param name="securityDescriptor">Der <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> der neuen <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />-Instanz.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AccessRightType">
      <summary>Ruft den <see cref="T:System.Type" /> des sicherungsfähigen Objekts ab, das diesem <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt zugeordnet ist.</summary>
      <returns>Der Typ des sicherungsfähigen Objekts, das diesem <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt zugeordnet ist.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.AccessRuleFactory(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.AccessControl.AccessRule" />-Klasse mit den angegebenen Werten.</summary>
      <returns>Das von dieser Methode erstellte <see cref="T:System.Security.AccessControl.AccessRule" />-Objekt.</returns>
      <param name="identityReference">Die Identität, für die die Zugriffsregel gilt.Dabei muss es sich um ein Objekt handeln, das in eine <see cref="T:System.Security.Principal.SecurityIdentifier" /> umgewandelt werden kann.</param>
      <param name="accessMask">Die Zugriffsmaske dieser Regel.Die Zugriffsmaske ist eine 32-Bit-Auflistung anonymer Bits, deren Bedeutung von den einzelnen Integratoren definiert wird.</param>
      <param name="isInherited">true, wenn diese Regel von einem übergeordneten Container geerbt wird.</param>
      <param name="inheritanceFlags">Gibt die Vererbungseigenschaften der Zugriffsregel an.</param>
      <param name="propagationFlags">Gibt an, ob geerbte Zugriffsregeln automatisch weitergegeben werden.Die Weitergabeflags werden ignoriert, wenn <paramref name="inheritanceFlags" /> auf <see cref="F:System.Security.AccessControl.InheritanceFlags.None" /> festgelegt ist.</param>
      <param name="type">Gibt den gültigen Zugriffssteuerungstyp an.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AccessRulesModified">
      <summary>Ruft einen booleschen Wert ab, der angibt, ob die diesem <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt zugeordneten Zugriffsregeln geändert wurden, oder legt diesen Wert fest.</summary>
      <returns>true, wenn die diesem <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt zugeordneten Zugriffsregeln geändert wurden, andernfalls false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AccessRuleType">
      <summary>Ruft den <see cref="T:System.Type" /> des Objekts ab, dem die Zugriffsregeln dieses <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekts zugeordnet sind.Das <see cref="T:System.Type" />-Objekt muss ein Objekt sein, das in ein <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekt umgewandelt werden kann.</summary>
      <returns>Der Typ des Objekts, dem die Zugriffsregeln dieses <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekts zugeordnet sind.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AreAccessRulesCanonical">
      <summary>Ruft einen booleschen Wert ab, der angibt, ob die diesem <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt zugeordneten Zugriffsregeln in kanonischer Reihenfolge vorliegen.</summary>
      <returns>true, wenn die Zugriffsregeln in kanonischer Reihenfolge vorliegen, andernfalls false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AreAccessRulesProtected">
      <summary>Ruft einen booleschen Wert ab, der angibt, ob die freigegebene Zugriffssteuerungsliste (DACL – Discretionary Access Control List), die diesem <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt zugeordnet ist, geschützt ist.</summary>
      <returns>true, wenn die DACL geschützt ist, andernfalls false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AreAuditRulesCanonical">
      <summary>Ruft einen booleschen Wert ab, der angibt, ob die diesem <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt zugeordneten Überwachungsregeln in kanonischer Reihenfolge vorliegen.</summary>
      <returns>true, wenn die Überwachungsregeln in kanonischer Reihenfolge vorliegen, andernfalls false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AreAuditRulesProtected">
      <summary>Ruft einen booleschen Wert ab, der angibt, ob die Systemzugriffssteuerungsliste (SACL – System Access Control List), die diesem <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt zugeordnet ist, geschützt ist.</summary>
      <returns>true, wenn die SACL geschützt ist, andernfalls false.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.AuditRuleFactory(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.AccessControl.AuditRule" />-Klasse mit den angegebenen Werten.</summary>
      <returns>Das von dieser Methode erstellte <see cref="T:System.Security.AccessControl.AuditRule" />-Objekt.</returns>
      <param name="identityReference">Die Identität, auf die die Überwachungsregel angewendet wird.Dabei muss es sich um ein Objekt handeln, das in eine <see cref="T:System.Security.Principal.SecurityIdentifier" /> umgewandelt werden kann.</param>
      <param name="accessMask">Die Zugriffsmaske dieser Regel.Die Zugriffsmaske ist eine 32-Bit-Auflistung anonymer Bits, deren Bedeutung von den einzelnen Integratoren definiert wird.</param>
      <param name="isInherited">true, wenn diese Regel von einem übergeordneten Container geerbt wird.</param>
      <param name="inheritanceFlags">Gibt die Vererbungseigenschaften der Überwachungsregel an.</param>
      <param name="propagationFlags">Gibt an, ob geerbte Überwachungsregeln automatisch weitergegeben werden.Die Weitergabeflags werden ignoriert, wenn <paramref name="inheritanceFlags" /> auf <see cref="F:System.Security.AccessControl.InheritanceFlags.None" /> festgelegt ist.</param>
      <param name="flags">Gibt die Bedingungen an, für die die Regel überwacht wird.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AuditRulesModified">
      <summary>Ruft einen booleschen Wert ab, der angibt, ob die diesem <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt zugeordneten Überwachungsregeln geändert wurden, oder legt diesen Wert fest.</summary>
      <returns>true, wenn die diesem <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt zugeordneten Überwachungsregeln geändert wurden, andernfalls false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AuditRuleType">
      <summary>Ruft das <see cref="T:System.Type" />-Objekt ab, dem die Überwachungsregeln dieses <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekts zugeordnet sind.Das <see cref="T:System.Type" />-Objekt muss ein Objekt sein, das in ein <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekt umgewandelt werden kann.</summary>
      <returns>Der Typ des Objekts, dem die Überwachungsregeln dieses <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekts zugeordnet sind.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.GetGroup(System.Type)">
      <summary>Ruft die primäre Gruppe ab, die dem angegebenen Besitzer zugeordnet ist.</summary>
      <returns>Die primäre Gruppe, die dem angegebenen Besitzer zugeordnet ist.</returns>
      <param name="targetType">Der Besitzer, für den die primäre Gruppe abgerufen werden soll. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.GetOwner(System.Type)">
      <summary>Ruft den Benutzer ab, der der angegebenen primären Gruppe zugeordnet ist.</summary>
      <returns>Der Benutzer mit der angegebenen Gruppe.</returns>
      <param name="targetType">Die primäre Gruppe, für die der Benutzer abgerufen werden soll.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.GetSecurityDescriptorBinaryForm">
      <summary>Gibt ein Array von Bytewerten zurück, das die Sicherheitsbeschreibungsinformationen für dieses <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt darstellt.</summary>
      <returns>Ein Array von Bytewerten, das die Sicherheitsbeschreibung für dieses <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt darstellt.Diese Methode gibt null zurück, wenn dieses <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt keine Sicherheitsinformationen enthält.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.GetSecurityDescriptorSddlForm(System.Security.AccessControl.AccessControlSections)">
      <summary>Gibt die SDDL-Darstellung (Security Descriptor Definition Language) der angegebenen Abschnitte der von diesem <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt dargestellten Sicherheitsbeschreibung zurück.</summary>
      <returns>Die SDDL-Darstellung der angegebenen Abschnitte der Sicherheitsbeschreibung, die diesem <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt zugeordnet ist.</returns>
      <param name="includeSections">Gibt an, welche Abschnitte (Zugriffsregeln, Überwachungsregeln, primäre Gruppe, Besitzer) der Sicherheitsbeschreibung abgerufen werden sollen.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.GroupModified">
      <summary>Ruft einen booleschen Wert ab, der angibt, ob die dem sicherungsfähigen Objekt zugeordnete Gruppe geändert wurde, oder legt diesen fest. </summary>
      <returns>true, wenn die dem sicherungsfähigen Objekt zugeordnete Gruppe geändert wurde, andernfalls false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.IsContainer">
      <summary>Ruft einen booleschen Wert ab, der angibt, ob dieses <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt ein Containerobjekt ist.</summary>
      <returns>true, wenn das <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt ein Containerobjekt ist, andernfalls false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.IsDS">
      <summary>Ruft einen booleschen Wert ab, der angibt, ob dieses <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt ein Verzeichnisobjekt ist.</summary>
      <returns>true, wenn das <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt ein Verzeichnisobjekt ist, andernfalls false.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.IsSddlConversionSupported">
      <summary>Gibt einen booleschen Wert zurück, der angibt, ob die diesem <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt zugeordnete Sicherheitsbeschreibung in das SDDL (Security Descriptor Definition Language)-Format umgewandelt werden kann.</summary>
      <returns>true, wenn die diesem <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt zugeordnete Sicherheitsbeschreibung in das SDDL-Format (Security Descriptor Definition Language) umgewandelt werden kann, andernfalls false.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.ModifyAccess(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AccessRule,System.Boolean@)">
      <summary>Wendet die angegebene Änderung auf die freigegebene Zugriffssteuerungsliste (DACL – Discretionary Access Control List) an, die diesem <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt zugeordnet ist.</summary>
      <returns>true, wenn die DACL erfolgreich geändert wird, andernfalls false.</returns>
      <param name="modification">Die Änderung, die auf die DACL angewendet werden soll.</param>
      <param name="rule">Die zu ändernde Zugriffsregel.</param>
      <param name="modified">true, wenn die DACL erfolgreich geändert wird, andernfalls false.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.ModifyAccessRule(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AccessRule,System.Boolean@)">
      <summary>Wendet die angegebene Änderung auf die freigegebene Zugriffssteuerungsliste (DACL – Discretionary Access Control List) an, die diesem <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt zugeordnet ist.</summary>
      <returns>true, wenn die DACL erfolgreich geändert wird, andernfalls false.</returns>
      <param name="modification">Die Änderung, die auf die DACL angewendet werden soll.</param>
      <param name="rule">Die zu ändernde Zugriffsregel.</param>
      <param name="modified">true, wenn die DACL erfolgreich geändert wird, andernfalls false.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.ModifyAudit(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AuditRule,System.Boolean@)">
      <summary>Wendet die angegebene Änderung auf die Systemzugriffssteuerungsliste (SACL – System Access Control List) an, die diesem <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt zugeordnet ist.</summary>
      <returns>true, wenn die SACL erfolgreich geändert wird, andernfalls false.</returns>
      <param name="modification">Die Änderung, die auf die SACL angewendet werden soll.</param>
      <param name="rule">Die zu ändernde Überwachungsregel.</param>
      <param name="modified">true, wenn die SACL erfolgreich geändert wird, andernfalls false.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.ModifyAuditRule(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AuditRule,System.Boolean@)">
      <summary>Wendet die angegebene Änderung auf die Systemzugriffssteuerungsliste (SACL – System Access Control List) an, die diesem <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt zugeordnet ist.</summary>
      <returns>true, wenn die SACL erfolgreich geändert wird, andernfalls false.</returns>
      <param name="modification">Die Änderung, die auf die SACL angewendet werden soll.</param>
      <param name="rule">Die zu ändernde Überwachungsregel.</param>
      <param name="modified">true, wenn die SACL erfolgreich geändert wird, andernfalls false.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.OwnerModified">
      <summary>Ruft einen booleschen Wert ab, der angibt, ob der Besitzer des sicherungsfähigen Objekts geändert wurde, oder legt diesen Wert fest.</summary>
      <returns>true, wenn der Besitzer des sicherungsfähigen Objekts geändert wurde, andernfalls false.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.Persist(System.Boolean,System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>Führt eine dauerhafte Speicherung der angegebenen Abschnitte der Sicherheitsbeschreibung aus, die diesem <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt zugeordnet sind.Es wird empfohlen, dass die Werte der <paramref name="includeSections" />-Parameter, die an den Konstruktor übergeben werden, und die Persist-Methoden identisch sind.Weitere Informationen finden Sie in den Hinweisen.</summary>
      <param name="enableOwnershipPrivilege">true, um das Recht zu aktivieren, das dem Aufrufer ermöglicht, den Besitz des Objekts zu übernehmen.</param>
      <param name="name">Der Name, der zum Abrufen der dauerhaft gespeicherten Informationen verwendet wird.</param>
      <param name="includeSections">Einer der <see cref="T:System.Security.AccessControl.AccessControlSections" />-Enumerationswerte, der die Abschnitte der Sicherheitsbeschreibung (Zugriffsregeln, Überwachungsregeln, Besitzer, primäre Gruppe) des sicherungsfähigen Objekts angibt, die gespeichert werden sollen.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.Persist(System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections)">
      <summary>Führt eine dauerhafte Speicherung der angegebenen Abschnitte der Sicherheitsbeschreibung aus, die diesem <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt zugeordnet sind.Es wird empfohlen, dass die Werte der <paramref name="includeSections" />-Parameter, die an den Konstruktor übergeben werden, und die Persist-Methoden identisch sind.Weitere Informationen finden Sie in den Hinweisen.</summary>
      <param name="handle">Das Handle, das zum Abrufen der dauerhaft gespeicherten Informationen verwendet wird.</param>
      <param name="includeSections">Einer der <see cref="T:System.Security.AccessControl.AccessControlSections" />-Enumerationswerte, der die Abschnitte der Sicherheitsbeschreibung (Zugriffsregeln, Überwachungsregeln, Besitzer, primäre Gruppe) des sicherungsfähigen Objekts angibt, die gespeichert werden sollen.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.Persist(System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>Führt eine dauerhafte Speicherung der angegebenen Abschnitte der Sicherheitsbeschreibung aus, die diesem <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt zugeordnet sind.Es wird empfohlen, dass die Werte der <paramref name="includeSections" />-Parameter, die an den Konstruktor übergeben werden, und die Persist-Methoden identisch sind.Weitere Informationen finden Sie in den Hinweisen.</summary>
      <param name="name">Der Name, der zum Abrufen der dauerhaft gespeicherten Informationen verwendet wird.</param>
      <param name="includeSections">Einer der <see cref="T:System.Security.AccessControl.AccessControlSections" />-Enumerationswerte, der die Abschnitte der Sicherheitsbeschreibung (Zugriffsregeln, Überwachungsregeln, Besitzer, primäre Gruppe) des sicherungsfähigen Objekts angibt, die gespeichert werden sollen.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.PurgeAccessRules(System.Security.Principal.IdentityReference)">
      <summary>Entfernt alle der angegebenen <see cref="T:System.Security.Principal.IdentityReference" /> zugeordneten Zugriffsregeln.</summary>
      <param name="identity">Die <see cref="T:System.Security.Principal.IdentityReference" />, für die alle Zugriffsregeln entfernt werden sollen.</param>
      <exception cref="T:System.InvalidOperationException">Die Zugriffsregeln liegen nicht in kanonischer Reihenfolge vor.</exception>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.PurgeAuditRules(System.Security.Principal.IdentityReference)">
      <summary>Entfernt alle der angegebenen <see cref="T:System.Security.Principal.IdentityReference" /> zugeordneten Überwachungsregeln.</summary>
      <param name="identity">Die <see cref="T:System.Security.Principal.IdentityReference" />, für die alle Überwachungsregeln entfernt werden sollen.</param>
      <exception cref="T:System.InvalidOperationException">Die Überwachungsregeln liegen nicht in kanonischer Reihenfolge vor.</exception>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.ReadLock">
      <summary>Sperrt dieses <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt für Lesezugriff.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.ReadUnlock">
      <summary>Entsperrt dieses <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt für Lesezugriff.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetAccessRuleProtection(System.Boolean,System.Boolean)">
      <summary>Legt den Schutz der Zugriffsregeln fest, die diesem <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt zugeordnet sind, oder entfernt diesen.Geschützte Zugriffsregeln können von übergeordneten Objekten nicht durch Vererbung geändert werden.</summary>
      <param name="isProtected">true, wenn die diesem <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt zugeordneten Zugriffsregeln vor Vererbung geschützt werden sollen, false, wenn Vererbung zugelassen werden soll.</param>
      <param name="preserveInheritance">true, wenn geerbte Zugriffsregeln beibehalten werden sollen, false wenn geerbte Zugriffsregeln entfernt werden sollen.Dieser Parameter wird ignoriert, wenn <paramref name="isProtected" />false ist.</param>
      <exception cref="T:System.InvalidOperationException">Diese Methode versucht, geerbte Regeln aus einer nicht kanonischen freigegebenen Zugriffssteuerungsliste (DACL – Discretionary Access Control List) zu entfernen.</exception>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetAuditRuleProtection(System.Boolean,System.Boolean)">
      <summary>Legt den Schutz der Überwachungsregeln fest, die diesem <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt zugeordnet sind, oder entfernt diesen.Geschützte Überwachungsregeln können von übergeordneten Objekten nicht durch Vererbung geändert werden.</summary>
      <param name="isProtected">true, wenn die diesem <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt zugeordneten Überwachungsregeln vor Vererbung geschützt werden sollen, false, wenn Vererbung zugelassen werden soll.</param>
      <param name="preserveInheritance">true, wenn geerbte Überwachungsregeln beibehalten werden sollen, false wenn geerbte Überwachungsregeln entfernt werden sollen.Dieser Parameter wird ignoriert, wenn <paramref name="isProtected" />false ist.</param>
      <exception cref="T:System.InvalidOperationException">Diese Methode versucht, geerbte Regeln aus einer nicht kanonischen Systemzugriffssteuerungsliste (SACL – System Access Control List) zu entfernen.</exception>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetGroup(System.Security.Principal.IdentityReference)">
      <summary>Legt die primäre Gruppe für die Sicherheitsbeschreibung fest, die diesem <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt zugeordnet ist.</summary>
      <param name="identity">Die festzulegende primäre Gruppe.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetOwner(System.Security.Principal.IdentityReference)">
      <summary>Legt den Besitzer der Sicherheitsbeschreibung fest, die diesem <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt zugeordnet ist.</summary>
      <param name="identity">Der festzulegende Besitzer.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetSecurityDescriptorBinaryForm(System.Byte[])">
      <summary>Legt die Sicherheitsbeschreibung für dieses <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt durch das angegebene Array von Bytewerten fest.</summary>
      <param name="binaryForm">Das Array von Bytes, das zum Festlegen der Sicherheitsbeschreibung verwendet wird.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetSecurityDescriptorBinaryForm(System.Byte[],System.Security.AccessControl.AccessControlSections)">
      <summary>Legt die angegebenen Abschnitte der Sicherheitsbeschreibung für dieses <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt durch das angegebene Array von Bytewerten fest.</summary>
      <param name="binaryForm">Das Array von Bytes, das zum Festlegen der Sicherheitsbeschreibung verwendet wird.</param>
      <param name="includeSections">Die Abschnitte (Zugriffsregeln, Überwachungsregeln, Besitzer, primäre Gruppe) der festzulegenden Sicherheitsbeschreibung.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetSecurityDescriptorSddlForm(System.String)">
      <summary>Legt die Sicherheitsbeschreibung für dieses <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt durch die angegebene SDDL-Zeichenfolge (Security Descriptor Definition Language) fest.</summary>
      <param name="sddlForm">Die SDDL-Zeichenfolge, durch die die Sicherheitsbeschreibung festgelegt wird.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetSecurityDescriptorSddlForm(System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>Legt die angegebenen Abschnitte der Sicherheitsbeschreibung für dieses <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt durch die angegebene SDDL-Zeichenfolge (Security Descriptor Definition Language) fest.</summary>
      <param name="sddlForm">Die SDDL-Zeichenfolge, durch die die Sicherheitsbeschreibung festgelegt wird.</param>
      <param name="includeSections">Die Abschnitte (Zugriffsregeln, Überwachungsregeln, Besitzer, primäre Gruppe) der festzulegenden Sicherheitsbeschreibung.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.WriteLock">
      <summary>Sperrt dieses <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt für den Schreibzugriff.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.WriteUnlock">
      <summary>Entsperrt dieses <see cref="T:System.Security.AccessControl.ObjectSecurity" />-Objekt für Schreibzugriff.</summary>
    </member>
    <member name="T:System.Security.AccessControl.ObjectSecurity`1">
      <summary>Stellt die Möglichkeit bereit, Zugriff auf Objekte ohne direkte Bearbeitung von Zugriffssteuerungslisten (ACLs) zu steuern, gewährt außerdem die Möglichkeit zur Typumwandlung von Zugriffsrechten. </summary>
      <typeparam name="T">Die Zugriffsrechte für das Objekt.</typeparam>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.#ctor(System.Boolean,System.Security.AccessControl.ResourceType)">
      <summary>Initialisiert eine neue Instanz der ObjectSecurity’1-Klasse.</summary>
      <param name="isContainer">true, wenn das neue <see cref="T:System.Security.AccessControl.ObjectSecurity`1" />-Objekt ein Containerobjekt ist.</param>
      <param name="resourceType">Der Ressourcentyp.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections)">
      <summary>Initialisiert eine neue Instanz der ObjectSecurity’1-Klasse.</summary>
      <param name="isContainer">true, wenn das neue <see cref="T:System.Security.AccessControl.ObjectSecurity`1" />-Objekt ein Containerobjekt ist.</param>
      <param name="resourceType">Der Ressourcentyp.</param>
      <param name="safeHandle">Ein Handler.</param>
      <param name="includeSections">Die einzuschließenden Abschnitte.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections,System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode,System.Object)">
      <summary>Initialisiert eine neue Instanz der ObjectSecurity’1-Klasse.</summary>
      <param name="isContainer">true, wenn das neue <see cref="T:System.Security.AccessControl.ObjectSecurity`1" />-Objekt ein Containerobjekt ist.</param>
      <param name="resourceType">Der Ressourcentyp.</param>
      <param name="safeHandle">Ein Handler.</param>
      <param name="includeSections">Die einzuschließenden Abschnitte.</param>
      <param name="exceptionFromErrorCode">Ein von Integratoren implementierter Delegat, der benutzerdefinierte Ausnahmen bereitstellt.</param>
      <param name="exceptionContext">Ein Objekt, das Kontextinformationen über die Quelle oder das Ziel der Ausnahme bereitstellt.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>Initialisiert eine neue Instanz der ObjectSecurity’1-Klasse.</summary>
      <param name="isContainer">true, wenn das neue <see cref="T:System.Security.AccessControl.ObjectSecurity`1" />-Objekt ein Containerobjekt ist.</param>
      <param name="resourceType">Der Ressourcentyp.</param>
      <param name="name">Der Name des sicherungsfähigen Objekts, dem das neue <see cref="T:System.Security.AccessControl.ObjectSecurity`1" />-Objekt zugeordnet wird.</param>
      <param name="includeSections">Die einzuschließenden Abschnitte.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.String,System.Security.AccessControl.AccessControlSections,System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode,System.Object)">
      <summary>Initialisiert eine neue Instanz der ObjectSecurity’1-Klasse.</summary>
      <param name="isContainer">true, wenn das neue <see cref="T:System.Security.AccessControl.ObjectSecurity`1" />-Objekt ein Containerobjekt ist.</param>
      <param name="resourceType">Der Ressourcentyp.</param>
      <param name="name">Der Name des sicherungsfähigen Objekts, dem das neue <see cref="T:System.Security.AccessControl.ObjectSecurity`1" />-Objekt zugeordnet wird.</param>
      <param name="includeSections">Die einzuschließenden Abschnitte. </param>
      <param name="exceptionFromErrorCode">Ein von Integratoren implementierter Delegat, der benutzerdefinierte Ausnahmen bereitstellt.</param>
      <param name="exceptionContext">Ein Objekt, das Kontextinformationen über die Quelle oder das Ziel der Ausnahme bereitstellt.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity`1.AccessRightType">
      <summary>Ruft den Typ des sicherungsfähigen Objekts ab, das diesem ObjectSecurity’1-Objekt zugeordnet ist.</summary>
      <returns>Das Typ des sicherungsfähigen Objekts, das der aktuellen Instanz zugeordnet ist.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.AccessRuleFactory(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
      <summary>Initialisiert eine neue Instanz der ObjectAccessRule-Klasse, die eine neue Zugriffssteuerungsregel für das zugeordnete Sicherheitsobjekt darstellt.</summary>
      <returns>Stellt eine neue Zugriffssteuerungsregel für den angegebenen Benutzer mit den angegebenen Zugriffsrechten und Flags sowie der angegebenen Zugriffssteuerung dar.</returns>
      <param name="identityReference">Stellt ein Benutzerkonto dar.</param>
      <param name="accessMask">Der Zugriffs-Typ</param>
      <param name="isInherited">true, wenn die Zugriffsregel geerbt wurde, andernfalls false.</param>
      <param name="inheritanceFlags">Gibt an, wie Zugriffsmasken zu untergeordneten Objekten weitergegeben werden.</param>
      <param name="propagationFlags">Gibt an, wie Einträge für die Zugriffssteuerung (ACEs) an untergeordnete Objekte weitergegeben werden.</param>
      <param name="type">Gibt an, ob der Zugriff gewährt oder verweigert wird.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity`1.AccessRuleType">
      <summary>Ruft den Typ des Objekts ab, dem die Zugriffsregeln dieses ObjectSecurity’1-Objekts zugeordnet sind. </summary>
      <returns>Der Typ des Objekts, das den Zugriffsregeln des aktuellen Instanz zugeordnet ist.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.AddAccessRule(System.Security.AccessControl.AccessRule{`0})">
      <summary>Fügt der DACL (Discretionary Access Control List), die diesem ObjectSecurity`1-Objekt zugeordnet ist, die angegebene Zugriffsregel hinzu.</summary>
      <param name="rule">Die hinzuzufügende Regel</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.AddAuditRule(System.Security.AccessControl.AuditRule{`0})">
      <summary>Fügt der SACL (System Access Control List), die diesem ObjectSecurity`1-Objekt zugeordnet ist, die angegebene Überwachungsregel hinzu.</summary>
      <param name="rule">Die hinzuzufügende Überwachungsregel.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.AuditRuleFactory(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.AccessControl.AuditRule" />-Klasse, die die angegebene Überwachungsregel für den angegebenen Benutzer darstellt.</summary>
      <returns>Gibt die angegebene Überwachungsregel für den angegebenen Benutzer zurück.</returns>
      <param name="identityReference">Stellt ein Benutzerkonto dar. </param>
      <param name="accessMask">Eine ganze Zahl, die einen Zugriffstyp angibt.</param>
      <param name="isInherited">true, wenn die Zugriffsregel geerbt wurde, andernfalls false.</param>
      <param name="inheritanceFlags">Gibt an, wie Zugriffsmasken zu untergeordneten Objekten weitergegeben werden.</param>
      <param name="propagationFlags">Gibt an, wie Einträge für die Zugriffssteuerung (ACEs) an untergeordnete Objekte weitergegeben werden.</param>
      <param name="flags">Beschreibt den Typ der durchzuführenden Überwachung,.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity`1.AuditRuleType">
      <summary>Ruft den Typ des Objekts ab, dem die Überwachungsregeln dieses ObjectSecurity’1-Objekts zugeordnet sind.</summary>
      <returns>Das Typ-Objekt, das den Überwachungsregeln des aktuellen Instanz zugeordnet ist.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.Persist(System.Runtime.InteropServices.SafeHandle)">
      <summary>Führt mithilfe des angegebenen Handels eine dauerhafte Speicherung der Sicherheitsbeschreibung aus, die diesem ObjectSecurity`1-Objekt zugeordnet sind.</summary>
      <param name="handle">Das Handle des sicherungsfähigen Objekts, dem das ObjectSecurity`1-Objekt zugeordnet wird.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.Persist(System.String)">
      <summary>Führt mithilfe des angegebenen Namens eine dauerhafte Speicherung der Sicherheitsbeschreibung aus, die diesem ObjectSecurity`1-Objekt zugeordnet sind.</summary>
      <param name="name">Der Name des sicherungsfähigen Objekts, dem das ObjectSecurity`1-Objekt zugeordnet wird.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAccessRule(System.Security.AccessControl.AccessRule{`0})">
      <summary>Entfernt Zugriffsregeln, die dieselbe Sicherheits-ID und dieselbe Zugriffsmaske wie die angegebene Zugriffsregel enthalten, aus der DACL (Discretionary Access Control List), die diesem ObjectSecurity`1-Objekt zugeordnet ist.</summary>
      <returns>Gibt true zurück, wenn die Zugriffsregel erfolgreich entfernt wurde; andernfalls false.</returns>
      <param name="rule">Die zu entfernende Regel.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAccessRuleAll(System.Security.AccessControl.AccessRule{`0})">
      <summary>Entfernt alle Zugriffsregeln, die dieselbe Sicherheits-ID wie die angegebene Zugriffsregel enthalten, aus der DACL (Discretionary Access Control List), die diesem ObjectSecurity`1-Objekt zugeordnet ist.</summary>
      <param name="rule">Die zu entfernende Zugriffsregel.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAccessRuleSpecific(System.Security.AccessControl.AccessRule{`0})">
      <summary>Entfernt alle Zugriffsregeln, die der angegebenen Zugriffsregel genau entsprechen, aus der DACL (Discretionary Access Control List), die diesem ObjectSecurity`1-Objekt zugeordnet ist.</summary>
      <param name="rule">Die zu entfernende Zugriffsregel.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAuditRule(System.Security.AccessControl.AuditRule{`0})">
      <summary>Entfernt Überwachungsregeln, die dieselbe Sicherheits-ID und dieselbe Zugriffsmaske wie die angegebene Überwachungsregel enthalten, aus der SACL (System Access Control List), die diesem ObjectSecurity`1-Objekt zugeordnet ist.</summary>
      <returns>Gibt true zurück, wenn das Objekt entfernt wurde, andernfalls false.</returns>
      <param name="rule">Die zu entfernende Überwachungsregel.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAuditRuleAll(System.Security.AccessControl.AuditRule{`0})">
      <summary>Entfernt alle Überwachungsregeln, die dieselbe Sicherheits-ID wie die angegebene Überwachungsregel enthalten, aus der SACL (System Access Control List), die diesem ObjectSecurity`1-Objekt zugeordnet ist.</summary>
      <param name="rule">Die zu entfernende Überwachungsregel.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAuditRuleSpecific(System.Security.AccessControl.AuditRule{`0})">
      <summary>Entfernt alle Überwachungsregeln, die der angegebenen Überwachungsregel genau entsprechen, aus der SACL (System Access Control List), die diesem ObjectSecurity`1-Objekt zugeordnet ist.</summary>
      <param name="rule">Die zu entfernende Überwachungsregel.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.ResetAccessRule(System.Security.AccessControl.AccessRule{`0})">
      <summary>Entfernt alle Zugriffsregeln in der DACL (Discretionary Access Control List), die diesem ObjectSecurity`1-Objekt zugeordnet sind und fügt die festgelegte Zugriffsregel hinzu.</summary>
      <param name="rule">Die zurückzusetzende Zugriffsregel.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.SetAccessRule(System.Security.AccessControl.AccessRule{`0})">
      <summary>Entfernt alle Zugriffsregeln, die dieselbe Sicherheits-ID und denselben Qualifizierer wie die angegebene Zugriffsregel in der DACL (Discretionary Access Control List) enthalten, die diesem ObjectSecurity`1-Objekt zugeordnet ist, und fügt anschließend die angegebene Zugriffsregel hinzu.</summary>
      <param name="rule">Die festzulegende Zugriffsregel.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.SetAuditRule(System.Security.AccessControl.AuditRule{`0})">
      <summary>Entfernt alle Überwachungsregeln, die dieselbe Sicherheits-ID und denselben Qualifizierer wie die angegebene Überwachungsregel in der SACL (System Access Control List) enthalten, die diesem ObjectSecurity`1-Objekt zugeordnet ist, und fügt anschließend die angegebene Überwachungsregel hinzu.</summary>
      <param name="rule">Die festzulegende Überwachungsregel.</param>
    </member>
    <member name="T:System.Security.AccessControl.PrivilegeNotHeldException">
      <summary>Diese Ausnahme wird ausgelöst, wenn eine Methode in dem <see cref="N:System.Security.AccessControl" />-Namespace versucht, eine für diese Methode nicht verfügbare Berechtigung zu aktivieren.</summary>
    </member>
    <member name="M:System.Security.AccessControl.PrivilegeNotHeldException.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.AccessControl.PrivilegeNotHeldException" />-Klasse.</summary>
    </member>
    <member name="M:System.Security.AccessControl.PrivilegeNotHeldException.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.AccessControl.PrivilegeNotHeldException" />-Klasse unter Verwendung der angegebenen Berechtigung.</summary>
      <param name="privilege">Die nicht aktivierte Berechtigung.</param>
    </member>
    <member name="M:System.Security.AccessControl.PrivilegeNotHeldException.#ctor(System.String,System.Exception)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.AccessControl.PrivilegeNotHeldException" />-Klasse unter Verwendung der angegebenen Ausnahme.</summary>
      <param name="privilege">Die nicht aktivierte Berechtigung.</param>
      <param name="inner">Die Ausnahme, die die Ursache der aktuellen Ausnahme ist.Wenn der <paramref name="innerException" />-Parameter kein NULL-Verweis ist, (Nothing in Visual Basic), wird die aktuelle Ausnahme in einem catch-Block ausgelöst, der die innere Ausnahme behandelt.</param>
    </member>
    <member name="P:System.Security.AccessControl.PrivilegeNotHeldException.PrivilegeName">
      <summary>Ruft den Namen der nicht aktivierten Berechtigung ab.</summary>
      <returns>Der Name der Berechtigung, die von der Methode nicht aktiviert werden konnte.</returns>
    </member>
    <member name="T:System.Security.AccessControl.PropagationFlags">
      <summary>Gibt an, wie Einträge für die Zugriffssteuerung (ACEs) an untergeordnete Objekte weitergegeben werden.  Diese Flags sind nur von Bedeutung, wenn Vererbungsflags vorhanden sind. </summary>
    </member>
    <member name="F:System.Security.AccessControl.PropagationFlags.InheritOnly">
      <summary>Gibt an, dass der ACE nur an untergeordnete Objekte weitergegeben wird.Dies schließt untergeordnete Container- und Endobjekte ein.</summary>
    </member>
    <member name="F:System.Security.AccessControl.PropagationFlags.None">
      <summary>Gibt an, dass keine Vererbungsflags festgelegt sind.</summary>
    </member>
    <member name="F:System.Security.AccessControl.PropagationFlags.NoPropagateInherit">
      <summary>Gibt an, dass der ACE nicht an untergeordnete Objekte weitergegeben wird.</summary>
    </member>
    <member name="T:System.Security.AccessControl.QualifiedAce">
      <summary>Stellt einen Zugriffssteuerungseintrag (ACE – Access Control Entry) dar, der einen Qualifizierer enthält.Der durch ein <see cref="T:System.Security.AccessControl.AceQualifier" />-Objekt dargestellte Qualifizierer gibt an, ob der ACE Zugriff gewährt bzw. verweigert oder ob er Systemüberwachungen oder einen Systemalarm auslöst.Die <see cref="T:System.Security.AccessControl.QualifiedAce" />-Klasse ist die abstrakte Basisklasse der <see cref="T:System.Security.AccessControl.CommonAce" />-Klasse und der <see cref="T:System.Security.AccessControl.ObjectAce" />-Klasse.</summary>
    </member>
    <member name="P:System.Security.AccessControl.QualifiedAce.AceQualifier">
      <summary>Ruft einen Wert ab, der angibt, ob der ACE Zugriff gewährt, Zugriff verweigert, Systemüberwachungen auslöst oder einen Systemalarm auslöst.</summary>
      <returns>Ein Wert, der angibt, ob der ACE Zugriff gewährt, Zugriff verweigert, Systemüberwachungen auslöst oder einen Systemalarm auslöst.</returns>
    </member>
    <member name="M:System.Security.AccessControl.QualifiedAce.GetOpaque">
      <summary>Gibt die opaken Rückrufdaten zurück, die diesem <see cref="T:System.Security.AccessControl.QualifiedAce" />-Objekt zugeordnet sind. </summary>
      <returns>Ein Array von Bytewerten, das die opaken Rückrufdaten darstellt, die diesem <see cref="T:System.Security.AccessControl.QualifiedAce" />-Objekt zugeordnet sind.</returns>
    </member>
    <member name="P:System.Security.AccessControl.QualifiedAce.IsCallback">
      <summary>Gibt an, ob dieses <see cref="T:System.Security.AccessControl.QualifiedAce" />-Objekt Rückrufdaten enthält.</summary>
      <returns>true, wenn dieses <see cref="T:System.Security.AccessControl.QualifiedAce" />-Objekt Rückrufdaten enthält, andernfalls false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.QualifiedAce.OpaqueLength">
      <summary>Ruft die Länge der opaken Rückrufdaten ab, die diesem <see cref="T:System.Security.AccessControl.QualifiedAce" />-Objekt zugeordnet sind.Diese Eigenschaft ist nur für Zugriffssteuerungseinträge (ACEs) mit Rückruf gültig.</summary>
      <returns>Die Länge der opaken Rückrufdaten.</returns>
    </member>
    <member name="M:System.Security.AccessControl.QualifiedAce.SetOpaque(System.Byte[])">
      <summary>Legt die opaken Rückrufdaten fest, die diesem <see cref="T:System.Security.AccessControl.QualifiedAce" />-Objekt zugeordnet sind.</summary>
      <param name="opaque">Ein Array von Bytewerten, das die opaken Rückrufdaten darstellt, die diesem <see cref="T:System.Security.AccessControl.QualifiedAce" />-Objekt zugeordnet sind.</param>
    </member>
    <member name="T:System.Security.AccessControl.RawAcl">
      <summary>Stellt eine Access Control List (ACL – Zugriffssteuerungsliste) dar.</summary>
    </member>
    <member name="M:System.Security.AccessControl.RawAcl.#ctor(System.Byte,System.Int32)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.AccessControl.RawAcl" />-Klasse mit der angegebenen Revisionsebene.</summary>
      <param name="revision">Die Revisionsebene der neuen ACL.</param>
      <param name="capacity">Die Anzahl der möglichen ACEs, die dieses <see cref="T:System.Security.AccessControl.RawAcl" />-Objekt enthalten kann.Diese Zahl sollte nur als Anhaltspunkt verwendet werden.</param>
    </member>
    <member name="M:System.Security.AccessControl.RawAcl.#ctor(System.Byte[],System.Int32)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.AccessControl.RawAcl" />-Klasse aus der angegebenen binären Form.</summary>
      <param name="binaryForm">Ein Array von Bytewerten, das eine ACL darstellt.</param>
      <param name="offset">Der Offset im <paramref name="binaryForm" />-Parameter, ab dem das Rückgängigmachen des Marshalling von Daten beginnen soll.</param>
    </member>
    <member name="P:System.Security.AccessControl.RawAcl.BinaryLength">
      <summary>Ruft die Länge der binären Darstellung des aktuellen <see cref="T:System.Security.AccessControl.RawAcl" />-Objekts in Bytes ab.Diese Länge muss verwendet werden, bevor die ACL mithilfe der <see cref="M:System.Security.AccessControl.RawAcl.GetBinaryForm" />-Methode in ein binäres Array gemarshallt wird.</summary>
      <returns>Die Länge der binären Darstellung des aktuellen <see cref="T:System.Security.AccessControl.RawAcl" />-Objekts in Bytes.</returns>
    </member>
    <member name="P:System.Security.AccessControl.RawAcl.Count">
      <summary>Ruft die Anzahl der ACEs im aktuellen <see cref="T:System.Security.AccessControl.RawAcl" />-Objekt ab.</summary>
      <returns>Die Anzahl der ACEs im aktuellen <see cref="T:System.Security.AccessControl.RawAcl" />-Objekt.</returns>
    </member>
    <member name="M:System.Security.AccessControl.RawAcl.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Marshallt den Inhalt des <see cref="T:System.Security.AccessControl.RawAcl" />-Objekts in das angegebene Bytearray, wobei beim angegebenen Offset begonnen wird.</summary>
      <param name="binaryForm">Das Bytearray, in das der Inhalt von <see cref="T:System.Security.AccessControl.RawAcl" /> gemarshallt wird.</param>
      <param name="offset">Der Offset, bei dem das Marshallen begonnen werden soll.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> ist negativ oder zu hoch, um den gesamten <see cref="T:System.Security.AccessControl.RawAcl" /> in <paramref name="array" /> zu kopieren.</exception>
    </member>
    <member name="M:System.Security.AccessControl.RawAcl.InsertAce(System.Int32,System.Security.AccessControl.GenericAce)">
      <summary>Fügt den angegebenen ACE am angegebenen Index ein.</summary>
      <param name="index">Die Position, an der der neue ACE eingefügt werden soll.Geben Sie den Wert der <see cref="P:System.Security.AccessControl.RawAcl.Count" />-Eigenschaft an, wenn ein ACE am Ende des <see cref="T:System.Security.AccessControl.RawAcl" />-Objekts eingefügt werden soll.</param>
      <param name="ace">Der einzufügende ACE.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> ist negativ oder zu hoch, um den gesamten <see cref="T:System.Security.AccessControl.GenericAcl" /> in <paramref name="array" /> zu kopieren.</exception>
    </member>
    <member name="P:System.Security.AccessControl.RawAcl.Item(System.Int32)">
      <summary>Ruft den ACE am angegebenen Index ab oder legt diesen fest.</summary>
      <returns>Der ACE am angegebenen Index.</returns>
      <param name="index">Der nullbasierte Index des ACE, der abgerufen oder festgelegt werden soll.</param>
    </member>
    <member name="M:System.Security.AccessControl.RawAcl.RemoveAce(System.Int32)">
      <summary>Entfernt den ACE von der angegebenen Position.</summary>
      <param name="index">Der nullbasierte Index des zu entfernenden ACE.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Der Wert des <paramref name="index" />-Parameters ist größer als der Wert <see cref="P:System.Security.AccessControl.RawAcl.Count" />-Eigenschaft minus 1, oder er ist negativ.</exception>
    </member>
    <member name="P:System.Security.AccessControl.RawAcl.Revision">
      <summary>Ruft die Revisionsebene von <see cref="T:System.Security.AccessControl.RawAcl" /> ab.</summary>
      <returns>Ein Bytewert, der die Revisionsebene von <see cref="T:System.Security.AccessControl.RawAcl" /> angibt.</returns>
    </member>
    <member name="T:System.Security.AccessControl.RawSecurityDescriptor">
      <summary>Stellt eine Sicherheitsbeschreibung dar.Zu einer Sicherheitsbeschreibung gehören ein Besitzer, eine primäre Gruppe, eine DACL und eine SACL.</summary>
    </member>
    <member name="M:System.Security.AccessControl.RawSecurityDescriptor.#ctor(System.Byte[],System.Int32)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />-Klasse unter Verwendung des angegebenen Arrays von Bytewerten.</summary>
      <param name="binaryForm">Das Array von Bytewerten, aus dem das neue <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />-Objekt erstellt werden soll.</param>
      <param name="offset">Der Offset im <paramref name="binaryForm" />-Array, bei dem mit dem Kopieren begonnen werden soll.</param>
    </member>
    <member name="M:System.Security.AccessControl.RawSecurityDescriptor.#ctor(System.Security.AccessControl.ControlFlags,System.Security.Principal.SecurityIdentifier,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.RawAcl,System.Security.AccessControl.RawAcl)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />-Klasse mit den angegebenen Werten.</summary>
      <param name="flags">Flags, die das Verhalten des neuen <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />-Objekts angeben.</param>
      <param name="owner">Der Besitzer des neuen <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />-Objekts.</param>
      <param name="group">Die primäre Gruppe für das neue <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />-Objekt.</param>
      <param name="systemAcl">Die SACL für das neue <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />-Objekt.</param>
      <param name="discretionaryAcl">Die DACL (Discretionary Access Control List, freigegebene Zugriffssteuerungsliste) für das neue <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />-Objekt.</param>
    </member>
    <member name="M:System.Security.AccessControl.RawSecurityDescriptor.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />-Klasse mit der angegebenen SDDL-Zeichenfolge (Security Descriptor Definition Language).</summary>
      <param name="sddlForm">Die SDDL-Zeichenfolge, aus der das neue <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />-Objekt erstellt werden soll.</param>
    </member>
    <member name="P:System.Security.AccessControl.RawSecurityDescriptor.ControlFlags">
      <summary>Ruft Werte ab, die das Verhalten des <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />-Objekts angeben.</summary>
      <returns>Mindestens ein Wert der <see cref="T:System.Security.AccessControl.ControlFlags" />-Enumeration, kombiniert mit einem logischen Operator OR.</returns>
    </member>
    <member name="P:System.Security.AccessControl.RawSecurityDescriptor.DiscretionaryAcl">
      <summary>Ruft die DACL für dieses <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />-Objekt ab oder legt diese fest.Die DACL enthält Zugriffsregeln.</summary>
      <returns>Die DACL für dieses <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />-Objekt.</returns>
    </member>
    <member name="P:System.Security.AccessControl.RawSecurityDescriptor.Group">
      <summary>Ruft die primäre Gruppe für dieses <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />-Objekt ab oder legt diese fest.</summary>
      <returns>Die primäre Gruppe für dieses <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />-Objekt.</returns>
    </member>
    <member name="P:System.Security.AccessControl.RawSecurityDescriptor.Owner">
      <summary>Ruft den Besitzer des Objekts ab, dem dieses <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />-Objekt zugeordnet ist, oder legt diesen fest.</summary>
      <returns>Der Besitzer des Objekts, dem dieses <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />-Objekt zugeordnet ist.</returns>
    </member>
    <member name="P:System.Security.AccessControl.RawSecurityDescriptor.ResourceManagerControl">
      <summary>Ruft einen Bytewert ab, der die Steuerelementbits des Ressourcen-Managers darstellt, die diesem <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />-Objekt zugeordnet sind, oder legt diesen fest.</summary>
      <returns>Ein Bytewert, der die Steuerelementbits des Ressourcen-Managers darstellt, die diesem <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />-Objekt zugeordnet sind.</returns>
    </member>
    <member name="M:System.Security.AccessControl.RawSecurityDescriptor.SetFlags(System.Security.AccessControl.ControlFlags)">
      <summary>Legt die <see cref="P:System.Security.AccessControl.RawSecurityDescriptor.ControlFlags" />-Eigenschaft dieses <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />-Objekts auf den angegebenen Wert fest.</summary>
      <param name="flags">Mindestens ein Wert der <see cref="T:System.Security.AccessControl.ControlFlags" />-Enumeration, kombiniert mit einem logischen Operator OR.</param>
    </member>
    <member name="P:System.Security.AccessControl.RawSecurityDescriptor.SystemAcl">
      <summary>Ruft die SACL (System Access Control List, Systemzugriffssteuerungsliste) für dieses <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />-Objekt ab oder legt diese fest.Die SACL enthält Überwachungsregeln.</summary>
      <returns>Die SACL für dieses <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />-Objekt.</returns>
    </member>
    <member name="T:System.Security.AccessControl.ResourceType">
      <summary>Gibt die definierten systemeigenen Objekttypen an.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.DSObject">
      <summary>Ein Verzeichnisdienstobjekt (DS – Directory Service), ein Eigenschaftensatz oder eine einzelne Eigenschaft eines Verzeichnisdienstobjekts.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.DSObjectAll">
      <summary>Ein Verzeichnisdienstobjekt und alle seine Eigenschaftensätze und Eigenschaften.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.FileObject">
      <summary>Eine Datei oder ein Verzeichnis.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.KernelObject">
      <summary>Ein lokales Kernelobjekt.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.LMShare">
      <summary>Eine Netzwerkfreigabe.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.Printer">
      <summary>Ein Drucker.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.ProviderDefined">
      <summary>Ein von einem Anbieter definiertes Objekt.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.RegistryKey">
      <summary>Ein Registrierungsschlüssel.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.RegistryWow6432Key">
      <summary>Ein Objekt für einen Registrierungseintrag unter WOW64.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.Service">
      <summary>Ein Windows-Dienst.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.Unknown">
      <summary>Ein unbekannter Objekttyp.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.WindowObject">
      <summary>Ein Arbeitsstations- oder ein Desktopobjekt auf dem lokalen Computer.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.WmiGuidObject">
      <summary>Ein Windows-Verwaltungsinstrumentationsobjekt (WMI).</summary>
    </member>
    <member name="T:System.Security.AccessControl.SecurityInfos">
      <summary>Gibt den Abschnitt einer Sicherheitsbeschreibung an, die abgefragt oder festgelegt werden soll.</summary>
    </member>
    <member name="F:System.Security.AccessControl.SecurityInfos.DiscretionaryAcl">
      <summary>Gibt die DACL (Discretionary Access Control List) an.</summary>
    </member>
    <member name="F:System.Security.AccessControl.SecurityInfos.Group">
      <summary>Gibt den primären Gruppenbezeichner an.</summary>
    </member>
    <member name="F:System.Security.AccessControl.SecurityInfos.Owner">
      <summary>Gibt den Besitzerbezeichner an.</summary>
    </member>
    <member name="F:System.Security.AccessControl.SecurityInfos.SystemAcl">
      <summary>Gibt die SACL (System Access Control List) an.</summary>
    </member>
    <member name="T:System.Security.AccessControl.SystemAcl">
      <summary>Stellt eine Systemzugriffssteuerungsliste (SACL – System Access Control List) dar.</summary>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.#ctor(System.Boolean,System.Boolean,System.Byte,System.Int32)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.AccessControl.SystemAcl" />-Klasse mit den angegebenen Werten.</summary>
      <param name="isContainer">true, wenn das neue <see cref="T:System.Security.AccessControl.SystemAcl" />-Objekt ein Container ist.</param>
      <param name="isDS">true, wenn das neue <see cref="T:System.Security.AccessControl.SystemAcl" />-Objekt eine Zugriffssteuerungsliste (ACL) für ein Verzeichnisobjekt ist.</param>
      <param name="revision">Die Revisionsebene des neuen <see cref="T:System.Security.AccessControl.SystemAcl" />-Objekts.</param>
      <param name="capacity">Die Anzahl der möglichen Zugriffssteuerungseinträge (ACEs – Access Control Entries), die dieses <see cref="T:System.Security.AccessControl.SystemAcl" />-Objekt enthalten kann.Diese Zahl sollte nur als Anhaltspunkt verwendet werden.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.#ctor(System.Boolean,System.Boolean,System.Int32)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.AccessControl.SystemAcl" />-Klasse mit den angegebenen Werten.</summary>
      <param name="isContainer">true, wenn das neue <see cref="T:System.Security.AccessControl.SystemAcl" />-Objekt ein Container ist.</param>
      <param name="isDS">true, wenn das neue <see cref="T:System.Security.AccessControl.SystemAcl" />-Objekt eine Zugriffssteuerungsliste (ACL) für ein Verzeichnisobjekt ist.</param>
      <param name="capacity">Die Anzahl der möglichen Zugriffssteuerungseinträge (ACEs – Access Control Entries), die dieses <see cref="T:System.Security.AccessControl.SystemAcl" />-Objekt enthalten kann.Diese Zahl sollte nur als Anhaltspunkt verwendet werden.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.#ctor(System.Boolean,System.Boolean,System.Security.AccessControl.RawAcl)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.AccessControl.SystemAcl" />-Klasse mit den angegebenen Werten des angegebenen <see cref="T:System.Security.AccessControl.RawAcl" />-Objekts.</summary>
      <param name="isContainer">true, wenn das neue <see cref="T:System.Security.AccessControl.SystemAcl" />-Objekt ein Container ist.</param>
      <param name="isDS">true, wenn das neue <see cref="T:System.Security.AccessControl.SystemAcl" />-Objekt eine Zugriffssteuerungsliste (ACL) für ein Verzeichnisobjekt ist.</param>
      <param name="rawAcl">Das zugrunde liegende <see cref="T:System.Security.AccessControl.RawAcl" />-Objekt für das neue <see cref="T:System.Security.AccessControl.SystemAcl" />-Objekt.Geben Sie null an, um eine leere ACL zu erstellen.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.AddAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Fügt dem aktuellen <see cref="T:System.Security.AccessControl.SystemAcl" />-Objekt eine Überwachungsregel hinzu.</summary>
      <param name="auditFlags">Der Typ der hinzuzufügenden Überwachungsregel.</param>
      <param name="sid">Der <see cref="T:System.Security.Principal.SecurityIdentifier" />, für den eine Überwachungsregel hinzugefügt werden soll.</param>
      <param name="accessMask">Die Zugriffsmaske für die neue Überwachungsregel.</param>
      <param name="inheritanceFlags">Flags, die die Vererbungseigenschaften der neuen Überwachungsregel angeben.</param>
      <param name="propagationFlags">Flags, die die Weitergabeeigenschaften der Vererbung für die neue Überwachungsregel angeben.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.AddAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>Fügt dem aktuellen <see cref="T:System.Security.AccessControl.SystemAcl" />-Objekt eine Überwachungsregel mit den angegebenen Einstellungen hinzu.Verwenden Sie diese Methode für Zugriffssteuerungslisten (ACLs) für Verzeichnisobjekte, wenn Sie den Objekttyp oder den geerbten Objekttyp für die neue Überwachungsregel angeben.</summary>
      <param name="auditFlags">Der Typ der hinzuzufügenden Überwachungsregel.</param>
      <param name="sid">Der <see cref="T:System.Security.Principal.SecurityIdentifier" />, für den eine Überwachungsregel hinzugefügt werden soll.</param>
      <param name="accessMask">Die Zugriffsmaske für die neue Überwachungsregel.</param>
      <param name="inheritanceFlags">Flags, die die Vererbungseigenschaften der neuen Überwachungsregel angeben.</param>
      <param name="propagationFlags">Flags, die die Weitergabeeigenschaften der Vererbung für die neue Überwachungsregel angeben.</param>
      <param name="objectFlags">Flags, die angeben, ob der <paramref name="objectType" />-Parameter und der <paramref name="inheritedObjectType" />-Parameter Nicht-null-Werte enthalten.</param>
      <param name="objectType">Die Identität der Klasse von Objekten, für die die neue Überwachungsregel gilt.</param>
      <param name="inheritedObjectType">Die Identität der Klasse von untergeordneten Objekten, die die neue Überwachungsregel erben können.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.AddAudit(System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAuditRule)">
      <summary>Fügt dem aktuellen <see cref="T:System.Security.AccessControl.SystemAcl" />-Objekt eine Überwachungsregel hinzu.</summary>
      <param name="sid">Der <see cref="T:System.Security.Principal.SecurityIdentifier" />, für den eine Überwachungsregel hinzugefügt werden soll.</param>
      <param name="rule">Die <see cref="T:System.Security.AccessControl.ObjectAuditRule" />für die neue Überwachungsregel.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.RemoveAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Entfernt die angegebene Überwachungsregel aus dem aktuellen <see cref="T:System.Security.AccessControl.SystemAcl" />-Objekt.</summary>
      <returns>true, wenn diese Methode die angegebene Überwachungsregel erfolgreich entfernt hat, andernfalls false.</returns>
      <param name="auditFlags">Der Typ der zu entfernenden Überwachungsregel.</param>
      <param name="sid">Der <see cref="T:System.Security.Principal.SecurityIdentifier" />, für den eine Überwachungsregel entfernt werden soll.</param>
      <param name="accessMask">Die Zugriffsmaske für die Regel, die entfernt werden soll.</param>
      <param name="inheritanceFlags">Flags, die die Vererbungseigenschaften der Regel angeben, die entfernt werden sollen.</param>
      <param name="propagationFlags">Flags, die die Weitergabeeigenschaften der Vererbung für die Regel angeben, die entfernt werden sollen.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.RemoveAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>Entfernt die angegebene Überwachungsregel aus dem aktuellen <see cref="T:System.Security.AccessControl.SystemAcl" />-Objekt.Verwenden Sie diese Methode für ACLs für Verzeichnisobjekte, wenn Sie den Objekttyp oder den geerbten Objekttyp angeben.</summary>
      <returns>true, wenn diese Methode die angegebene Überwachungsregel erfolgreich entfernt hat, andernfalls false.</returns>
      <param name="auditFlags">Der Typ der zu entfernenden Überwachungsregel.</param>
      <param name="sid">Der <see cref="T:System.Security.Principal.SecurityIdentifier" />, für den eine Überwachungsregel entfernt werden soll.</param>
      <param name="accessMask">Die Zugriffsmaske für die Regel, die entfernt werden soll.</param>
      <param name="inheritanceFlags">Flags, die die Vererbungseigenschaften der Regel angeben, die entfernt werden sollen.</param>
      <param name="propagationFlags">Flags, die die Weitergabeeigenschaften der Vererbung für die Regel angeben, die entfernt werden sollen.</param>
      <param name="objectFlags">Flags, die angeben, ob der <paramref name="objectType" />-Parameter und der <paramref name="inheritedObjectType" />-Parameter Nicht-null-Werte enthalten.</param>
      <param name="objectType">Die Identität der Klasse von Objekten, für die die entfernte Überwachungssteuerungsregel gilt.</param>
      <param name="inheritedObjectType">Die Identität der Klasse von untergeordneten Objekten, die die entfernte Überwachungsregel erben können.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.RemoveAudit(System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAuditRule)">
      <summary>Entfernt die angegebene Überwachungsregel aus dem aktuellen <see cref="T:System.Security.AccessControl.SystemAcl" />-Objekt.</summary>
      <returns>true, wenn diese Methode die angegebene Überwachungsregel erfolgreich entfernt hat, andernfalls false.</returns>
      <param name="sid">Der <see cref="T:System.Security.Principal.SecurityIdentifier" />, für den eine Überwachungsregel entfernt werden soll.</param>
      <param name="rule">Der <see cref="T:System.Security.AccessControl.ObjectAuditRule" />, für den eine Überwachungsregel entfernt werden soll.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.RemoveAuditSpecific(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Entfernt die angegebene Überwachungsregel aus dem aktuellen <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />-Objekt.</summary>
      <param name="auditFlags">Der Typ der zu entfernenden Überwachungsregel.</param>
      <param name="sid">Der <see cref="T:System.Security.Principal.SecurityIdentifier" />, für den eine Überwachungsregel entfernt werden soll.</param>
      <param name="accessMask">Die Zugriffsmaske für die Regel, die entfernt werden soll.</param>
      <param name="inheritanceFlags">Flags, die die Vererbungseigenschaften der Regel angeben, die entfernt werden sollen.</param>
      <param name="propagationFlags">Flags, die die Weitergabeeigenschaften der Vererbung für die Regel angeben, die entfernt werden sollen.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.RemoveAuditSpecific(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>Entfernt die angegebene Überwachungsregel aus dem aktuellen <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />-Objekt.Verwenden Sie diese Methode für ACLs für Verzeichnisobjekte, wenn Sie den Objekttyp oder den geerbten Objekttyp angeben.</summary>
      <param name="auditFlags">Der Typ der zu entfernenden Überwachungsregel.</param>
      <param name="sid">Der <see cref="T:System.Security.Principal.SecurityIdentifier" />, für den eine Überwachungsregel entfernt werden soll.</param>
      <param name="accessMask">Die Zugriffsmaske für die Regel, die entfernt werden soll.</param>
      <param name="inheritanceFlags">Flags, die die Vererbungseigenschaften der Regel angeben, die entfernt werden sollen.</param>
      <param name="propagationFlags">Flags, die die Weitergabeeigenschaften der Vererbung für die Regel angeben, die entfernt werden sollen.</param>
      <param name="objectFlags">Flags, die angeben, ob der <paramref name="objectType" />-Parameter und der <paramref name="inheritedObjectType" />-Parameter Nicht-null-Werte enthalten.</param>
      <param name="objectType">Die Identität der Klasse von Objekten, für die die entfernte Überwachungssteuerungsregel gilt.</param>
      <param name="inheritedObjectType">Die Identität der Klasse von untergeordneten Objekten, die die entfernte Überwachungsregel erben können.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.RemoveAuditSpecific(System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAuditRule)">
      <summary>Entfernt die angegebene Überwachungsregel aus dem aktuellen <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />-Objekt.</summary>
      <param name="sid">Der <see cref="T:System.Security.Principal.SecurityIdentifier" />, für den eine Überwachungsregel entfernt werden soll.</param>
      <param name="rule">Die <see cref="T:System.Security.AccessControl.ObjectAuditRule" /> für die Regel entfernt werden soll.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.SetAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Legt die angegebene Überwachungsregel für das angegebene <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekt fest.</summary>
      <param name="auditFlags">Die festzulegende Überwachungsbedingung.</param>
      <param name="sid">Der <see cref="T:System.Security.Principal.SecurityIdentifier" />, für den eine Überwachungsregel festgelegt werden soll.</param>
      <param name="accessMask">Die Zugriffsmaske für die neue Überwachungsregel.</param>
      <param name="inheritanceFlags">Flags, die die Vererbungseigenschaften der neuen Überwachungsregel angeben.</param>
      <param name="propagationFlags">Flags, die die Weitergabeeigenschaften der Vererbung für die neue Überwachungsregel angeben.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.SetAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>Legt die angegebene Überwachungsregel für das angegebene <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekt fest.Verwenden Sie diese Methode für ACLs für Verzeichnisobjekte, wenn Sie den Objekttyp oder den geerbten Objekttyp angeben.</summary>
      <param name="auditFlags">Die festzulegende Überwachungsbedingung.</param>
      <param name="sid">Der <see cref="T:System.Security.Principal.SecurityIdentifier" />, für den eine Überwachungsregel festgelegt werden soll.</param>
      <param name="accessMask">Die Zugriffsmaske für die neue Überwachungsregel.</param>
      <param name="inheritanceFlags">Flags, die die Vererbungseigenschaften der neuen Überwachungsregel angeben.</param>
      <param name="propagationFlags">Flags, die die Weitergabeeigenschaften der Vererbung für die neue Überwachungsregel angeben.</param>
      <param name="objectFlags">Flags, die angeben, ob der <paramref name="objectType" />-Parameter und der <paramref name="inheritedObjectType" />-Parameter Nicht-null-Werte enthalten.</param>
      <param name="objectType">Die Identität der Klasse von Objekten, für die die neue Überwachungsregel gilt.</param>
      <param name="inheritedObjectType">Die Identität der Klasse von untergeordneten Objekten, die die neue Überwachungsregel erben können.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.SetAudit(System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAuditRule)">
      <summary>Legt die angegebene Überwachungsregel für das angegebene <see cref="T:System.Security.Principal.SecurityIdentifier" />-Objekt fest.</summary>
      <param name="sid">Der <see cref="T:System.Security.Principal.SecurityIdentifier" />, für den eine Überwachungsregel festgelegt werden soll.</param>
      <param name="rule">Der <see cref="T:System.Security.AccessControl.ObjectAuditRule" />, für den eine Überwachungsregel festgelegt werden soll.</param>
    </member>
  </members>
</doc>