﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using WeifenLuo.WinFormsUI;
using AssistPro.navtex;
using AssistPro.Lib;
using System.Windows.Forms;
using System.IO;
using static AssistPro.navtex.communication.CommPort;
using System.IO.Ports;

namespace AssistPro.navtex.communication
{
    public partial class Serial_Setting_Ini
    {
        private string GetPath()
        {
            string s = Application.StartupPath + "\\Setting";
            DirectoryInfo di = new DirectoryInfo(s);
            if(di.Exists == false)
            {
                di.Create();
            }

            return s;
        }
        private string GetFileName_Ini(int n)
        {
            string s = string.Format("\\Serial{0}.ini", n);

            return s;
        }

        public void StoreWrite(int n, Serial_Port_Info_c info)
        {
            string s = string.Format(GetPath() + GetFileName_Ini(n));

            IniFile ini = new IniFile(s);
            ini.WriteValue("Port", "PortName", info.PortName);
            ini.WriteValue("Port", "BaudRate", info.BaudRate);
            ini.WriteValue("Port", "DataBits", info.DataBits);
            ini.WriteValue("Port", "Parity", info.Parity.ToString());
            ini.WriteValue("Port", "StopBits", info.StopBits.ToString());
            ini.WriteValue("Port", "Handshake", info.Handshake.ToString());
        }

        public Serial_Port_Info_c StoreRead(int n)
        {
            Serial_Port_Info_c info = new Serial_Port_Info_c();
            string s = string.Format(GetPath() + GetFileName_Ini(n));

            IniFile ini = new IniFile(s);
            info.PortName   = ini.ReadValue("Port", "PortName", info.PortName);
            info.BaudRate   = ini.ReadValue("Port", "BaudRate", info.BaudRate);
            info.DataBits   = ini.ReadValue("Port", "DataBits", info.DataBits);
            info.Parity     = (Parity)Enum.Parse(typeof(Parity), ini.ReadValue("Port", "Parity", info.Parity.ToString()));
            info.StopBits   = (StopBits)Enum.Parse(typeof(StopBits), ini.ReadValue("Port", "StopBits", info.StopBits.ToString()));
            info.Handshake  = (Handshake)Enum.Parse(typeof(Handshake), ini.ReadValue("Port", "Handshake", info.Handshake.ToString()));

            return info;
        }
    }
}
