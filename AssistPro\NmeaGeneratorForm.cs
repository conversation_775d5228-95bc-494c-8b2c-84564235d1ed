﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO.Ports;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Net;
using System.Net.Sockets;
using WeifenLuo.WinFormsUI;

namespace AssistPro
{
    public partial class NmeaGeneratorForm : DockContent
    {
        private const char NMEA_END_CHAR_1 = '\n';
        private const int NMEA_MAX_LENGTH = 70;

        private SerialManager m_SerialManager;
        private SerialPort m_SerialPort;
        private byte m_hbt_tick;

        private UdpClient udpclient;
        IPEndPoint remoteEP;
        string m_tag_block;        
        public NmeaGeneratorForm()
        {
            InitializeComponent();

            cb_CommList.Enabled = true;
            reset_ack_sentence();
            reset_hbt_sentence();
            reset_acn_sentence();
            reset_alr_sentence();

            rb_61162_1_2.Checked = true;
        }

        private void btn_ConnectIO_Click(object sender, EventArgs e)
        {
            try
            {
                if (cb_CommList.Text == "")
                {
                    MessageBox.Show(" You need select port. ");
                    return;
                }

                if (cb_CommList.Enabled)
                {
                    m_SerialManager = new SerialManager(cb_CommList.Text, int.Parse(cb_baudrate.Text));

                    m_SerialPort = m_SerialManager.GetInstance();

                    m_SerialPort.Open();
                    btn_ConnectIO.Text = "DISCONNECT";
                    cb_CommList.Enabled = false;
                    gb_ack.Enabled = true;
                    gb_hbt.Enabled = true;
                    gb_acn.Enabled = true;
                    gb_alr.Enabled = true;
                    btn_ConnectIO.BackColor = Color.PaleGreen;
                }
                else
                {
                    m_SerialPort.Close();
                    btn_ConnectIO.Text = "CONNECT";
                    cb_CommList.Enabled = true;
                    if (ts_ip_connect.Text == "CONNECT")
                    {
                        reset_ack_sentence();
                        reset_hbt_sentence();
                        reset_acn_sentence();
                        reset_alr_sentence();
                    }
                    btn_ConnectIO.BackColor = Color.Silver;
                }
            }
            catch (Exception ea)
            {
                MessageBox.Show(ea.Message);
                cb_CommList.Enabled = true;
                btn_ConnectIO.BackColor = Color.Silver;
                btn_ConnectIO.Text = "CONNECT";
            }
        }

        private void Load_NmeaGenerator(object sender, EventArgs e)
        {
            foreach (string name in SerialPort.GetPortNames())
            {
                cb_CommList.Items.Add(name);
            }
        }

        private byte CalculateChecksum(string nmeaStr)
        {
            string n = nmeaStr.StartsWith("$") ? nmeaStr.Substring(1) : nmeaStr;

            byte chk = 0;
            int index = 0;

            while (index < n.Length && n[index] != '*' && n[index] != NMEA_END_CHAR_1)
            {
                if (index > NMEA_MAX_LENGTH)
                {
                    return 0; // Sentence too long
                }
                chk ^= (byte)n[index++];
            }

            return chk;
        }

        private void ComposeAckSentence()
        {
            int.TryParse(tb_alert_id.Text, out int id);
            string sentence = $"$aaACK,{id:D3}";
            byte checksum = CalculateChecksum(sentence);
            tb_ack_sentence.Text = $"{sentence}*{checksum:X2}\r\n";
        }

        private void tb_alert_id_keydown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                bool isValid = int.TryParse(tb_alert_id.Text, out int id);
                if (!isValid || id < 0 || id > 999)
                {
                    MessageBox.Show("Alert ID : 0 <= value <= 999 is range, entering value is wrong", "Input Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    tb_alert_id.Clear();
                    tb_alert_id.Focus();
                    reset_ack_sentence();
                    gb_ack.Enabled = true;
                }
                else
                {
                    ComposeAckSentence();
                }
                e.Handled = true;
                e.SuppressKeyPress = true;
            }
        }

        private void bt_send_ack_Click(object sender, EventArgs e)
        {
            if (rb_61162_1_2.Checked)
            {
                if (m_SerialPort.IsOpen)
                {
                    m_SerialPort.Write(Encoding.ASCII.GetBytes(tb_ack_sentence.Text), 0, tb_ack_sentence.Text.Length);
                }
            }
            else
            {
                string sentence = ComposeTagblock() + tb_ack_sentence.Text;
                byte[] buffer = Encoding.ASCII.GetBytes(sentence);
                udpclient.Send(buffer, buffer.Length, remoteEP);
            }
        }

        private void reset_ack_sentence()
        {
            gb_ack.Enabled = false;            
            tb_alert_id.Text = "1";
            ComposeAckSentence();
        }

        private void reset_hbt_sentence()
        {
            gb_hbt.Enabled = false;
            tb_interval.Text = "1";
            tb_hbt_sentence.Text = "";
            bt_run.Text = "RUN";
            timer_hbt.Stop();
            m_hbt_tick = 0;
        }

        private void reset_acn_sentence()
        {
            gb_acn.Enabled = false;
            tb_acn_alert_id.Text = "1";
            tb_acn_alert_inst.Text = "1";
            ComposeAcnSentence();
        }

        private void reset_alr_sentence()
        {
            gb_alr.Enabled = false;
            tb_alr_alert_id.Text = "1";
            tb_alr_desc.Text = "RX:FAILURE";
            ComposeAlrSentence();
        }

        private void tb_inteval_keydown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                bool isValid = float.TryParse(tb_interval.Text, out float interval);
                if (!isValid || interval < 0.0 || interval > 9999999.0)
                {
                    MessageBox.Show("Interval : 0 <= value <= 9999999 is range, entering value is wrong", "Input Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    tb_interval.Clear();
                    tb_interval.Focus();   
                    reset_hbt_sentence();
                    gb_hbt.Enabled = true;
                }
                else
                {
                    timer_hbt.Interval = (int)(interval * 1000);                    
                }
                e.Handled = true;
                e.SuppressKeyPress = true;
            }
        }

        private void bt_run_Click(object sender, EventArgs e)
        {
            if (bt_run.Text == "RUN")
            {
                bt_run.Text = "STOP";
                float interval;
                if (float.TryParse(tb_interval.Text, out interval))
                {
                    timer_hbt.Start();
                }
            }
            else
            {
                bt_run.Text = "RUN";
                timer_hbt.Stop();
            }
        }

        private void hbt_timer_tick(object sender, EventArgs e)
        {
            if (rb_61162_1_2.Checked)
            {
                if (m_SerialPort.IsOpen)
                {
                    m_hbt_tick++;
                    if (m_hbt_tick == 10)
                    {
                        m_hbt_tick = 0;
                    }
                    string sentence = $"$aaHBT,{tb_interval.Text:F1},A,{m_hbt_tick}";
                    byte checksum = CalculateChecksum(sentence);
                    tb_hbt_sentence.Text = $"{sentence}*{checksum:X2}\r\n";
                    m_SerialPort.Write(Encoding.ASCII.GetBytes(tb_hbt_sentence.Text), 0, tb_hbt_sentence.Text.Length);
                }
            }
            else
            {
                m_hbt_tick++;
                if (m_hbt_tick == 10)
                {
                    m_hbt_tick = 0;
                }
                string sentence = $"$aaHBT,{tb_interval.Text:F1},A,{m_hbt_tick}";
                string full_sentence = ComposeTagblock() + tb_hbt_sentence.Text;
                byte[] buffer = Encoding.ASCII.GetBytes(full_sentence);
                udpclient.Send(buffer, buffer.Length, remoteEP);
            }
        }

        private string getAcnCommand()
        {
            if (cb_cmd.SelectedIndex == 0)
            {
                return "A";
            }
            else if (cb_cmd.SelectedIndex == 1)
            {
                return "Q";
            }
            else if (cb_cmd.SelectedIndex == 2)
            {
                return "O";
            }
            else if (cb_cmd.SelectedIndex == 3)
            {
                return "S";
            }
            return "";
        }

        private string getAlrAlarmContition()
        {
            if (cb_alr_status.SelectedIndex == 0)
            {
                return "A"; // threshold exceeded
            }
            else if (cb_alr_status.SelectedIndex == 1)
            {
                return "V"; // not exceeded
            }
            return "";
        }

        private string getAlrAlarmAckState()
        {
            if (cb_alr_ack.SelectedIndex == 0)
            {
                return "A"; // Acknowledged
            }
            else if (cb_alr_ack.SelectedIndex == 1)
            {
                return "V"; // Unacknowledged
            }
            return "";
        }

        private string ComposeTagblock()
        {
            if (rb_61162_1_2.Checked == false)
            {
                string header = "UdPbC" + "\0";
                string tag_body_src = string.IsNullOrEmpty(tb_450_src.Text) ? "s:" : "s:" + tb_450_src.Text;
                string tag_body_dst = string.IsNullOrEmpty(tb_450_dst.Text) ? "d:" : "d:" + tb_450_dst.Text;
                string tag_body = tag_body_src + "," + tag_body_dst;
                byte checksum = CalculateChecksum(tag_body);
                m_tag_block = header+ "\\" + tag_body + "*" + checksum.ToString("X2") +"\\";
                return m_tag_block;
            }
            return "";
        }

        private void ComposeAcnSentence()
        {
            int.TryParse(tb_acn_alert_id.Text, out int id);
            int.TryParse(tb_acn_alert_inst.Text, out int inst);
            string sentence = $"$aaACN,{DateTime.Now:hhmmss.ss},INT,{id:D7},{inst:D6},{getAcnCommand()},C";
            byte checksum = CalculateChecksum(sentence);
            tb_acn_sentence.Text = $"{sentence}*{checksum:X2}\r\n";
        }
    
        private void tb_acn_alert_id_keydown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                bool isValid = int.TryParse(tb_acn_alert_id.Text, out int id);
                int.TryParse(tb_acn_alert_inst.Text, out int inst);                
                if (!isValid || id < 1 || id > 9999999)
                {
                    MessageBox.Show("Alert ID : 1 <= value <= 9999999 is range, entering value is wrong", "Input Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    tb_acn_alert_id.Text = "1";
                    tb_acn_alert_id.Clear();
                    tb_acn_alert_id.Focus();
                    reset_acn_sentence();
                    gb_acn.Enabled = true;
                }
                else
                {
                    ComposeAcnSentence();
                }
                e.Handled = true;
                e.SuppressKeyPress = true;
            }
        }

        private void tb_acn_alert_inst_keydown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                int.TryParse(tb_acn_alert_id.Text, out int id);
                bool isValid = int.TryParse(tb_acn_alert_inst.Text, out int inst);
                if (!isValid || inst < 1 || inst > 999999)
                {
                    MessageBox.Show("Alert ID : 1 <= value <= 999999 is range, entering value is wrong", "Input Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    tb_acn_alert_inst.Text = "1";
                    tb_acn_alert_inst.Clear();
                    tb_acn_alert_inst.Focus();
                    reset_acn_sentence();
                    gb_acn.Enabled = true;
                }
                else
                {
                    ComposeAcnSentence();
                }
                e.Handled = true;
                e.SuppressKeyPress = true;
            }
        }

        private void bt_acn_send_Click(object sender, EventArgs e)
        {
             if (rb_61162_1_2.Checked)
            {            
                if (m_SerialPort.IsOpen)
                {
                    m_SerialPort.Write(Encoding.ASCII.GetBytes(tb_acn_sentence.Text), 0, tb_acn_sentence.Text.Length);
                }
            }
            else
            {
                string sentence = ComposeTagblock() + tb_acn_sentence.Text;
                byte[] buffer = Encoding.ASCII.GetBytes(sentence);
                udpclient.Send(buffer, buffer.Length, remoteEP);
            }
        }

        private void Acn_Cb_SelectedIndexChanged(object sender, EventArgs e)
        {
            ComposeAcnSentence();
        }

        private void ComposeAlrSentence()
        {
            int.TryParse(tb_alr_alert_id.Text, out int id);
            string sentence = $"$aaALR,{DateTime.Now:hhmmss.ss},{id:D3},{getAlrAlarmContition()},{getAlrAlarmAckState()},{tb_alr_desc.Text}";
            byte checksum = CalculateChecksum(sentence);
            tb_alr_sentence.Text = $"{sentence}*{checksum:X2}\r\n";
        }

        private void tb_alr_alert_id_keydown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                bool isValid = int.TryParse(tb_alr_alert_id.Text, out int id);
                if (!isValid || id < 0 || id > 999)
                {
                    MessageBox.Show("Alert ID : 0 <= value <= 999 is range, entering value is wrong", "Input Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    tb_alr_alert_id.Clear();
                    tb_alr_alert_id.Focus();
                    reset_alr_sentence();
                    gb_alr.Enabled = true;
                }
                else
                {
                    ComposeAlrSentence();
                }
                e.Handled = true;
                e.SuppressKeyPress = true;
            }
        }

        private void Alr_ack_SelectedIndexChanged(object sender, EventArgs e)
        {
            ComposeAlrSentence();
        }

        private void Alr_status_SelectedIndexChanged(object sender, EventArgs e)
        {
            ComposeAlrSentence();
        }

        private void bt_alr_send_Click(object sender, EventArgs e)
        {
             if (rb_61162_1_2.Checked)
            {            
                if (m_SerialPort.IsOpen)
                {
                    m_SerialPort.Write(Encoding.ASCII.GetBytes(tb_alr_sentence.Text), 0, tb_alr_sentence.Text.Length);
                }
            }
            else
            {
                string sentence = ComposeTagblock() + tb_alr_sentence.Text;
                byte[] buffer = Encoding.ASCII.GetBytes(sentence);
                udpclient.Send(buffer, buffer.Length, remoteEP);
            }
        }

        private void toolStripButton1_Click(object sender, EventArgs e)
        {
            try
            {
                if (ts_ip_addr.Text == "")
                {
                    MessageBox.Show(" Please enter IP address. ");
                    return;
                }

                if (ts_port.Text == "")
                {
                    MessageBox.Show(" Please enter port number. ");
                    return;
                }

                if (ts_ip_connect.Text == "CONNECT")
                {
                    udpclient = new UdpClient();
                    IPAddress multicastaddress = IPAddress.Parse(ts_ip_addr.Text);
                    udpclient.JoinMulticastGroup(multicastaddress);
                    remoteEP = new IPEndPoint(multicastaddress, int.Parse(ts_port.Text));
                    ts_ip_connect.Text = "DISCONNECT";
                    gb_ack.Enabled = true;
                    gb_hbt.Enabled = true;
                    gb_acn.Enabled = true;
                    gb_alr.Enabled = true;
                    ts_ip_connect.BackColor = Color.PaleGreen;
                }
                else
                {
                    udpclient.Close();
                    ts_ip_connect.Text = "CONNECT";
                    if (btn_ConnectIO.Text == "CONNECT")
                    {
                        reset_ack_sentence();
                        reset_hbt_sentence();
                        reset_acn_sentence();
                        reset_alr_sentence();
                    }
                    ts_ip_connect.BackColor = Color.Silver;
                }
            }
            catch (Exception ea)
            {
                MessageBox.Show(ea.Message);
                ts_ip_connect.BackColor = Color.Silver;
                ts_ip_connect.Text = "CONNECT";
            }
        }

        private void BtnIPConnect_Click(object sender, EventArgs e)
        {
            try
            {
                if (ts_ip_addr.Text == "")
                {
                    MessageBox.Show(" Please enter IP address. ");
                    return;
                }

                if (ts_port.Text == "")
                {
                    MessageBox.Show(" Please enter port number. ");
                    return;
                }

                if (ts_ip_connect.Text == "CONNECT")
                {
                    udpclient = new UdpClient();
                    IPAddress multicastaddress = IPAddress.Parse(ts_ip_addr.Text);
                    udpclient.JoinMulticastGroup(multicastaddress);
                    remoteEP = new IPEndPoint(multicastaddress, int.Parse(ts_port.Text));
                    ts_ip_connect.Text = "DISCONNECT";
                    gb_ack.Enabled = true;
                    gb_hbt.Enabled = true;
                    gb_acn.Enabled = true;
                    gb_alr.Enabled = true;
                    ts_ip_connect.BackColor = Color.PaleGreen;
                }
                else
                {
                    udpclient.Close();
                    ts_ip_connect.Text = "CONNECT";
                    if (btn_ConnectIO.Text == "CONNECT")
                    {
                        reset_ack_sentence();
                        reset_hbt_sentence();
                        reset_acn_sentence();
                        reset_alr_sentence();
                    }
                    ts_ip_connect.BackColor = Color.Silver;
                }
            }
            catch (Exception ea)
            {
                MessageBox.Show(ea.Message);
                ts_ip_connect.BackColor = Color.Silver;
                ts_ip_connect.Text = "CONNECT";
            }
        }
    }
}
