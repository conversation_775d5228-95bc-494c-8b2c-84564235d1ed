﻿using AssistPro.Lib;
using AssistPro.navtex.communication;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using WeifenLuo.WinFormsUI;
using static AssistPro.navtex.communication.CommPort;

namespace AssistPro.navtex.sentence
{
    public partial class Sentence_Create_Form : DockContent
    {
        static readonly Sentence_Create_Form instance = new Sentence_Create_Form();

        private CommPort m_CommPort = CommPort.Instance;
        private CommProcess m_CommProc = CommProcess.Instance;
        private Common m_Common = Common.Instance;
        private UdpPort m_UdpPort = UdpPort.Instance;

        private int ins_repeat_run;
        private int bam_repeat_run;

        public static Sentence_Create_Form Instance
        {
            get
            {
                return instance;
            }
        }
        
        public Sentence_Create_Form()
        {
            InitializeComponent();

            rb_61162_1_2.Checked = true;
            tb_450_line_count.Text = string.Format("1");
        }

        private byte CalcurateChecksum(string nmeaStr)
        {
            string n = nmeaStr.StartsWith("$") ? nmeaStr.Substring(1) : nmeaStr;

            byte chk = 0;
            int index = 0;

            while ((index < n.Length) && (n[index] != '*') && (n[index] != '\n'))
            {
                if (index > 70)
                {
                    // Sentence too long
                    return 0;
                }
                chk ^= (byte)n[index++];
            }
            return chk;
        }

        private string ComposeTagblock()
        {
            string tag_body = string.Empty;

            if (rb_61162_1_2.Checked == false)
            {
                string header = "UdPbC" + "\0";

                if (cb_s_enable.Checked == true)
                {
                    string tag_body_src = string.IsNullOrEmpty(tb_450_src.Text) ? ("s:") : ("s:" + tb_450_src.Text);
                    tag_body += tag_body_src;
                }

                if (cb_d_enable.Checked == true)
                {
                    string tag_body_dst = string.IsNullOrEmpty(tb_450_dst.Text) ? ("d:") : ("d:" + tb_450_dst.Text);
                    if (tag_body == "")
                    {
                        tag_body += tag_body_dst;
                    }
                    else
                    {
                        tag_body += "," + tag_body_dst;
                    }
                }

                if (cb_n_enable.Checked == true)
                {
                    int n;
                    try
                    {
                        n = int.Parse(tb_450_line_count.Text);
                    }
                    catch (Exception e)
                    {
                        tb_450_line_count.Text = "1";
                        n = 1;
                    }

                    string tag_body_line_count = string.Format("n:{0}", n);
                    if (tag_body == "")
                    {
                        tag_body += tag_body_line_count;
                    }
                    else
                    {
                        tag_body += "," + tag_body_line_count;
                    }

                    tb_450_line_count.Text = string.Format("{0}", n + 1);
                }

                byte checksum = CalcurateChecksum(tag_body);
                string m_tag_block = header + "\\" + tag_body + "*" + checksum.ToString("X2") + "\\";

                return m_tag_block;
            }

            return "";
        }

        private void m_INS_send_Click(object sender, EventArgs e)
        {
            string msg = string.Empty;
            string send_msg = string.Empty;

            try
            {
                msg = string.Format("{0}", m_INS_sentence.Text);
                byte checksum = m_Common.check_sum(msg.Substring(1));
                msg += string.Format("*{0:X2}\r\n", checksum);

                if (rb_61162_1_2.Checked)
                {
                    send_msg = msg;

                    m_CommPort.StrSend(send_msg, (int)Serial_t.SERIAL_TYPE_INS);
                    m_monitor.AppendText(string.Format("INS Send : ") + msg);
                }
                else
                {
                    send_msg = ComposeTagblock() + msg;
                    m_UdpPort.StrSend(send_msg);

                    send_msg = send_msg.Replace("\0", "\\0");
                    m_monitor.AppendText(string.Format("INS Send : ") + send_msg);
                }
            }
            catch (Exception ex)
            {

            }
        }

        private void m_BAM_send_Click(object sender, EventArgs e)
        {
            string msg = string.Empty;
            string send_msg = string.Empty;

            try
            {
                msg = string.Format("{0}", m_BAM_sentence.Text);
                byte checksum = m_Common.check_sum(msg.Substring(1));
                msg += string.Format("*{0:X2}\r\n", checksum);

                if (rb_61162_1_2.Checked)
                {
                    send_msg = msg;

                    m_CommPort.StrSend(send_msg, (int)Serial_t.SERIAL_TYPE_BAM);
                    m_monitor.AppendText(string.Format("BAM Send : ") + msg);
                }
                else
                {
                    send_msg = ComposeTagblock() + msg;
                    m_UdpPort.StrSend(send_msg);

                    send_msg = send_msg.Replace("\0", "\\0");
                    m_monitor.AppendText(string.Format("BAM Send : ") + send_msg);
                }
            }
            catch (Exception ex)
            {

            }
        }

        private void m_INS_Repeat_Click(object sender, EventArgs e)
        {
            if(ins_repeat_run == 0)
            {
                ins_repeat_run = 1;
                ins_timer.Start();
                try
                {
                    ins_timer.Interval = int.Parse(m_INS_timer_msec.Text);
                }
                catch (Exception ex)
                {
                    ins_timer.Interval = 100;
                    m_INS_timer_msec.Text = "100";
                }
                m_INS_Repeat.BackColor = Color.LightGreen;
            }
            else
            {
                ins_repeat_run = 0;
                ins_timer.Stop();
                m_INS_Repeat.BackColor = Color.White;
            }
        }

        private void m_BAM_Repeat_Click(object sender, EventArgs e)
        {
            if (bam_repeat_run == 0)
            {
                bam_repeat_run = 1;
                bam_timer.Start();
                try
                {
                    bam_timer.Interval = int.Parse(m_BAM_timer_msec.Text);
                }
                catch (Exception ex)
                {
                    bam_timer.Interval = 100;
                    m_BAM_timer_msec.Text = "100";
                }
                m_BAM_Repeat.BackColor = Color.LightGreen;
            }
            else
            {
                bam_repeat_run = 0;
                bam_timer.Stop();
                m_BAM_Repeat.BackColor = Color.White;
            }
        }

        private void ins_timer_tick_handle(object sender, EventArgs e)
        {
            string msg = string.Empty;
            string send_msg = string.Empty;

            try
            {
                msg = string.Format("{0}", m_INS_sentence.Text);
                byte checksum = m_Common.check_sum(msg.Substring(1));
                msg += string.Format("*{0:X2}\r\n", checksum);

                if (rb_61162_1_2.Checked)
                {
                    send_msg = msg;

                    m_CommPort.StrSend(send_msg, (int)Serial_t.SERIAL_TYPE_INS);
                    m_monitor.AppendText(string.Format("INS Send : ") + msg);
                }
                else
                {
                    send_msg = ComposeTagblock() + msg;
                    m_UdpPort.StrSend(send_msg);

                    send_msg = send_msg.Replace("\0", "\\0");
                    m_monitor.AppendText(string.Format("INS Send : ") + send_msg);
                }
            }
            catch (Exception ex)
            {

            }
        }

        private void bam_timer_tick_handle(object sender, EventArgs e)
        {
            string msg = string.Empty;
            string send_msg = string.Empty;

            try
            {
                msg = string.Format("{0}", m_BAM_sentence.Text);
                byte checksum = m_Common.check_sum(msg.Substring(1));
                msg += string.Format("*{0:X2}\r\n", checksum);

                if (rb_61162_1_2.Checked)
                {
                    send_msg = msg;

                    m_CommPort.StrSend(send_msg, (int)Serial_t.SERIAL_TYPE_BAM);
                    m_monitor.AppendText(string.Format("BAM Send : ") + msg);
                }
                else
                {
                    send_msg = ComposeTagblock() + msg;
                    m_UdpPort.StrSend(send_msg);

                    send_msg = send_msg.Replace("\0", "\\0");
                    m_monitor.AppendText(string.Format("BAM Send : ") + send_msg);
                }
            }
            catch (Exception ex)
            {

            }
        }
    }
}
