﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AssistPro.Lib
{
    public sealed class Common
    {
        static readonly Common instance = new Common();

        static Common()
        {

        }

        Common()
        {

        }
        public static Common Instance
        {
            get
            {
                return instance;
            }
        }

        public ushort crc16_calc(ushort init_crc, byte[] data, int len)
        {
            ushort crc = init_crc;

            for (ushort i = 1; i <= len; i++)
            {
                crc = (ushort)((crc >> 8) | (crc << 8));
                crc ^= (ushort)data[i];
                crc ^= (ushort)((crc & 0x00FF) >> 4);
                crc ^= (ushort)((crc << 8) << 4);
                crc ^= (ushort)(((crc & 0x00FF) << 4) << 1);
            }
            return crc;
        }

        public ushort nav_crc16_calc(ushort init_crc, byte[] data, int len)
        {
            ushort crc = init_crc;

            for (ushort i = 4; i <= (len + 3); i++)
            {
                crc = (ushort)((crc >> 8) | (crc << 8));
                crc ^= (ushort)data[i];
                crc ^= (ushort)((crc & 0x00FF) >> 4);
                crc ^= (ushort)((crc << 8) << 4);
                crc ^= (ushort)(((crc & 0x00FF) << 4) << 1);
            }
            return crc;
        }

        public byte check_sum(string str)
        {
            byte checksum = 0;
            foreach (char c in str)
            {
                if (c != '*' && c != 0x0D && c != '\0')
                {
                    checksum ^= (byte)c;
                }
            }
            return checksum;
        }
    }
}
