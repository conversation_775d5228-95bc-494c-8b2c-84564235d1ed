﻿using AssistPro.navtex.communication;
using AssistPro.navtex.monitor;
using AssistPro.navtex.msg;
using AssistPro.navtex.sentence;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Net.Sockets;
using System.Net;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using WeifenLuo.WinFormsUI;
using static AssistPro.navtex.communication.CommPort;

namespace AssistPro.navtex
{
    public partial class IN3000R_Form : DockContent
    {
        public static IN3000R_Form _in3000r_form;
        private static IN3000R_Form instance;
        private Nav_Msg_Form _Nav_Msg_Form;
        private Nmea_Monitor_Form _Nmea_Monitor_Form;
        private Interface_Monitor_Form _Interface_Monitor_Form;
        private Sentence_CRQ_Form _Sentence_CRQ_Form;
        private Sentence_NRM_Form _Sentence_NRM_Form;
        private Sentence_BAM_Form _Sentence_BAM_Form;
        private Sentence_Create_Form _Sentence_Create_Form;

        private CommPort m_CommPort = CommPort.Instance;
        private CommProcess m_CommProc = CommProcess.Instance;
        private UdpPort m_UdpPort = UdpPort.Instance;
        public static IN3000R_Form Instance
        {
            get
            {
                return instance;
            }
        }
        public IN3000R_Form()
        {
            InitializeComponent();

            _in3000r_form = this;
            instance = this;

            this.Load += Message_Form_Load;
            this.Load += Nmea_Monitor_Form_Load;
            this.Load += Interface_Monitor_Form_Load;
            this.Load += Sentence_CRQ_Form_Load;
            this.Load += Sentence_NRM_Form_Load;
            this.Load += Sentence_BAM_Form_Load;
            this.Load += Sentence_Create_Form_Load;
            this.Load += Load_End;

            // bugfix '25.02.14
            // Opening the serial port simultaneously with loading causes a freeze
            /*
            string str = string.Empty;
            int n = 0;
            Serial_Port_Info_c info = new Serial_Port_Info_c();
            Serial_Setting_Ini ini = new Serial_Setting_Ini();

            n = (int)Serial_t.SERIAL_TYPE_MAIN;
            info = ini.StoreRead(n);
            m_CommPort.Open(n, info);
            str = m_CommPort.IsStatus(n);
            toolStripTextBox_Main.Text = str;
            if (str == "Close")
            {
                toolStripTextBox_Main.BackColor = Color.FromArgb(255, 0, 0);
            }
            else
            {
                toolStripTextBox_Main.BackColor = Color.FromArgb(0, 255, 0);
            }

            n = (int)Serial_t.SERIAL_TYPE_INS;
            info = ini.StoreRead(n);
            m_CommPort.Open(n, info);
            str = m_CommPort.IsStatus(n);
            toolStripTextBox_INS.Text = str;
            if (str == "Close")
            {
                toolStripTextBox_INS.BackColor = Color.FromArgb(255, 0, 0);
            }
            else
            {
                toolStripTextBox_INS.BackColor = Color.FromArgb(0, 255, 0);
            }

            n = (int)Serial_t.SERIAL_TYPE_BAM;
            info = ini.StoreRead(n);
            m_CommPort.Open(n, info);
            str = m_CommPort.IsStatus(n);
            toolStripTextBox_BAM.Text = str;
            if (str == "Close")
            {
                toolStripTextBox_BAM.BackColor = Color.FromArgb(255, 0, 0);
            }
            else
            {
                toolStripTextBox_BAM.BackColor = Color.FromArgb(0, 255, 0);
            }
            */
        }

        private void Message_Form_Load(object sender, EventArgs e)
        {
            _Nav_Msg_Form = new Nav_Msg_Form();

            _Nav_Msg_Form.TopLevel = false;
            tabControl.TabPages[0].Controls.Add(_Nav_Msg_Form);
            tabControl.SelectedIndex = 0;
            _Nav_Msg_Form.WindowState = System.Windows.Forms.FormWindowState.Maximized;
            _Nav_Msg_Form.Show();
        }

        private void Nmea_Monitor_Form_Load(object sender, EventArgs e)
        {
            _Nmea_Monitor_Form = new Nmea_Monitor_Form();

            _Nmea_Monitor_Form.TopLevel = false;
            tabControl.TabPages[1].Controls.Add(_Nmea_Monitor_Form);
            tabControl.SelectedIndex = 1;
            _Nmea_Monitor_Form.WindowState = System.Windows.Forms.FormWindowState.Maximized;
            _Nmea_Monitor_Form.Show();
        }

        private void Interface_Monitor_Form_Load(object sender, EventArgs e)
        {
            _Interface_Monitor_Form = new Interface_Monitor_Form();

            _Interface_Monitor_Form.TopLevel = false;
            tabControl.TabPages[2].Controls.Add(_Interface_Monitor_Form);
            tabControl.SelectedIndex = 2;
            _Interface_Monitor_Form.WindowState = System.Windows.Forms.FormWindowState.Maximized;
            _Interface_Monitor_Form.Show();
        }

        private void Sentence_CRQ_Form_Load(object sender, EventArgs e)
        {
            _Sentence_CRQ_Form = new Sentence_CRQ_Form();

            _Sentence_CRQ_Form.TopLevel = false;
            tabControl.TabPages[3].Controls.Add(_Sentence_CRQ_Form);
            tabControl.SelectedIndex = 3;
            _Sentence_CRQ_Form.WindowState = System.Windows.Forms.FormWindowState.Maximized;
            _Sentence_CRQ_Form.Show();
        }

        private void Sentence_NRM_Form_Load(object sender, EventArgs e)
        {
            _Sentence_NRM_Form = new Sentence_NRM_Form();

            _Sentence_NRM_Form.TopLevel = false;
            tabControl.TabPages[4].Controls.Add(_Sentence_NRM_Form);
            tabControl.SelectedIndex = 4;
            _Sentence_NRM_Form.WindowState = System.Windows.Forms.FormWindowState.Maximized;
            _Sentence_NRM_Form.Show();
        }

        private void Sentence_BAM_Form_Load(object sender, EventArgs e)
        {
            _Sentence_BAM_Form = new Sentence_BAM_Form();

            _Sentence_BAM_Form.TopLevel = false;
            tabControl.TabPages[5].Controls.Add(_Sentence_BAM_Form);
            tabControl.SelectedIndex = 5;
            _Sentence_BAM_Form.WindowState = System.Windows.Forms.FormWindowState.Maximized;
            _Sentence_BAM_Form.Show();
        }

        private void Sentence_Create_Form_Load(object sender, EventArgs e)
        {
            _Sentence_Create_Form = new Sentence_Create_Form();

            _Sentence_Create_Form.TopLevel = false;
            tabControl.TabPages[6].Controls.Add(_Sentence_Create_Form);
            tabControl.SelectedIndex = 6;
            _Sentence_Create_Form.WindowState = System.Windows.Forms.FormWindowState.Maximized;
            _Sentence_Create_Form.Show();
        }

        private void Load_End(object sender, EventArgs e)
        {
            tabControl.SelectedIndex = 0;
            tabControl.TabPages[0].Show();
        }

        // delegate used for Invoke
        internal delegate void StringDelegate(string data, int n);
        internal delegate void BytesDelegate(byte[] dataIn);
        public void FSK_Serial_On_Status_Changed(string status, int n)
        {
            // Handle multi-threading
            if(this.IsHandleCreated && !this.IsDisposed)
            {
                if (InvokeRequired)
                {
                    Invoke(new StringDelegate(FSK_Serial_On_Status_Changed), new object[] { status, n });
                    return;
                }

                if (n == (int)Serial_t.SERIAL_TYPE_MAIN)
                {
                    //toolStripTextBox_FSK_Status.Text = status;
                }
                else if (n == (int)Serial_t.SERIAL_TYPE_INS)
                {
                      //toolStripTextBox_INS.Text = status;
                }
                else if (n == (int)Serial_t.SERIAL_TYPE_BAM)
                {
                     //toolStripTextBox_BAM.Text = status;
                }
                else if (n == (int)Serial_t.SERIAL_TYPE_MON_1)
                {
                     //toolStripTextBox_Mon_1.Text = status;
                }
                else if (n == (int)Serial_t.SERIAL_TYPE_MON_2)
                {
                    //toolStripTextBox_Mon_2.Text = status;
                }
                else if (n == (int)Serial_t.SERIAL_TYPE_MON_3)
                {
                    //toolStripTextBox_Mon_3.Text = status;
                }
            }
        }

        private void toolStripButton_FSK_Connection_Click(object sender, EventArgs e)
        {
            if (m_CommPort.IsOpen((int)Serial_t.SERIAL_TYPE_MAIN))
            {
                m_CommPort.Close((int)Serial_t.SERIAL_TYPE_MAIN);

                string str = m_CommPort.IsStatus((int)Serial_t.SERIAL_TYPE_MAIN);
                toolStripTextBox_Main.Text = str;
                if (str == "Close")
                {
                    toolStripTextBox_Main.BackColor = Color.FromArgb(255, 0, 0);
                }
            }
            else
            {
                Serial_Setting_Form m_serial_setting_form = new Serial_Setting_Form((int)Serial_t.SERIAL_TYPE_MAIN);
                m_serial_setting_form.Show();
            }
        }

        private void toolStripButton_Connection_INS_Click(object sender, EventArgs e)
        {
            if(m_CommPort.IsOpen((int)Serial_t.SERIAL_TYPE_INS))
            {
                m_CommPort.Close((int)Serial_t.SERIAL_TYPE_INS);

                string str = m_CommPort.IsStatus((int)Serial_t.SERIAL_TYPE_INS);
                toolStripTextBox_INS.Text = str;
                if(str == "Close")
                {
                    toolStripTextBox_INS.BackColor = Color.FromArgb(255, 0, 0);
                }
            }
            else
            {
                Serial_Setting_Form m_serial_setting_form = new Serial_Setting_Form((int)Serial_t.SERIAL_TYPE_INS);
                m_serial_setting_form.Show();
            }
        }

        private void toolStripButton_Connection_BAM_Click(object sender, EventArgs e)
        {
            if (m_CommPort.IsOpen((int)Serial_t.SERIAL_TYPE_BAM))
            {
                m_CommPort.Close((int)Serial_t.SERIAL_TYPE_BAM);

                string str = m_CommPort.IsStatus((int)Serial_t.SERIAL_TYPE_BAM);
                toolStripTextBox_BAM.Text = str;
                if (str == "Close")
                {
                    toolStripTextBox_BAM.BackColor = Color.FromArgb(255, 0, 0);
                }
            }
            else
            {
                Serial_Setting_Form m_serial_setting_form = new Serial_Setting_Form((int)Serial_t.SERIAL_TYPE_BAM);
                m_serial_setting_form.Show();
            }
        }

        private void toolStripButton_Connection_Mon_1_Click(object sender, EventArgs e)
        {
            if (m_CommPort.IsOpen((int)Serial_t.SERIAL_TYPE_MON_1))
            {
                m_CommPort.Close((int)Serial_t.SERIAL_TYPE_MON_1);

                string str = m_CommPort.IsStatus((int)Serial_t.SERIAL_TYPE_MON_1);
                toolStripTextBox_Mon_1.Text = str;
                if (str == "Close")
                {
                    toolStripTextBox_Mon_1.BackColor = Color.FromArgb(255, 0, 0);
                }
            }
            else
            {
                Serial_Setting_Form m_serial_setting_form = new Serial_Setting_Form((int)Serial_t.SERIAL_TYPE_MON_1);
                m_serial_setting_form.Show();
            }
        }

        private void toolStripButton_Connection_Mon_2_Click(object sender, EventArgs e)
        {
            if (m_CommPort.IsOpen((int)Serial_t.SERIAL_TYPE_MON_2))
            {
                m_CommPort.Close((int)Serial_t.SERIAL_TYPE_MON_2);

                string str = m_CommPort.IsStatus((int)Serial_t.SERIAL_TYPE_MON_2);
                toolStripTextBox_Mon_2.Text = str;
                if (str == "Close")
                {
                    toolStripTextBox_Mon_2.BackColor = Color.FromArgb(255, 0, 0);
                }
            }
            else
            {
                Serial_Setting_Form m_serial_setting_form = new Serial_Setting_Form((int)Serial_t.SERIAL_TYPE_MON_2);
                m_serial_setting_form.Show();
            }
        }

        private void toolStripButton_Connection_Mon_3_Click(object sender, EventArgs e)
        {
            if (m_CommPort.IsOpen((int)Serial_t.SERIAL_TYPE_MON_3))
            {
                m_CommPort.Close((int)Serial_t.SERIAL_TYPE_MON_3);

                string str = m_CommPort.IsStatus((int)Serial_t.SERIAL_TYPE_MON_3);
                toolStripTextBox_Mon_3.Text = str;
                if (str == "Close")
                {
                    toolStripTextBox_Mon_3.BackColor = Color.FromArgb(255, 0, 0);
                }
            }
            else
            {
                Serial_Setting_Form m_serial_setting_form = new Serial_Setting_Form((int)Serial_t.SERIAL_TYPE_MON_3);
                m_serial_setting_form.Show();
            }
        }

        private void IN3000R_Form_FormClosing(object sender, FormClosingEventArgs e)
        {
            _Nav_Msg_Form.Dispose();
            _Nmea_Monitor_Form.Dispose();
            for(int i=0; i<(int)Serial_t.SERIAL_TYPE_MAX; i++)
            {
                m_CommPort.Close(i);
            }
            this.Dispose();
        }

        private void tabControl_SelectedIndexChanged(object sender, EventArgs e)
        {
            //MessageBox.Show(string.Format("{0}", tabControl.SelectedIndex));
        }

        private void toolStripTextBox_Main_Click(object sender, EventArgs e)
        {
            if (m_CommPort.IsOpen((int)Serial_t.SERIAL_TYPE_MAIN))
            {
                m_CommPort.Close((int)Serial_t.SERIAL_TYPE_MAIN);
            }
            else
            {
                Serial_Port_Info_c info = new Serial_Port_Info_c();
                Serial_Setting_Ini ini = new Serial_Setting_Ini();
                info = ini.StoreRead((int)Serial_t.SERIAL_TYPE_MAIN);

                m_CommPort.Open((int)Serial_t.SERIAL_TYPE_MAIN, info);
            }

            string str = m_CommPort.IsStatus((int)Serial_t.SERIAL_TYPE_MAIN);
            toolStripTextBox_Main.Text = str;
            if (str == "Close")
            {
                toolStripTextBox_Main.BackColor = Color.FromArgb(255, 0, 0);
            }
            else
            {
                toolStripTextBox_Main.BackColor = Color.FromArgb(0, 255, 0);
            }
        }

        private void toolStripTextBox_INS_Click(object sender, EventArgs e)
        {
            if (m_CommPort.IsOpen((int)Serial_t.SERIAL_TYPE_INS))
            {
                m_CommPort.Close((int)Serial_t.SERIAL_TYPE_INS);
            }
            else
            {
                Serial_Port_Info_c info = new Serial_Port_Info_c();
                Serial_Setting_Ini ini = new Serial_Setting_Ini();
                info = ini.StoreRead((int)Serial_t.SERIAL_TYPE_INS);

                m_CommPort.Open((int)Serial_t.SERIAL_TYPE_INS, info);
            }

            string str = m_CommPort.IsStatus((int)Serial_t.SERIAL_TYPE_INS);
            toolStripTextBox_INS.Text = str;
            if (str == "Close")
            {
                toolStripTextBox_INS.BackColor = Color.FromArgb(255, 0, 0);
            }
            else
            {
                toolStripTextBox_INS.BackColor = Color.FromArgb(0, 255, 0);
            }
        }

        private void toolStripTextBox_BAM_Click(object sender, EventArgs e)
        {
            if (m_CommPort.IsOpen((int)Serial_t.SERIAL_TYPE_BAM))
            {
                m_CommPort.Close((int)Serial_t.SERIAL_TYPE_BAM);
            }
            else
            {
                Serial_Port_Info_c info = new Serial_Port_Info_c();
                Serial_Setting_Ini ini = new Serial_Setting_Ini();
                info = ini.StoreRead((int)Serial_t.SERIAL_TYPE_BAM);

                m_CommPort.Open((int)Serial_t.SERIAL_TYPE_BAM, info);
            }

            string str = m_CommPort.IsStatus((int)Serial_t.SERIAL_TYPE_BAM);
            toolStripTextBox_BAM.Text = str;
            if (str == "Close")
            {
                toolStripTextBox_BAM.BackColor = Color.FromArgb(255, 0, 0);
            }
            else
            {
                toolStripTextBox_BAM.BackColor = Color.FromArgb(0, 255, 0);
            }
        }

        private void toolStripTextBox_Mon_1_Click(object sender, EventArgs e)
        {
            if (m_CommPort.IsOpen((int)Serial_t.SERIAL_TYPE_MON_1))
            {
                m_CommPort.Close((int)Serial_t.SERIAL_TYPE_MON_1);
            }
            else
            {
                Serial_Port_Info_c info = new Serial_Port_Info_c();
                Serial_Setting_Ini ini = new Serial_Setting_Ini();
                info = ini.StoreRead((int)Serial_t.SERIAL_TYPE_MON_1);

                m_CommPort.Open((int)Serial_t.SERIAL_TYPE_MON_1, info);
            }

            string str = m_CommPort.IsStatus((int)Serial_t.SERIAL_TYPE_MON_1);
            toolStripTextBox_Mon_1.Text = str;
            if (str == "Close")
            {
                toolStripTextBox_Mon_1.BackColor = Color.FromArgb(255, 0, 0);
            }
            else
            {
                toolStripTextBox_Mon_1.BackColor = Color.FromArgb(0, 255, 0);
            }
        }

        private void toolStripTextBox_Mon_2_Click(object sender, EventArgs e)
        {
            if (m_CommPort.IsOpen((int)Serial_t.SERIAL_TYPE_MON_2))
            {
                m_CommPort.Close((int)Serial_t.SERIAL_TYPE_MON_2);
            }
            else
            {
                Serial_Port_Info_c info = new Serial_Port_Info_c();
                Serial_Setting_Ini ini = new Serial_Setting_Ini();
                info = ini.StoreRead((int)Serial_t.SERIAL_TYPE_MON_2);

                m_CommPort.Open((int)Serial_t.SERIAL_TYPE_MON_2, info);
            }

            string str = m_CommPort.IsStatus((int)Serial_t.SERIAL_TYPE_MON_2);
            toolStripTextBox_Mon_2.Text = str;
            if (str == "Close")
            {
                toolStripTextBox_Mon_2.BackColor = Color.FromArgb(255, 0, 0);
            }
            else
            {
                toolStripTextBox_Mon_2.BackColor = Color.FromArgb(0, 255, 0);
            }
        }

        private void toolStripTextBox_Mon_3_Click(object sender, EventArgs e)
        {
            if (m_CommPort.IsOpen((int)Serial_t.SERIAL_TYPE_MON_3))
            {
                m_CommPort.Close((int)Serial_t.SERIAL_TYPE_MON_3);
            }
            else
            {
                Serial_Port_Info_c info = new Serial_Port_Info_c();
                Serial_Setting_Ini ini = new Serial_Setting_Ini();
                info = ini.StoreRead((int)Serial_t.SERIAL_TYPE_MON_3);

                m_CommPort.Open((int)Serial_t.SERIAL_TYPE_MON_3, info);
            }

            string str = m_CommPort.IsStatus((int)Serial_t.SERIAL_TYPE_MON_3);
            toolStripTextBox_Mon_3.Text = str;
            if (str == "Close")
            {
                toolStripTextBox_Mon_3.BackColor = Color.FromArgb(255, 0, 0);
            }
            else
            {
                toolStripTextBox_Mon_3.BackColor = Color.FromArgb(0, 255, 0);
            }
        }

        private void toolStripButton_Connection_LAN_Click(object sender, EventArgs e)
        {
            //toolStripTextBox_LAN.Text
            //toolStripTextBox_Port.Text

            if(m_UdpPort.IsOpen() == 0)
            {
                m_UdpPort.Open();
            }
            else
            {
                m_UdpPort.Close();
            }
        }
    }
}
