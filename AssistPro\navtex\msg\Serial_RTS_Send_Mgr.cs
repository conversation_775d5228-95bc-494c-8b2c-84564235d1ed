﻿using AssistPro.Lib;
using AssistPro.navtex.communication;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using static AssistPro.navtex.communication.CommPort;

namespace AssistPro.navtex.msg
{
    public partial class Serial_RTS_Send_Mgr
    {
        static readonly Serial_RTS_Send_Mgr instance = new Serial_RTS_Send_Mgr();

        private CommPort m_CommPort = CommPort.Instance;

        public enum Serial_FSK_t
        {
            Mark = 0,
            Space = 1,
        }
        private enum Ctrl_t
        {
            SERIAL_FSK_TYPE_STOP = 0,
            SERIAL_FSK_TYPE_MARK,
            SERIAL_FSK_TYPE_SPACE,
            SERIAL_FSK_TYPE_DOT,
            SERIAL_FSK_TYPE_MSG,
        }

        private class Ctrl_c
        {
            public bool isProcessing = false;                   // status
            public int mode = (int)Ctrl_t.SERIAL_FSK_TYPE_STOP; // mode
            public int[] buffer = new int[1048576];               // 1M buffer
            public long f_cnt = 0;                               // front count
            public long r_cnt = 0;                               // rear count
            public bool tog_dot = false;
        }
        private Ctrl_c m_Ctrl = new Ctrl_c();

        ATimer m_timer;

        public static Serial_RTS_Send_Mgr Instance
        {
            get
            {
                return instance;
            }
        }

        Serial_RTS_Send_Mgr()
        {
            m_timer = new ATimer(2, 10, Timer_Callback);
        }

        public void Clear_Buffer()
        {
            m_Ctrl.mode = (int)Ctrl_t.SERIAL_FSK_TYPE_STOP;
            m_Ctrl.f_cnt = 0;
            m_Ctrl.r_cnt = 0;
            Array.Clear(m_Ctrl.buffer, 0, m_Ctrl.buffer.Length);
        }

        public void Add_Buffer(int data)
        {
            m_Ctrl.buffer[m_Ctrl.f_cnt] = data;
            m_Ctrl.f_cnt += 1;
        }
        public void Send_Message()
        {
            m_Ctrl.r_cnt = 0;
            m_Ctrl.mode = (int)Ctrl_t.SERIAL_FSK_TYPE_MSG;
            m_timer.Start();
        }

        public void Timer_Callback()
        {
            if (m_CommPort.IsOpen((int)Serial_t.SERIAL_TYPE_MAIN) == true)
            {
                if (m_Ctrl.mode == (int)Ctrl_t.SERIAL_FSK_TYPE_MARK)
                {
                    m_CommPort.SetRtsIO((int)Serial_t.SERIAL_TYPE_MAIN, (int)Serial_FSK_t.Mark);
                }
                else if(m_Ctrl.mode == (int)Ctrl_t.SERIAL_FSK_TYPE_SPACE)
                {
                    m_CommPort.SetRtsIO((int)Serial_t.SERIAL_TYPE_MAIN, (int)Serial_FSK_t.Space);
                }
                else if(m_Ctrl.mode == (int)Ctrl_t.SERIAL_FSK_TYPE_DOT)
                {
                    if(m_Ctrl.tog_dot == false)
                    {
                        m_CommPort.SetRtsIO((int)Serial_t.SERIAL_TYPE_MAIN, (int)Serial_FSK_t.Mark);
                        m_Ctrl.tog_dot = true;
                    }
                    else
                    {
                        m_CommPort.SetRtsIO((int)Serial_t.SERIAL_TYPE_MAIN, (int)Serial_FSK_t.Space);
                        m_Ctrl.tog_dot = false;
                    }
                }
                else if(m_Ctrl.mode == (int)Ctrl_t.SERIAL_FSK_TYPE_MSG)
                {
                    if(m_Ctrl.r_cnt < m_Ctrl.f_cnt)
                    {
                        if (m_Ctrl.buffer[m_Ctrl.r_cnt] == (int)Serial_FSK_t.Mark)
                        {
                            m_CommPort.SetRtsIO((int)Serial_t.SERIAL_TYPE_MAIN, (int)Serial_FSK_t.Mark);
                        }
                        else if (m_Ctrl.buffer[m_Ctrl.r_cnt] == (int)Serial_FSK_t.Space)
                        {
                            m_CommPort.SetRtsIO((int)Serial_t.SERIAL_TYPE_MAIN, (int)Serial_FSK_t.Space);
                        }
                    }

                    m_Ctrl.r_cnt += 1;
                    if(m_Ctrl.r_cnt > m_Ctrl.f_cnt)
                    {
                        Clear_Buffer();
                    }
                }
                else if(m_Ctrl.mode == (int)Ctrl_t.SERIAL_FSK_TYPE_STOP)
                {
                    m_timer.Stop();
                }
            }
            else
            {
                m_Ctrl.mode = (int)Ctrl_t.SERIAL_FSK_TYPE_STOP;
                m_timer.Stop();
            }
        }

        public void MarkOut()
        {
            m_Ctrl.mode = (int)Ctrl_t.SERIAL_FSK_TYPE_MARK;
            m_timer.Start();
        }

        public void SpaceOut()
        {
            m_Ctrl.mode = (int)Ctrl_t.SERIAL_FSK_TYPE_SPACE;
            m_timer.Start();
        }

        public void DotOut()
        {
            m_Ctrl.mode = (int)Ctrl_t.SERIAL_FSK_TYPE_DOT;
            m_timer.Start();
        }

        public void Stop()
        {
            m_Ctrl.mode = (int)Ctrl_t.SERIAL_FSK_TYPE_STOP;
            Clear_Buffer();
        }
    }
}
