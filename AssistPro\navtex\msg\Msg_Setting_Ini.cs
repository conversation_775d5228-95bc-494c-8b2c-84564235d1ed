﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AssistPro.navtex;
using System.Windows.Forms;
using System.Drawing;
using AssistPro.Lib;

namespace AssistPro.navtex.msg
{
    public partial class Msg_Setting_Ini
    {
        public class Msg_Setting_Info_c
        {
            public string strSpace = "1785";
            public string strMark = "1615";
            public string strBPS = "100";
            public string strMsg = " ";
            public string strPC_SpeakerVolum = "100";
        }

        private string GetPath()
        {
            string s = Application.StartupPath + "\\Setting";
            DirectoryInfo di = new DirectoryInfo(s);
            if (di.Exists == false)
            {
                di.Create();
            }

            return s;
        }

        private string GetFileName_Ini()
        {
            string s = string.Format("\\FSK.ini");

            return s;
        }

        public void StoreWrite(Msg_Setting_Info_c info)
        {
            string s = string.Format(GetPath() + GetFileName_Ini());
            IniFile ini = new IniFile(s);

            ini.WriteValue("FSK", "Space", info.strSpace);
            ini.WriteValue("FSK", "Mark", info.strMark);
            ini.WriteValue("FSK", "BPS", info.strBPS);
            ini.WriteValue("FSK", "PC_SpeakerVolum", info.strPC_SpeakerVolum);

            info.strMsg = info.strMsg.Replace("\n", "!@");
            info.strMsg = info.strMsg.Replace("\r", "#$");
            ini.WriteValue("FSK", "Message", info.strMsg);
        }
        
        public Msg_Setting_Info_c StoreRead()
        {
            Msg_Setting_Info_c info = new Msg_Setting_Info_c();
            string s = string.Format(GetPath() + GetFileName_Ini());

            IniFile ini = new IniFile(s);
            info.strSpace           = ini.ReadValue("FSK", "Space", info.strSpace);
            info.strMark            = ini.ReadValue("FSK", "Mark", info.strMark);
            info.strBPS             = ini.ReadValue("FSK", "BPS", info.strBPS);
            info.strPC_SpeakerVolum = ini.ReadValue("FSK", "PC_SpeakerVolum", info.strPC_SpeakerVolum);
            info.strMsg             = ini.ReadValue("FSK", "Message", info.strMsg);

            info.strMsg = info.strMsg.Replace("!@", "\n");
            info.strMsg = info.strMsg.Replace("#$", "\r");

            return info;
        }
    }
}
