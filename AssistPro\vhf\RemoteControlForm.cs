﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO.Ports;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using WeifenLuo.WinFormsUI;
using static AssistPro.SerialManager;
using static AssistPro.vhf.SerialRmtProtocol;


namespace AssistPro.vhf
{
    public partial class RemoteControlForm : DockContent
    {
        private DefaultChannelInfo defaultChannelInfo;
        private string m_region_name;

        private byte m_Sender;
        private byte m_Receiver;
        private SerialManager m_SerialManager;
        private SerialPort m_SerialPort;
        private Thread m_serialThread;
        private SerialRmtProtocol m_DataProtocol;

        public RemoteControlForm()
        {
            InitializeComponent();
            defaultChannelInfo = new DefaultChannelInfo();
        }
        private void loading_channel_table()
        {
            List<ChannelInfo> channels = defaultChannelInfo.RegionChannels.FirstOrDefault(rc => rc.RegionName == m_region_name)?.Channels;

            if (channels != null && channels.Count > 0)
            {
                lb_channetable.Items.Clear();
                foreach (var channel in channels)
                {
                    if (channel.SubChannel == 'W')
                    {
                        lb_channetable.Items.Add(channel.SubChannel + channel.ChannelNumber.Trim());
                    }
                    else
                    {
                        lb_channetable.Items.Add(channel.ChannelNumber.Trim() + channel.SubChannel);
                    }
                    //lb_channetable.Items.Add(channel.ChannelNumber + " - " + channel.TransmitFrequency + "/" + channel.ReceiveFrequency);                    
                }
            }
        }

        private void rb_region_checked_changed(object sender, EventArgs e)
        {
            lb_channetable.SelectedIndex = -1;
            tb_channel.Text = "";

            RadioButton rb = sender as RadioButton;
            if (rb == null || !rb.Checked)
                return;

            m_region_name = rb.Text;
            loading_channel_table();
        }


        private void main_vol_trackbar_scroll(object sender, EventArgs e)
        {
            byte trackBarValue = (byte)tb_rmt_vol.Value;
            lb_vol_value.Text = trackBarValue.ToString();

            RmtCtrlVolume(trackBarValue);
            Thread.Sleep(100);
        }

        private void main_sql_trackbar_scroll(object sender, EventArgs e)
        {
            byte trackBarValue = (byte)tb_squelch.Value;
            lb_sql_value.Text = trackBarValue.ToString();

            RmtCtrlSQL(trackBarValue);
            Thread.Sleep(100);
        }

        private void RmtCtrlService()
        {
            byte dsc_sig_routing = (byte)(cb_dsc_signal_routing.Checked ? 1 : 0);
            byte dsc_ber_mode = (byte)(cb_dsc_ber_test_mode.Checked ? 1 : 0);
            byte dsc_test_mode = rb_dsc_out_off.Checked ? (byte)0 :
                                    rb_dsc_mark.Checked ? (byte)1 :
                                    rb_dsc_space.Checked ? (byte)2 : (byte)3;

            byte ptt = (byte)(cb_PTT.Checked ? 1 : 0);


            ushort cmd;
            byte cmd_type = SerialRmtProtocol.CMD_SET_NOTI;
            ushort cmd_param = SerialRmtProtocol.RMT_CTRL_SERVICE_SET;
            cmd = m_DataProtocol.GenerateCMD(cmd_type, m_Sender, m_Receiver);

            m_DataProtocol.m_DataFrame.data_buf[0] = dsc_sig_routing;
            m_DataProtocol.m_DataFrame.data_buf[1] = dsc_ber_mode;
            m_DataProtocol.m_DataFrame.data_buf[2] = dsc_test_mode;
            m_DataProtocol.m_DataFrame.data_buf[3] = ptt;

            m_DataProtocol.SendDataFrame(cmd, cmd_param, m_DataProtocol.m_DataFrame.data_buf, 4);
        }


        private void RmtCtrlPTT(byte ptt)
        {
            ushort cmd;
            byte cmd_type = SerialRmtProtocol.CMD_SET_NOTI;
            ushort cmd_param = SerialRmtProtocol.RMT_CTRL_PTT_SET;
            cmd = m_DataProtocol.GenerateCMD(cmd_type, m_Sender, m_Receiver);

            m_DataProtocol.m_DataFrame.data_buf[0] = ptt;
            m_DataProtocol.m_DataFrame.data_buf[1] = 0;
            m_DataProtocol.m_DataFrame.data_buf[2] = 0;
            m_DataProtocol.m_DataFrame.data_buf[3] = 0;

            m_DataProtocol.SendDataFrame(cmd, cmd_param, m_DataProtocol.m_DataFrame.data_buf, 4);
        }

        private void RmtCtrlPWR(byte pwr)
        {
            ushort cmd;
            byte cmd_type = SerialRmtProtocol.CMD_SET_NOTI;
            ushort cmd_param = SerialRmtProtocol.RMT_CTRL_PWR_SET;
            cmd = m_DataProtocol.GenerateCMD(cmd_type, m_Sender, m_Receiver);

            m_DataProtocol.m_DataFrame.data_buf[0] = pwr;
            m_DataProtocol.m_DataFrame.data_buf[1] = 0;
            m_DataProtocol.m_DataFrame.data_buf[2] = 0;
            m_DataProtocol.m_DataFrame.data_buf[3] = 0;

            m_DataProtocol.SendDataFrame(cmd, cmd_param, m_DataProtocol.m_DataFrame.data_buf, 4);
        }


        private void RmtCtrlSQL(byte sql)
        {
            ushort cmd;
            byte cmd_type = SerialRmtProtocol.CMD_SET_NOTI;
            ushort cmd_param = SerialRmtProtocol.RMT_CTRL_SQL_SET;
            cmd = m_DataProtocol.GenerateCMD(cmd_type, m_Sender, m_Receiver);

            m_DataProtocol.m_DataFrame.data_buf[0] = sql;
            m_DataProtocol.m_DataFrame.data_buf[1] = 0;
            m_DataProtocol.m_DataFrame.data_buf[2] = 0;
            m_DataProtocol.m_DataFrame.data_buf[3] = 0;

            m_DataProtocol.SendDataFrame(cmd, cmd_param, m_DataProtocol.m_DataFrame.data_buf, 4);
        }

        private void RmtCtrlVolume(byte volume)
        {
            ushort cmd;
            byte cmd_type = SerialRmtProtocol.CMD_SET_NOTI;
            ushort cmd_param = SerialRmtProtocol.RMT_CTRL_VOL_SET;
            cmd = m_DataProtocol.GenerateCMD(cmd_type, m_Sender, m_Receiver);

            m_DataProtocol.m_DataFrame.data_buf[0] = volume;
            m_DataProtocol.m_DataFrame.data_buf[1] = 0;
            m_DataProtocol.m_DataFrame.data_buf[2] = 0;
            m_DataProtocol.m_DataFrame.data_buf[3] = 0;

            m_DataProtocol.SendDataFrame(cmd, cmd_param, m_DataProtocol.m_DataFrame.data_buf, 4);
        }

        private void RmtCtrlChannel(ushort channel_num, byte sub_channel)
        {
            ushort cmd;
            byte cmd_type = SerialRmtProtocol.CMD_SET_NOTI;
            ushort cmd_param = SerialRmtProtocol.RMT_CTRL_CH_SET;
            cmd = m_DataProtocol.GenerateCMD(cmd_type, m_Sender, m_Receiver);

            m_DataProtocol.m_DataFrame.data_buf[0] = (byte)(channel_num & 0xFF);
            m_DataProtocol.m_DataFrame.data_buf[1] = (byte)((channel_num >> 8) & 0xFF);
            m_DataProtocol.m_DataFrame.data_buf[2] = sub_channel;

            if (m_region_name == "INT'L")
            {
                m_DataProtocol.m_DataFrame.data_buf[3] = 0;
            }
            else if (m_region_name == "CANADA")
            {
                m_DataProtocol.m_DataFrame.data_buf[3] = 1;
            }
            else if (m_region_name == "USA")
            {
                m_DataProtocol.m_DataFrame.data_buf[3] = 2;
            }
            else // IWW
            {
                m_DataProtocol.m_DataFrame.data_buf[3] = 3;
            }

            m_DataProtocol.SendDataFrame(cmd, cmd_param, m_DataProtocol.m_DataFrame.data_buf, 4);

        }

        private void tb_channel_keydown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                string channelNumber = tb_channel.Text;
                var channels = defaultChannelInfo.RegionChannels.FirstOrDefault(rc => rc.RegionName == m_region_name)?.Channels;

                string channel;
                for (int i = 0; i < channels.Count; i++)
                {
                    channel = channels[i].ChannelNumber.Trim() + channels[i].SubChannel;
                    if (channel == channelNumber.Trim())
                    {
                        lb_channetable.SelectedIndex = i;

                        RmtCtrlChannel(ushort.Parse(channels[i].ChannelNumber.Trim()), Convert.ToByte(channels[i].SubChannel));

                        lb_rx_freq.Text = channels[i].ReceiveFrequency == -1 ? "N/A" : (channels[i].ReceiveFrequency / 1000.0).ToString("0.000") + " MHz";
                        lb_tx_freq.Text = channels[i].TransmitFrequency == -1 ? "N/A" : (channels[i].TransmitFrequency / 1000.0).ToString("0.000") + " MHz";

                        break;
                    }
                }
            }
        }

        private void SelectedIndexChanged_lbChannel(object sender, EventArgs e)
        {           
            if (lb_channetable.SelectedIndex != -1)
            {
                ChannelInfo channel = defaultChannelInfo.RegionChannels.FirstOrDefault(rc => rc.RegionName == m_region_name)?.Channels[lb_channetable.SelectedIndex];

                if (channel.SubChannel == 'W')
                {
                    tb_channel.Text = channel.SubChannel + channel.ChannelNumber.Trim();
                }
                else
                {
                    tb_channel.Text = channel.ChannelNumber.Trim() + channel.SubChannel;
                }
    
                lb_rx_freq.Text = channel.ReceiveFrequency == -1 ? "N/A" : (channel.ReceiveFrequency / 1000.0).ToString("0.000") + " MHz";
                lb_tx_freq.Text = channel.TransmitFrequency == -1 ? "N/A" : (channel.TransmitFrequency / 1000.0).ToString("0.000") + " MHz";

                RmtCtrlChannel(ushort.Parse(channel.ChannelNumber.Trim()), Convert.ToByte(channel.SubChannel));
            }
        }

        private void bt_ch_down_Click(object sender, EventArgs e)
        {
            if (lb_channetable.SelectedIndex > 0)
            {
                lb_channetable.SelectedIndex--;
            }
        }

        private void bt_ch_up_Click(object sender, EventArgs e)
        {
            if (lb_channetable.SelectedIndex < lb_channetable.Items.Count - 1)
            {
                lb_channetable.SelectedIndex++;
            }
        }
        
        private void SerialDataThread()
        {
            byte TempData = 0;

            while (true)
            {
                while (m_SerialManager.Dequeue(ref TempData) != BUFF_STATUS.BUFF_NULL_DATA)
                {
                    if (m_DataProtocol.m_RcvFrame.bReceiving == false)
                    {
                        if (TempData == SerialRmtProtocol.FRM_SFD)
                        {
                            m_DataProtocol.ResetReceiveFrame();
                            m_DataProtocol.m_RcvFrame.rx_data[m_DataProtocol.m_RcvFrame.rcv_cnt++] = TempData;
                            m_DataProtocol.m_RcvFrame.bReceiving = true;
                        }
                    }
                    else
                    {
                        m_DataProtocol.m_RcvFrame.rx_data[m_DataProtocol.m_RcvFrame.rcv_cnt++] = TempData;
                        if (m_DataProtocol.m_RcvFrame.rcv_cnt == 3)
                        {
                            m_DataProtocol.m_RcvFrame.frame_len = (ushort)((m_DataProtocol.m_RcvFrame.rx_data[1])
                                                                          | ((m_DataProtocol.m_RcvFrame.rx_data[2] << 8) & 0xFF00));

                            if (m_DataProtocol.m_RcvFrame.frame_len < DataProtocol.FRAME_HEADER_SIZE ||
                                m_DataProtocol.m_RcvFrame.frame_len > DataProtocol.MAX_FRAME_LEN)
                            {
                                m_DataProtocol.ResetReceiveFrame();
                            }
                        }
                        else if (m_DataProtocol.m_RcvFrame.rcv_cnt >= m_DataProtocol.m_RcvFrame.frame_len + DataProtocol.FRAME_CRC_SIZE + 1)  // CRC(2) + FRM_SFD(1)
                        {
                            switch (m_DataProtocol.ParseCommand(m_DataProtocol.m_RcvFrame.rx_data, m_DataProtocol.m_RcvFrame.frame_len, m_DataProtocol.m_RcvFrame.rcv_cnt))
                            {
                                case SerialRmtProtocol.ERROR.ERR_CRC:
                                    {
                                        Console.WriteLine("ERROR.ERR_CRC");
                                        m_DataProtocol.ResetReceiveFrame();
                                        break;
                                    }
                                case SerialRmtProtocol.ERROR.ERR_LEN:
                                    {
                                        Console.WriteLine("ERROR.ERR_LEN");
                                        m_DataProtocol.ResetReceiveFrame();
                                    }
                                    break;
                                case SerialRmtProtocol.ERROR.ERR_SFD:
                                    {
                                        Console.WriteLine("ERROR.ERR_SFD");
                                        m_DataProtocol.ResetReceiveFrame();
                                    }
                                    break;
                                case SerialRmtProtocol.ERROR.ERR_OK:
                                    if (m_DataProtocol.m_DataFrame.RxFrameIdx > 0)
                                    {
                                        m_DataProtocol.m_DataFrame.RxFrameIdx = 0;
                                        m_DataProtocol.ProcCommandHandler();
                                    }
                                    break;
                                default:
                                    break;
                            }
                            m_DataProtocol.ResetReceiveFrame();
                        }
                    }
                }
                //Console.WriteLine("Thread");
                Thread.Sleep(1);
            }
        }

        private void serialPort_DataReceived(object sender, SerialDataReceivedEventArgs e)
        {
            this.Invoke(new EventHandler(SerialData_Received));
        }
        private void SerialData_Received(object s, EventArgs e)
        {
            m_SerialManager.Enqueue();
        }
        private void btn_ConnectIO_Click(object sender, EventArgs e)
        {
            try
            {
                if (cb_comport.Text == "")
                {
                    MessageBox.Show(" You need select port. ");
                    return;
                }

                if (cb_comport.Enabled)
                {
                    m_SerialManager = new SerialManager(cb_comport.Text, int.Parse(cb_baudrate.Text));
                    m_SerialPort = m_SerialManager.GetInstance();
                    m_SerialPort.DataReceived += serialPort_DataReceived;
                    m_SerialPort.Open();

                    btn_ConnectIO.Text = "DISCONNECT";
                    cb_comport.Enabled = false;
                    cb_baudrate.Enabled = false;
                    btn_ConnectIO.BackColor = Color.PaleGreen;

                    enable_rmt_control_widget(true);

                    m_DataProtocol = new SerialRmtProtocol(m_SerialPort);

                    m_serialThread = new Thread(SerialDataThread);
                    m_serialThread.Priority = ThreadPriority.Highest;
                    m_serialThread.Start();
                }
                else
                {
                    m_SerialPort.Close();
                    btn_ConnectIO.Text = "CONNECT";
                    cb_comport.Enabled = true;
                    cb_baudrate.Enabled = true;
                    btn_ConnectIO.BackColor = Color.Silver;

                    enable_rmt_control_widget(false);

                    m_serialThread.Abort();
                }
            }
            catch (Exception ea)
            {
                MessageBox.Show(ea.Message);
                cb_comport.Enabled = true;
                btn_ConnectIO.BackColor = Color.Silver;
                btn_ConnectIO.Text = "CONNECT";
            }

        }

        private void enable_rmt_control_widget(bool enable)
        {
            if (enable)
            {
                gb_rmt_serial.Enabled = true;
                gb_knob.Enabled = true;
                gb_service.Enabled = true;
                gb_dsc_test_pattern.Enabled = true;
                gb_power.Enabled = true;
                cb_PTT.Enabled = true;
            }
            else
            {
                gb_rmt_serial.Enabled = false;
                gb_knob.Enabled = false;
                gb_service.Enabled = false;
                gb_dsc_test_pattern.Enabled = false;
                gb_power.Enabled = false;
                cb_PTT.Enabled = false;
            }
        }

        private void Load_RemoteControl(object sender, EventArgs e)
        {
            foreach (string name in SerialPort.GetPortNames())
            {
                cb_comport.Items.Add(name);
            }
            m_Sender = SerialRmtProtocol.REMOTE_SERIAL;
            m_Receiver = SerialRmtProtocol.CONTROLLER;

            btn_ConnectIO.Enabled = true;

            enable_rmt_control_widget(false);

            m_region_name = "INT'L";

            loading_channel_table();
        }

        private void cb_PTT_CheckedChanged(object sender, EventArgs e)
        {
            if (cb_PTT.Checked)
            {
                cb_PTT.BackColor = Color.LightGreen; // Pressed 상태 배경색
                cb_PTT.ForeColor = Color.Black;
                rb_dsc_out_off.Checked = true;
                RmtCtrlPTT(1);
            }
            else
            {
                cb_PTT.BackColor = SystemColors.Control; // 기본 배경색
                cb_PTT.ForeColor = Color.Black;
                RmtCtrlPTT(0);
            }
        }

        private void rb_pwr_checked_changed(object sender, EventArgs e)
        {
            if (rb_1w.Checked)
            {
                RmtCtrlPWR(0);
            }
            else
            {
                RmtCtrlPWR(1);
            }
        }

        private void cb_service_CheckedChanged(object sender, EventArgs e)
        {
            RmtCtrlService();
        }

        private void rb_dsc_test_mode_clicked(object sender, EventArgs e)
        {
            RmtCtrlService();
        }
    }
}


public class ChannelInfo
{
    public enum PowerType
    {
        PWR_1W,
        PWR_25W,
    }
    public string ChannelNumber;
    public char SubChannel;
    public int TransmitFrequency;
    public int ReceiveFrequency;
    public string DuplexMode;
    public PowerType Power;
    public bool Tag;

   
    public ChannelInfo(string channelNumber,char sub_channel, int transmitFrequency, int receiveFrequency, string duplexMode, PowerType power, bool tag)
    {
        ChannelNumber = channelNumber;
        SubChannel = sub_channel;
        TransmitFrequency = transmitFrequency;
        ReceiveFrequency = receiveFrequency;
        DuplexMode = duplexMode;
        Power = power;
        Tag = tag;
    }
}

public class RegionChannelInfo
{
    public string RegionName;
    public List<ChannelInfo> Channels;

    public RegionChannelInfo(string regionName, List<ChannelInfo> channels)
    {
        RegionName = regionName;
        Channels = channels;
    }
}

public class DefaultChannelInfo
{
    public List<RegionChannelInfo> RegionChannels;

    public DefaultChannelInfo()
    {
        RegionChannels = new List<RegionChannelInfo>
        {
            new RegionChannelInfo("INT'L", new List<ChannelInfo>
            {
                new ChannelInfo("  1",  ' ', 156050, 160650, "PORT-PUBLIC",     ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  2",  ' ', 156100, 160700, "PORT-PUBLIC",     ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  3",  ' ', 156150, 160750, "PORT-PUBLIC",     ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  4",  ' ', 156200, 160800, "PORT-PUBLIC",     ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  5",  ' ', 156250, 160850, "PORT-PUBLIC",     ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  6",  ' ', 156300, 156300, "INTERSHIP",       ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  7",  ' ', 156350, 160950, "PORT-PUBLIC",     ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  8",  ' ', 156400, 156400, "INTERSHIP",       ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  9",  ' ', 156450, 156450, "PORT-INTERSHIP",  ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 10",  ' ', 156500, 156500, "PORT-INTERSHIP",  ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 11",  ' ', 156550, 156550, "PORT",            ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 12",  ' ', 156600, 156600, "PORT",            ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 13",  ' ', 156650, 156650, "PORT-INTERSHIP",  ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 14",  ' ', 156700, 156700, "PORT",            ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 15",  ' ', 156750, 156750, "PORT-INTERSHIP",  ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 16",  ' ', 156800, 156800, "DISTRESS/CALL",   ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 17",  ' ', 156850, 156850, "PORT-INTERSHIP",  ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 18",  ' ', 156900, 161500, "PORT-PUBLIC",     ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 19",  ' ', 156950, 161550, "PORT-PUBLIC",     ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("1019", ' ', 156950, 156950, "PORT",            ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("2019", ' ',    -1, 161550, "PORT",             ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 20",  ' ', 157000, 161600, "PORT-PUBLIC",     ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("1020", ' ', 157000, 157000, "PORT",            ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("2020", ' ',    -1, 161600, "PORT",             ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 21",  ' ', 157050, 161650, "",                ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 22",  ' ', 157100, 161700, "",                ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 23",  ' ', 157150, 161750, "",                ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 24",  ' ', 157200, 161800, "",                ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 25",  ' ', 157250, 161850, "",                ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 26",  ' ', 157300, 161900, "",                ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 27",  ' ', 157350, 161950, "PORT-PUBLIC",     ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("1027", ' ', 157350, 157350, "PORT",            ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 28",  ' ', 157400, 162000, "PORT-PUBLIC",     ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("1028", ' ', 157400, 157400, "PORT",            ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 60",  ' ', 156025, 160625, "PORT-PUBLIC",     ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 61",  ' ', 156075, 160675, "PORT-PUBLIC",     ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 62",  ' ', 156125, 160725, "PORT-PUBLIC",     ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 63",  ' ', 156175, 160775, "PORT-PUBLIC",     ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 64",  ' ', 156225, 160825, "PORT-PUBLIC",     ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 65",  ' ', 156275, 160875, "PORT-PUBLIC",     ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 66",  ' ', 156325, 160925, "PORT-PUBLIC",     ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 67",  ' ', 156375, 156375, "PORT-INTERSHIP",  ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 68",  ' ', 156425, 156425, "PORT",            ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 69",  ' ', 156475, 156475, "PORT-INTERSHIP",  ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 70",  ' ', 156525, 156525, "DSC",             ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 71",  ' ', 156575, 156575, "",                ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 72",  ' ', 156625, 156625, "INTERSHIP",       ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 73",  ' ', 156675, 156675, "PORT-INTERSHIP",  ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 74",  ' ', 156725, 156725, "PORT",            ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 75",  ' ', 156775, 156775, "PORT",            ChannelInfo.PowerType.PWR_1W,  false),
                new ChannelInfo(" 76",  ' ', 156825, 156825, "PORT",            ChannelInfo.PowerType.PWR_1W,  false),
                new ChannelInfo(" 77",  ' ', 156875, 156875, "PORT-INTERSHIP",  ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 78",  ' ', 156925, 161525, "PORT-INTERSHIP",  ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("1078", ' ', 156925, 156925, "PORT",            ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("2078", ' ',     -1, 161525, "PORT",            ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 79",  ' ', 156975, 161575, "PORT-PUBLIC",     ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("1079", ' ', 156975, 156975, "PORT",            ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("2079", ' ',     -1, 161575, "PORT",            ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 80",  ' ', 157025, 161625, "",                ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 81",  ' ', 157075, 161675, "",                ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 82",  ' ', 157125, 161725, "",                ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 83",  ' ', 157175, 161775, "",                ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 84",  ' ', 157225, 161825, "",                ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 85",  ' ', 157275, 161875, "",                ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 86",  ' ', 157325, 161925, "",                ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 87",  ' ', 157375, 157375, "PORT",            ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 88",  ' ', 157425, 157425, "PORT",            ChannelInfo.PowerType.PWR_25W, false)
            }),
            new RegionChannelInfo("CANADA", new List<ChannelInfo>
            {
                new ChannelInfo("  1", ' ',  156050, 160650, "DUPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  2", ' ',  156100, 160700, "DUPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  3", ' ',  156150, 160750, "DUPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  4", 'A', 156200, 156200, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  5", 'A', 156250, 156250, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  6", ' ',  156300, 156300, "SIMPLEX",        ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  7", 'A', 156350, 156350, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  8", ' ',  156400, 156400, "SIMPLEX",        ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  9", ' ',  156450, 156450, "SIMPLEX",        ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 10", ' ',  156500, 156500, "SIMPLEX",        ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 11", ' ',  156550, 156550, "SIMPLEX",        ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 12", ' ',  156600, 156600, "SIMPLEX",        ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 13", ' ',  156650, 156650, "SIMPLEX",        ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 14", ' ',  156700, 156700, "SIMPLEX",        ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 15", ' ',  156750, 156750, "SIMPLEX",        ChannelInfo.PowerType.PWR_1W,  false),
                new ChannelInfo(" 16", ' ',  156800, 156800, "DISTRESS/CALL",  ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 17", ' ',  156850, 156850, "SIMPLEX",        ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 18", 'A', 156900, 156900, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 19", 'A', 156950, 156950, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 20", ' ',  157000, 161600, "DUPLEX",         ChannelInfo.PowerType.PWR_1W,  false),
                new ChannelInfo(" 21", 'A', 157050, 157050, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 21", 'B',    -1, 161650, "SIMPLEX",          ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 22", 'A', 157100, 157100, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 23", ' ',  157150, 161750, "DUPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 24", ' ',  157200, 161800, "DUPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 25", ' ',  157250, 161850, "DUPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 26", ' ',  157300, 161900, "DUPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 27", ' ',  157350, 161950, "DUPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 28", ' ',  157400, 162000, "DUPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 60", ' ',  156025, 160625, "DUPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 61", 'A', 156075, 156075, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 62", 'A', 156125, 156125, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 63", 'A', 156175, 156175, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 64", ' ',  156225, 160825, "DUPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 64", 'A', 156225, 156225, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 65", 'A', 156275, 156275, "SIMPLEX",         ChannelInfo.PowerType.PWR_1W,  false),
                new ChannelInfo(" 66", 'A', 156325, 156325, "SIMPLEX",         ChannelInfo.PowerType.PWR_1W,  false),
                new ChannelInfo(" 67", ' ',  156375, 156375, "SIMPLEX",        ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 68", ' ',  156425, 156425, "SIMPLEX",        ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 69", ' ',  156475, 156475, "SIMPLEX",        ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 70", ' ',  156525, 156525, "DSC",            ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 71", ' ', 156575, 156575, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 72", ' ', 156625, 156625, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 73", ' ', 156675, 156675, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 74", ' ', 156725, 156725, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 75", ' ', 156775, 156775, "SIMPLEX",         ChannelInfo.PowerType.PWR_1W,  false),
                new ChannelInfo(" 76", ' ', 156825, 156825, "SIMPLEX",         ChannelInfo.PowerType.PWR_1W,  false),
                new ChannelInfo(" 77", ' ', 156875, 156875, "SIMPLEX",         ChannelInfo.PowerType.PWR_1W,  false),
                new ChannelInfo(" 78", 'A', 156925, 156925, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 79", 'A', 156975, 156975, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 80", 'A', 157025, 157025, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 81", 'A', 157075, 157075, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 82", 'A', 157125, 157125, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 83", 'A', 157175, 157175, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 83", 'B',    -1, 161775, "SIMPLEX",          ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 84", ' ', 157225, 161825, "DUPLEX",          ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 85", ' ', 157275, 161875, "DUPLEX",          ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 86", ' ', 157325, 161925, "DUPLEX",          ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 87", ' ', 157375, 157375, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo(" 88", ' ', 157425, 157425, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  1", 'W',    -1, 162550, "SIMPLEX",          ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  2", 'W',    -1, 162400, "SIMPLEX",          ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  3", 'W',    -1, 162475, "SIMPLEX",          ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  4", 'W',    -1, 162425, "SIMPLEX",          ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  5", 'W',    -1, 162450, "SIMPLEX",          ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  6", 'W',    -1, 162500, "SIMPLEX",          ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  7", 'W',    -1, 162525, "SIMPLEX",          ChannelInfo.PowerType.PWR_25W, false)
            }),
            new RegionChannelInfo("USA", new List<ChannelInfo>
            {
                new ChannelInfo("   1", 'A', 156050, 156050, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("   5", 'A', 156250, 156250, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("   6", ' ',  156300, 156300, "SIMPLEX",        ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("   7", 'A', 156350, 156350, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("   8", ' ',  156400, 156400, "SIMPLEX",        ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("   9", ' ',  156450, 156450, "SIMPLEX",        ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  10", ' ',  156500, 156500, "SIMPLEX",        ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  11", ' ',  156550, 156550, "SIMPLEX",        ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  12", ' ',  156600, 156600, "SIMPLEX",        ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  13", ' ',  156650, 156650, "SIMPLEX",        ChannelInfo.PowerType.PWR_1W,  false),
                new ChannelInfo("  14", ' ',  156700, 156700, "SIMPLEX",        ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  15", ' ',    -1, 156750, "SIMPLEX",          ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  16", ' ',  156800, 156800, "DISTRESS/CALL",  ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  17", ' ',  156850, 156850, "SIMPLEX",        ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  18", 'A', 156900, 156900, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  19", 'A', 156950, 156950, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  20", ' ',  157000, 161600, "DUPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  20", 'A', 157000, 157000, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  21", 'A', 157050, 157050, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  22", 'A', 157100, 157100, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  23", 'A', 157150, 157150, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  24", ' ',  157200, 161800, "DUPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  25", ' ',  157250, 161850, "DUPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  26", ' ',  157300, 161900, "DUPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  27", ' ',  157350, 161950, "DUPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  28", ' ',  157400, 162000, "DUPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  63", 'A', 156175, 156175, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  65", 'A', 156275, 156275, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  66", 'A', 156325, 156325, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  67", ' ',  156375, 156375, "SIMPLEX",        ChannelInfo.PowerType.PWR_1W,  false),
                new ChannelInfo("  68", ' ',  156425, 156425, "SIMPLEX",        ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  69", ' ',  156475, 156475, "SIMPLEX",        ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  70", ' ',  156525, 156525, "DSC",            ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  71", ' ',  156575, 156575, "SIMPLEX",        ChannelInfo.PowerType.PWR_1W,  false),
                new ChannelInfo("  72", ' ',  156625, 156625, "SIMPLEX",        ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  73", ' ',  156675, 156675, "SIMPLEX",        ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  74", ' ',  156725, 156725, "SIMPLEX",        ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  77", ' ',  156875, 156875, "SIMPLEX",        ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  78", 'A', 156925, 156925, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  79", 'A', 156975, 156975, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  80", 'A', 157025, 157025, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  81", 'A', 157075, 157075, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  82", 'A', 157125, 157125, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  83", 'A', 157175, 157175, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  84", ' ',  157225, 161825, "DUPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  85", ' ',  157275, 161875, "DUPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  86", ' ',  157325, 161925, "DUPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  87", ' ',  157375, 157375, "SIMPLEX",        ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  88", ' ',  157425, 157425, "SIMPLEX",        ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  1", 'W',      -1, 162550, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  2", 'W',      -1, 162400, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  3", 'W',      -1, 162475, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  4", 'W',      -1, 162425, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  5", 'W',      -1, 162450, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  6", 'W',      -1, 162500, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  7", 'W',      -1, 162525, "SIMPLEX",         ChannelInfo.PowerType.PWR_25W, false)
            }),
            new RegionChannelInfo("IWW", new List<ChannelInfo>
            {
                new ChannelInfo("   1", ' ', 156050, 160650, "PORT-PUBLIC",    ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("   2", ' ', 156100, 160700, "PORT-PUBLIC",    ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("   3", ' ', 156150, 160750, "PORT-PUBLIC",    ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("   4", ' ', 156200, 160800, "PORT-PUBLIC",    ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("   5", ' ', 156250, 160850, "PORT-PUBLIC",    ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("   6", ' ', 156300, 156300, "INTERSHIP",      ChannelInfo.PowerType.PWR_1W,  false),
                new ChannelInfo("   7", ' ', 156350, 160950, "PORT-PUBLIC",    ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("   8", ' ', 156400, 156400, "INTERSHIP",      ChannelInfo.PowerType.PWR_1W,  false),
                new ChannelInfo("   9", ' ', 156450, 156450, "PORT-INTERSHIP", ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  10", ' ', 156500, 156500, "PORT-INTERSHIP", ChannelInfo.PowerType.PWR_1W,  false),
                new ChannelInfo("  11", ' ', 156550, 156550, "PORT",           ChannelInfo.PowerType.PWR_1W,  false),
                new ChannelInfo("  12", ' ', 156600, 156600, "PORT",           ChannelInfo.PowerType.PWR_1W,  false),
                new ChannelInfo("  13", ' ', 156650, 156650, "PORT-INTERSHIP", ChannelInfo.PowerType.PWR_1W,  false),
                new ChannelInfo("  14", ' ', 156700, 156700, "PORT",           ChannelInfo.PowerType.PWR_1W,  false),
                new ChannelInfo("  15", ' ', 156750, 156750, "PORT-INTERSHIP", ChannelInfo.PowerType.PWR_1W,  false),
                new ChannelInfo("  16", ' ', 156800, 156800, "DISTRESS/CALL",  ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  17", ' ', 156850, 156850, "PORT-INTERSHIP", ChannelInfo.PowerType.PWR_1W,  false),
                new ChannelInfo("  18", ' ', 156900, 161500, "PORT-PUBLIC",    ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  19", ' ', 156950, 161550, "PORT-PUBLIC",    ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("1019", ' ', 156950, 156950, "PORT",           ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("2019", ' ',    -1, 161550, "PORT",            ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  20", ' ', 157000, 161600, "PORT-PUBLIC",    ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("1020", ' ', 157000, 157000, "PORT",           ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("2020", ' ',    -1, 161600, "PORT",            ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  27", ' ', 157350, 161950, "PORT-PUBLIC",    ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("1027", ' ', 157350, 157350, "PORT",           ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  28", ' ', 157400, 162000, "PORT-PUBLIC",    ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("1028", ' ', 157400, 157400, "PORT",           ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  60", ' ', 156025, 160625, "PORT-PUBLIC",    ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  61", ' ', 156075, 160675, "PORT-PUBLIC",    ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  62", ' ', 156125, 160725, "PORT-PUBLIC",    ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  63", ' ', 156175, 160775, "PORT-PUBLIC",    ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  64", ' ', 156225, 160825, "PORT-PUBLIC",    ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  65", ' ', 156275, 160875, "PORT-PUBLIC",    ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  66", ' ', 156325, 160925, "PORT-PUBLIC",    ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  67", ' ', 156375, 156375, "PORT-INTERSHIP", ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  68", ' ', 156425, 156425, "PORT",           ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  69", ' ', 156475, 156475, "PORT-INTERSHIP", ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  70", ' ', 156525, 156525, "DSC",            ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  71", ' ', 156575, 156575, "",               ChannelInfo.PowerType.PWR_1W,  false),
                new ChannelInfo("  72", ' ', 156625, 156625, "INTERSHIP",      ChannelInfo.PowerType.PWR_1W,  false),
                new ChannelInfo("  73", ' ', 156675, 156675, "PORT-INTERSHIP", ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  74", ' ', 156725, 156725, "PORT",           ChannelInfo.PowerType.PWR_1W,  false),
                new ChannelInfo("  75", ' ', 156775, 156775, "PORT",           ChannelInfo.PowerType.PWR_1W,  false),
                new ChannelInfo("  76", ' ', 156825, 156825, "PORT",           ChannelInfo.PowerType.PWR_1W,  false),
                new ChannelInfo("  77", ' ', 156875, 156875, "INTERSHIP",      ChannelInfo.PowerType.PWR_1W,  false),
                new ChannelInfo("  78", ' ', 156925, 161525, "PORT-INTERSHIP", ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("1078", ' ', 156925, 156925, "PORT",           ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("2078", ' ',    -1, 161525, "PORT",            ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  79", ' ', 156975, 161575, "PORT-PUBLIC",    ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("1079", ' ', 156975, 156975, "PORT",           ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("2079", ' ',    -1, 161575, "PORT",            ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  87", ' ', 157375, 157375, "PORT",           ChannelInfo.PowerType.PWR_25W, false),
                new ChannelInfo("  88", ' ', 157425, 157425, "PORT",           ChannelInfo.PowerType.PWR_25W, false),
            }),
        };
    }
}
