﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Security.Principal.Windows</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle">
      <summary>[ESSENTIEL POUR LA SÉCURITÉ] Fournit un handle sécurisé à un thread Windows ou à un jeton d'accès de processus.Pour plus d’informations, consultez Jetons d'accès</summary>
    </member>
    <member name="M:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle.#ctor(System.IntPtr)">
      <summary>[ESSENTIEL POUR LA SÉCURITÉ] Initialise une nouvelle instance de la classe <see cref="T:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle" />.</summary>
      <param name="handle">Objet <see cref="T:System.IntPtr" /> qui représente le handle préexistant à utiliser.L'utilisation de <see cref="F:System.IntPtr.Zero" /> retourne un handle non valide.</param>
    </member>
    <member name="P:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle.InvalidHandle">
      <summary>[ESSENTIEL POUR LA SÉCURITÉ] Retourne un handle non valide en instanciant un objet <see cref="T:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle" /> avec <see cref="F:System.IntPtr.Zero" />.</summary>
      <returns>Retourne un objet <see cref="T:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle" />.</returns>
    </member>
    <member name="P:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle.IsInvalid">
      <summary>[ESSENTIEL POUR LA SÉCURITÉ] Obtient une valeur qui indique si le handle n'est pas valide.</summary>
      <returns>true si le handle n'est pas valide ; sinon false.</returns>
    </member>
    <member name="T:System.Security.Principal.IdentityNotMappedException">
      <summary>Représente une exception pour une entité de sécurité dont l'identité n'a pas pu être mappée à une identité connue.</summary>
    </member>
    <member name="M:System.Security.Principal.IdentityNotMappedException.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.Principal.IdentityNotMappedException" />.</summary>
    </member>
    <member name="M:System.Security.Principal.IdentityNotMappedException.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.Principal.IdentityNotMappedException" /> en utilisant le message d'erreur spécifié.</summary>
      <param name="message">Message d'erreur indiquant la raison de l'exception.</param>
    </member>
    <member name="M:System.Security.Principal.IdentityNotMappedException.#ctor(System.String,System.Exception)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.Principal.IdentityNotMappedException" /> en utilisant le message d'erreur et l'exception interne spécifiés.</summary>
      <param name="message">Message d'erreur indiquant la raison de l'exception.</param>
      <param name="inner">Exception qui constitue la cause de l'exception actuelle.Si <paramref name="inner" /> n'est pas null, l'exception en cours est levée dans un bloc catch qui gère l'exception interne.</param>
    </member>
    <member name="P:System.Security.Principal.IdentityNotMappedException.UnmappedIdentities">
      <summary>Représente la collection d'identités non mappées pour une exception <see cref="T:System.Security.Principal.IdentityNotMappedException" />.</summary>
      <returns>Collection d'identités non mappées.</returns>
    </member>
    <member name="T:System.Security.Principal.IdentityReference">
      <summary>Représente une identité et constitue la classe de base des classes <see cref="T:System.Security.Principal.NTAccount" /> et <see cref="T:System.Security.Principal.SecurityIdentifier" />.Cette classe ne fournit pas de constructeur public, et ne peut donc pas être héritée.</summary>
    </member>
    <member name="M:System.Security.Principal.IdentityReference.Equals(System.Object)">
      <summary>Retourne une valeur qui indique si l'objet spécifié équivaut à cette instance de la classe <see cref="T:System.Security.Principal.IdentityReference" />.</summary>
      <returns>true si <paramref name="o" /> est un objet avec le même type sous-jacent et la même valeur que cette instance <see cref="T:System.Security.Principal.IdentityReference" /> ; sinon false.</returns>
      <param name="o">Un objet à comparer avec cette instance <see cref="T:System.Security.Principal.IdentityReference" /> ou une référence null.</param>
    </member>
    <member name="M:System.Security.Principal.IdentityReference.GetHashCode">
      <summary>Sert de fonction de hachage pour la <see cref="T:System.Security.Principal.IdentityReference" />.<see cref="M:System.Security.Principal.IdentityReference.GetHashCode" /> peut être utilisé dans des algorithmes de hachage et des structures de données telles qu'une table de hachage.</summary>
      <returns>Code de hachage de cet objet <see cref="T:System.Security.Principal.IdentityReference" />.</returns>
    </member>
    <member name="M:System.Security.Principal.IdentityReference.IsValidTargetType(System.Type)">
      <summary>Retourne une valeur qui indique si le type spécifié est un type de traduction valide pour la classe <see cref="T:System.Security.Principal.IdentityReference" />.</summary>
      <returns>true si <paramref name="targetType" /> est un type de traduction valide pour la classe <see cref="T:System.Security.Principal.IdentityReference" /> ; sinon false.</returns>
      <param name="targetType">Type interrogé quant à sa validité pour servir de conversion de <see cref="T:System.Security.Principal.IdentityReference" />.Les types cibles suivants sont valides :<see cref="T:System.Security.Principal.NTAccount" /><see cref="T:System.Security.Principal.SecurityIdentifier" /></param>
    </member>
    <member name="M:System.Security.Principal.IdentityReference.op_Equality(System.Security.Principal.IdentityReference,System.Security.Principal.IdentityReference)">
      <summary>Compare deux objets <see cref="T:System.Security.Principal.IdentityReference" /> pour déterminer s'ils sont égaux.Ils sont considérés égaux s'ils ont la même représentation de nom complet que celle retournée par la propriété <see cref="P:System.Security.Principal.IdentityReference.Value" /> ou s'ils sont tous les deux null.</summary>
      <returns>true si <paramref name="left" /> est égal à <paramref name="right" /> ; sinon, false.</returns>
      <param name="left">L'opérande <see cref="T:System.Security.Principal.IdentityReference" /> gauche à utiliser pour la comparaison d'égalité.Ce paramètre peut être null.</param>
      <param name="right">L'opérande <see cref="T:System.Security.Principal.IdentityReference" /> droit à utiliser pour la comparaison d'égalité.Ce paramètre peut être null.</param>
    </member>
    <member name="M:System.Security.Principal.IdentityReference.op_Inequality(System.Security.Principal.IdentityReference,System.Security.Principal.IdentityReference)">
      <summary>Compare deux objets <see cref="T:System.Security.Principal.IdentityReference" /> pour déterminer s'ils sont inégaux.Ils ne sont pas considérés égaux si leurs représentations de nom complet sont différentes de celle qui est retournée par la propriété <see cref="P:System.Security.Principal.IdentityReference.Value" /> ou si l'un des objets est null et que l'autre ne l'est pas.</summary>
      <returns>true si <paramref name="left" /> et <paramref name="right" /> ne sont pas égaux ; sinon, false.</returns>
      <param name="left">L'opérande <see cref="T:System.Security.Principal.IdentityReference" /> gauche à utiliser pour la comparaison d'inégalité.Ce paramètre peut être null.</param>
      <param name="right">Opérande <see cref="T:System.Security.Principal.IdentityReference" /> droit à utiliser pour la comparaison d'inégalité.Ce paramètre peut être null.</param>
    </member>
    <member name="M:System.Security.Principal.IdentityReference.ToString">
      <summary>Retourne la représentation sous forme de chaîne de l'identité représentée par l'objet <see cref="T:System.Security.Principal.IdentityReference" />.</summary>
      <returns>L'identité au format de chaîne.</returns>
    </member>
    <member name="M:System.Security.Principal.IdentityReference.Translate(System.Type)">
      <summary>Traduit le nom du compte représenté par l'objet <see cref="T:System.Security.Principal.IdentityReference" /> dans un autre type dérivé de <see cref="T:System.Security.Principal.IdentityReference" />.</summary>
      <returns>Identité convertie.</returns>
      <param name="targetType">Type cible pour la conversion de <see cref="T:System.Security.Principal.IdentityReference" />. </param>
    </member>
    <member name="P:System.Security.Principal.IdentityReference.Value">
      <summary>Obtient la valeur de chaîne de l'identité représentée par l'objet <see cref="T:System.Security.Principal.IdentityReference" />.</summary>
      <returns>Valeur de chaîne de l'identité représentée par l'objet <see cref="T:System.Security.Principal.IdentityReference" />.</returns>
    </member>
    <member name="T:System.Security.Principal.IdentityReferenceCollection">
      <summary>Représente une collection d'objets <see cref="T:System.Security.Principal.IdentityReference" /> et fournit un mode de conversion des jeux d'objets dérivés de <see cref="T:System.Security.Principal.IdentityReference" /> en types dérivés de <see cref="T:System.Security.Principal.IdentityReference" />. </summary>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> sans aucun élément dans la collection.</summary>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.#ctor(System.Int32)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> en utilisant la taille initiale spécifiée.</summary>
      <param name="capacity">Nombre initial d'éléments dans la collection.La valeur de <paramref name="capacity" /> n'est qu'une indication ; il ne s'agit pas nécessairement du nombre maximal d'éléments créés.</param>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.Add(System.Security.Principal.IdentityReference)">
      <summary>Ajoute un objet <see cref="T:System.Security.Principal.IdentityReference" /> à la collection <see cref="T:System.Security.Principal.IdentityReferenceCollection" />.</summary>
      <param name="identity">Objet <see cref="T:System.Security.Principal.IdentityReference" /> à ajouter à la collection.</param>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.Clear">
      <summary>Efface tous les objets <see cref="T:System.Security.Principal.IdentityReference" /> de la collection <see cref="T:System.Security.Principal.IdentityReferenceCollection" />.</summary>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.Contains(System.Security.Principal.IdentityReference)">
      <summary>Indique si la collection <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> contient l'objet <see cref="T:System.Security.Principal.IdentityReference" /> spécifié.</summary>
      <returns>true si la collection contient l'objet spécifié.</returns>
      <param name="identity">Objet <see cref="T:System.Security.Principal.IdentityReference" /> à vérifier.</param>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.CopyTo(System.Security.Principal.IdentityReference[],System.Int32)">
      <summary>Copie la collection <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> dans un tableau <see cref="T:System.Security.Principal.IdentityReferenceCollection" />, en commençant à l'index spécifié.</summary>
      <param name="array">Objet tableau <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> dans lequel la collection <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> doit être copiée.</param>
      <param name="offset">Index de base zéro dans le <paramref name="array" /> où la collection <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> doit être copiée.</param>
    </member>
    <member name="P:System.Security.Principal.IdentityReferenceCollection.Count">
      <summary>Obtient le nombre d'éléments contenus dans la collection <see cref="T:System.Security.Principal.IdentityReferenceCollection" />.</summary>
      <returns>Nombre d'objets <see cref="T:System.Security.Principal.IdentityReference" /> présents dans la collection <see cref="T:System.Security.Principal.IdentityReferenceCollection" />.</returns>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.GetEnumerator">
      <summary>Obtient un énumérateur qui peut être utilisé pour itérer au sein de la collection <see cref="T:System.Security.Principal.IdentityReferenceCollection" />.</summary>
      <returns>Énumérateur de la collection <see cref="T:System.Security.Principal.IdentityReferenceCollection" />.</returns>
    </member>
    <member name="P:System.Security.Principal.IdentityReferenceCollection.Item(System.Int32)">
      <summary>Obtient ou définit le nœud à l'index spécifié de la collection <see cref="T:System.Security.Principal.IdentityReferenceCollection" />.</summary>
      <returns>
        <see cref="T:System.Security.Principal.IdentityReference" /> à l'index spécifié dans la collection.Si <paramref name="index" /> est supérieur ou égal au nombre de nœuds dans la collection, la valeur de retour est null.</returns>
      <param name="index">Index de base zéro dans la collection <see cref="T:System.Security.Principal.IdentityReferenceCollection" />.</param>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.Remove(System.Security.Principal.IdentityReference)">
      <summary>Supprime l'objet <see cref="T:System.Security.Principal.IdentityReference" /> spécifié de la collection.</summary>
      <returns>true si l'objet spécifié a été supprimé de la collection.</returns>
      <param name="identity">Objet <see cref="T:System.Security.Principal.IdentityReference" /> à supprimer.</param>
    </member>
    <member name="P:System.Security.Principal.IdentityReferenceCollection.System#Collections#Generic#ICollection{T}#IsReadOnly"></member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Obtient un énumérateur qui peut être utilisé pour itérer au sein de la collection <see cref="T:System.Security.Principal.IdentityReferenceCollection" />.</summary>
      <returns>Énumérateur de la collection <see cref="T:System.Security.Principal.IdentityReferenceCollection" />.</returns>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.Translate(System.Type)">
      <summary>Convertit les objets de la collection en type spécifié.Appeler cette méthode revient à appeler <see cref="M:System.Security.Principal.IdentityReferenceCollection.Translate(System.Type,System.Boolean)" /> avec la valeur false affectée au second paramètre, ce qui signifie que les exceptions ne seront pas levées pour les éléments dont la conversion échoue.</summary>
      <returns>Une collection <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> qui représente le contenu converti de la collection d'origine.</returns>
      <param name="targetType">Type dans lequel les éléments de la collection sont convertis.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.Translate(System.Type,System.Boolean)">
      <summary>Convertit les objets de la collection en type spécifié et utilise la tolérance de panne spécifiée pour gérer ou ignorer des erreurs associées à un type ne disposant pas de mappage de conversion.</summary>
      <returns>Une collection <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> qui représente le contenu converti de la collection d'origine.</returns>
      <param name="targetType">Type dans lequel les éléments de la collection sont convertis.</param>
      <param name="forceSuccess">Valeur Boolean déterminant la façon dont les erreurs de conversion sont gérées.Si <paramref name="forceSuccess" /> a la valeur true, les erreurs de conversion dues à un mappage introuvable pour la traduction entraînent l'échec de la conversion et la levée d'exceptions.Si <paramref name="forceSuccess" /> a la valeur false, les types dont la conversion a échoué en raison d'un mappage introuvable pour la traduction sont copiés sans être convertis dans la collection qui est retournée.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="T:System.Security.Principal.NTAccount">
      <summary>Représente un utilisateur ou un compte de groupe.</summary>
    </member>
    <member name="M:System.Security.Principal.NTAccount.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.Principal.NTAccount" /> en utilisant le nom spécifié.</summary>
      <param name="name">Nom utilisé pour créer l'objet <see cref="T:System.Security.Principal.NTAccount" />.Ce paramètre ne peut pas avoir la valeur null ou être une chaîne vide.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> est une chaîne vide.ou<paramref name="name" /> est trop longue.</exception>
    </member>
    <member name="M:System.Security.Principal.NTAccount.#ctor(System.String,System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.Principal.NTAccount" /> en utilisant le nom de domaine et le nom de compte spécifiés. </summary>
      <param name="domainName">Nom du domaine.Ce paramètre peut avoir la valeur null ou être une chaîne vide.Les noms de domaines qui correspondent à des valeurs null sont traités comme une chaîne vide.</param>
      <param name="accountName">Nom du compte.Ce paramètre ne peut pas avoir la valeur null ou être une chaîne vide.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="accountName" /> a la valeur null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="accountName" /> est une chaîne vide.ou<paramref name="accountName" /> est trop longue.ou<paramref name="domainName" /> est trop longue.</exception>
    </member>
    <member name="M:System.Security.Principal.NTAccount.Equals(System.Object)">
      <summary>Retourne une valeur indiquant si cet objet <see cref="T:System.Security.Principal.NTAccount" /> équivaut à un objet spécifié.</summary>
      <returns>true si <paramref name="o" /> est un objet avec le même type sous-jacent et la même valeur que cet objet <see cref="T:System.Security.Principal.NTAccount" /> ; sinon false.</returns>
      <param name="o">Objet à comparer à cet objet <see cref="T:System.Security.Principal.NTAccount" /> ou null.</param>
    </member>
    <member name="M:System.Security.Principal.NTAccount.GetHashCode">
      <summary>Sert de fonction de hachage à l'objet <see cref="T:System.Security.Principal.NTAccount" /> actif.La méthode <see cref="M:System.Security.Principal.NTAccount.GetHashCode" /> peut être utilisée dans des algorithmes de hachage et des structures de données telles qu'une table de hachage.</summary>
      <returns>Valeur de hachage pour l'objet <see cref="T:System.Security.Principal.NTAccount" /> en cours.</returns>
    </member>
    <member name="M:System.Security.Principal.NTAccount.IsValidTargetType(System.Type)">
      <summary>Retourne une valeur qui indique si le type spécifié est un type de traduction valide pour la classe <see cref="T:System.Security.Principal.NTAccount" />.</summary>
      <returns>true si <paramref name="targetType" /> est un type de traduction valide pour la classe <see cref="T:System.Security.Principal.NTAccount" /> ; sinon false.</returns>
      <param name="targetType">Type interrogé quant à sa validité pour servir de conversion de <see cref="T:System.Security.Principal.NTAccount" />.Les types cibles suivants sont valides :- <see cref="T:System.Security.Principal.NTAccount" />- <see cref="T:System.Security.Principal.SecurityIdentifier" /></param>
    </member>
    <member name="M:System.Security.Principal.NTAccount.op_Equality(System.Security.Principal.NTAccount,System.Security.Principal.NTAccount)">
      <summary>Compare deux objets <see cref="T:System.Security.Principal.NTAccount" /> pour déterminer s'ils sont égaux.Ils sont considérés égaux s'ils ont la même représentation de nom complet que celle retournée par la propriété <see cref="P:System.Security.Principal.NTAccount.Value" /> ou s'ils sont tous les deux null.</summary>
      <returns>true si <paramref name="left" /> est égal à <paramref name="right" /> ; sinon false.</returns>
      <param name="left">L'opérande gauche à utiliser pour la comparaison d'égalité.Ce paramètre peut être null.</param>
      <param name="right">L'opérande droit à utiliser pour la comparaison d'égalité.Ce paramètre peut être null.</param>
    </member>
    <member name="M:System.Security.Principal.NTAccount.op_Inequality(System.Security.Principal.NTAccount,System.Security.Principal.NTAccount)">
      <summary>Compare deux objets <see cref="T:System.Security.Principal.NTAccount" /> pour déterminer s'ils sont inégaux.Ils ne sont pas considérés égaux si leurs représentations de nom complet sont différentes de celle qui est retournée par la propriété <see cref="P:System.Security.Principal.NTAccount.Value" /> ou si l'un des objets est null et que l'autre ne l'est pas.</summary>
      <returns>true si <paramref name="left" /> et <paramref name="right" /> ne sont pas égaux ; sinon false.</returns>
      <param name="left">L'opérande gauche à utiliser pour la comparaison d'inégalité.Ce paramètre peut être null.</param>
      <param name="right">Opérande droit à utiliser pour la comparaison d'inégalité.Ce paramètre peut être null.</param>
    </member>
    <member name="M:System.Security.Principal.NTAccount.ToString">
      <summary>Retourne le nom du compte, au format Domaine\Compte, pour le compte représenté par l'objet <see cref="T:System.Security.Principal.NTAccount" />.</summary>
      <returns>Nom du compte au format Domaine\Compte.</returns>
    </member>
    <member name="M:System.Security.Principal.NTAccount.Translate(System.Type)">
      <summary>Traduit le nom du compte représenté par l'objet <see cref="T:System.Security.Principal.NTAccount" /> dans un autre type dérivé de <see cref="T:System.Security.Principal.IdentityReference" />.</summary>
      <returns>Identité convertie.</returns>
      <param name="targetType">Type cible pour la conversion de <see cref="T:System.Security.Principal.NTAccount" />.Le type cible doit être un type considéré valide par la méthode <see cref="M:System.Security.Principal.NTAccount.IsValidTargetType(System.Type)" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="targetType " />est null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="targetType " />n'est pas un type <see cref="T:System.Security.Principal.IdentityReference" />.</exception>
      <exception cref="T:System.Security.Principal.IdentityNotMappedException">Impossible de traduire certaines ou toutes les références d'identité.</exception>
      <exception cref="T:System.SystemException">Le nom du compte source est trop long.ouUn code d'erreur Win32 a été retourné.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="P:System.Security.Principal.NTAccount.Value">
      <summary>Retourne une représentation sous forme de chaîne en majuscules de cet objet <see cref="T:System.Security.Principal.NTAccount" />.</summary>
      <returns>Représentation sous forme de chaîne en majuscules de cet objet <see cref="T:System.Security.Principal.NTAccount" />.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="T:System.Security.Principal.SecurityIdentifier">
      <summary>Représente un identificateur de sécurité (SID) et fournit des opérations de marshaling et de comparaison des SID.</summary>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.#ctor(System.Byte[],System.Int32)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.Principal.SecurityIdentifier" /> en utilisant une représentation binaire spécifiée d'un identificateur de sécurité (SID).</summary>
      <param name="binaryForm">Tableau d'octets qui représente le SID.</param>
      <param name="offset">Offset d'octet à utiliser comme index de départ dans <paramref name="binaryForm" />. </param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.#ctor(System.IntPtr)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.Principal.SecurityIdentifier" /> en utilisant un entier qui représente la forme binaire d'un identificateur de sécurité (SID).</summary>
      <param name="binaryForm">Entier qui représente la forme binaire d'un SID.</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.#ctor(System.Security.Principal.WellKnownSidType,System.Security.Principal.SecurityIdentifier)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.Principal.SecurityIdentifier" /> en utilisant le type d'identificateur de sécurité (SID) connu spécifié et le SID du domaine.</summary>
      <param name="sidType">Valeur de l'énumération.Cette valeur ne doit pas être <see cref="F:System.Security.Principal.WellKnownSidType.LogonIdsSid" />.</param>
      <param name="domainSid">SID du domaine.Cette valeur est requise pour les valeurs <see cref="T:System.Security.Principal.WellKnownSidType" /> suivantes.Ce paramètre est ignoré pour toutes les autres valeurs <see cref="T:System.Security.Principal.WellKnownSidType" />.- <see cref="F:System.Security.Principal.WellKnownSidType.AccountAdministratorSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountGuestSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountKrbtgtSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountDomainAdminsSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountDomainUsersSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountDomainGuestsSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountComputersSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountControllersSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountCertAdminsSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountSchemaAdminsSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountEnterpriseAdminsSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountPolicyAdminsSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountRasAndIasServersSid" /></param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.Principal.SecurityIdentifier" /> en utilisant l'identificateur de sécurité (SID) spécifié au format SDDL (Security Descriptor Definition Language).</summary>
      <param name="sddlForm">Chaîne SDDL pour le SID permettant de créer l'objet <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
    </member>
    <member name="P:System.Security.Principal.SecurityIdentifier.AccountDomainSid">
      <summary>Retourne la partie de l'identificateur de sécurité (SID) du domaine de compte du SID représenté par l'objet <see cref="T:System.Security.Principal.SecurityIdentifier" /> si le SID représente un SID de compte Windows.Si le SID ne représente pas un SID de compte Windows, cette propriété retourne <see cref="T:System.ArgumentNullException" />.</summary>
      <returns>Partie du SID du domaine de compte du SID représenté par l'objet <see cref="T:System.Security.Principal.SecurityIdentifier" /> si le SID représente un SID de compte Windows ; sinon, retourne <see cref="T:System.ArgumentNullException" />.</returns>
    </member>
    <member name="P:System.Security.Principal.SecurityIdentifier.BinaryLength">
      <summary>Retourne la longueur, en octets, de l'identificateur de sécurité (SID) représenté par l'objet <see cref="T:System.Security.Principal.SecurityIdentifier" />.</summary>
      <returns>Longueur, en octets, du SID représenté par l'objet <see cref="T:System.Security.Principal.SecurityIdentifier" />.</returns>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.CompareTo(System.Security.Principal.SecurityIdentifier)">
      <summary>Compare l'objet <see cref="T:System.Security.Principal.SecurityIdentifier" /> en cours à l'objet <see cref="T:System.Security.Principal.SecurityIdentifier" /> spécifié.</summary>
      <returns>Nombre signé indiquant les valeurs relatives de cette instance et <paramref name="sid" />.Valeur de retour Description  Inférieur à zéro Cette instance est inférieure à <paramref name="sid" />. Zéro Cette instance est égale à <paramref name="sid" />. Supérieure à zéro Cette instance est supérieure à <paramref name="sid" />. </returns>
      <param name="sid">Objet à comparer avec l'objet actif.</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.Equals(System.Object)">
      <summary>Retourne une valeur indiquant si cet objet <see cref="T:System.Security.Principal.SecurityIdentifier" /> équivaut à un objet spécifié.</summary>
      <returns>true si <paramref name="o" /> est un objet avec le même type sous-jacent et la même valeur que cet objet <see cref="T:System.Security.Principal.SecurityIdentifier" /> ; sinon false.</returns>
      <param name="o">Objet à comparer à cet objet <see cref="T:System.Security.Principal.SecurityIdentifier" /> ou null.</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.Equals(System.Security.Principal.SecurityIdentifier)">
      <summary>Indique si l'objet <see cref="T:System.Security.Principal.SecurityIdentifier" /> spécifié est égal à l'objet <see cref="T:System.Security.Principal.SecurityIdentifier" /> en cours.</summary>
      <returns>true si la valeur de <paramref name="sid" /> est égale à la valeur de l'objet <see cref="T:System.Security.Principal.SecurityIdentifier" /> en cours.</returns>
      <param name="sid">Objet à comparer avec l'objet actif.</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Copie la représentation binaire de l'identificateur de sécurité (SID) spécifié représenté par la classe <see cref="T:System.Security.Principal.SecurityIdentifier" /> dans un tableau d'octets.</summary>
      <param name="binaryForm">Tableau d'octets devant recevoir le SID copié.</param>
      <param name="offset">Offset d'octet à utiliser comme index de départ dans <paramref name="binaryForm" />. </param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.GetHashCode">
      <summary>Sert de fonction de hachage à l'objet <see cref="T:System.Security.Principal.SecurityIdentifier" /> actif.La méthode <see cref="M:System.Security.Principal.SecurityIdentifier.GetHashCode" /> peut être utilisée dans des algorithmes de hachage et des structures de données telles qu'une table de hachage.</summary>
      <returns>Valeur de hachage pour l'objet <see cref="T:System.Security.Principal.SecurityIdentifier" /> actif.</returns>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.IsAccountSid">
      <summary>Retourne une valeur qui indique si l'identificateur de sécurité (SID) représenté par cet objet <see cref="T:System.Security.Principal.SecurityIdentifier" /> est un SID de compte Windows valide.</summary>
      <returns>true si le SID représenté par cet objet <see cref="T:System.Security.Principal.SecurityIdentifier" /> est un SID de compte Windows valide ; sinon, false.</returns>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.IsEqualDomainSid(System.Security.Principal.SecurityIdentifier)">
      <summary>Retourne une valeur qui indique si l'identificateur de sécurité (SID) représenté par cet objet <see cref="T:System.Security.Principal.SecurityIdentifier" /> provient du même domaine que le SID spécifié.</summary>
      <returns>true si le SID représenté par cet objet <see cref="T:System.Security.Principal.SecurityIdentifier" /> se trouve dans le même domaine que le SID <paramref name="sid" /> ; sinon, false.</returns>
      <param name="sid">SID à comparer à cet objet <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.IsValidTargetType(System.Type)">
      <summary>Retourne une valeur qui indique si le type spécifié est un type de traduction valide pour la classe <see cref="T:System.Security.Principal.SecurityIdentifier" />.</summary>
      <returns>true si <paramref name="targetType" /> est un type de traduction valide pour la classe <see cref="T:System.Security.Principal.SecurityIdentifier" /> ; sinon false.</returns>
      <param name="targetType">Type interrogé quant à sa validité pour servir de conversion de <see cref="T:System.Security.Principal.SecurityIdentifier" />.Les types cibles suivants sont valides :- <see cref="T:System.Security.Principal.NTAccount" />- <see cref="T:System.Security.Principal.SecurityIdentifier" /></param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.IsWellKnown(System.Security.Principal.WellKnownSidType)">
      <summary>Retourne une valeur qui indique si l'objet <see cref="T:System.Security.Principal.SecurityIdentifier" /> correspond au type d'identificateur de sécurité (SID) connu spécifié. </summary>
      <returns>true si <paramref name="type" /> est le type de SID pour l'objet <see cref="T:System.Security.Principal.SecurityIdentifier" /> ; sinon, false.</returns>
      <param name="type">Valeur à comparer avec l'objet <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
    </member>
    <member name="F:System.Security.Principal.SecurityIdentifier.MaxBinaryLength">
      <summary>Retourne la taille maximale, en octets, de la représentation binaire de l'identificateur de sécurité.</summary>
    </member>
    <member name="F:System.Security.Principal.SecurityIdentifier.MinBinaryLength">
      <summary>Retourne la taille minimale, en octets, de la représentation binaire de l'identificateur de sécurité.</summary>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.op_Equality(System.Security.Principal.SecurityIdentifier,System.Security.Principal.SecurityIdentifier)">
      <summary>Compare deux objets <see cref="T:System.Security.Principal.SecurityIdentifier" /> pour déterminer s'ils sont égaux.Ils sont considérés égaux s'ils ont la même représentation canonique que celle retournée par la propriété <see cref="P:System.Security.Principal.SecurityIdentifier.Value" /> ou s'ils sont tous les deux null.</summary>
      <returns>true si <paramref name="left" /> est égal à <paramref name="right" /> ; sinon, false.</returns>
      <param name="left">L'opérande gauche à utiliser pour la comparaison d'égalité.Ce paramètre peut être null.</param>
      <param name="right">L'opérande droit à utiliser pour la comparaison d'égalité.Ce paramètre peut être null.</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.op_Inequality(System.Security.Principal.SecurityIdentifier,System.Security.Principal.SecurityIdentifier)">
      <summary>Compare deux objets <see cref="T:System.Security.Principal.SecurityIdentifier" /> pour déterminer s'ils sont inégaux.Ils ne sont pas considérés égaux si leurs représentations de nom complet sont différentes de celle qui est retournée par la propriété <see cref="P:System.Security.Principal.SecurityIdentifier.Value" /> ou si l'un des objets est null et que l'autre ne l'est pas.</summary>
      <returns>true si <paramref name="left" /> et <paramref name="right" /> ne sont pas égaux ; sinon, false.</returns>
      <param name="left">L'opérande gauche à utiliser pour la comparaison d'inégalité.Ce paramètre peut être null.</param>
      <param name="right">Opérande droit à utiliser pour la comparaison d'inégalité.Ce paramètre peut être null.</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.ToString">
      <summary>Retourne l'identificateur de sécurité (SID), au format Security Descriptor Definition Language (SDDL), pour le compte représenté par l'objet <see cref="T:System.Security.Principal.SecurityIdentifier" />.S-1-5-9 est un exemple de format SDDL.</summary>
      <returns>SID, au format SDDL, pour le compte représenté par l'objet <see cref="T:System.Security.Principal.SecurityIdentifier" />.</returns>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.Translate(System.Type)">
      <summary>Traduit le nom du compte représenté par l'objet <see cref="T:System.Security.Principal.SecurityIdentifier" /> dans un autre type dérivé de <see cref="T:System.Security.Principal.IdentityReference" />.</summary>
      <returns>Identité convertie.</returns>
      <param name="targetType">Type cible pour la conversion de <see cref="T:System.Security.Principal.SecurityIdentifier" />.Le type cible doit être un type considéré valide par la méthode <see cref="M:System.Security.Principal.SecurityIdentifier.IsValidTargetType(System.Type)" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="targetType " />est null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="targetType " />n'est pas un type <see cref="T:System.Security.Principal.IdentityReference" />.</exception>
      <exception cref="T:System.Security.Principal.IdentityNotMappedException">Impossible de traduire certaines ou toutes les références d'identité.</exception>
      <exception cref="T:System.SystemException">Un code d'erreur Win32 a été retourné.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="P:System.Security.Principal.SecurityIdentifier.Value">
      <summary>Retourne une chaîne SDDL (Security Descriptor Definition Language) en majuscules pour l'identificateur de sécurité (SID) représenté par cet objet <see cref="T:System.Security.Principal.SecurityIdentifier" />.</summary>
      <returns>Chaîne SDDL en majuscules pour le SID représenté par l'objet <see cref="T:System.Security.Principal.SecurityIdentifier" />.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="T:System.Security.Principal.TokenAccessLevels">
      <summary>Définit les privilèges du compte d'utilisateur associé au jeton d'accès. </summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.AdjustDefault">
      <summary>L'utilisateur peut modifier le groupe principal, la liste de contrôle d'accès discrétionnaire (DACL) ou le propriétaire par défaut du jeton.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.AdjustGroups">
      <summary>L'utilisateur peut modifier les attributs des groupes dans le jeton.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.AdjustPrivileges">
      <summary>L'utilisateur peut activer ou désactiver les privilèges dans le jeton.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.AdjustSessionId">
      <summary>L'utilisateur peut ajuster l'identificateur de session du jeton.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.AllAccess">
      <summary>L'utilisateur dispose de tous les accès possibles au jeton.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.AssignPrimary">
      <summary>L'utilisateur peut attacher un jeton principal à un processus.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.Duplicate">
      <summary>L'utilisateur peut dupliquer le jeton.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.Impersonate">
      <summary>L'utilisateur peut emprunter l'identité d'un client.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.MaximumAllowed">
      <summary>Valeur maximale pouvant être assignée pour l'énumération <see cref="T:System.Security.Principal.TokenAccessLevels" />.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.Query">
      <summary>L'utilisateur peut interroger le jeton.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.QuerySource">
      <summary>L'utilisateur peut interroger la source du jeton.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.Read">
      <summary>L'utilisateur dispose des droits de lecture standard et du privilège <see cref="F:System.Security.Principal.TokenAccessLevels.Query" /> pour le jeton.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.Write">
      <summary>L'utilisateur dispose des droits d'écriture standard ainsi que des privilèges <see cref="F:System.Security.Principal.TokenAccessLevels.AdjustPrivileges,F:System.Security.Principal.TokenAccessLevels.AdjustGroups" /> et <see cref="F:System.Security.Principal.TokenAccessLevels.AdjustDefault" /> pour le jeton.</summary>
    </member>
    <member name="T:System.Security.Principal.WellKnownSidType">
      <summary>Définit un ensemble d'identificateurs de sécurité (SID) fréquemment utilisés.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountAdministratorSid">
      <summary>Indique un SID qui correspond au groupe d'administrateurs de compte.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountCertAdminsSid">
      <summary>Indique un SID qui correspond au groupe d'administrateurs de certificat.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountComputersSid">
      <summary>Indique un SID qui correspond au groupe d'ordinateurs de compte.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountControllersSid">
      <summary>Indique un SID qui correspond au groupe de contrôleurs de compte.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountDomainAdminsSid">
      <summary>Indique un SID qui correspond au groupe d'administrateurs de domaine de compte.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountDomainGuestsSid">
      <summary>Indique un SID qui correspond au groupe d'invités de domaine de compte.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountDomainUsersSid">
      <summary>Indique un SID qui correspond au groupe d'utilisateurs de domaine de compte.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountEnterpriseAdminsSid">
      <summary>Indique un SID qui correspond au groupe des administrateurs de l'entreprise.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountGuestSid">
      <summary>Indique un SID qui correspond au groupe d'invités de compte.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountKrbtgtSid">
      <summary>Indique un SID qui correspond au groupe cible de compte Kerberos.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountPolicyAdminsSid">
      <summary>Indique un SID qui correspond au groupe des administrateurs de stratégie.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountRasAndIasServersSid">
      <summary>Indique un SID qui correspond au compte de serveur RAS et IAS.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountSchemaAdminsSid">
      <summary>Indique un SID qui correspond au groupe d'administrateurs de schéma.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AnonymousSid">
      <summary>Indique un SID pour le compte anonyme.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AuthenticatedUserSid">
      <summary>Indique un SID pour un utilisateur authentifié.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BatchSid">
      <summary>Indique un SID pour un processus par lots.Ce SID est ajouté au processus d'un jeton lorsqu'il se connecte en tant que tâche batch.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinAccountOperatorsSid">
      <summary>Indique un SID qui correspond au compte des opérateurs de compte.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinAdministratorsSid">
      <summary>Indique un SID qui correspond au compte administrateur.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinAuthorizationAccessSid">
      <summary>Indique un SID qui correspond au groupe d'accès d'autorisation Windows.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinBackupOperatorsSid">
      <summary>Indique un SID qui correspond au groupe des opérateurs de sauvegarde.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinDomainSid">
      <summary>Indique un SID qui correspond au compte de domaine.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinGuestsSid">
      <summary>Indique un SID qui correspond au compte d'invité.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinIncomingForestTrustBuildersSid">
      <summary>Indique un SID qui permet à un utilisateur de créer des approbations de forêt entrantes.Il est ajouté au jeton des utilisateurs qui sont un membre du groupe prédéfini Générateurs d'approbations de forêt entrante dans le domaine racine de la forêt.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinNetworkConfigurationOperatorsSid">
      <summary>Indique un SID qui correspond au groupe des opérateurs de réseau.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinPerformanceLoggingUsersSid">
      <summary>Indique un SID qui correspond au groupe d'utilisateurs disposant d'un accès à distance qui permet de surveiller l'ordinateur.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinPerformanceMonitoringUsersSid">
      <summary>Indique un SID qui correspond au groupe d'utilisateurs disposant d'un accès à distance qui permet de planifier la journalisation des compteurs de performance sur cet ordinateur.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinPowerUsersSid">
      <summary>Indique un SID qui correspond au groupe des utilisateurs avec pouvoir.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinPreWindows2000CompatibleAccessSid">
      <summary>Indique un SID qui correspond aux comptes compatibles avec les versions antérieures à Windows 2000.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinPrintOperatorsSid">
      <summary>Indique un SID qui correspond au groupe des opérateurs d'impression.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinRemoteDesktopUsersSid">
      <summary>Indique un SID qui correspond aux utilisateurs du bureau à distance.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinReplicatorSid">
      <summary>Indique un SID qui correspond au compte du réplicateur.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinSystemOperatorsSid">
      <summary>Indique un SID qui correspond au groupe des opérateurs système.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinUsersSid">
      <summary>Indique un SID qui correspond aux comptes d'utilisateurs intégrés.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.CreatorGroupServerSid">
      <summary>Indique un SID de Creator Group Server.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.CreatorGroupSid">
      <summary>Indique un SID qui correspond au groupe créateur d'un objet.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.CreatorOwnerServerSid">
      <summary>Indique un SID de Creator Owner Server.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.CreatorOwnerSid">
      <summary>Indique un SID qui correspond au propriétaire ou au créateur d'un objet.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.DialupSid">
      <summary>Indique un SID pour un compte d'accès à distance.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.DigestAuthenticationSid">
      <summary>Indique un SID présent lorsque le package d'authentification Microsoft Digest a authentifié le client.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.EnterpriseControllersSid">
      <summary>Indique un SID pour un contrôleur d'entreprise.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.InteractiveSid">
      <summary>Indique un SID pour un compte interactif.Ce SID est ajouté au processus d'un jeton lorsqu'il se connecte de manière interactive.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.LocalServiceSid">
      <summary>Indique un SID qui correspond à un service local.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.LocalSid">
      <summary>Indique un SID local.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.LocalSystemSid">
      <summary>Indique un SID qui correspond au système local.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.LogonIdsSid">
      <summary>Indique un SID qui correspond aux ID de connexion.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.MaxDefined">
      <summary>Indique le SID maximal défini dans l'énumération <see cref="T:System.Security.Principal.WellKnownSidType" />.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.NetworkServiceSid">
      <summary>Indique un SID qui correspond à un service réseau.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.NetworkSid">
      <summary>Indique un SID pour un compte réseau.Ce SID est ajouté au processus d'un jeton lorsqu'il se connecte sur un réseau.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.NTAuthoritySid">
      <summary>Indique un SID pour l'autorité Windows NT.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.NtlmAuthenticationSid">
      <summary>Indique un SID présent lorsque le package d'authentification Microsoft NTLM a authentifié le client.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.NullSid">
      <summary>Indique un SID null.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.OtherOrganizationSid">
      <summary>Indique un SID présent lorsque l'utilisateur s'est authentifié sur une forêt avec l'option d'authentification sélective activée.Si ce SID est présent, <see cref="F:System.Security.Principal.WellKnownSidType.ThisOrganizationSid" /> ne peut pas être présent.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.ProxySid">
      <summary>Indique un SID de proxy.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.RemoteLogonIdSid">
      <summary>Indique un SID qui correspond aux connexions à distance.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.RestrictedCodeSid">
      <summary>Indique un SID pour du code restreint.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.SChannelAuthenticationSid">
      <summary>Indique un SID présent lorsque le package d'authentification de canal sécurisé (SSL/TLS) a authentifié le client.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.SelfSid">
      <summary>Indique un SID pour soi-même.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.ServiceSid">
      <summary>Indique un SID pour un service.Ce SID est ajouté au processus d'un jeton lorsqu'il se connecte en tant que service.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.TerminalServerSid">
      <summary>Indique un SID qui correspond à un compte de serveur Terminal Server.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.ThisOrganizationSid">
      <summary>Indique un SID présent lorsque l'utilisateur s'est authentifié depuis la forêt ou sur une approbation pour laquelle l'option d'authentification sélective n'est pas activée.Si ce SID est présent, <see cref="F:System.Security.Principal.WellKnownSidType.OtherOrganizationSid" /> ne peut pas être présent.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.WinBuiltinTerminalServerLicenseServersSid">
      <summary>Indique un SID présent dans un serveur qui peut délivrer des licences Terminal Server.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.WorldSid">
      <summary>Indique un SID qui correspond à tous.</summary>
    </member>
    <member name="T:System.Security.Principal.WindowsBuiltInRole">
      <summary>Spécifie les rôles communs à utiliser avec <see cref="M:System.Security.Principal.WindowsPrincipal.IsInRole(System.String)" />.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.AccountOperator">
      <summary>Les opérateurs de compte gèrent les comptes d'utilisateurs sur un ordinateur ou un domaine.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.Administrator">
      <summary>Les administrateurs ont un accès total et illimité à l'ordinateur ou au domaine.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.BackupOperator">
      <summary>Les opérateurs de sauvegarde peuvent substituer des restrictions de sécurité dans le but unique de sauvegarder ou restaurer des fichiers.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.Guest">
      <summary>Les invités sont plus limités que les utilisateurs.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.PowerUser">
      <summary>Les utilisateurs avec pouvoir possèdent la plupart des autorisations administratives avec quelques restrictions.Ainsi, les utilisateurs avec pouvoirs peuvent exécuter des applications héritées (legacy), en plus des applications certifiées.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.PrintOperator">
      <summary>Les opérateurs d'impression peuvent prendre le contrôle d'une imprimante.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.Replicator">
      <summary>Les réplicateurs prennent en charge la réplication des fichiers dans un domaine.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.SystemOperator">
      <summary>Un opérateur système gère un ordinateur particulier.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.User">
      <summary>Les utilisateurs ne peuvent pas effectuer des modifications accidentelles ou intentionnelles à l'échelle du système.Ainsi, les utilisateurs peuvent exécuter des applications certifiées, mais pas la plupart des applications héritées (legacy).</summary>
    </member>
    <member name="T:System.Security.Principal.WindowsIdentity">
      <summary>Représente un utilisateur Windows.</summary>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.#ctor(System.IntPtr)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.Principal.WindowsIdentity" /> pour l'utilisateur représenté par le jeton de compte Windows spécifié.</summary>
      <param name="userToken">Jeton de compte pour l'utilisateur au nom duquel le code est en cours d'exécution. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="userToken" /> is 0.-or-<paramref name="userToken" /> is duplicated and invalid for impersonation.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the correct permissions. -or-A Win32 error occurred.</exception>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.#ctor(System.IntPtr,System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.Principal.WindowsIdentity" /> pour l'utilisateur représenté par le jeton de compte Windows et le type d'authentification spécifiés.</summary>
      <param name="userToken">Jeton de compte pour l'utilisateur au nom duquel le code est en cours d'exécution. </param>
      <param name="type">(Utilisation à titre informatif uniquement.) Type d'authentification utilisé pour identifier l'utilisateur.Pour plus d'informations, consultez la section Notes.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="userToken" /> is 0.-or-<paramref name="userToken" /> is duplicated and invalid for impersonation.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the correct permissions. -or-A Win32 error occurred.</exception>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.AccessToken">
      <summary>[ESSENTIEL POUR LA SÉCURITÉ] Obtient ce <see cref="T:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle" /> pour cette instance de <see cref="T:System.Security.Principal.WindowsIdentity" />. </summary>
      <returns>Retourne <see cref="T:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle" />.</returns>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.Dispose">
      <summary>Libère toutes les ressources utilisées par <see cref="T:System.Security.Principal.WindowsIdentity" />. </summary>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.Dispose(System.Boolean)">
      <summary>Libère les ressources non managées utilisées par l'objet <see cref="T:System.Security.Principal.WindowsIdentity" /> et libère éventuellement les ressources managées. </summary>
      <param name="disposing">true pour libérer les ressources managées et non managées ; false pour ne libérer que les ressources non managées. </param>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.GetAnonymous">
      <summary>Retourne un objet <see cref="T:System.Security.Principal.WindowsIdentity" /> que vous pouvez utiliser comme valeur de sentinelle dans votre code pour représenter un utilisateur anonyme.La valeur de propriété ne représente pas l'identité anonyme prédéfinie utilisée par le système d'exploitation Windows.</summary>
      <returns>Objet qui représente un utilisateur anonyme.</returns>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.GetCurrent">
      <summary>Retourne un objet <see cref="T:System.Security.Principal.WindowsIdentity" /> qui représente l'utilisateur Windows actuel.</summary>
      <returns>Objet qui représente l'utilisateur actuel.</returns>
      <exception cref="T:System.Security.SecurityException">The caller does not have the correct permissions. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlPrincipal" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.GetCurrent(System.Boolean)">
      <summary>Retourne un objet <see cref="T:System.Security.Principal.WindowsIdentity" /> représentant l'identité Windows pour le thread ou le processus, selon la valeur du paramètre <paramref name="ifImpersonating" />.</summary>
      <returns>Objet qui représente un utilisateur Windows.</returns>
      <param name="ifImpersonating">true pour retourner <see cref="T:System.Security.Principal.WindowsIdentity" /> uniquement si le thread emprunte actuellement une identité ; false pour retourner le <see cref="T:System.Security.Principal.WindowsIdentity" /> du thread s'il emprunte une identité ou le <see cref="T:System.Security.Principal.WindowsIdentity" /> du processus si le thread n'emprunte pas actuellement d'identité.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlPrincipal" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.GetCurrent(System.Security.Principal.TokenAccessLevels)">
      <summary>Retourne un objet <see cref="T:System.Security.Principal.WindowsIdentity" /> qui représente l'utilisateur Windows actuel, en utilisant le niveau d'accès du jeton souhaité spécifié.</summary>
      <returns>Objet qui représente l'utilisateur actuel.</returns>
      <param name="desiredAccess">Combinaison d'opérations de bits des valeurs d'énumération. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlPrincipal" />
      </PermissionSet>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.Groups">
      <summary>Obtient les groupes auxquels l'utilisateur Windows actuel appartient.</summary>
      <returns>Objet qui représente les groupes auxquels l'utilisateur Windows actuel appartient.</returns>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.ImpersonationLevel">
      <summary>Obtient le niveau d'emprunt d'identité pour l'utilisateur.</summary>
      <returns>Une des valeurs d'énumération qui spécifie le niveau d'emprunt d'identité. </returns>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.IsAnonymous">
      <summary>Obtient une valeur qui indique si le compte d'utilisateur est identifié en tant que compte anonyme par le système.</summary>
      <returns>true si le compte d'utilisateur est un compte anonyme ; sinon, false.</returns>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.IsGuest">
      <summary>Obtient une valeur indiquant si le compte d'utilisateur est identifié en tant que compte <see cref="F:System.Security.Principal.WindowsAccountType.Guest" /> par le système.</summary>
      <returns>true si le compte d'utilisateur est un compte <see cref="F:System.Security.Principal.WindowsAccountType.Guest" /> ; sinon, false.</returns>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.IsSystem">
      <summary>Obtient une valeur indiquant si le compte d'utilisateur est identifié en tant que compte <see cref="F:System.Security.Principal.WindowsAccountType.System" /> par le système.</summary>
      <returns>true si le compte d'utilisateur est un compte <see cref="F:System.Security.Principal.WindowsAccountType.System" /> ; sinon, false.</returns>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.Owner">
      <summary>Obtient l'identificateur de sécurité (SID) pour le propriétaire du jeton.</summary>
      <returns>Objet pour le propriétaire du jeton.</returns>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.RunImpersonated(Microsoft.Win32.SafeHandles.SafeAccessTokenHandle,System.Action)">
      <summary>Exécute l'action spécifiée en tant qu'identité Windows empruntée.Au lieu d'utiliser un appel de méthode emprunté et d'exécuter votre fonction dans <see cref="T:System.Security.Principal.WindowsImpersonationContext" />, vous pouvez utiliser <see cref="M:System.Security.Principal.WindowsIdentity.RunImpersonated(Microsoft.Win32.SafeHandles.SafeAccessTokenHandle,System.Action)" /> et fournir votre fonction directement en tant que paramètre.</summary>
      <param name="safeAccessTokenHandle">SafeAccessTokenHandle de l'identité Windows empruntée.</param>
      <param name="action">System.Action à exécuter. </param>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.RunImpersonated``1(Microsoft.Win32.SafeHandles.SafeAccessTokenHandle,System.Func{``0})">
      <summary>Exécute la fonction spécifiée en tant qu'identité Windows empruntée.Au lieu d'utiliser un appel de méthode emprunté et d'exécuter votre fonction dans <see cref="T:System.Security.Principal.WindowsImpersonationContext" />, vous pouvez utiliser <see cref="M:System.Security.Principal.WindowsIdentity.RunImpersonated(Microsoft.Win32.SafeHandles.SafeAccessTokenHandle,System.Action)" /> et fournir votre fonction directement en tant que paramètre.</summary>
      <returns>Retourne le résultat de la fonction.</returns>
      <param name="safeAccessTokenHandle">SafeAccessTokenHandle de l'identité Windows empruntée.</param>
      <param name="func">System.Func à exécuter.</param>
      <typeparam name="T">Type d'objet utilisé et retourné par la fonction.</typeparam>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.User">
      <summary>Obtient l'identificateur de sécurité (SID) pour l'utilisateur.</summary>
      <returns>Objet pour l'utilisateur.</returns>
    </member>
    <member name="T:System.Security.Principal.WindowsPrincipal">
      <summary>Permet au code de vérifier l'appartenance à un groupe Windows d'un utilisateur Windows.</summary>
    </member>
    <member name="M:System.Security.Principal.WindowsPrincipal.#ctor(System.Security.Principal.WindowsIdentity)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.Principal.WindowsPrincipal" /> à l'aide de l'objet <see cref="T:System.Security.Principal.WindowsIdentity" /> spécifié.</summary>
      <param name="ntIdentity">Objet à partir duquel la nouvelle instance de <see cref="T:System.Security.Principal.WindowsPrincipal" /> doit être créée. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="ntIdentity" /> a la valeur null. </exception>
    </member>
    <member name="M:System.Security.Principal.WindowsPrincipal.IsInRole(System.Int32)">
      <summary>Détermine si l'objet Principal actuel appartient au groupe d'utilisateurs Windows avec l'identificateur relatif (RID, Relative Identifier) spécifié.</summary>
      <returns>true si l'entité de sécurité actuelle est membre du groupe d'utilisateurs Windows spécifié, autrement dit s'il appartient à un rôle particulier ; sinon, false.</returns>
      <param name="rid">RID du groupe d'utilisateurs Windows dans lequel rechercher l'état d'appartenance de l'objet Principal. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Principal.WindowsPrincipal.IsInRole(System.Security.Principal.SecurityIdentifier)">
      <summary>Détermine si l'objet Principal actuel appartient au groupe d'utilisateurs Windows avec l'identificateur de sécurité (SID) spécifié.</summary>
      <returns>true si l'objet Principal actuel est membre du groupe d'utilisateurs Windows spécifié ; sinon, false.</returns>
      <param name="sid">
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> qui identifie de manière unique un groupe d'utilisateurs Windows.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sid" /> a la valeur null.</exception>
      <exception cref="T:System.Security.SecurityException">Windows a retourné une erreur Win32.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Principal.WindowsPrincipal.IsInRole(System.Security.Principal.WindowsBuiltInRole)">
      <summary>Détermine si l'objet Principal actuel appartient au groupe d'utilisateurs Windows avec le <see cref="T:System.Security.Principal.WindowsBuiltInRole" /> spécifié.</summary>
      <returns>true si l'objet Principal actuel est membre du groupe d'utilisateurs Windows spécifié ; sinon, false.</returns>
      <param name="role">Une des valeurs de <see cref="T:System.Security.Principal.WindowsBuiltInRole" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="role" /> n'est pas une valeur <see cref="T:System.Security.Principal.WindowsBuiltInRole" /> valide.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
  </members>
</doc>