﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using NAudio.CoreAudioApi;
using NAudio;
using static System.Net.Mime.MediaTypeNames;
using System.Windows.Media;
using static Microsoft.WindowsAPICodePack.Shell.PropertySystem.SystemProperties.System;

namespace AssistPro.ais
{
    public partial class AisVdmDetailForm : Form
    {
        private AISSentence m_AisSentence;

        private readonly string[,] VdmFieldArray = new string[,]
        {
            { "0001", "MSG ID" },
            { "0002", "REPEAT_IND" },
            { "0003", "USER_ID" },
            { "0004", "NAV_STATUS" },
            { "0005", "ROT" },
            { "0006", "SOG" },
            { "0007", "POS ACC" },
            { "0008", "LATITUDE" },
            { "0009", "LONGITUDE" },
            { "0010", "COG" },
            { "0011", "TRUE_HEADING" },
            { "0013", "TIME_STAMP" },
            { "0014", "RAIM_FLAG" },
            { "0070", "SYNC_STATE" },
            { "0071", "SLOT_TIMEOUT" },
            { "0073", "SUBMSG_SLOT" },
            { "0088", "SPARE3" },
            { "0137", "MANEUVER_IND" }
        };

        public AisVdmDetailForm(string message)
        {
            InitializeComponent();

            foreach (var msg in AisMessageRepository.AllMessages)
            {
                Console.WriteLine($"{msg.MessageId} ({msg.MessageName})");
                foreach (var field in msg.Fields)
                {
                    Console.WriteLine($"  {field.FieldId} : {field.FieldName}");
                }
            }

            m_AisSentence = new AISSentence(message);

            if (m_AisSentence != null)
            {
                string strMsgName = "";
                switch (m_AisSentence.m_Msg_ID)
                {
                    case 1:
                        // Slot offset
                        if (m_AisSentence.m_Slot_Timeout == 0)
                        {
                            strMsgName = "msg1-0";
                        }
                        // UTC hour and minute
                        else if (m_AisSentence.m_Slot_Timeout == 1)
                        {
                            strMsgName = "msg1-1";
                        }
                        // Slot number
                        else if (m_AisSentence.m_Slot_Timeout == 2 || m_AisSentence.m_Slot_Timeout == 4 || m_AisSentence.m_Slot_Timeout == 6)
                        {
                            strMsgName = "msg1-2";
                        }
                        // Received stations
                        else
                        {
                            strMsgName = "msg1-3";
                        }
                        break;
                    case 2:
                        // Slot offset
                        if (m_AisSentence.m_Slot_Timeout == 0)
                        {
                            strMsgName = "msg2-0";
                        }
                        // UTC hour and minute
                        else if (m_AisSentence.m_Slot_Timeout == 1)
                        {
                            strMsgName = "msg2-1";
                        }
                        // Slot number
                        else if (m_AisSentence.m_Slot_Timeout == 2 || m_AisSentence.m_Slot_Timeout == 4 || m_AisSentence.m_Slot_Timeout == 6)
                        {
                            strMsgName = "msg2-2";
                        }
                        // Received stations
                        else
                        {
                            strMsgName = "msg2-3";
                        }
                        break;
                    case 3:
                    case 5:
                    case 6:
                    case 8:
                    case 10:
                    case 12:
                    case 14:
                    case 15:
                    case 16:
                    case 17:
                    case 19:
                    case 20:
                    case 21:
                    case 23:
                    case 27:
                        strMsgName = "msg" + m_AisSentence.m_Msg_ID.ToString();
                        break;
                    case 4:
                        // UTC hour and minute
                        if (m_AisSentence.m_Slot_Timeout == 1)
                        {
                            strMsgName = "msg4-1";
                        }
                        // Slot number
                        else if (m_AisSentence.m_Slot_Timeout == 2 || m_AisSentence.m_Slot_Timeout == 4 || m_AisSentence.m_Slot_Timeout == 6)
                        {
                            strMsgName = "msg4-2";
                        }
                        // Received stations
                        else
                        {
                            strMsgName = "msg4-3";
                        }
                        break;
                    case 7:
                        strMsgName = "msg7-0";
                        break;
                    case 9:
                        if (m_AisSentence.m_State_Select == 0)
                        {
                            // Slot offset
                            if (m_AisSentence.m_Slot_Timeout == 0)
                            {
                                strMsgName = "msg9-0";
                            }
                            // UTC hour and minute
                            else if (m_AisSentence.m_Slot_Timeout == 1)
                            {
                                strMsgName = "msg9-1";
                            }
                            // Slot number
                            else if (m_AisSentence.m_Slot_Timeout == 2 || m_AisSentence.m_Slot_Timeout == 4 || m_AisSentence.m_Slot_Timeout == 6)
                            {
                                strMsgName = "msg9-2";
                            }
                            // Received stations
                            else
                            {
                                strMsgName = "msg9-3";
                            }
                        }
                        else 
                        {
                            strMsgName = "msg9-ITDMA";
                        }
                        break;
                    case 11:
                        if(m_AisSentence.m_Slot_Timeout == 0)
                        {
                            strMsgName = "msg11-0";
                        }
                        // UTC hour and minute
                        else if (m_AisSentence.m_Slot_Timeout == 1)
                        {
                            strMsgName = "msg11-1";
                        }
                        // Slot number
                        else if (m_AisSentence.m_Slot_Timeout == 2 || m_AisSentence.m_Slot_Timeout == 4 || m_AisSentence.m_Slot_Timeout == 6)
                        {
                            strMsgName = "msg11-2";
                        }
                        // Received stations
                        else
                        {
                            strMsgName = "msg11-3";
                        }
                        break;
                    case 13:
                        strMsgName = "msg13-0";
                        break;
                    case 18:
                        if (m_AisSentence.m_State_Select == 0)
                        {
                            // Slot offset
                            if (m_AisSentence.m_Slot_Timeout == 0)
                            {
                                strMsgName = "msg18";
                            }
                            // UTC hour and minute
                            else if (m_AisSentence.m_Slot_Timeout == 1)
                            {
                                strMsgName = "msg18-1";
                            }
                            // Slot number
                            else if (m_AisSentence.m_Slot_Timeout == 2 || m_AisSentence.m_Slot_Timeout == 4 || m_AisSentence.m_Slot_Timeout == 6)
                            {
                                strMsgName = "msg18-2";
                            }
                            // Received stations
                            else
                            {
                                strMsgName = "msg18-3";
                            }
                        }
                        else
                        {
                            strMsgName = "msg18-ITDMA";
                        }
                        break;
                    case 22:
                        if (m_AisSentence.m_Address_Ind == 0)
                        {
                            strMsgName = "msg22";
                        }
                        else
                        {
                            strMsgName = "msg22-1";
                        }
                        break;
                    case 24:
                        if (m_AisSentence.m_Part_Msg24 == 0)
                        {
                            strMsgName = "msg24-0";
                        }
                        else
                        {
                            strMsgName = "msg24-1";
                        }
                        break;
                    default:
                        break;
                }

                var msgDef = AisMessageRepository.AllMessages.FirstOrDefault(m => m.MessageName == strMsgName);
                if (msgDef != null)
                {
                    foreach (var field in msgDef.Fields)
                    {
                        var param = AisMessageRepository.AllParams
                            .FirstOrDefault(p => string.Equals(p.FieldName, field.FieldName, StringComparison.OrdinalIgnoreCase));

                        // BitLength와 Description 값 가져오기 (없으면 빈 문자열)
                        string bitLength = param?.BitLength ?? "";
                        string description = param?.Description ?? "";

                        string memberValue = "";
                        var property = typeof(AISSentence).GetFields()
                            .FirstOrDefault(f => string.Equals(f.Name, "m_" + field.FieldName, StringComparison.OrdinalIgnoreCase));

                        if (property != null)
                        {
                            var value = property.GetValue(m_AisSentence);
                            memberValue = value != null ? value.ToString() : "";
                        }

                        // DataGridView에 행 추가
                        dgv_VdmDetail.Rows.Add(field.FieldId, field.FieldName, bitLength, memberValue, description);
                    }
                }
            }
            else
            {
                ;
            }
        }

        private void dgv_VdmDetail_SelectionChanged(object sender, EventArgs e)
        {
            if (dgv_VdmDetail.SelectedRows.Count > 0)
            {
                var row = dgv_VdmDetail.SelectedRows[0];
                var fieldId = row.Cells[0].Value?.ToString();

                if (!string.IsNullOrEmpty(fieldId))
                {
                    var param = AisMessageRepository.AllParams
                        .FirstOrDefault(p => p.FieldId == fieldId);

                    if (param != null && param.Values != null && param.Values.Count > 0)
                    {
                        var sb = new StringBuilder();
                        foreach (var val in param.Values)
                        {
                            sb.AppendLine($"{val.Code} = {val.Description}");
                        }
                        tb_ItemDetail.Text = sb.ToString();
                    }
                    else
                    {
                        tb_ItemDetail.Text = string.Empty;
                    }
                }
                else
                {
                    tb_ItemDetail.Text = string.Empty;
                }
            }
            else
            {
                tb_ItemDetail.Text = string.Empty;
            }
        }
    }
}
