﻿namespace AssistPro
{
    partial class ioPortMonitorForm
    {
        /// <summary> 
        /// 필수 디자이너 변수입니다.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 사용 중인 모든 리소스를 정리합니다.
        /// </summary>
        /// <param name="disposing">관리되는 리소스를 삭제해야 하면 true이고, 그렇지 않으면 false입니다.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 구성 요소 디자이너에서 생성한 코드

        /// <summary> 
        /// 디자이너 지원에 필요한 메서드입니다. 
        /// 이 메서드의 내용을 코드 편집기로 수정하지 마세요.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ioPortMonitorForm));
            this.richtb_IOMonitor = new System.Windows.Forms.RichTextBox();
            this.toolStrip1 = new System.Windows.Forms.ToolStrip();
            this.toolStripLabel1 = new System.Windows.Forms.ToolStripLabel();
            this.cb_CommList = new System.Windows.Forms.ToolStripComboBox();
            this.btn_ConnectIO = new System.Windows.Forms.ToolStripButton();
            this.toolStripButton_clear = new System.Windows.Forms.ToolStripButton();
            this.toolStripButton_save = new System.Windows.Forms.ToolStripButton();
            this.saveFileDialog = new System.Windows.Forms.SaveFileDialog();
            this.toolStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // richtb_IOMonitor
            // 
            this.richtb_IOMonitor.Dock = System.Windows.Forms.DockStyle.Fill;
            this.richtb_IOMonitor.Location = new System.Drawing.Point(0, 0);
            this.richtb_IOMonitor.Name = "richtb_IOMonitor";
            this.richtb_IOMonitor.Size = new System.Drawing.Size(680, 373);
            this.richtb_IOMonitor.TabIndex = 0;
            this.richtb_IOMonitor.Text = "";
            // 
            // toolStrip1
            // 
            this.toolStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripLabel1,
            this.cb_CommList,
            this.btn_ConnectIO,
            this.toolStripButton_clear,
            this.toolStripButton_save});
            this.toolStrip1.Location = new System.Drawing.Point(0, 0);
            this.toolStrip1.Name = "toolStrip1";
            this.toolStrip1.Size = new System.Drawing.Size(680, 25);
            this.toolStrip1.TabIndex = 1;
            this.toolStrip1.Text = "toolStrip1";
            // 
            // toolStripLabel1
            // 
            this.toolStripLabel1.Font = new System.Drawing.Font("휴먼모음T", 9F);
            this.toolStripLabel1.Name = "toolStripLabel1";
            this.toolStripLabel1.Size = new System.Drawing.Size(32, 22);
            this.toolStripLabel1.Text = "COM";
            // 
            // cb_CommList
            // 
            this.cb_CommList.AutoSize = false;
            this.cb_CommList.Font = new System.Drawing.Font("휴먼모음T", 9F);
            this.cb_CommList.Margin = new System.Windows.Forms.Padding(5, 0, 5, 0);
            this.cb_CommList.Name = "cb_CommList";
            this.cb_CommList.Size = new System.Drawing.Size(80, 21);
            // 
            // btn_ConnectIO
            // 
            this.btn_ConnectIO.AutoSize = false;
            this.btn_ConnectIO.BackColor = System.Drawing.Color.Silver;
            this.btn_ConnectIO.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            this.btn_ConnectIO.Font = new System.Drawing.Font("휴먼모음T", 9F);
            this.btn_ConnectIO.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.btn_ConnectIO.Name = "btn_ConnectIO";
            this.btn_ConnectIO.Size = new System.Drawing.Size(80, 22);
            this.btn_ConnectIO.Text = "CONNECT";
            this.btn_ConnectIO.TextImageRelation = System.Windows.Forms.TextImageRelation.TextAboveImage;
            this.btn_ConnectIO.Click += new System.EventHandler(this.btn_ConnectIO_Click);
            // 
            // toolStripButton_clear
            // 
            this.toolStripButton_clear.Alignment = System.Windows.Forms.ToolStripItemAlignment.Right;
            this.toolStripButton_clear.AutoSize = false;
            this.toolStripButton_clear.BackColor = System.Drawing.Color.Silver;
            this.toolStripButton_clear.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            this.toolStripButton_clear.Font = new System.Drawing.Font("휴먼모음T", 9F);
            this.toolStripButton_clear.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.toolStripButton_clear.Margin = new System.Windows.Forms.Padding(0, 1, 10, 2);
            this.toolStripButton_clear.Name = "toolStripButton_clear";
            this.toolStripButton_clear.Size = new System.Drawing.Size(80, 22);
            this.toolStripButton_clear.Text = "CLEAR";
            this.toolStripButton_clear.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.toolStripButton_clear.Click += new System.EventHandler(this.toolStripButton_clear_Click);
            // 
            // toolStripButton_save
            // 
            this.toolStripButton_save.Alignment = System.Windows.Forms.ToolStripItemAlignment.Right;
            this.toolStripButton_save.AutoSize = false;
            this.toolStripButton_save.BackColor = System.Drawing.Color.Silver;
            this.toolStripButton_save.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            this.toolStripButton_save.Font = new System.Drawing.Font("휴먼모음T", 9F);
            this.toolStripButton_save.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.toolStripButton_save.Margin = new System.Windows.Forms.Padding(0, 1, 10, 2);
            this.toolStripButton_save.Name = "toolStripButton_save";
            this.toolStripButton_save.Size = new System.Drawing.Size(80, 22);
            this.toolStripButton_save.Text = "SAVE";
            this.toolStripButton_save.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.toolStripButton_save.Click += new System.EventHandler(this.toolStripButton_save_Click);
            // 
            // ioPortMonitorForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(680, 373);
            this.Controls.Add(this.toolStrip1);
            this.Controls.Add(this.richtb_IOMonitor);
            this.HideOnClose = true;
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "ioPortMonitorForm";
            this.TabText = "I/O Port Monitor";
            this.Text = "I/O Port Monitor";
            this.Load += new System.EventHandler(this.Load_ioPortMonitor);
            this.toolStrip1.ResumeLayout(false);
            this.toolStrip1.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.RichTextBox richtb_IOMonitor;
        private System.Windows.Forms.ToolStrip toolStrip1;
        private System.Windows.Forms.ToolStripLabel toolStripLabel1;
        private System.Windows.Forms.ToolStripComboBox cb_CommList;
        private System.Windows.Forms.ToolStripButton btn_ConnectIO;
        private System.Windows.Forms.ToolStripButton toolStripButton_clear;
        private System.Windows.Forms.ToolStripButton toolStripButton_save;
        private System.Windows.Forms.SaveFileDialog saveFileDialog;
    }
}
