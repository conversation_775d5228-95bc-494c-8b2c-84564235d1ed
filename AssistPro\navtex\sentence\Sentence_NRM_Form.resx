﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="m_send_btn.Font" type="System.Drawing.Font, System.Drawing">
    <value>굴림, 13.8pt, style=Bold</value>
  </data>
  <data name="m_send_btn.Location" type="System.Drawing.Point, System.Drawing">
    <value>738, 165</value>
  </data>
  <data name="m_send_btn.Size" type="System.Drawing.Size, System.Drawing">
    <value>144, 51</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="m_send_btn.TabIndex" type="System.Int32, mscorlib">
    <value>44</value>
  </data>
  <data name="m_send_btn.Text" xml:space="preserve">
    <value>Send</value>
  </data>
  <data name="&gt;&gt;m_send_btn.Name" xml:space="preserve">
    <value>m_send_btn</value>
  </data>
  <data name="&gt;&gt;m_send_btn.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_send_btn.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;m_send_btn.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="m_message_all_disable_btn.Location" type="System.Drawing.Point, System.Drawing">
    <value>229, 120</value>
  </data>
  <data name="m_message_all_disable_btn.Size" type="System.Drawing.Size, System.Drawing">
    <value>113, 27</value>
  </data>
  <data name="m_message_all_disable_btn.TabIndex" type="System.Int32, mscorlib">
    <value>36</value>
  </data>
  <data name="m_message_all_disable_btn.Text" xml:space="preserve">
    <value>All Disable</value>
  </data>
  <data name="&gt;&gt;m_message_all_disable_btn.Name" xml:space="preserve">
    <value>m_message_all_disable_btn</value>
  </data>
  <data name="&gt;&gt;m_message_all_disable_btn.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_message_all_disable_btn.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;m_message_all_disable_btn.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="m_message_all_enable_btn.Location" type="System.Drawing.Point, System.Drawing">
    <value>110, 120</value>
  </data>
  <data name="m_message_all_enable_btn.Size" type="System.Drawing.Size, System.Drawing">
    <value>113, 27</value>
  </data>
  <data name="m_message_all_enable_btn.TabIndex" type="System.Int32, mscorlib">
    <value>35</value>
  </data>
  <data name="m_message_all_enable_btn.Text" xml:space="preserve">
    <value>All Enable</value>
  </data>
  <data name="&gt;&gt;m_message_all_enable_btn.Name" xml:space="preserve">
    <value>m_message_all_enable_btn</value>
  </data>
  <data name="&gt;&gt;m_message_all_enable_btn.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_message_all_enable_btn.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;m_message_all_enable_btn.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="m_message_Z_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_message_Z_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>53, 125</value>
  </data>
  <data name="m_message_Z_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>38, 19</value>
  </data>
  <data name="m_message_Z_radio.TabIndex" type="System.Int32, mscorlib">
    <value>34</value>
  </data>
  <data name="m_message_Z_radio.Text" xml:space="preserve">
    <value>Z</value>
  </data>
  <data name="&gt;&gt;m_message_Z_radio.Name" xml:space="preserve">
    <value>m_message_Z_radio</value>
  </data>
  <data name="&gt;&gt;m_message_Z_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_message_Z_radio.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;m_message_Z_radio.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="m_message_Y_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_message_Y_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>9, 125</value>
  </data>
  <data name="m_message_Y_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>37, 19</value>
  </data>
  <data name="m_message_Y_radio.TabIndex" type="System.Int32, mscorlib">
    <value>33</value>
  </data>
  <data name="m_message_Y_radio.Text" xml:space="preserve">
    <value>Y</value>
  </data>
  <data name="&gt;&gt;m_message_Y_radio.Name" xml:space="preserve">
    <value>m_message_Y_radio</value>
  </data>
  <data name="&gt;&gt;m_message_Y_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_message_Y_radio.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;m_message_Y_radio.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="m_message_X_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_message_X_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>317, 91</value>
  </data>
  <data name="m_message_X_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>38, 19</value>
  </data>
  <data name="m_message_X_radio.TabIndex" type="System.Int32, mscorlib">
    <value>32</value>
  </data>
  <data name="m_message_X_radio.Text" xml:space="preserve">
    <value>X</value>
  </data>
  <data name="&gt;&gt;m_message_X_radio.Name" xml:space="preserve">
    <value>m_message_X_radio</value>
  </data>
  <data name="&gt;&gt;m_message_X_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_message_X_radio.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;m_message_X_radio.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="m_message_W_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_message_W_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>273, 91</value>
  </data>
  <data name="m_message_W_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>43, 19</value>
  </data>
  <data name="m_message_W_radio.TabIndex" type="System.Int32, mscorlib">
    <value>31</value>
  </data>
  <data name="m_message_W_radio.Text" xml:space="preserve">
    <value>W</value>
  </data>
  <data name="&gt;&gt;m_message_W_radio.Name" xml:space="preserve">
    <value>m_message_W_radio</value>
  </data>
  <data name="&gt;&gt;m_message_W_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_message_W_radio.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;m_message_W_radio.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="m_message_V_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_message_V_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>229, 91</value>
  </data>
  <data name="m_message_V_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>37, 19</value>
  </data>
  <data name="m_message_V_radio.TabIndex" type="System.Int32, mscorlib">
    <value>30</value>
  </data>
  <data name="m_message_V_radio.Text" xml:space="preserve">
    <value>V</value>
  </data>
  <data name="&gt;&gt;m_message_V_radio.Name" xml:space="preserve">
    <value>m_message_V_radio</value>
  </data>
  <data name="&gt;&gt;m_message_V_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_message_V_radio.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;m_message_V_radio.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="m_message_U_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_message_U_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>185, 91</value>
  </data>
  <data name="m_message_U_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>39, 19</value>
  </data>
  <data name="m_message_U_radio.TabIndex" type="System.Int32, mscorlib">
    <value>29</value>
  </data>
  <data name="m_message_U_radio.Text" xml:space="preserve">
    <value>U</value>
  </data>
  <data name="&gt;&gt;m_message_U_radio.Name" xml:space="preserve">
    <value>m_message_U_radio</value>
  </data>
  <data name="&gt;&gt;m_message_U_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_message_U_radio.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;m_message_U_radio.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="m_message_T_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_message_T_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>141, 91</value>
  </data>
  <data name="m_message_T_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>37, 19</value>
  </data>
  <data name="m_message_T_radio.TabIndex" type="System.Int32, mscorlib">
    <value>28</value>
  </data>
  <data name="m_message_T_radio.Text" xml:space="preserve">
    <value>T</value>
  </data>
  <data name="&gt;&gt;m_message_T_radio.Name" xml:space="preserve">
    <value>m_message_T_radio</value>
  </data>
  <data name="&gt;&gt;m_message_T_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_message_T_radio.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;m_message_T_radio.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="m_message_S_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_message_S_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>97, 91</value>
  </data>
  <data name="m_message_S_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>39, 19</value>
  </data>
  <data name="m_message_S_radio.TabIndex" type="System.Int32, mscorlib">
    <value>27</value>
  </data>
  <data name="m_message_S_radio.Text" xml:space="preserve">
    <value>S</value>
  </data>
  <data name="&gt;&gt;m_message_S_radio.Name" xml:space="preserve">
    <value>m_message_S_radio</value>
  </data>
  <data name="&gt;&gt;m_message_S_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_message_S_radio.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;m_message_S_radio.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="m_message_R_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_message_R_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>53, 91</value>
  </data>
  <data name="m_message_R_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>39, 19</value>
  </data>
  <data name="m_message_R_radio.TabIndex" type="System.Int32, mscorlib">
    <value>26</value>
  </data>
  <data name="m_message_R_radio.Text" xml:space="preserve">
    <value>R</value>
  </data>
  <data name="&gt;&gt;m_message_R_radio.Name" xml:space="preserve">
    <value>m_message_R_radio</value>
  </data>
  <data name="&gt;&gt;m_message_R_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_message_R_radio.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;m_message_R_radio.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="m_message_Q_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_message_Q_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>9, 91</value>
  </data>
  <data name="m_message_Q_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>41, 19</value>
  </data>
  <data name="m_message_Q_radio.TabIndex" type="System.Int32, mscorlib">
    <value>25</value>
  </data>
  <data name="m_message_Q_radio.Text" xml:space="preserve">
    <value>Q</value>
  </data>
  <data name="&gt;&gt;m_message_Q_radio.Name" xml:space="preserve">
    <value>m_message_Q_radio</value>
  </data>
  <data name="&gt;&gt;m_message_Q_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_message_Q_radio.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;m_message_Q_radio.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="m_message_P_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_message_P_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>317, 57</value>
  </data>
  <data name="m_message_P_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>39, 19</value>
  </data>
  <data name="m_message_P_radio.TabIndex" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="m_message_P_radio.Text" xml:space="preserve">
    <value>P</value>
  </data>
  <data name="&gt;&gt;m_message_P_radio.Name" xml:space="preserve">
    <value>m_message_P_radio</value>
  </data>
  <data name="&gt;&gt;m_message_P_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_message_P_radio.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;m_message_P_radio.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="m_message_O_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_message_O_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>273, 57</value>
  </data>
  <data name="m_message_O_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>41, 19</value>
  </data>
  <data name="m_message_O_radio.TabIndex" type="System.Int32, mscorlib">
    <value>23</value>
  </data>
  <data name="m_message_O_radio.Text" xml:space="preserve">
    <value>O</value>
  </data>
  <data name="&gt;&gt;m_message_O_radio.Name" xml:space="preserve">
    <value>m_message_O_radio</value>
  </data>
  <data name="&gt;&gt;m_message_O_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_message_O_radio.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;m_message_O_radio.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="m_message_N_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_message_N_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>229, 57</value>
  </data>
  <data name="m_message_N_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>38, 19</value>
  </data>
  <data name="m_message_N_radio.TabIndex" type="System.Int32, mscorlib">
    <value>22</value>
  </data>
  <data name="m_message_N_radio.Text" xml:space="preserve">
    <value>N</value>
  </data>
  <data name="&gt;&gt;m_message_N_radio.Name" xml:space="preserve">
    <value>m_message_N_radio</value>
  </data>
  <data name="&gt;&gt;m_message_N_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_message_N_radio.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;m_message_N_radio.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="m_message_M_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_message_M_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>185, 57</value>
  </data>
  <data name="m_message_M_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>41, 19</value>
  </data>
  <data name="m_message_M_radio.TabIndex" type="System.Int32, mscorlib">
    <value>21</value>
  </data>
  <data name="m_message_M_radio.Text" xml:space="preserve">
    <value>M</value>
  </data>
  <data name="&gt;&gt;m_message_M_radio.Name" xml:space="preserve">
    <value>m_message_M_radio</value>
  </data>
  <data name="&gt;&gt;m_message_M_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_message_M_radio.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;m_message_M_radio.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="m_message_L_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_message_L_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>141, 57</value>
  </data>
  <data name="m_message_L_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>37, 19</value>
  </data>
  <data name="m_message_L_radio.TabIndex" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="m_message_L_radio.Text" xml:space="preserve">
    <value>L</value>
  </data>
  <data name="&gt;&gt;m_message_L_radio.Name" xml:space="preserve">
    <value>m_message_L_radio</value>
  </data>
  <data name="&gt;&gt;m_message_L_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_message_L_radio.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;m_message_L_radio.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="m_message_K_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_message_K_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>97, 57</value>
  </data>
  <data name="m_message_K_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>37, 19</value>
  </data>
  <data name="m_message_K_radio.TabIndex" type="System.Int32, mscorlib">
    <value>19</value>
  </data>
  <data name="m_message_K_radio.Text" xml:space="preserve">
    <value>K</value>
  </data>
  <data name="&gt;&gt;m_message_K_radio.Name" xml:space="preserve">
    <value>m_message_K_radio</value>
  </data>
  <data name="&gt;&gt;m_message_K_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_message_K_radio.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;m_message_K_radio.ZOrder" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="m_message_J_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_message_J_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>53, 57</value>
  </data>
  <data name="m_message_J_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>35, 19</value>
  </data>
  <data name="m_message_J_radio.TabIndex" type="System.Int32, mscorlib">
    <value>18</value>
  </data>
  <data name="m_message_J_radio.Text" xml:space="preserve">
    <value>J</value>
  </data>
  <data name="&gt;&gt;m_message_J_radio.Name" xml:space="preserve">
    <value>m_message_J_radio</value>
  </data>
  <data name="&gt;&gt;m_message_J_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_message_J_radio.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;m_message_J_radio.ZOrder" xml:space="preserve">
    <value>18</value>
  </data>
  <data name="m_message_I_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_message_I_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>9, 57</value>
  </data>
  <data name="m_message_I_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>32, 19</value>
  </data>
  <data name="m_message_I_radio.TabIndex" type="System.Int32, mscorlib">
    <value>17</value>
  </data>
  <data name="m_message_I_radio.Text" xml:space="preserve">
    <value>I</value>
  </data>
  <data name="&gt;&gt;m_message_I_radio.Name" xml:space="preserve">
    <value>m_message_I_radio</value>
  </data>
  <data name="&gt;&gt;m_message_I_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_message_I_radio.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;m_message_I_radio.ZOrder" xml:space="preserve">
    <value>19</value>
  </data>
  <data name="m_message_H_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_message_H_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>317, 23</value>
  </data>
  <data name="m_message_H_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>39, 19</value>
  </data>
  <data name="m_message_H_radio.TabIndex" type="System.Int32, mscorlib">
    <value>16</value>
  </data>
  <data name="m_message_H_radio.Text" xml:space="preserve">
    <value>H</value>
  </data>
  <data name="&gt;&gt;m_message_H_radio.Name" xml:space="preserve">
    <value>m_message_H_radio</value>
  </data>
  <data name="&gt;&gt;m_message_H_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_message_H_radio.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;m_message_H_radio.ZOrder" xml:space="preserve">
    <value>20</value>
  </data>
  <data name="m_message_G_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_message_G_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>273, 23</value>
  </data>
  <data name="m_message_G_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>40, 19</value>
  </data>
  <data name="m_message_G_radio.TabIndex" type="System.Int32, mscorlib">
    <value>15</value>
  </data>
  <data name="m_message_G_radio.Text" xml:space="preserve">
    <value>G</value>
  </data>
  <data name="&gt;&gt;m_message_G_radio.Name" xml:space="preserve">
    <value>m_message_G_radio</value>
  </data>
  <data name="&gt;&gt;m_message_G_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_message_G_radio.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;m_message_G_radio.ZOrder" xml:space="preserve">
    <value>21</value>
  </data>
  <data name="m_message_F_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_message_F_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>229, 23</value>
  </data>
  <data name="m_message_F_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>37, 19</value>
  </data>
  <data name="m_message_F_radio.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="m_message_F_radio.Text" xml:space="preserve">
    <value>F</value>
  </data>
  <data name="&gt;&gt;m_message_F_radio.Name" xml:space="preserve">
    <value>m_message_F_radio</value>
  </data>
  <data name="&gt;&gt;m_message_F_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_message_F_radio.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;m_message_F_radio.ZOrder" xml:space="preserve">
    <value>22</value>
  </data>
  <data name="m_message_E_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_message_E_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>185, 23</value>
  </data>
  <data name="m_message_E_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>38, 19</value>
  </data>
  <data name="m_message_E_radio.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="m_message_E_radio.Text" xml:space="preserve">
    <value>E</value>
  </data>
  <data name="&gt;&gt;m_message_E_radio.Name" xml:space="preserve">
    <value>m_message_E_radio</value>
  </data>
  <data name="&gt;&gt;m_message_E_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_message_E_radio.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;m_message_E_radio.ZOrder" xml:space="preserve">
    <value>23</value>
  </data>
  <data name="m_message_D_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_message_D_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>141, 23</value>
  </data>
  <data name="m_message_D_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>39, 19</value>
  </data>
  <data name="m_message_D_radio.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="m_message_D_radio.Text" xml:space="preserve">
    <value>D</value>
  </data>
  <data name="&gt;&gt;m_message_D_radio.Name" xml:space="preserve">
    <value>m_message_D_radio</value>
  </data>
  <data name="&gt;&gt;m_message_D_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_message_D_radio.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;m_message_D_radio.ZOrder" xml:space="preserve">
    <value>24</value>
  </data>
  <data name="m_message_C_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_message_C_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>97, 23</value>
  </data>
  <data name="m_message_C_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>39, 19</value>
  </data>
  <data name="m_message_C_radio.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="m_message_C_radio.Text" xml:space="preserve">
    <value>C</value>
  </data>
  <data name="&gt;&gt;m_message_C_radio.Name" xml:space="preserve">
    <value>m_message_C_radio</value>
  </data>
  <data name="&gt;&gt;m_message_C_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_message_C_radio.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;m_message_C_radio.ZOrder" xml:space="preserve">
    <value>25</value>
  </data>
  <data name="m_message_B_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_message_B_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>53, 23</value>
  </data>
  <data name="m_message_B_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>39, 19</value>
  </data>
  <data name="m_message_B_radio.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="m_message_B_radio.Text" xml:space="preserve">
    <value>B</value>
  </data>
  <data name="&gt;&gt;m_message_B_radio.Name" xml:space="preserve">
    <value>m_message_B_radio</value>
  </data>
  <data name="&gt;&gt;m_message_B_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_message_B_radio.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;m_message_B_radio.ZOrder" xml:space="preserve">
    <value>26</value>
  </data>
  <data name="m_message_A_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_message_A_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>9, 23</value>
  </data>
  <data name="m_message_A_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>38, 19</value>
  </data>
  <data name="m_message_A_radio.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="m_message_A_radio.Text" xml:space="preserve">
    <value>A</value>
  </data>
  <data name="&gt;&gt;m_message_A_radio.Name" xml:space="preserve">
    <value>m_message_A_radio</value>
  </data>
  <data name="&gt;&gt;m_message_A_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_message_A_radio.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;m_message_A_radio.ZOrder" xml:space="preserve">
    <value>27</value>
  </data>
  <data name="groupBox3.Location" type="System.Drawing.Point, System.Drawing">
    <value>924, 398</value>
  </data>
  <data name="groupBox3.Size" type="System.Drawing.Size, System.Drawing">
    <value>392, 164</value>
  </data>
  <data name="groupBox3.TabIndex" type="System.Int32, mscorlib">
    <value>43</value>
  </data>
  <data name="groupBox3.Text" xml:space="preserve">
    <value>Message type mask</value>
  </data>
  <data name="&gt;&gt;groupBox3.Name" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;groupBox3.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox3.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;groupBox3.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="m_coverage_all_disable_btn.Location" type="System.Drawing.Point, System.Drawing">
    <value>229, 120</value>
  </data>
  <data name="m_coverage_all_disable_btn.Size" type="System.Drawing.Size, System.Drawing">
    <value>113, 27</value>
  </data>
  <data name="m_coverage_all_disable_btn.TabIndex" type="System.Int32, mscorlib">
    <value>36</value>
  </data>
  <data name="m_coverage_all_disable_btn.Text" xml:space="preserve">
    <value>All Disable</value>
  </data>
  <data name="&gt;&gt;m_coverage_all_disable_btn.Name" xml:space="preserve">
    <value>m_coverage_all_disable_btn</value>
  </data>
  <data name="&gt;&gt;m_coverage_all_disable_btn.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_coverage_all_disable_btn.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;m_coverage_all_disable_btn.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="m_coverage_all_enable_btn.Location" type="System.Drawing.Point, System.Drawing">
    <value>110, 120</value>
  </data>
  <data name="m_coverage_all_enable_btn.Size" type="System.Drawing.Size, System.Drawing">
    <value>113, 27</value>
  </data>
  <data name="m_coverage_all_enable_btn.TabIndex" type="System.Int32, mscorlib">
    <value>35</value>
  </data>
  <data name="m_coverage_all_enable_btn.Text" xml:space="preserve">
    <value>All Enable</value>
  </data>
  <data name="&gt;&gt;m_coverage_all_enable_btn.Name" xml:space="preserve">
    <value>m_coverage_all_enable_btn</value>
  </data>
  <data name="&gt;&gt;m_coverage_all_enable_btn.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_coverage_all_enable_btn.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;m_coverage_all_enable_btn.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="m_coverage_Z_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_coverage_Z_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>53, 125</value>
  </data>
  <data name="m_coverage_Z_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>38, 19</value>
  </data>
  <data name="m_coverage_Z_radio.TabIndex" type="System.Int32, mscorlib">
    <value>34</value>
  </data>
  <data name="m_coverage_Z_radio.Text" xml:space="preserve">
    <value>Z</value>
  </data>
  <data name="&gt;&gt;m_coverage_Z_radio.Name" xml:space="preserve">
    <value>m_coverage_Z_radio</value>
  </data>
  <data name="&gt;&gt;m_coverage_Z_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_coverage_Z_radio.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;m_coverage_Z_radio.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="m_coverage_Y_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_coverage_Y_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>9, 125</value>
  </data>
  <data name="m_coverage_Y_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>37, 19</value>
  </data>
  <data name="m_coverage_Y_radio.TabIndex" type="System.Int32, mscorlib">
    <value>33</value>
  </data>
  <data name="m_coverage_Y_radio.Text" xml:space="preserve">
    <value>Y</value>
  </data>
  <data name="&gt;&gt;m_coverage_Y_radio.Name" xml:space="preserve">
    <value>m_coverage_Y_radio</value>
  </data>
  <data name="&gt;&gt;m_coverage_Y_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_coverage_Y_radio.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;m_coverage_Y_radio.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="m_coverage_X_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_coverage_X_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>317, 91</value>
  </data>
  <data name="m_coverage_X_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>38, 19</value>
  </data>
  <data name="m_coverage_X_radio.TabIndex" type="System.Int32, mscorlib">
    <value>32</value>
  </data>
  <data name="m_coverage_X_radio.Text" xml:space="preserve">
    <value>X</value>
  </data>
  <data name="&gt;&gt;m_coverage_X_radio.Name" xml:space="preserve">
    <value>m_coverage_X_radio</value>
  </data>
  <data name="&gt;&gt;m_coverage_X_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_coverage_X_radio.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;m_coverage_X_radio.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="m_coverage_W_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_coverage_W_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>273, 91</value>
  </data>
  <data name="m_coverage_W_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>43, 19</value>
  </data>
  <data name="m_coverage_W_radio.TabIndex" type="System.Int32, mscorlib">
    <value>31</value>
  </data>
  <data name="m_coverage_W_radio.Text" xml:space="preserve">
    <value>W</value>
  </data>
  <data name="&gt;&gt;m_coverage_W_radio.Name" xml:space="preserve">
    <value>m_coverage_W_radio</value>
  </data>
  <data name="&gt;&gt;m_coverage_W_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_coverage_W_radio.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;m_coverage_W_radio.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="m_coverage_V_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_coverage_V_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>229, 91</value>
  </data>
  <data name="m_coverage_V_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>37, 19</value>
  </data>
  <data name="m_coverage_V_radio.TabIndex" type="System.Int32, mscorlib">
    <value>30</value>
  </data>
  <data name="m_coverage_V_radio.Text" xml:space="preserve">
    <value>V</value>
  </data>
  <data name="&gt;&gt;m_coverage_V_radio.Name" xml:space="preserve">
    <value>m_coverage_V_radio</value>
  </data>
  <data name="&gt;&gt;m_coverage_V_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_coverage_V_radio.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;m_coverage_V_radio.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="m_coverage_U_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_coverage_U_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>185, 91</value>
  </data>
  <data name="m_coverage_U_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>39, 19</value>
  </data>
  <data name="m_coverage_U_radio.TabIndex" type="System.Int32, mscorlib">
    <value>29</value>
  </data>
  <data name="m_coverage_U_radio.Text" xml:space="preserve">
    <value>U</value>
  </data>
  <data name="&gt;&gt;m_coverage_U_radio.Name" xml:space="preserve">
    <value>m_coverage_U_radio</value>
  </data>
  <data name="&gt;&gt;m_coverage_U_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_coverage_U_radio.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;m_coverage_U_radio.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="m_coverage_T_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_coverage_T_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>141, 91</value>
  </data>
  <data name="m_coverage_T_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>37, 19</value>
  </data>
  <data name="m_coverage_T_radio.TabIndex" type="System.Int32, mscorlib">
    <value>28</value>
  </data>
  <data name="m_coverage_T_radio.Text" xml:space="preserve">
    <value>T</value>
  </data>
  <data name="&gt;&gt;m_coverage_T_radio.Name" xml:space="preserve">
    <value>m_coverage_T_radio</value>
  </data>
  <data name="&gt;&gt;m_coverage_T_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_coverage_T_radio.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;m_coverage_T_radio.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="m_coverage_S_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_coverage_S_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>97, 91</value>
  </data>
  <data name="m_coverage_S_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>39, 19</value>
  </data>
  <data name="m_coverage_S_radio.TabIndex" type="System.Int32, mscorlib">
    <value>27</value>
  </data>
  <data name="m_coverage_S_radio.Text" xml:space="preserve">
    <value>S</value>
  </data>
  <data name="&gt;&gt;m_coverage_S_radio.Name" xml:space="preserve">
    <value>m_coverage_S_radio</value>
  </data>
  <data name="&gt;&gt;m_coverage_S_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_coverage_S_radio.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;m_coverage_S_radio.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="m_coverage_R_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_coverage_R_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>53, 91</value>
  </data>
  <data name="m_coverage_R_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>39, 19</value>
  </data>
  <data name="m_coverage_R_radio.TabIndex" type="System.Int32, mscorlib">
    <value>26</value>
  </data>
  <data name="m_coverage_R_radio.Text" xml:space="preserve">
    <value>R</value>
  </data>
  <data name="&gt;&gt;m_coverage_R_radio.Name" xml:space="preserve">
    <value>m_coverage_R_radio</value>
  </data>
  <data name="&gt;&gt;m_coverage_R_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_coverage_R_radio.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;m_coverage_R_radio.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="m_coverage_Q_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_coverage_Q_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>9, 91</value>
  </data>
  <data name="m_coverage_Q_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>41, 19</value>
  </data>
  <data name="m_coverage_Q_radio.TabIndex" type="System.Int32, mscorlib">
    <value>25</value>
  </data>
  <data name="m_coverage_Q_radio.Text" xml:space="preserve">
    <value>Q</value>
  </data>
  <data name="&gt;&gt;m_coverage_Q_radio.Name" xml:space="preserve">
    <value>m_coverage_Q_radio</value>
  </data>
  <data name="&gt;&gt;m_coverage_Q_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_coverage_Q_radio.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;m_coverage_Q_radio.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="m_coverage_P_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_coverage_P_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>317, 57</value>
  </data>
  <data name="m_coverage_P_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>39, 19</value>
  </data>
  <data name="m_coverage_P_radio.TabIndex" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="m_coverage_P_radio.Text" xml:space="preserve">
    <value>P</value>
  </data>
  <data name="&gt;&gt;m_coverage_P_radio.Name" xml:space="preserve">
    <value>m_coverage_P_radio</value>
  </data>
  <data name="&gt;&gt;m_coverage_P_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_coverage_P_radio.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;m_coverage_P_radio.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="m_coverage_O_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_coverage_O_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>273, 57</value>
  </data>
  <data name="m_coverage_O_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>41, 19</value>
  </data>
  <data name="m_coverage_O_radio.TabIndex" type="System.Int32, mscorlib">
    <value>23</value>
  </data>
  <data name="m_coverage_O_radio.Text" xml:space="preserve">
    <value>O</value>
  </data>
  <data name="&gt;&gt;m_coverage_O_radio.Name" xml:space="preserve">
    <value>m_coverage_O_radio</value>
  </data>
  <data name="&gt;&gt;m_coverage_O_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_coverage_O_radio.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;m_coverage_O_radio.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="m_coverage_N_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_coverage_N_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>229, 57</value>
  </data>
  <data name="m_coverage_N_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>38, 19</value>
  </data>
  <data name="m_coverage_N_radio.TabIndex" type="System.Int32, mscorlib">
    <value>22</value>
  </data>
  <data name="m_coverage_N_radio.Text" xml:space="preserve">
    <value>N</value>
  </data>
  <data name="&gt;&gt;m_coverage_N_radio.Name" xml:space="preserve">
    <value>m_coverage_N_radio</value>
  </data>
  <data name="&gt;&gt;m_coverage_N_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_coverage_N_radio.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;m_coverage_N_radio.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="m_coverage_M_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_coverage_M_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>185, 57</value>
  </data>
  <data name="m_coverage_M_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>41, 19</value>
  </data>
  <data name="m_coverage_M_radio.TabIndex" type="System.Int32, mscorlib">
    <value>21</value>
  </data>
  <data name="m_coverage_M_radio.Text" xml:space="preserve">
    <value>M</value>
  </data>
  <data name="&gt;&gt;m_coverage_M_radio.Name" xml:space="preserve">
    <value>m_coverage_M_radio</value>
  </data>
  <data name="&gt;&gt;m_coverage_M_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_coverage_M_radio.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;m_coverage_M_radio.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="m_coverage_L_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_coverage_L_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>141, 57</value>
  </data>
  <data name="m_coverage_L_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>37, 19</value>
  </data>
  <data name="m_coverage_L_radio.TabIndex" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="m_coverage_L_radio.Text" xml:space="preserve">
    <value>L</value>
  </data>
  <data name="&gt;&gt;m_coverage_L_radio.Name" xml:space="preserve">
    <value>m_coverage_L_radio</value>
  </data>
  <data name="&gt;&gt;m_coverage_L_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_coverage_L_radio.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;m_coverage_L_radio.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="m_coverage_K_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_coverage_K_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>97, 57</value>
  </data>
  <data name="m_coverage_K_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>37, 19</value>
  </data>
  <data name="m_coverage_K_radio.TabIndex" type="System.Int32, mscorlib">
    <value>19</value>
  </data>
  <data name="m_coverage_K_radio.Text" xml:space="preserve">
    <value>K</value>
  </data>
  <data name="&gt;&gt;m_coverage_K_radio.Name" xml:space="preserve">
    <value>m_coverage_K_radio</value>
  </data>
  <data name="&gt;&gt;m_coverage_K_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_coverage_K_radio.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;m_coverage_K_radio.ZOrder" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="m_coverage_J_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_coverage_J_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>53, 57</value>
  </data>
  <data name="m_coverage_J_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>35, 19</value>
  </data>
  <data name="m_coverage_J_radio.TabIndex" type="System.Int32, mscorlib">
    <value>18</value>
  </data>
  <data name="m_coverage_J_radio.Text" xml:space="preserve">
    <value>J</value>
  </data>
  <data name="&gt;&gt;m_coverage_J_radio.Name" xml:space="preserve">
    <value>m_coverage_J_radio</value>
  </data>
  <data name="&gt;&gt;m_coverage_J_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_coverage_J_radio.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;m_coverage_J_radio.ZOrder" xml:space="preserve">
    <value>18</value>
  </data>
  <data name="m_coverage_I_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_coverage_I_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>9, 57</value>
  </data>
  <data name="m_coverage_I_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>32, 19</value>
  </data>
  <data name="m_coverage_I_radio.TabIndex" type="System.Int32, mscorlib">
    <value>17</value>
  </data>
  <data name="m_coverage_I_radio.Text" xml:space="preserve">
    <value>I</value>
  </data>
  <data name="&gt;&gt;m_coverage_I_radio.Name" xml:space="preserve">
    <value>m_coverage_I_radio</value>
  </data>
  <data name="&gt;&gt;m_coverage_I_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_coverage_I_radio.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;m_coverage_I_radio.ZOrder" xml:space="preserve">
    <value>19</value>
  </data>
  <data name="m_coverage_H_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_coverage_H_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>317, 23</value>
  </data>
  <data name="m_coverage_H_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>39, 19</value>
  </data>
  <data name="m_coverage_H_radio.TabIndex" type="System.Int32, mscorlib">
    <value>16</value>
  </data>
  <data name="m_coverage_H_radio.Text" xml:space="preserve">
    <value>H</value>
  </data>
  <data name="&gt;&gt;m_coverage_H_radio.Name" xml:space="preserve">
    <value>m_coverage_H_radio</value>
  </data>
  <data name="&gt;&gt;m_coverage_H_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_coverage_H_radio.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;m_coverage_H_radio.ZOrder" xml:space="preserve">
    <value>20</value>
  </data>
  <data name="m_coverage_G_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_coverage_G_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>273, 23</value>
  </data>
  <data name="m_coverage_G_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>40, 19</value>
  </data>
  <data name="m_coverage_G_radio.TabIndex" type="System.Int32, mscorlib">
    <value>15</value>
  </data>
  <data name="m_coverage_G_radio.Text" xml:space="preserve">
    <value>G</value>
  </data>
  <data name="&gt;&gt;m_coverage_G_radio.Name" xml:space="preserve">
    <value>m_coverage_G_radio</value>
  </data>
  <data name="&gt;&gt;m_coverage_G_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_coverage_G_radio.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;m_coverage_G_radio.ZOrder" xml:space="preserve">
    <value>21</value>
  </data>
  <data name="m_coverage_F_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_coverage_F_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>229, 23</value>
  </data>
  <data name="m_coverage_F_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>37, 19</value>
  </data>
  <data name="m_coverage_F_radio.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="m_coverage_F_radio.Text" xml:space="preserve">
    <value>F</value>
  </data>
  <data name="&gt;&gt;m_coverage_F_radio.Name" xml:space="preserve">
    <value>m_coverage_F_radio</value>
  </data>
  <data name="&gt;&gt;m_coverage_F_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_coverage_F_radio.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;m_coverage_F_radio.ZOrder" xml:space="preserve">
    <value>22</value>
  </data>
  <data name="m_coverage_E_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_coverage_E_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>185, 23</value>
  </data>
  <data name="m_coverage_E_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>38, 19</value>
  </data>
  <data name="m_coverage_E_radio.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="m_coverage_E_radio.Text" xml:space="preserve">
    <value>E</value>
  </data>
  <data name="&gt;&gt;m_coverage_E_radio.Name" xml:space="preserve">
    <value>m_coverage_E_radio</value>
  </data>
  <data name="&gt;&gt;m_coverage_E_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_coverage_E_radio.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;m_coverage_E_radio.ZOrder" xml:space="preserve">
    <value>23</value>
  </data>
  <data name="m_coverage_D_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_coverage_D_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>141, 23</value>
  </data>
  <data name="m_coverage_D_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>39, 19</value>
  </data>
  <data name="m_coverage_D_radio.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="m_coverage_D_radio.Text" xml:space="preserve">
    <value>D</value>
  </data>
  <data name="&gt;&gt;m_coverage_D_radio.Name" xml:space="preserve">
    <value>m_coverage_D_radio</value>
  </data>
  <data name="&gt;&gt;m_coverage_D_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_coverage_D_radio.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;m_coverage_D_radio.ZOrder" xml:space="preserve">
    <value>24</value>
  </data>
  <data name="m_coverage_C_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_coverage_C_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>97, 23</value>
  </data>
  <data name="m_coverage_C_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>39, 19</value>
  </data>
  <data name="m_coverage_C_radio.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="m_coverage_C_radio.Text" xml:space="preserve">
    <value>C</value>
  </data>
  <data name="&gt;&gt;m_coverage_C_radio.Name" xml:space="preserve">
    <value>m_coverage_C_radio</value>
  </data>
  <data name="&gt;&gt;m_coverage_C_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_coverage_C_radio.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;m_coverage_C_radio.ZOrder" xml:space="preserve">
    <value>25</value>
  </data>
  <data name="m_coverage_B_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_coverage_B_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>53, 23</value>
  </data>
  <data name="m_coverage_B_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>39, 19</value>
  </data>
  <data name="m_coverage_B_radio.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="m_coverage_B_radio.Text" xml:space="preserve">
    <value>B</value>
  </data>
  <data name="&gt;&gt;m_coverage_B_radio.Name" xml:space="preserve">
    <value>m_coverage_B_radio</value>
  </data>
  <data name="&gt;&gt;m_coverage_B_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_coverage_B_radio.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;m_coverage_B_radio.ZOrder" xml:space="preserve">
    <value>26</value>
  </data>
  <data name="m_coverage_A_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_coverage_A_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>9, 23</value>
  </data>
  <data name="m_coverage_A_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>38, 19</value>
  </data>
  <data name="m_coverage_A_radio.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="m_coverage_A_radio.Text" xml:space="preserve">
    <value>A</value>
  </data>
  <data name="&gt;&gt;m_coverage_A_radio.Name" xml:space="preserve">
    <value>m_coverage_A_radio</value>
  </data>
  <data name="&gt;&gt;m_coverage_A_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_coverage_A_radio.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;m_coverage_A_radio.ZOrder" xml:space="preserve">
    <value>27</value>
  </data>
  <data name="groupBox2.Location" type="System.Drawing.Point, System.Drawing">
    <value>526, 398</value>
  </data>
  <data name="groupBox2.Size" type="System.Drawing.Size, System.Drawing">
    <value>392, 164</value>
  </data>
  <data name="groupBox2.TabIndex" type="System.Int32, mscorlib">
    <value>42</value>
  </data>
  <data name="groupBox2.Text" xml:space="preserve">
    <value>Coverage area mask</value>
  </data>
  <data name="&gt;&gt;groupBox2.Name" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;groupBox2.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;groupBox2.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="m_4209_5KHz_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_4209_5KHz_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>18, 94</value>
  </data>
  <data name="m_4209_5KHz_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>99, 19</value>
  </data>
  <data name="m_4209_5KHz_radio.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="m_4209_5KHz_radio.Text" xml:space="preserve">
    <value>4209.5KHz</value>
  </data>
  <data name="&gt;&gt;m_4209_5KHz_radio.Name" xml:space="preserve">
    <value>m_4209_5KHz_radio</value>
  </data>
  <data name="&gt;&gt;m_4209_5KHz_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_4209_5KHz_radio.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;m_4209_5KHz_radio.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="m_518KHz_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_518KHz_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>18, 60</value>
  </data>
  <data name="m_518KHz_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>78, 19</value>
  </data>
  <data name="m_518KHz_radio.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="m_518KHz_radio.Text" xml:space="preserve">
    <value>518KHz</value>
  </data>
  <data name="&gt;&gt;m_518KHz_radio.Name" xml:space="preserve">
    <value>m_518KHz_radio</value>
  </data>
  <data name="&gt;&gt;m_518KHz_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_518KHz_radio.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;m_518KHz_radio.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="m_490KHz_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_490KHz_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>18, 26</value>
  </data>
  <data name="m_490KHz_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>78, 19</value>
  </data>
  <data name="m_490KHz_radio.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="m_490KHz_radio.Text" xml:space="preserve">
    <value>490KHz</value>
  </data>
  <data name="&gt;&gt;m_490KHz_radio.Name" xml:space="preserve">
    <value>m_490KHz_radio</value>
  </data>
  <data name="&gt;&gt;m_490KHz_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_490KHz_radio.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;m_490KHz_radio.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="groupBox1.Location" type="System.Drawing.Point, System.Drawing">
    <value>738, 228</value>
  </data>
  <data name="groupBox1.Size" type="System.Drawing.Size, System.Drawing">
    <value>180, 164</value>
  </data>
  <data name="groupBox1.TabIndex" type="System.Int32, mscorlib">
    <value>41</value>
  </data>
  <data name="groupBox1.Text" xml:space="preserve">
    <value>Frequency</value>
  </data>
  <data name="&gt;&gt;groupBox1.Name" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;groupBox1.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;groupBox1.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="m_ins_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_ins_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>41, 128</value>
  </data>
  <data name="m_ins_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>50, 19</value>
  </data>
  <data name="m_ins_radio.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="m_ins_radio.Text" xml:space="preserve">
    <value>INS</value>
  </data>
  <data name="&gt;&gt;m_ins_radio.Name" xml:space="preserve">
    <value>m_ins_radio</value>
  </data>
  <data name="&gt;&gt;m_ins_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_ins_radio.Parent" xml:space="preserve">
    <value>Purpose</value>
  </data>
  <data name="&gt;&gt;m_ins_radio.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="m_printer_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_printer_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>41, 94</value>
  </data>
  <data name="m_printer_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>69, 19</value>
  </data>
  <data name="m_printer_radio.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="m_printer_radio.Text" xml:space="preserve">
    <value>Printer</value>
  </data>
  <data name="&gt;&gt;m_printer_radio.Name" xml:space="preserve">
    <value>m_printer_radio</value>
  </data>
  <data name="&gt;&gt;m_printer_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_printer_radio.Parent" xml:space="preserve">
    <value>Purpose</value>
  </data>
  <data name="&gt;&gt;m_printer_radio.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="m_storage_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_storage_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>41, 60</value>
  </data>
  <data name="m_storage_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>79, 19</value>
  </data>
  <data name="m_storage_radio.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="m_storage_radio.Text" xml:space="preserve">
    <value>Storage</value>
  </data>
  <data name="&gt;&gt;m_storage_radio.Name" xml:space="preserve">
    <value>m_storage_radio</value>
  </data>
  <data name="&gt;&gt;m_storage_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_storage_radio.Parent" xml:space="preserve">
    <value>Purpose</value>
  </data>
  <data name="&gt;&gt;m_storage_radio.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="m_given_radio.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="m_given_radio.Location" type="System.Drawing.Point, System.Drawing">
    <value>41, 26</value>
  </data>
  <data name="m_given_radio.Size" type="System.Drawing.Size, System.Drawing">
    <value>66, 19</value>
  </data>
  <data name="m_given_radio.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="m_given_radio.Text" xml:space="preserve">
    <value>Given</value>
  </data>
  <data name="&gt;&gt;m_given_radio.Name" xml:space="preserve">
    <value>m_given_radio</value>
  </data>
  <data name="&gt;&gt;m_given_radio.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_given_radio.Parent" xml:space="preserve">
    <value>Purpose</value>
  </data>
  <data name="&gt;&gt;m_given_radio.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="Purpose.Location" type="System.Drawing.Point, System.Drawing">
    <value>526, 227</value>
  </data>
  <data name="Purpose.Size" type="System.Drawing.Size, System.Drawing">
    <value>206, 165</value>
  </data>
  <data name="Purpose.TabIndex" type="System.Int32, mscorlib">
    <value>40</value>
  </data>
  <data name="Purpose.Text" xml:space="preserve">
    <value>Purpose</value>
  </data>
  <data name="&gt;&gt;Purpose.Name" xml:space="preserve">
    <value>Purpose</value>
  </data>
  <data name="&gt;&gt;Purpose.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;Purpose.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;Purpose.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="m_NRM_monitor.Font" type="System.Drawing.Font, System.Drawing">
    <value>Roboto, 9.5pt</value>
  </data>
  <data name="m_NRM_monitor.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 31</value>
  </data>
  <data name="m_NRM_monitor.MaxLength" type="System.Int32, mscorlib">
    <value>1000000000</value>
  </data>
  <data name="m_NRM_monitor.Multiline" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="m_NRM_monitor.ScrollBars" type="System.Windows.Forms.ScrollBars, System.Windows.Forms">
    <value>Vertical</value>
  </data>
  <data name="m_NRM_monitor.Size" type="System.Drawing.Size, System.Drawing">
    <value>508, 574</value>
  </data>
  <data name="m_NRM_monitor.TabIndex" type="System.Int32, mscorlib">
    <value>45</value>
  </data>
  <data name="&gt;&gt;m_NRM_monitor.Name" xml:space="preserve">
    <value>m_NRM_monitor</value>
  </data>
  <data name="&gt;&gt;m_NRM_monitor.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_NRM_monitor.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;m_NRM_monitor.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="m_NRM_talk_id_textbox.Location" type="System.Drawing.Point, System.Drawing">
    <value>17, 25</value>
  </data>
  <data name="m_NRM_talk_id_textbox.Size" type="System.Drawing.Size, System.Drawing">
    <value>108, 25</value>
  </data>
  <data name="m_NRM_talk_id_textbox.TabIndex" type="System.Int32, mscorlib">
    <value>50</value>
  </data>
  <data name="&gt;&gt;m_NRM_talk_id_textbox.Name" xml:space="preserve">
    <value>m_NRM_talk_id_textbox</value>
  </data>
  <data name="&gt;&gt;m_NRM_talk_id_textbox.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;m_NRM_talk_id_textbox.Parent" xml:space="preserve">
    <value>groupBox4</value>
  </data>
  <data name="&gt;&gt;m_NRM_talk_id_textbox.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="groupBox4.Location" type="System.Drawing.Point, System.Drawing">
    <value>526, 158</value>
  </data>
  <data name="groupBox4.Size" type="System.Drawing.Size, System.Drawing">
    <value>196, 63</value>
  </data>
  <data name="groupBox4.TabIndex" type="System.Int32, mscorlib">
    <value>52</value>
  </data>
  <data name="groupBox4.Text" xml:space="preserve">
    <value>Talker ID</value>
  </data>
  <data name="&gt;&gt;groupBox4.Name" xml:space="preserve">
    <value>groupBox4</value>
  </data>
  <data name="&gt;&gt;groupBox4.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox4.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;groupBox4.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="tb_450_line_count.Location" type="System.Drawing.Point, System.Drawing">
    <value>274, 89</value>
  </data>
  <data name="tb_450_line_count.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="tb_450_line_count.Size" type="System.Drawing.Size, System.Drawing">
    <value>82, 25</value>
  </data>
  <data name="tb_450_line_count.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="&gt;&gt;tb_450_line_count.Name" xml:space="preserve">
    <value>tb_450_line_count</value>
  </data>
  <data name="&gt;&gt;tb_450_line_count.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tb_450_line_count.Parent" xml:space="preserve">
    <value>gb_interface</value>
  </data>
  <data name="&gt;&gt;tb_450_line_count.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="label13.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label13.Location" type="System.Drawing.Point, System.Drawing">
    <value>248, 94</value>
  </data>
  <data name="label13.Size" type="System.Drawing.Size, System.Drawing">
    <value>21, 15</value>
  </data>
  <data name="label13.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="label13.Text" xml:space="preserve">
    <value>N:</value>
  </data>
  <data name="&gt;&gt;label13.Name" xml:space="preserve">
    <value>label13</value>
  </data>
  <data name="&gt;&gt;label13.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label13.Parent" xml:space="preserve">
    <value>gb_interface</value>
  </data>
  <data name="&gt;&gt;label13.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="cb_n_enable.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="cb_n_enable.Location" type="System.Drawing.Point, System.Drawing">
    <value>384, 92</value>
  </data>
  <data name="cb_n_enable.Size" type="System.Drawing.Size, System.Drawing">
    <value>85, 19</value>
  </data>
  <data name="cb_n_enable.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="cb_n_enable.Text" xml:space="preserve">
    <value>n enable</value>
  </data>
  <data name="&gt;&gt;cb_n_enable.Name" xml:space="preserve">
    <value>cb_n_enable</value>
  </data>
  <data name="&gt;&gt;cb_n_enable.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cb_n_enable.Parent" xml:space="preserve">
    <value>gb_interface</value>
  </data>
  <data name="&gt;&gt;cb_n_enable.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="cb_d_enable.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="cb_d_enable.Location" type="System.Drawing.Point, System.Drawing">
    <value>383, 59</value>
  </data>
  <data name="cb_d_enable.Size" type="System.Drawing.Size, System.Drawing">
    <value>85, 19</value>
  </data>
  <data name="cb_d_enable.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="cb_d_enable.Text" xml:space="preserve">
    <value>d enable</value>
  </data>
  <data name="&gt;&gt;cb_d_enable.Name" xml:space="preserve">
    <value>cb_d_enable</value>
  </data>
  <data name="&gt;&gt;cb_d_enable.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cb_d_enable.Parent" xml:space="preserve">
    <value>gb_interface</value>
  </data>
  <data name="&gt;&gt;cb_d_enable.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="cb_s_enable.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="cb_s_enable.Location" type="System.Drawing.Point, System.Drawing">
    <value>384, 25</value>
  </data>
  <data name="cb_s_enable.Size" type="System.Drawing.Size, System.Drawing">
    <value>85, 19</value>
  </data>
  <data name="cb_s_enable.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="cb_s_enable.Text" xml:space="preserve">
    <value>s enable</value>
  </data>
  <data name="&gt;&gt;cb_s_enable.Name" xml:space="preserve">
    <value>cb_s_enable</value>
  </data>
  <data name="&gt;&gt;cb_s_enable.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cb_s_enable.Parent" xml:space="preserve">
    <value>gb_interface</value>
  </data>
  <data name="&gt;&gt;cb_s_enable.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="tb_450_dst.Location" type="System.Drawing.Point, System.Drawing">
    <value>274, 56</value>
  </data>
  <data name="tb_450_dst.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="tb_450_dst.Size" type="System.Drawing.Size, System.Drawing">
    <value>82, 25</value>
  </data>
  <data name="tb_450_dst.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="tb_450_dst.Text" xml:space="preserve">
    <value>CV0001</value>
  </data>
  <data name="&gt;&gt;tb_450_dst.Name" xml:space="preserve">
    <value>tb_450_dst</value>
  </data>
  <data name="&gt;&gt;tb_450_dst.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tb_450_dst.Parent" xml:space="preserve">
    <value>gb_interface</value>
  </data>
  <data name="&gt;&gt;tb_450_dst.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="tb_450_src.Location" type="System.Drawing.Point, System.Drawing">
    <value>274, 22</value>
  </data>
  <data name="tb_450_src.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="tb_450_src.Size" type="System.Drawing.Size, System.Drawing">
    <value>82, 25</value>
  </data>
  <data name="tb_450_src.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="tb_450_src.Text" xml:space="preserve">
    <value>EI0001</value>
  </data>
  <data name="&gt;&gt;tb_450_src.Name" xml:space="preserve">
    <value>tb_450_src</value>
  </data>
  <data name="&gt;&gt;tb_450_src.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tb_450_src.Parent" xml:space="preserve">
    <value>gb_interface</value>
  </data>
  <data name="&gt;&gt;tb_450_src.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="label12.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label12.Location" type="System.Drawing.Point, System.Drawing">
    <value>248, 61</value>
  </data>
  <data name="label12.Size" type="System.Drawing.Size, System.Drawing">
    <value>22, 15</value>
  </data>
  <data name="label12.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="label12.Text" xml:space="preserve">
    <value>D:</value>
  </data>
  <data name="&gt;&gt;label12.Name" xml:space="preserve">
    <value>label12</value>
  </data>
  <data name="&gt;&gt;label12.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label12.Parent" xml:space="preserve">
    <value>gb_interface</value>
  </data>
  <data name="&gt;&gt;label12.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="label11.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label11.Location" type="System.Drawing.Point, System.Drawing">
    <value>248, 26</value>
  </data>
  <data name="label11.Size" type="System.Drawing.Size, System.Drawing">
    <value>22, 15</value>
  </data>
  <data name="label11.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="label11.Text" xml:space="preserve">
    <value>S:</value>
  </data>
  <data name="&gt;&gt;label11.Name" xml:space="preserve">
    <value>label11</value>
  </data>
  <data name="&gt;&gt;label11.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label11.Parent" xml:space="preserve">
    <value>gb_interface</value>
  </data>
  <data name="&gt;&gt;label11.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="rb_61162_450.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="rb_61162_450.Location" type="System.Drawing.Point, System.Drawing">
    <value>136, 49</value>
  </data>
  <data name="rb_61162_450.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="rb_61162_450.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 19</value>
  </data>
  <data name="rb_61162_450.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="rb_61162_450.Text" xml:space="preserve">
    <value>61162-450</value>
  </data>
  <data name="&gt;&gt;rb_61162_450.Name" xml:space="preserve">
    <value>rb_61162_450</value>
  </data>
  <data name="&gt;&gt;rb_61162_450.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;rb_61162_450.Parent" xml:space="preserve">
    <value>gb_interface</value>
  </data>
  <data name="&gt;&gt;rb_61162_450.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="rb_61162_1_2.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="rb_61162_1_2.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 49</value>
  </data>
  <data name="rb_61162_1_2.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="rb_61162_1_2.Size" type="System.Drawing.Size, System.Drawing">
    <value>98, 19</value>
  </data>
  <data name="rb_61162_1_2.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="rb_61162_1_2.Text" xml:space="preserve">
    <value>61162-1/2</value>
  </data>
  <data name="&gt;&gt;rb_61162_1_2.Name" xml:space="preserve">
    <value>rb_61162_1_2</value>
  </data>
  <data name="&gt;&gt;rb_61162_1_2.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;rb_61162_1_2.Parent" xml:space="preserve">
    <value>gb_interface</value>
  </data>
  <data name="&gt;&gt;rb_61162_1_2.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="gb_interface.Font" type="System.Drawing.Font, System.Drawing">
    <value>굴림, 9pt</value>
  </data>
  <data name="gb_interface.Location" type="System.Drawing.Point, System.Drawing">
    <value>526, 31</value>
  </data>
  <data name="gb_interface.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="gb_interface.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="gb_interface.Size" type="System.Drawing.Size, System.Drawing">
    <value>495, 120</value>
  </data>
  <data name="gb_interface.TabIndex" type="System.Int32, mscorlib">
    <value>53</value>
  </data>
  <data name="gb_interface.Text" xml:space="preserve">
    <value>INTERFACE</value>
  </data>
  <data name="&gt;&gt;gb_interface.Name" xml:space="preserve">
    <value>gb_interface</value>
  </data>
  <data name="&gt;&gt;gb_interface.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;gb_interface.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;gb_interface.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>8, 15</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>1400, 800</value>
  </data>
  <data name="$this.TabText" xml:space="preserve">
    <value>Sentence_NRM_Form</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>Sentence_NRM_Form</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>Sentence_NRM_Form</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>WeifenLuo.WinFormsUI.DockContent, WeifenLuo.WinFormsUI.Docking, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
</root>