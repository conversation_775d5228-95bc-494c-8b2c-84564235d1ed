﻿namespace AssistPro.vhf
{
    partial class IV2500A_Form
    {
        /// <summary> 
        /// 필수 디자이너 변수입니다.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 사용 중인 모든 리소스를 정리합니다.
        /// </summary>
        /// <param name="disposing">관리되는 리소스를 삭제해야 하면 true이고, 그렇지 않으면 false입니다.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 구성 요소 디자이너에서 생성한 코드

        /// <summary> 
        /// 디자이너 지원에 필요한 메서드입니다. 
        /// 이 메서드의 내용을 코드 편집기로 수정하지 마세요.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle23 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle24 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle13 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle14 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle15 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle16 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle17 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle18 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle19 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle20 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle21 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle22 = new System.Windows.Forms.DataGridViewCellStyle();
            this.toolStrip1 = new System.Windows.Forms.ToolStrip();
            this.toolStripLabel1 = new System.Windows.Forms.ToolStripLabel();
            this.cbCom = new System.Windows.Forms.ToolStripComboBox();
            this.btnConnect = new System.Windows.Forms.ToolStripButton();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.btnSetController = new System.Windows.Forms.ToolStripButton();
            this.lb_bb_ver = new System.Windows.Forms.ToolStripLabel();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tabPage1 = new System.Windows.Forms.TabPage();
            this.groupBox6 = new System.Windows.Forms.GroupBox();
            this.groupBox9 = new System.Windows.Forms.GroupBox();
            this.label47 = new System.Windows.Forms.Label();
            this.tb_dcoffset = new System.Windows.Forms.TextBox();
            this.label46 = new System.Windows.Forms.Label();
            this.label35 = new System.Windows.Forms.Label();
            this.tb_dds_adj = new System.Windows.Forms.TextBox();
            this.label32 = new System.Windows.Forms.Label();
            this.tb_pwr_cnt = new System.Windows.Forms.TextBox();
            this.label34 = new System.Windows.Forms.Label();
            this.label36 = new System.Windows.Forms.Label();
            this.groupBox8 = new System.Windows.Forms.GroupBox();
            this.lb_wkr_rx_pwr = new System.Windows.Forms.Label();
            this.tb_wkr_freq = new System.Windows.Forms.TextBox();
            this.label21 = new System.Windows.Forms.Label();
            this.label33 = new System.Windows.Forms.Label();
            this.tb_wkr_rx_pwr = new System.Windows.Forms.TrackBar();
            this.label23 = new System.Windows.Forms.Label();
            this.groupBox7 = new System.Windows.Forms.GroupBox();
            this.label43 = new System.Windows.Forms.Label();
            this.label42 = new System.Windows.Forms.Label();
            this.tb_main_tx_freq = new System.Windows.Forms.TextBox();
            this.lb_main_tx_pwr = new System.Windows.Forms.Label();
            this.lb_main_rx_pwr = new System.Windows.Forms.Label();
            this.label29 = new System.Windows.Forms.Label();
            this.tb_main_tx_pwr = new System.Windows.Forms.TrackBar();
            this.label24 = new System.Windows.Forms.Label();
            this.tb_main_rx_pwr = new System.Windows.Forms.TrackBar();
            this.tb_main_rx_freq = new System.Windows.Forms.TextBox();
            this.label30 = new System.Windows.Forms.Label();
            this.label22 = new System.Windows.Forms.Label();
            this.gb_audio_codec = new System.Windows.Forms.GroupBox();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.lb_out_rx_bb = new System.Windows.Forms.Label();
            this.lb_front_ext_spk = new System.Windows.Forms.Label();
            this.lb_rear_wing_h_spk = new System.Windows.Forms.Label();
            this.lb_front_h_spk = new System.Windows.Forms.Label();
            this.tb_out_rx_bb = new System.Windows.Forms.TrackBar();
            this.label25 = new System.Windows.Forms.Label();
            this.tb_front_ext_spk = new System.Windows.Forms.TrackBar();
            this.label26 = new System.Windows.Forms.Label();
            this.tb_rear_wing_h_spk = new System.Windows.Forms.TrackBar();
            this.label27 = new System.Windows.Forms.Label();
            this.tb_front_h_spk = new System.Windows.Forms.TrackBar();
            this.label28 = new System.Windows.Forms.Label();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.lb_rx_bb = new System.Windows.Forms.Label();
            this.lb_wing_mic = new System.Windows.Forms.Label();
            this.lb_rear_mic = new System.Windows.Forms.Label();
            this.lb_front_mic = new System.Windows.Forms.Label();
            this.tb_rx_bb = new System.Windows.Forms.TrackBar();
            this.label20 = new System.Windows.Forms.Label();
            this.tb_wing_mic = new System.Windows.Forms.TrackBar();
            this.label19 = new System.Windows.Forms.Label();
            this.tb_rear_mic = new System.Windows.Forms.TrackBar();
            this.label18 = new System.Windows.Forms.Label();
            this.tb_front_mic = new System.Windows.Forms.TrackBar();
            this.label17 = new System.Windows.Forms.Label();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.label12 = new System.Windows.Forms.Label();
            this.tb_limiter = new System.Windows.Forms.TextBox();
            this.label11 = new System.Windows.Forms.Label();
            this.lb_att_value = new System.Windows.Forms.Label();
            this.lb_gain_value = new System.Windows.Forms.Label();
            this.tb_att = new System.Windows.Forms.TextBox();
            this.tb_gain = new System.Windows.Forms.TextBox();
            this.att_trackBar = new System.Windows.Forms.TrackBar();
            this.label10 = new System.Windows.Forms.Label();
            this.gain_trackBar = new System.Windows.Forms.TrackBar();
            this.label9 = new System.Windows.Forms.Label();
            this.gb_OpMode = new System.Windows.Forms.GroupBox();
            this.rb_SpaceMode = new System.Windows.Forms.RadioButton();
            this.rb_MarkMode = new System.Windows.Forms.RadioButton();
            this.rb_DotMode = new System.Windows.Forms.RadioButton();
            this.rb_TxMode = new System.Windows.Forms.RadioButton();
            this.rb_RxMode = new System.Windows.Forms.RadioButton();
            this.tabPage2 = new System.Windows.Forms.TabPage();
            this.groupBox5 = new System.Windows.Forms.GroupBox();
            this.lb_selftest_tot_fail = new System.Windows.Forms.Label();
            this.lb_selftest_tot_pass = new System.Windows.Forms.Label();
            this.dgv_selftest = new System.Windows.Forms.DataGridView();
            this.TimeCol = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ItemCol = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ResultCol = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.bt_export = new System.Windows.Forms.Button();
            this.btn_clean_selftest = new System.Windows.Forms.Button();
            this.btn_run = new System.Windows.Forms.Button();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.lb_time = new System.Windows.Forms.Label();
            this.lb_elapse = new System.Windows.Forms.Label();
            this.numericUpDown1 = new System.Windows.Forms.NumericUpDown();
            this.lb_data_bad = new System.Windows.Forms.Label();
            this.lb_timeout = new System.Windows.Forms.Label();
            this.lb_rate = new System.Windows.Forms.Label();
            this.lb_retry = new System.Windows.Forms.Label();
            this.lb_pass = new System.Windows.Forms.Label();
            this.lb_count = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.bt_clear = new System.Windows.Forms.Button();
            this.btn_stop = new System.Windows.Forms.Button();
            this.label1 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.btn_start = new System.Windows.Forms.Button();
            this.label6 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.tabPage3 = new System.Windows.Forms.TabPage();
            this.tableLayoutPanel1 = new System.Windows.Forms.TableLayoutPanel();
            this.dgvDscSend = new System.Windows.Forms.DataGridView();
            this.DscTxType = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.DscTxFormat = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.DscTxAddr = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.DscTxCategory = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.DscTxSelfID = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.DscTxTeleCom = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.DscTxTCom1 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.DscTxTCom2 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.DscTxSubCom = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.DscTxFreq = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.DscTxDistID = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.DscTxNature = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.DscTxLatitude = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.DscTxLongitude = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.DscTxUtc = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.DscTxEos = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.DscTxExpn = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.SendDsc = new System.Windows.Forms.DataGridViewButtonColumn();
            this.dgvDscRsv = new System.Windows.Forms.DataGridView();
            this.RsvTime = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.DscFormat = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.DscAddr = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.DscCategory = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.DscSelfID = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.DscTeleCom1 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.DscTeleCom2 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.DscFreq = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.DscDistID = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.DscNature = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.DscPOS = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.DscUTC = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.DscExpnPos = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.DscEOS = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.panel1 = new System.Windows.Forms.Panel();
            this.lb_rx_dsc = new System.Windows.Forms.Label();
            this.panel2 = new System.Windows.Forms.Panel();
            this.lb_tx_dsc = new System.Windows.Forms.Label();
            this.tableLayoutPanel2 = new System.Windows.Forms.TableLayoutPanel();
            this.btn_rx_clear = new System.Windows.Forms.Button();
            this.btn_import = new System.Windows.Forms.Button();
            this.btn_export = new System.Windows.Forms.Button();
            this.tableLayoutPanel3 = new System.Windows.Forms.TableLayoutPanel();
            this.btnDSCClear = new System.Windows.Forms.Button();
            this.btn_tx_import = new System.Windows.Forms.Button();
            this.btnDSCAdd = new System.Windows.Forms.Button();
            this.btnDSCDelete = new System.Windows.Forms.Button();
            this.btnDscEdit = new System.Windows.Forms.Button();
            this.btn_tx_export = new System.Windows.Forms.Button();
            this.label31 = new System.Windows.Forms.Label();
            this.tb_dsc_send_auto_intval = new System.Windows.Forms.TextBox();
            this.btn_dsc_auto_send = new System.Windows.Forms.Button();
            this.label48 = new System.Windows.Forms.Label();
            this.tb_num_of_runs = new System.Windows.Forms.TextBox();
            this.cb_keep_run = new System.Windows.Forms.CheckBox();
            this.label13 = new System.Windows.Forms.Label();
            this.tabPage4 = new System.Windows.Forms.TabPage();
            this.tableLayoutPanel4 = new System.Windows.Forms.TableLayoutPanel();
            this.tableLayoutPanel5 = new System.Windows.Forms.TableLayoutPanel();
            this.tb_title = new System.Windows.Forms.TextBox();
            this.tb_category = new System.Windows.Forms.TextBox();
            this.tb_priority = new System.Windows.Forms.TextBox();
            this.tb_instance = new System.Windows.Forms.TextBox();
            this.lb_ID = new System.Windows.Forms.Label();
            this.label14 = new System.Windows.Forms.Label();
            this.lb_priority = new System.Windows.Forms.Label();
            this.lb_instance = new System.Windows.Forms.Label();
            this.label15 = new System.Windows.Forms.Label();
            this.tb_id = new System.Windows.Forms.TextBox();
            this.tb_desc = new System.Windows.Forms.TextBox();
            this.label16 = new System.Windows.Forms.Label();
            this.bt_notify = new System.Windows.Forms.Button();
            this.tableLayoutPanel6 = new System.Windows.Forms.TableLayoutPanel();
            this.gb_state = new System.Windows.Forms.GroupBox();
            this.rb_ractifiy = new System.Windows.Forms.RadioButton();
            this.rb_raise = new System.Windows.Forms.RadioButton();
            this.gb_alert_list = new System.Windows.Forms.GroupBox();
            this.lb_alert = new System.Windows.Forms.ListBox();
            this.tabPage5 = new System.Windows.Forms.TabPage();
            this.groupBox10 = new System.Windows.Forms.GroupBox();
            this.tb_lz_mins = new System.Windows.Forms.TextBox();
            this.tb_lz_hours = new System.Windows.Forms.TextBox();
            this.label45 = new System.Windows.Forms.Label();
            this.label44 = new System.Windows.Forms.Label();
            this.tb_latitude = new System.Windows.Forms.TextBox();
            this.tb_logitude = new System.Windows.Forms.TextBox();
            this.tb_time = new System.Windows.Forms.TextBox();
            this.tb_date = new System.Windows.Forms.TextBox();
            this.label38 = new System.Windows.Forms.Label();
            this.label41 = new System.Windows.Forms.Label();
            this.label39 = new System.Windows.Forms.Label();
            this.label40 = new System.Windows.Forms.Label();
            this.bt_rx_ext_full_save = new System.Windows.Forms.Button();
            this.bt_rx_ext_full_clear = new System.Windows.Forms.Button();
            this.rich_tb_full_sentence = new System.Windows.Forms.RichTextBox();
            this.label37 = new System.Windows.Forms.Label();
            this.tabPage6 = new System.Windows.Forms.TabPage();
            this.bt_record = new System.Windows.Forms.Button();
            this.lb_playback_time = new System.Windows.Forms.Label();
            this.tb_audiotrack = new System.Windows.Forms.TrackBar();
            this.lb_rec_time = new System.Windows.Forms.Label();
            this.bt_delete_track = new System.Windows.Forms.Button();
            this.bt_delete_all = new System.Windows.Forms.Button();
            this.bt_playback = new System.Windows.Forms.Button();
            this.lb_audiotrack = new System.Windows.Forms.ListBox();
            this.bt_get_audiotrack = new System.Windows.Forms.Button();
            this.tick_timer = new System.Windows.Forms.Timer(this.components);
            this.timer_connect = new System.Windows.Forms.Timer(this.components);
            this.AudioTrack_timer = new System.Windows.Forms.Timer(this.components);
            this.dsc_send_timer = new System.Windows.Forms.Timer(this.components);
            this.toolStrip1.SuspendLayout();
            this.tabControl1.SuspendLayout();
            this.tabPage1.SuspendLayout();
            this.groupBox6.SuspendLayout();
            this.groupBox9.SuspendLayout();
            this.groupBox8.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tb_wkr_rx_pwr)).BeginInit();
            this.groupBox7.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tb_main_tx_pwr)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tb_main_rx_pwr)).BeginInit();
            this.gb_audio_codec.SuspendLayout();
            this.groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tb_out_rx_bb)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tb_front_ext_spk)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tb_rear_wing_h_spk)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tb_front_h_spk)).BeginInit();
            this.groupBox4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tb_rx_bb)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tb_wing_mic)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tb_rear_mic)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tb_front_mic)).BeginInit();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.att_trackBar)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gain_trackBar)).BeginInit();
            this.gb_OpMode.SuspendLayout();
            this.tabPage2.SuspendLayout();
            this.groupBox5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgv_selftest)).BeginInit();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDown1)).BeginInit();
            this.tabPage3.SuspendLayout();
            this.tableLayoutPanel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvDscSend)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dgvDscRsv)).BeginInit();
            this.panel1.SuspendLayout();
            this.panel2.SuspendLayout();
            this.tableLayoutPanel2.SuspendLayout();
            this.tableLayoutPanel3.SuspendLayout();
            this.tabPage4.SuspendLayout();
            this.tableLayoutPanel4.SuspendLayout();
            this.tableLayoutPanel5.SuspendLayout();
            this.tableLayoutPanel6.SuspendLayout();
            this.gb_state.SuspendLayout();
            this.gb_alert_list.SuspendLayout();
            this.tabPage5.SuspendLayout();
            this.groupBox10.SuspendLayout();
            this.tabPage6.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tb_audiotrack)).BeginInit();
            this.SuspendLayout();
            // 
            // toolStrip1
            // 
            this.toolStrip1.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.toolStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripLabel1,
            this.cbCom,
            this.btnConnect,
            this.toolStripSeparator1,
            this.btnSetController,
            this.lb_bb_ver});
            this.toolStrip1.Location = new System.Drawing.Point(0, 0);
            this.toolStrip1.Name = "toolStrip1";
            this.toolStrip1.Size = new System.Drawing.Size(1137, 25);
            this.toolStrip1.TabIndex = 0;
            this.toolStrip1.Text = "toolStrip1";
            // 
            // toolStripLabel1
            // 
            this.toolStripLabel1.Font = new System.Drawing.Font("휴먼모음T", 9F);
            this.toolStripLabel1.Name = "toolStripLabel1";
            this.toolStripLabel1.Size = new System.Drawing.Size(32, 22);
            this.toolStripLabel1.Text = "COM";
            // 
            // cbCom
            // 
            this.cbCom.AutoSize = false;
            this.cbCom.Font = new System.Drawing.Font("휴먼모음T", 9F);
            this.cbCom.Margin = new System.Windows.Forms.Padding(5, 0, 5, 0);
            this.cbCom.Name = "cbCom";
            this.cbCom.Size = new System.Drawing.Size(80, 21);
            // 
            // btnConnect
            // 
            this.btnConnect.AutoSize = false;
            this.btnConnect.BackColor = System.Drawing.Color.Silver;
            this.btnConnect.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            this.btnConnect.Font = new System.Drawing.Font("휴먼모음T", 9F);
            this.btnConnect.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.btnConnect.Name = "btnConnect";
            this.btnConnect.Size = new System.Drawing.Size(80, 22);
            this.btnConnect.Text = "CONNECT";
            this.btnConnect.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageAboveText;
            this.btnConnect.Click += new System.EventHandler(this.btnConnect_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(6, 25);
            // 
            // btnSetController
            // 
            this.btnSetController.Alignment = System.Windows.Forms.ToolStripItemAlignment.Right;
            this.btnSetController.AutoSize = false;
            this.btnSetController.BackColor = System.Drawing.Color.Silver;
            this.btnSetController.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            this.btnSetController.Font = new System.Drawing.Font("휴먼모음T", 9F);
            this.btnSetController.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.btnSetController.Margin = new System.Windows.Forms.Padding(0, 1, 10, 2);
            this.btnSetController.Name = "btnSetController";
            this.btnSetController.Size = new System.Drawing.Size(120, 22);
            this.btnSetController.Text = "FRONT DISPLAY";
            this.btnSetController.ToolTipText = "FRONT DISPLAY";
            this.btnSetController.Click += new System.EventHandler(this.btnSetController_Click);
            // 
            // lb_bb_ver
            // 
            this.lb_bb_ver.Alignment = System.Windows.Forms.ToolStripItemAlignment.Right;
            this.lb_bb_ver.AutoSize = false;
            this.lb_bb_ver.Font = new System.Drawing.Font("휴먼모음T", 9F);
            this.lb_bb_ver.ForeColor = System.Drawing.Color.Red;
            this.lb_bb_ver.Name = "lb_bb_ver";
            this.lb_bb_ver.Size = new System.Drawing.Size(160, 22);
            this.lb_bb_ver.Text = "BASEBAND VER :";
            this.lb_bb_ver.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // tabControl1
            // 
            this.tabControl1.Controls.Add(this.tabPage1);
            this.tabControl1.Controls.Add(this.tabPage2);
            this.tabControl1.Controls.Add(this.tabPage3);
            this.tabControl1.Controls.Add(this.tabPage4);
            this.tabControl1.Controls.Add(this.tabPage5);
            this.tabControl1.Controls.Add(this.tabPage6);
            this.tabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControl1.Font = new System.Drawing.Font("휴먼모음T", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.tabControl1.ItemSize = new System.Drawing.Size(40, 18);
            this.tabControl1.Location = new System.Drawing.Point(0, 25);
            this.tabControl1.Margin = new System.Windows.Forms.Padding(3, 30, 3, 2);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(1137, 633);
            this.tabControl1.TabIndex = 1;
            this.tabControl1.SelectedIndexChanged += new System.EventHandler(this.tabcontrol_SelectedIndexChanged);
            // 
            // tabPage1
            // 
            this.tabPage1.Controls.Add(this.groupBox6);
            this.tabPage1.Controls.Add(this.gb_audio_codec);
            this.tabPage1.Controls.Add(this.groupBox2);
            this.tabPage1.Controls.Add(this.gb_OpMode);
            this.tabPage1.Font = new System.Drawing.Font("휴먼모음T", 10F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.tabPage1.ForeColor = System.Drawing.SystemColors.ControlText;
            this.tabPage1.Location = new System.Drawing.Point(4, 22);
            this.tabPage1.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tabPage1.Name = "tabPage1";
            this.tabPage1.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tabPage1.Size = new System.Drawing.Size(1129, 607);
            this.tabPage1.TabIndex = 0;
            this.tabPage1.Text = "Display";
            this.tabPage1.UseVisualStyleBackColor = true;
            // 
            // groupBox6
            // 
            this.groupBox6.Controls.Add(this.groupBox9);
            this.groupBox6.Controls.Add(this.groupBox8);
            this.groupBox6.Controls.Add(this.groupBox7);
            this.groupBox6.Font = new System.Drawing.Font("휴먼모음T", 9.75F);
            this.groupBox6.Location = new System.Drawing.Point(8, 246);
            this.groupBox6.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox6.Name = "groupBox6";
            this.groupBox6.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox6.Size = new System.Drawing.Size(1113, 132);
            this.groupBox6.TabIndex = 12;
            this.groupBox6.TabStop = false;
            this.groupBox6.Text = "RF DDS";
            // 
            // groupBox9
            // 
            this.groupBox9.Controls.Add(this.label47);
            this.groupBox9.Controls.Add(this.tb_dcoffset);
            this.groupBox9.Controls.Add(this.label46);
            this.groupBox9.Controls.Add(this.label35);
            this.groupBox9.Controls.Add(this.tb_dds_adj);
            this.groupBox9.Controls.Add(this.label32);
            this.groupBox9.Controls.Add(this.tb_pwr_cnt);
            this.groupBox9.Controls.Add(this.label34);
            this.groupBox9.Controls.Add(this.label36);
            this.groupBox9.Location = new System.Drawing.Point(570, 80);
            this.groupBox9.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox9.Name = "groupBox9";
            this.groupBox9.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox9.Size = new System.Drawing.Size(537, 42);
            this.groupBox9.TabIndex = 13;
            this.groupBox9.TabStop = false;
            this.groupBox9.Text = "ETC";
            // 
            // label47
            // 
            this.label47.AutoSize = true;
            this.label47.Location = new System.Drawing.Point(500, 18);
            this.label47.Name = "label47";
            this.label47.Size = new System.Drawing.Size(15, 14);
            this.label47.TabIndex = 14;
            this.label47.Text = "V";
            // 
            // tb_dcoffset
            // 
            this.tb_dcoffset.Location = new System.Drawing.Point(434, 14);
            this.tb_dcoffset.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tb_dcoffset.Name = "tb_dcoffset";
            this.tb_dcoffset.Size = new System.Drawing.Size(64, 22);
            this.tb_dcoffset.TabIndex = 13;
            this.tb_dcoffset.Text = "0";
            this.tb_dcoffset.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.tb_dcoffset.KeyDown += new System.Windows.Forms.KeyEventHandler(this.tb_dcoffset_keydown);
            // 
            // label46
            // 
            this.label46.AutoSize = true;
            this.label46.Location = new System.Drawing.Point(357, 18);
            this.label46.Name = "label46";
            this.label46.Size = new System.Drawing.Size(75, 14);
            this.label46.TabIndex = 12;
            this.label46.Text = "DC OFFSET";
            // 
            // label35
            // 
            this.label35.AutoSize = true;
            this.label35.Location = new System.Drawing.Point(330, 18);
            this.label35.Name = "label35";
            this.label35.Size = new System.Drawing.Size(15, 14);
            this.label35.TabIndex = 11;
            this.label35.Text = "V";
            // 
            // tb_dds_adj
            // 
            this.tb_dds_adj.Location = new System.Drawing.Point(265, 14);
            this.tb_dds_adj.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tb_dds_adj.Name = "tb_dds_adj";
            this.tb_dds_adj.Size = new System.Drawing.Size(65, 22);
            this.tb_dds_adj.TabIndex = 10;
            this.tb_dds_adj.Text = "1.5";
            this.tb_dds_adj.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.tb_dds_adj.KeyDown += new System.Windows.Forms.KeyEventHandler(this.tb_dds_adj_keydown);
            // 
            // label32
            // 
            this.label32.AutoSize = true;
            this.label32.Location = new System.Drawing.Point(201, 18);
            this.label32.Name = "label32";
            this.label32.Size = new System.Drawing.Size(60, 14);
            this.label32.TabIndex = 9;
            this.label32.Text = "DDS ADJ";
            // 
            // tb_pwr_cnt
            // 
            this.tb_pwr_cnt.Location = new System.Drawing.Point(84, 14);
            this.tb_pwr_cnt.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tb_pwr_cnt.Name = "tb_pwr_cnt";
            this.tb_pwr_cnt.Size = new System.Drawing.Size(84, 22);
            this.tb_pwr_cnt.TabIndex = 6;
            this.tb_pwr_cnt.Text = "1.8";
            this.tb_pwr_cnt.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.tb_pwr_cnt.KeyDown += new System.Windows.Forms.KeyEventHandler(this.tb_pwr_cnt_keydown);
            // 
            // label34
            // 
            this.label34.AutoSize = true;
            this.label34.Location = new System.Drawing.Point(15, 18);
            this.label34.Name = "label34";
            this.label34.Size = new System.Drawing.Size(65, 14);
            this.label34.TabIndex = 5;
            this.label34.Text = "PWR CNT";
            // 
            // label36
            // 
            this.label36.AutoSize = true;
            this.label36.Location = new System.Drawing.Point(174, 18);
            this.label36.Name = "label36";
            this.label36.Size = new System.Drawing.Size(15, 14);
            this.label36.TabIndex = 8;
            this.label36.Text = "V";
            // 
            // groupBox8
            // 
            this.groupBox8.Controls.Add(this.lb_wkr_rx_pwr);
            this.groupBox8.Controls.Add(this.tb_wkr_freq);
            this.groupBox8.Controls.Add(this.label21);
            this.groupBox8.Controls.Add(this.label33);
            this.groupBox8.Controls.Add(this.tb_wkr_rx_pwr);
            this.groupBox8.Controls.Add(this.label23);
            this.groupBox8.Location = new System.Drawing.Point(570, 18);
            this.groupBox8.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox8.Name = "groupBox8";
            this.groupBox8.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox8.Size = new System.Drawing.Size(537, 60);
            this.groupBox8.TabIndex = 10;
            this.groupBox8.TabStop = false;
            this.groupBox8.Text = "WKR DDS";
            // 
            // lb_wkr_rx_pwr
            // 
            this.lb_wkr_rx_pwr.AutoSize = true;
            this.lb_wkr_rx_pwr.Font = new System.Drawing.Font("휴먼모음T", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.lb_wkr_rx_pwr.ForeColor = System.Drawing.Color.Blue;
            this.lb_wkr_rx_pwr.Location = new System.Drawing.Point(485, 22);
            this.lb_wkr_rx_pwr.Name = "lb_wkr_rx_pwr";
            this.lb_wkr_rx_pwr.Size = new System.Drawing.Size(26, 18);
            this.lb_wkr_rx_pwr.TabIndex = 27;
            this.lb_wkr_rx_pwr.Text = "31";
            // 
            // tb_wkr_freq
            // 
            this.tb_wkr_freq.Location = new System.Drawing.Point(53, 24);
            this.tb_wkr_freq.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tb_wkr_freq.Name = "tb_wkr_freq";
            this.tb_wkr_freq.Size = new System.Drawing.Size(115, 22);
            this.tb_wkr_freq.TabIndex = 6;
            this.tb_wkr_freq.Text = "167.225";
            this.tb_wkr_freq.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.tb_wkr_freq.KeyDown += new System.Windows.Forms.KeyEventHandler(this.tb_wkr_freq_keydown);
            // 
            // label21
            // 
            this.label21.AutoSize = true;
            this.label21.Location = new System.Drawing.Point(13, 26);
            this.label21.Name = "label21";
            this.label21.Size = new System.Drawing.Size(37, 14);
            this.label21.TabIndex = 5;
            this.label21.Text = "FREQ";
            // 
            // label33
            // 
            this.label33.AutoSize = true;
            this.label33.Location = new System.Drawing.Point(237, 26);
            this.label33.Name = "label33";
            this.label33.Size = new System.Drawing.Size(73, 14);
            this.label33.TabIndex = 16;
            this.label33.Text = "RX POWER";
            // 
            // tb_wkr_rx_pwr
            // 
            this.tb_wkr_rx_pwr.BackColor = System.Drawing.SystemColors.Window;
            this.tb_wkr_rx_pwr.LargeChange = 1;
            this.tb_wkr_rx_pwr.Location = new System.Drawing.Point(316, 22);
            this.tb_wkr_rx_pwr.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tb_wkr_rx_pwr.Maximum = 31;
            this.tb_wkr_rx_pwr.Name = "tb_wkr_rx_pwr";
            this.tb_wkr_rx_pwr.Size = new System.Drawing.Size(172, 45);
            this.tb_wkr_rx_pwr.TabIndex = 15;
            this.tb_wkr_rx_pwr.Value = 31;
            this.tb_wkr_rx_pwr.Scroll += new System.EventHandler(this.wkr_rx_power_trackbar_scroll);
            // 
            // label23
            // 
            this.label23.AutoSize = true;
            this.label23.Location = new System.Drawing.Point(174, 30);
            this.label23.Name = "label23";
            this.label23.Size = new System.Drawing.Size(32, 14);
            this.label23.TabIndex = 8;
            this.label23.Text = "MHz";
            // 
            // groupBox7
            // 
            this.groupBox7.Controls.Add(this.label43);
            this.groupBox7.Controls.Add(this.label42);
            this.groupBox7.Controls.Add(this.tb_main_tx_freq);
            this.groupBox7.Controls.Add(this.lb_main_tx_pwr);
            this.groupBox7.Controls.Add(this.lb_main_rx_pwr);
            this.groupBox7.Controls.Add(this.label29);
            this.groupBox7.Controls.Add(this.tb_main_tx_pwr);
            this.groupBox7.Controls.Add(this.label24);
            this.groupBox7.Controls.Add(this.tb_main_rx_pwr);
            this.groupBox7.Controls.Add(this.tb_main_rx_freq);
            this.groupBox7.Controls.Add(this.label30);
            this.groupBox7.Controls.Add(this.label22);
            this.groupBox7.Location = new System.Drawing.Point(15, 18);
            this.groupBox7.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox7.Name = "groupBox7";
            this.groupBox7.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox7.Size = new System.Drawing.Size(537, 106);
            this.groupBox7.TabIndex = 9;
            this.groupBox7.TabStop = false;
            this.groupBox7.Text = "MAIN DDS";
            // 
            // label43
            // 
            this.label43.AutoSize = true;
            this.label43.Location = new System.Drawing.Point(189, 66);
            this.label43.Name = "label43";
            this.label43.Size = new System.Drawing.Size(32, 14);
            this.label43.TabIndex = 32;
            this.label43.Text = "MHz";
            // 
            // label42
            // 
            this.label42.AutoSize = true;
            this.label42.Location = new System.Drawing.Point(4, 66);
            this.label42.Name = "label42";
            this.label42.Size = new System.Drawing.Size(59, 14);
            this.label42.TabIndex = 31;
            this.label42.Text = "TX FREQ";
            // 
            // tb_main_tx_freq
            // 
            this.tb_main_tx_freq.Location = new System.Drawing.Point(70, 62);
            this.tb_main_tx_freq.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tb_main_tx_freq.Name = "tb_main_tx_freq";
            this.tb_main_tx_freq.Size = new System.Drawing.Size(115, 22);
            this.tb_main_tx_freq.TabIndex = 30;
            this.tb_main_tx_freq.Text = "84.9";
            this.tb_main_tx_freq.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.tb_main_tx_freq.KeyDown += new System.Windows.Forms.KeyEventHandler(this.tb_main_tx_freq_keydown);
            // 
            // lb_main_tx_pwr
            // 
            this.lb_main_tx_pwr.AutoSize = true;
            this.lb_main_tx_pwr.Font = new System.Drawing.Font("휴먼모음T", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.lb_main_tx_pwr.ForeColor = System.Drawing.Color.Blue;
            this.lb_main_tx_pwr.Location = new System.Drawing.Point(489, 62);
            this.lb_main_tx_pwr.Name = "lb_main_tx_pwr";
            this.lb_main_tx_pwr.Size = new System.Drawing.Size(26, 18);
            this.lb_main_tx_pwr.TabIndex = 29;
            this.lb_main_tx_pwr.Text = "31";
            // 
            // lb_main_rx_pwr
            // 
            this.lb_main_rx_pwr.AutoSize = true;
            this.lb_main_rx_pwr.Font = new System.Drawing.Font("휴먼모음T", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.lb_main_rx_pwr.ForeColor = System.Drawing.Color.Blue;
            this.lb_main_rx_pwr.Location = new System.Drawing.Point(489, 22);
            this.lb_main_rx_pwr.Name = "lb_main_rx_pwr";
            this.lb_main_rx_pwr.Size = new System.Drawing.Size(26, 18);
            this.lb_main_rx_pwr.TabIndex = 28;
            this.lb_main_rx_pwr.Text = "31";
            // 
            // label29
            // 
            this.label29.AutoSize = true;
            this.label29.Location = new System.Drawing.Point(232, 66);
            this.label29.Name = "label29";
            this.label29.Size = new System.Drawing.Size(72, 14);
            this.label29.TabIndex = 14;
            this.label29.Text = "TX POWER";
            // 
            // tb_main_tx_pwr
            // 
            this.tb_main_tx_pwr.BackColor = System.Drawing.SystemColors.Window;
            this.tb_main_tx_pwr.LargeChange = 1;
            this.tb_main_tx_pwr.Location = new System.Drawing.Point(311, 58);
            this.tb_main_tx_pwr.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tb_main_tx_pwr.Maximum = 31;
            this.tb_main_tx_pwr.Name = "tb_main_tx_pwr";
            this.tb_main_tx_pwr.Size = new System.Drawing.Size(172, 45);
            this.tb_main_tx_pwr.TabIndex = 13;
            this.tb_main_tx_pwr.Value = 31;
            this.tb_main_tx_pwr.Scroll += new System.EventHandler(this.main_tx_power_trackbar_scroll);
            // 
            // label24
            // 
            this.label24.AutoSize = true;
            this.label24.Location = new System.Drawing.Point(232, 26);
            this.label24.Name = "label24";
            this.label24.Size = new System.Drawing.Size(73, 14);
            this.label24.TabIndex = 12;
            this.label24.Text = "RX POWER";
            // 
            // tb_main_rx_pwr
            // 
            this.tb_main_rx_pwr.BackColor = System.Drawing.SystemColors.Window;
            this.tb_main_rx_pwr.LargeChange = 1;
            this.tb_main_rx_pwr.Location = new System.Drawing.Point(311, 22);
            this.tb_main_rx_pwr.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tb_main_rx_pwr.Maximum = 31;
            this.tb_main_rx_pwr.Name = "tb_main_rx_pwr";
            this.tb_main_rx_pwr.Size = new System.Drawing.Size(172, 45);
            this.tb_main_rx_pwr.TabIndex = 11;
            this.tb_main_rx_pwr.Value = 31;
            this.tb_main_rx_pwr.Scroll += new System.EventHandler(this.main_rx_power_trackbar_scroll);
            // 
            // tb_main_rx_freq
            // 
            this.tb_main_rx_freq.Location = new System.Drawing.Point(70, 24);
            this.tb_main_rx_freq.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tb_main_rx_freq.Name = "tb_main_rx_freq";
            this.tb_main_rx_freq.Size = new System.Drawing.Size(115, 22);
            this.tb_main_rx_freq.TabIndex = 4;
            this.tb_main_rx_freq.Text = "135.2";
            this.tb_main_rx_freq.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.tb_main_rx_freq.KeyDown += new System.Windows.Forms.KeyEventHandler(this.tb_main_rx_freq_keydown);
            // 
            // label30
            // 
            this.label30.AutoSize = true;
            this.label30.Location = new System.Drawing.Point(4, 26);
            this.label30.Name = "label30";
            this.label30.Size = new System.Drawing.Size(60, 14);
            this.label30.TabIndex = 0;
            this.label30.Text = "RX FREQ";
            // 
            // label22
            // 
            this.label22.AutoSize = true;
            this.label22.Location = new System.Drawing.Point(187, 26);
            this.label22.Name = "label22";
            this.label22.Size = new System.Drawing.Size(32, 14);
            this.label22.TabIndex = 7;
            this.label22.Text = "MHz";
            // 
            // gb_audio_codec
            // 
            this.gb_audio_codec.Controls.Add(this.groupBox3);
            this.gb_audio_codec.Controls.Add(this.groupBox4);
            this.gb_audio_codec.Font = new System.Drawing.Font("휴먼모음T", 9.75F);
            this.gb_audio_codec.Location = new System.Drawing.Point(333, 14);
            this.gb_audio_codec.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.gb_audio_codec.Name = "gb_audio_codec";
            this.gb_audio_codec.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.gb_audio_codec.Size = new System.Drawing.Size(788, 222);
            this.gb_audio_codec.TabIndex = 11;
            this.gb_audio_codec.TabStop = false;
            this.gb_audio_codec.Text = "Audio Codec";
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.lb_out_rx_bb);
            this.groupBox3.Controls.Add(this.lb_front_ext_spk);
            this.groupBox3.Controls.Add(this.lb_rear_wing_h_spk);
            this.groupBox3.Controls.Add(this.lb_front_h_spk);
            this.groupBox3.Controls.Add(this.tb_out_rx_bb);
            this.groupBox3.Controls.Add(this.label25);
            this.groupBox3.Controls.Add(this.tb_front_ext_spk);
            this.groupBox3.Controls.Add(this.label26);
            this.groupBox3.Controls.Add(this.tb_rear_wing_h_spk);
            this.groupBox3.Controls.Add(this.label27);
            this.groupBox3.Controls.Add(this.tb_front_h_spk);
            this.groupBox3.Controls.Add(this.label28);
            this.groupBox3.Location = new System.Drawing.Point(368, 22);
            this.groupBox3.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox3.Size = new System.Drawing.Size(414, 192);
            this.groupBox3.TabIndex = 27;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "Volume";
            // 
            // lb_out_rx_bb
            // 
            this.lb_out_rx_bb.AutoSize = true;
            this.lb_out_rx_bb.Font = new System.Drawing.Font("휴먼모음T", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.lb_out_rx_bb.ForeColor = System.Drawing.Color.Blue;
            this.lb_out_rx_bb.Location = new System.Drawing.Point(356, 138);
            this.lb_out_rx_bb.Name = "lb_out_rx_bb";
            this.lb_out_rx_bb.Size = new System.Drawing.Size(36, 18);
            this.lb_out_rx_bb.TabIndex = 26;
            this.lb_out_rx_bb.Text = "0dB";
            // 
            // lb_front_ext_spk
            // 
            this.lb_front_ext_spk.AutoSize = true;
            this.lb_front_ext_spk.Font = new System.Drawing.Font("휴먼모음T", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.lb_front_ext_spk.ForeColor = System.Drawing.Color.Blue;
            this.lb_front_ext_spk.Location = new System.Drawing.Point(356, 104);
            this.lb_front_ext_spk.Name = "lb_front_ext_spk";
            this.lb_front_ext_spk.Size = new System.Drawing.Size(36, 18);
            this.lb_front_ext_spk.TabIndex = 26;
            this.lb_front_ext_spk.Text = "0dB";
            // 
            // lb_rear_wing_h_spk
            // 
            this.lb_rear_wing_h_spk.AutoSize = true;
            this.lb_rear_wing_h_spk.Font = new System.Drawing.Font("휴먼모음T", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.lb_rear_wing_h_spk.ForeColor = System.Drawing.Color.Blue;
            this.lb_rear_wing_h_spk.Location = new System.Drawing.Point(356, 68);
            this.lb_rear_wing_h_spk.Name = "lb_rear_wing_h_spk";
            this.lb_rear_wing_h_spk.Size = new System.Drawing.Size(36, 18);
            this.lb_rear_wing_h_spk.TabIndex = 26;
            this.lb_rear_wing_h_spk.Text = "0dB";
            // 
            // lb_front_h_spk
            // 
            this.lb_front_h_spk.AutoSize = true;
            this.lb_front_h_spk.Font = new System.Drawing.Font("휴먼모음T", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.lb_front_h_spk.ForeColor = System.Drawing.Color.Blue;
            this.lb_front_h_spk.Location = new System.Drawing.Point(356, 34);
            this.lb_front_h_spk.Name = "lb_front_h_spk";
            this.lb_front_h_spk.Size = new System.Drawing.Size(36, 18);
            this.lb_front_h_spk.TabIndex = 26;
            this.lb_front_h_spk.Text = "0dB";
            // 
            // tb_out_rx_bb
            // 
            this.tb_out_rx_bb.BackColor = System.Drawing.SystemColors.Window;
            this.tb_out_rx_bb.LargeChange = 2;
            this.tb_out_rx_bb.Location = new System.Drawing.Point(143, 138);
            this.tb_out_rx_bb.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tb_out_rx_bb.Maximum = 24;
            this.tb_out_rx_bb.Name = "tb_out_rx_bb";
            this.tb_out_rx_bb.Size = new System.Drawing.Size(211, 45);
            this.tb_out_rx_bb.SmallChange = 2;
            this.tb_out_rx_bb.TabIndex = 21;
            this.tb_out_rx_bb.Scroll += new System.EventHandler(this.out_rx_bb_trackbar_scroll);
            // 
            // label25
            // 
            this.label25.AutoSize = true;
            this.label25.Location = new System.Drawing.Point(84, 142);
            this.label25.Name = "label25";
            this.label25.Size = new System.Drawing.Size(52, 14);
            this.label25.TabIndex = 20;
            this.label25.Text = "MIC AF";
            // 
            // tb_front_ext_spk
            // 
            this.tb_front_ext_spk.BackColor = System.Drawing.SystemColors.Window;
            this.tb_front_ext_spk.LargeChange = 2;
            this.tb_front_ext_spk.Location = new System.Drawing.Point(143, 102);
            this.tb_front_ext_spk.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tb_front_ext_spk.Maximum = 24;
            this.tb_front_ext_spk.Name = "tb_front_ext_spk";
            this.tb_front_ext_spk.Size = new System.Drawing.Size(211, 45);
            this.tb_front_ext_spk.SmallChange = 2;
            this.tb_front_ext_spk.TabIndex = 18;
            this.tb_front_ext_spk.Scroll += new System.EventHandler(this.front_ext_spk_trackbar_scroll);
            // 
            // label26
            // 
            this.label26.AutoSize = true;
            this.label26.Location = new System.Drawing.Point(18, 102);
            this.label26.Name = "label26";
            this.label26.Size = new System.Drawing.Size(118, 14);
            this.label26.TabIndex = 17;
            this.label26.Text = "FRONT / EXT SPK";
            // 
            // tb_rear_wing_h_spk
            // 
            this.tb_rear_wing_h_spk.BackColor = System.Drawing.SystemColors.Window;
            this.tb_rear_wing_h_spk.LargeChange = 2;
            this.tb_rear_wing_h_spk.Location = new System.Drawing.Point(143, 66);
            this.tb_rear_wing_h_spk.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tb_rear_wing_h_spk.Maximum = 24;
            this.tb_rear_wing_h_spk.Name = "tb_rear_wing_h_spk";
            this.tb_rear_wing_h_spk.Size = new System.Drawing.Size(211, 45);
            this.tb_rear_wing_h_spk.SmallChange = 2;
            this.tb_rear_wing_h_spk.TabIndex = 15;
            this.tb_rear_wing_h_spk.Scroll += new System.EventHandler(this.rear_wing_h_spk_trackbar_scroll);
            // 
            // label27
            // 
            this.label27.AutoSize = true;
            this.label27.Location = new System.Drawing.Point(4, 66);
            this.label27.Name = "label27";
            this.label27.Size = new System.Drawing.Size(134, 14);
            this.label27.TabIndex = 14;
            this.label27.Text = "REAR / WING H SPK";
            // 
            // tb_front_h_spk
            // 
            this.tb_front_h_spk.BackColor = System.Drawing.SystemColors.Window;
            this.tb_front_h_spk.LargeChange = 2;
            this.tb_front_h_spk.Location = new System.Drawing.Point(142, 32);
            this.tb_front_h_spk.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tb_front_h_spk.Maximum = 24;
            this.tb_front_h_spk.Name = "tb_front_h_spk";
            this.tb_front_h_spk.Size = new System.Drawing.Size(211, 45);
            this.tb_front_h_spk.SmallChange = 2;
            this.tb_front_h_spk.TabIndex = 12;
            this.tb_front_h_spk.Scroll += new System.EventHandler(this.front_h_spk_trackbar_scroll);
            // 
            // label28
            // 
            this.label28.AutoSize = true;
            this.label28.Location = new System.Drawing.Point(43, 32);
            this.label28.Name = "label28";
            this.label28.Size = new System.Drawing.Size(93, 14);
            this.label28.TabIndex = 11;
            this.label28.Text = "FRONT H SPK";
            // 
            // groupBox4
            // 
            this.groupBox4.Controls.Add(this.lb_rx_bb);
            this.groupBox4.Controls.Add(this.lb_wing_mic);
            this.groupBox4.Controls.Add(this.lb_rear_mic);
            this.groupBox4.Controls.Add(this.lb_front_mic);
            this.groupBox4.Controls.Add(this.tb_rx_bb);
            this.groupBox4.Controls.Add(this.label20);
            this.groupBox4.Controls.Add(this.tb_wing_mic);
            this.groupBox4.Controls.Add(this.label19);
            this.groupBox4.Controls.Add(this.tb_rear_mic);
            this.groupBox4.Controls.Add(this.label18);
            this.groupBox4.Controls.Add(this.tb_front_mic);
            this.groupBox4.Controls.Add(this.label17);
            this.groupBox4.Location = new System.Drawing.Point(6, 22);
            this.groupBox4.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox4.Size = new System.Drawing.Size(356, 186);
            this.groupBox4.TabIndex = 16;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "IN AMP GAIN";
            // 
            // lb_rx_bb
            // 
            this.lb_rx_bb.AutoSize = true;
            this.lb_rx_bb.Font = new System.Drawing.Font("휴먼모음T", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.lb_rx_bb.ForeColor = System.Drawing.Color.Blue;
            this.lb_rx_bb.Location = new System.Drawing.Point(303, 138);
            this.lb_rx_bb.Name = "lb_rx_bb";
            this.lb_rx_bb.Size = new System.Drawing.Size(36, 18);
            this.lb_rx_bb.TabIndex = 26;
            this.lb_rx_bb.Text = "0dB";
            // 
            // lb_wing_mic
            // 
            this.lb_wing_mic.AutoSize = true;
            this.lb_wing_mic.Font = new System.Drawing.Font("휴먼모음T", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.lb_wing_mic.ForeColor = System.Drawing.Color.Blue;
            this.lb_wing_mic.Location = new System.Drawing.Point(303, 104);
            this.lb_wing_mic.Name = "lb_wing_mic";
            this.lb_wing_mic.Size = new System.Drawing.Size(36, 18);
            this.lb_wing_mic.TabIndex = 26;
            this.lb_wing_mic.Text = "0dB";
            // 
            // lb_rear_mic
            // 
            this.lb_rear_mic.AutoSize = true;
            this.lb_rear_mic.Font = new System.Drawing.Font("휴먼모음T", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.lb_rear_mic.ForeColor = System.Drawing.Color.Blue;
            this.lb_rear_mic.Location = new System.Drawing.Point(303, 68);
            this.lb_rear_mic.Name = "lb_rear_mic";
            this.lb_rear_mic.Size = new System.Drawing.Size(36, 18);
            this.lb_rear_mic.TabIndex = 26;
            this.lb_rear_mic.Text = "0dB";
            // 
            // lb_front_mic
            // 
            this.lb_front_mic.AutoSize = true;
            this.lb_front_mic.Font = new System.Drawing.Font("휴먼모음T", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.lb_front_mic.ForeColor = System.Drawing.Color.Blue;
            this.lb_front_mic.Location = new System.Drawing.Point(303, 34);
            this.lb_front_mic.Name = "lb_front_mic";
            this.lb_front_mic.Size = new System.Drawing.Size(36, 18);
            this.lb_front_mic.TabIndex = 26;
            this.lb_front_mic.Text = "0dB";
            // 
            // tb_rx_bb
            // 
            this.tb_rx_bb.BackColor = System.Drawing.SystemColors.Window;
            this.tb_rx_bb.LargeChange = 1;
            this.tb_rx_bb.Location = new System.Drawing.Point(90, 138);
            this.tb_rx_bb.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tb_rx_bb.Maximum = 11;
            this.tb_rx_bb.Name = "tb_rx_bb";
            this.tb_rx_bb.Size = new System.Drawing.Size(211, 45);
            this.tb_rx_bb.TabIndex = 21;
            this.tb_rx_bb.Value = 2;
            this.tb_rx_bb.Scroll += new System.EventHandler(this.rx_bb_trackbar_scroll);
            // 
            // label20
            // 
            this.label20.AutoSize = true;
            this.label20.Location = new System.Drawing.Point(30, 142);
            this.label20.Name = "label20";
            this.label20.Size = new System.Drawing.Size(45, 14);
            this.label20.TabIndex = 20;
            this.label20.Text = "SP AF";
            // 
            // tb_wing_mic
            // 
            this.tb_wing_mic.BackColor = System.Drawing.SystemColors.Window;
            this.tb_wing_mic.LargeChange = 1;
            this.tb_wing_mic.Location = new System.Drawing.Point(90, 102);
            this.tb_wing_mic.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tb_wing_mic.Maximum = 11;
            this.tb_wing_mic.Name = "tb_wing_mic";
            this.tb_wing_mic.Size = new System.Drawing.Size(211, 45);
            this.tb_wing_mic.TabIndex = 18;
            this.tb_wing_mic.Value = 2;
            this.tb_wing_mic.Scroll += new System.EventHandler(this.wing_mic_trackbar_scroll);
            // 
            // label19
            // 
            this.label19.AutoSize = true;
            this.label19.Location = new System.Drawing.Point(7, 102);
            this.label19.Name = "label19";
            this.label19.Size = new System.Drawing.Size(69, 14);
            this.label19.TabIndex = 17;
            this.label19.Text = "WING MIC";
            // 
            // tb_rear_mic
            // 
            this.tb_rear_mic.BackColor = System.Drawing.SystemColors.Window;
            this.tb_rear_mic.LargeChange = 1;
            this.tb_rear_mic.Location = new System.Drawing.Point(90, 66);
            this.tb_rear_mic.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tb_rear_mic.Maximum = 11;
            this.tb_rear_mic.Name = "tb_rear_mic";
            this.tb_rear_mic.Size = new System.Drawing.Size(211, 45);
            this.tb_rear_mic.TabIndex = 15;
            this.tb_rear_mic.Value = 2;
            this.tb_rear_mic.Scroll += new System.EventHandler(this.rear_mic_trackbar_scroll);
            // 
            // label18
            // 
            this.label18.AutoSize = true;
            this.label18.Location = new System.Drawing.Point(7, 66);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(68, 14);
            this.label18.TabIndex = 14;
            this.label18.Text = "REAR MIC";
            // 
            // tb_front_mic
            // 
            this.tb_front_mic.BackColor = System.Drawing.SystemColors.Window;
            this.tb_front_mic.LargeChange = 1;
            this.tb_front_mic.Location = new System.Drawing.Point(89, 32);
            this.tb_front_mic.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tb_front_mic.Maximum = 11;
            this.tb_front_mic.Name = "tb_front_mic";
            this.tb_front_mic.Size = new System.Drawing.Size(211, 45);
            this.tb_front_mic.TabIndex = 12;
            this.tb_front_mic.Value = 2;
            this.tb_front_mic.Scroll += new System.EventHandler(this.front_mic_trackbar_scroll);
            // 
            // label17
            // 
            this.label17.AutoSize = true;
            this.label17.Location = new System.Drawing.Point(-2, 32);
            this.label17.Name = "label17";
            this.label17.Size = new System.Drawing.Size(77, 14);
            this.label17.TabIndex = 11;
            this.label17.Text = "FRONT MIC";
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.label12);
            this.groupBox2.Controls.Add(this.tb_limiter);
            this.groupBox2.Controls.Add(this.label11);
            this.groupBox2.Controls.Add(this.lb_att_value);
            this.groupBox2.Controls.Add(this.lb_gain_value);
            this.groupBox2.Controls.Add(this.tb_att);
            this.groupBox2.Controls.Add(this.tb_gain);
            this.groupBox2.Controls.Add(this.att_trackBar);
            this.groupBox2.Controls.Add(this.label10);
            this.groupBox2.Controls.Add(this.gain_trackBar);
            this.groupBox2.Controls.Add(this.label9);
            this.groupBox2.Font = new System.Drawing.Font("휴먼모음T", 9.75F);
            this.groupBox2.Location = new System.Drawing.Point(8, 66);
            this.groupBox2.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox2.Size = new System.Drawing.Size(290, 170);
            this.groupBox2.TabIndex = 1;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "Baseband";
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(121, 138);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(22, 14);
            this.label12.TabIndex = 10;
            this.label12.Text = "dB";
            // 
            // tb_limiter
            // 
            this.tb_limiter.Location = new System.Drawing.Point(75, 134);
            this.tb_limiter.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tb_limiter.Name = "tb_limiter";
            this.tb_limiter.Size = new System.Drawing.Size(40, 22);
            this.tb_limiter.TabIndex = 9;
            this.tb_limiter.Text = "0.0";
            this.tb_limiter.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.tb_limiter.KeyDown += new System.Windows.Forms.KeyEventHandler(this.tb_limiter_keydown);
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(23, 136);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(46, 14);
            this.label11.TabIndex = 8;
            this.label11.Text = "Limiter";
            // 
            // lb_att_value
            // 
            this.lb_att_value.AutoSize = true;
            this.lb_att_value.Location = new System.Drawing.Point(111, 78);
            this.lb_att_value.Name = "lb_att_value";
            this.lb_att_value.Size = new System.Drawing.Size(75, 14);
            this.lb_att_value.TabIndex = 7;
            this.lb_att_value.Text = "OUT * ATT";
            // 
            // lb_gain_value
            // 
            this.lb_gain_value.AutoSize = true;
            this.lb_gain_value.Location = new System.Drawing.Point(111, 22);
            this.lb_gain_value.Name = "lb_gain_value";
            this.lb_gain_value.Size = new System.Drawing.Size(77, 14);
            this.lb_gain_value.TabIndex = 6;
            this.lb_gain_value.Text = "OUT * Gain";
            // 
            // tb_att
            // 
            this.tb_att.Location = new System.Drawing.Point(49, 74);
            this.tb_att.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tb_att.Name = "tb_att";
            this.tb_att.Size = new System.Drawing.Size(45, 22);
            this.tb_att.TabIndex = 5;
            this.tb_att.Text = "0";
            this.tb_att.KeyDown += new System.Windows.Forms.KeyEventHandler(this.tb_att_keydown);
            // 
            // tb_gain
            // 
            this.tb_gain.Location = new System.Drawing.Point(49, 18);
            this.tb_gain.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tb_gain.Name = "tb_gain";
            this.tb_gain.Size = new System.Drawing.Size(44, 22);
            this.tb_gain.TabIndex = 4;
            this.tb_gain.Text = "0";
            this.tb_gain.KeyDown += new System.Windows.Forms.KeyEventHandler(this.tb_gain_keydown);
            // 
            // att_trackBar
            // 
            this.att_trackBar.BackColor = System.Drawing.SystemColors.Window;
            this.att_trackBar.Location = new System.Drawing.Point(10, 94);
            this.att_trackBar.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.att_trackBar.Maximum = 200;
            this.att_trackBar.Name = "att_trackBar";
            this.att_trackBar.Size = new System.Drawing.Size(274, 45);
            this.att_trackBar.TabIndex = 3;
            this.att_trackBar.Scroll += new System.EventHandler(this.att_trackbar_scroll);
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(7, 76);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(29, 14);
            this.label10.TabIndex = 2;
            this.label10.Text = "ATT";
            // 
            // gain_trackBar
            // 
            this.gain_trackBar.BackColor = System.Drawing.SystemColors.Window;
            this.gain_trackBar.Location = new System.Drawing.Point(10, 40);
            this.gain_trackBar.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.gain_trackBar.Maximum = 500;
            this.gain_trackBar.Name = "gain_trackBar";
            this.gain_trackBar.Size = new System.Drawing.Size(274, 45);
            this.gain_trackBar.TabIndex = 1;
            this.gain_trackBar.Scroll += new System.EventHandler(this.gain_trackbar_scroll);
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(7, 22);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(36, 14);
            this.label9.TabIndex = 0;
            this.label9.Text = "GAIN";
            // 
            // gb_OpMode
            // 
            this.gb_OpMode.Controls.Add(this.rb_SpaceMode);
            this.gb_OpMode.Controls.Add(this.rb_MarkMode);
            this.gb_OpMode.Controls.Add(this.rb_DotMode);
            this.gb_OpMode.Controls.Add(this.rb_TxMode);
            this.gb_OpMode.Controls.Add(this.rb_RxMode);
            this.gb_OpMode.Font = new System.Drawing.Font("휴먼모음T", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.gb_OpMode.Location = new System.Drawing.Point(8, 6);
            this.gb_OpMode.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.gb_OpMode.Name = "gb_OpMode";
            this.gb_OpMode.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.gb_OpMode.Size = new System.Drawing.Size(290, 54);
            this.gb_OpMode.TabIndex = 0;
            this.gb_OpMode.TabStop = false;
            this.gb_OpMode.Text = "OP mode";
            // 
            // rb_SpaceMode
            // 
            this.rb_SpaceMode.AutoSize = true;
            this.rb_SpaceMode.Location = new System.Drawing.Point(221, 22);
            this.rb_SpaceMode.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.rb_SpaceMode.Name = "rb_SpaceMode";
            this.rb_SpaceMode.Size = new System.Drawing.Size(64, 18);
            this.rb_SpaceMode.TabIndex = 4;
            this.rb_SpaceMode.Text = "SPACE";
            this.rb_SpaceMode.UseVisualStyleBackColor = true;
            this.rb_SpaceMode.Click += new System.EventHandler(this.rb_OPmode_CheckedChanged);
            // 
            // rb_MarkMode
            // 
            this.rb_MarkMode.AutoSize = true;
            this.rb_MarkMode.Location = new System.Drawing.Point(155, 22);
            this.rb_MarkMode.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.rb_MarkMode.Name = "rb_MarkMode";
            this.rb_MarkMode.Size = new System.Drawing.Size(60, 18);
            this.rb_MarkMode.TabIndex = 3;
            this.rb_MarkMode.Text = "MARK";
            this.rb_MarkMode.UseVisualStyleBackColor = true;
            this.rb_MarkMode.Click += new System.EventHandler(this.rb_OPmode_CheckedChanged);
            // 
            // rb_DotMode
            // 
            this.rb_DotMode.AutoSize = true;
            this.rb_DotMode.Location = new System.Drawing.Point(100, 22);
            this.rb_DotMode.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.rb_DotMode.Name = "rb_DotMode";
            this.rb_DotMode.Size = new System.Drawing.Size(49, 18);
            this.rb_DotMode.TabIndex = 2;
            this.rb_DotMode.Text = "DOT";
            this.rb_DotMode.UseVisualStyleBackColor = true;
            this.rb_DotMode.Click += new System.EventHandler(this.rb_OPmode_CheckedChanged);
            // 
            // rb_TxMode
            // 
            this.rb_TxMode.AutoSize = true;
            this.rb_TxMode.Location = new System.Drawing.Point(54, 22);
            this.rb_TxMode.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.rb_TxMode.Name = "rb_TxMode";
            this.rb_TxMode.Size = new System.Drawing.Size(40, 18);
            this.rb_TxMode.TabIndex = 1;
            this.rb_TxMode.Text = "TX";
            this.rb_TxMode.UseVisualStyleBackColor = true;
            this.rb_TxMode.Click += new System.EventHandler(this.rb_OPmode_CheckedChanged);
            // 
            // rb_RxMode
            // 
            this.rb_RxMode.AutoSize = true;
            this.rb_RxMode.Checked = true;
            this.rb_RxMode.Location = new System.Drawing.Point(7, 22);
            this.rb_RxMode.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.rb_RxMode.Name = "rb_RxMode";
            this.rb_RxMode.Size = new System.Drawing.Size(41, 18);
            this.rb_RxMode.TabIndex = 0;
            this.rb_RxMode.TabStop = true;
            this.rb_RxMode.Text = "RX";
            this.rb_RxMode.UseVisualStyleBackColor = true;
            this.rb_RxMode.Click += new System.EventHandler(this.rb_OPmode_CheckedChanged);
            // 
            // tabPage2
            // 
            this.tabPage2.Controls.Add(this.groupBox5);
            this.tabPage2.Controls.Add(this.groupBox1);
            this.tabPage2.Font = new System.Drawing.Font("휴먼모음T", 10F, System.Drawing.FontStyle.Bold);
            this.tabPage2.Location = new System.Drawing.Point(4, 22);
            this.tabPage2.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tabPage2.Name = "tabPage2";
            this.tabPage2.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tabPage2.Size = new System.Drawing.Size(1356, 719);
            this.tabPage2.TabIndex = 1;
            this.tabPage2.Text = "DIAGNOSIS";
            this.tabPage2.UseVisualStyleBackColor = true;
            // 
            // groupBox5
            // 
            this.groupBox5.Controls.Add(this.lb_selftest_tot_fail);
            this.groupBox5.Controls.Add(this.lb_selftest_tot_pass);
            this.groupBox5.Controls.Add(this.dgv_selftest);
            this.groupBox5.Controls.Add(this.bt_export);
            this.groupBox5.Controls.Add(this.btn_clean_selftest);
            this.groupBox5.Controls.Add(this.btn_run);
            this.groupBox5.Font = new System.Drawing.Font("휴먼모음T", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.groupBox5.Location = new System.Drawing.Point(17, 214);
            this.groupBox5.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox5.Name = "groupBox5";
            this.groupBox5.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox5.Size = new System.Drawing.Size(406, 282);
            this.groupBox5.TabIndex = 8;
            this.groupBox5.TabStop = false;
            this.groupBox5.Text = "Self Test";
            // 
            // lb_selftest_tot_fail
            // 
            this.lb_selftest_tot_fail.AutoSize = true;
            this.lb_selftest_tot_fail.ForeColor = System.Drawing.Color.Red;
            this.lb_selftest_tot_fail.Location = new System.Drawing.Point(102, 58);
            this.lb_selftest_tot_fail.Name = "lb_selftest_tot_fail";
            this.lb_selftest_tot_fail.Size = new System.Drawing.Size(57, 14);
            this.lb_selftest_tot_fail.TabIndex = 6;
            this.lb_selftest_tot_fail.Text = "FAIL : 0";
            // 
            // lb_selftest_tot_pass
            // 
            this.lb_selftest_tot_pass.AutoSize = true;
            this.lb_selftest_tot_pass.ForeColor = System.Drawing.Color.Green;
            this.lb_selftest_tot_pass.Location = new System.Drawing.Point(10, 58);
            this.lb_selftest_tot_pass.Name = "lb_selftest_tot_pass";
            this.lb_selftest_tot_pass.Size = new System.Drawing.Size(63, 14);
            this.lb_selftest_tot_pass.TabIndex = 6;
            this.lb_selftest_tot_pass.Text = "PASS : 0";
            // 
            // dgv_selftest
            // 
            this.dgv_selftest.AllowUserToAddRows = false;
            this.dgv_selftest.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            dataGridViewCellStyle23.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle23.BackColor = System.Drawing.SystemColors.Control;
            dataGridViewCellStyle23.Font = new System.Drawing.Font("휴먼모음T", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            dataGridViewCellStyle23.ForeColor = System.Drawing.SystemColors.WindowText;
            dataGridViewCellStyle23.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle23.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle23.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgv_selftest.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle23;
            this.dgv_selftest.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgv_selftest.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.TimeCol,
            this.ItemCol,
            this.ResultCol});
            dataGridViewCellStyle24.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle24.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle24.Font = new System.Drawing.Font("휴먼모음T", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            dataGridViewCellStyle24.ForeColor = System.Drawing.SystemColors.ControlText;
            dataGridViewCellStyle24.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle24.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle24.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv_selftest.DefaultCellStyle = dataGridViewCellStyle24;
            this.dgv_selftest.Location = new System.Drawing.Point(6, 86);
            this.dgv_selftest.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.dgv_selftest.Name = "dgv_selftest";
            this.dgv_selftest.RowHeadersVisible = false;
            this.dgv_selftest.RowHeadersWidth = 62;
            this.dgv_selftest.RowTemplate.Height = 23;
            this.dgv_selftest.Size = new System.Drawing.Size(384, 192);
            this.dgv_selftest.TabIndex = 5;
            // 
            // TimeCol
            // 
            this.TimeCol.FillWeight = 121.8274F;
            this.TimeCol.HeaderText = "TIME";
            this.TimeCol.MinimumWidth = 8;
            this.TimeCol.Name = "TimeCol";
            this.TimeCol.ReadOnly = true;
            // 
            // ItemCol
            // 
            this.ItemCol.FillWeight = 89.0863F;
            this.ItemCol.HeaderText = "ITEM";
            this.ItemCol.MinimumWidth = 8;
            this.ItemCol.Name = "ItemCol";
            this.ItemCol.ReadOnly = true;
            // 
            // ResultCol
            // 
            this.ResultCol.FillWeight = 89.0863F;
            this.ResultCol.HeaderText = "RESULT";
            this.ResultCol.MinimumWidth = 8;
            this.ResultCol.Name = "ResultCol";
            this.ResultCol.ReadOnly = true;
            // 
            // bt_export
            // 
            this.bt_export.Location = new System.Drawing.Point(268, 26);
            this.bt_export.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.bt_export.Name = "bt_export";
            this.bt_export.Size = new System.Drawing.Size(122, 22);
            this.bt_export.TabIndex = 1;
            this.bt_export.Text = "EXPORT";
            this.bt_export.UseVisualStyleBackColor = true;
            this.bt_export.Click += new System.EventHandler(this.btn_selftest_export_Click);
            // 
            // btn_clean_selftest
            // 
            this.btn_clean_selftest.Location = new System.Drawing.Point(140, 26);
            this.btn_clean_selftest.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.btn_clean_selftest.Name = "btn_clean_selftest";
            this.btn_clean_selftest.Size = new System.Drawing.Size(122, 22);
            this.btn_clean_selftest.TabIndex = 1;
            this.btn_clean_selftest.Text = "CLEAR";
            this.btn_clean_selftest.UseVisualStyleBackColor = true;
            this.btn_clean_selftest.Click += new System.EventHandler(this.btn_selftest_clear_Click);
            // 
            // btn_run
            // 
            this.btn_run.Location = new System.Drawing.Point(8, 26);
            this.btn_run.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.btn_run.Name = "btn_run";
            this.btn_run.Size = new System.Drawing.Size(122, 22);
            this.btn_run.TabIndex = 1;
            this.btn_run.Text = "START";
            this.btn_run.UseVisualStyleBackColor = true;
            this.btn_run.Click += new System.EventHandler(this.btn_run_Click);
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.lb_time);
            this.groupBox1.Controls.Add(this.lb_elapse);
            this.groupBox1.Controls.Add(this.numericUpDown1);
            this.groupBox1.Controls.Add(this.lb_data_bad);
            this.groupBox1.Controls.Add(this.lb_timeout);
            this.groupBox1.Controls.Add(this.lb_rate);
            this.groupBox1.Controls.Add(this.lb_retry);
            this.groupBox1.Controls.Add(this.lb_pass);
            this.groupBox1.Controls.Add(this.lb_count);
            this.groupBox1.Controls.Add(this.label5);
            this.groupBox1.Controls.Add(this.bt_clear);
            this.groupBox1.Controls.Add(this.btn_stop);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Controls.Add(this.label3);
            this.groupBox1.Controls.Add(this.btn_start);
            this.groupBox1.Controls.Add(this.label6);
            this.groupBox1.Controls.Add(this.label8);
            this.groupBox1.Controls.Add(this.label7);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.label4);
            this.groupBox1.Font = new System.Drawing.Font("휴먼모음T", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.groupBox1.Location = new System.Drawing.Point(17, 22);
            this.groupBox1.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox1.Size = new System.Drawing.Size(213, 162);
            this.groupBox1.TabIndex = 0;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "Data Protocol Test";
            // 
            // lb_time
            // 
            this.lb_time.AutoSize = true;
            this.lb_time.Location = new System.Drawing.Point(121, 144);
            this.lb_time.Name = "lb_time";
            this.lb_time.Size = new System.Drawing.Size(45, 14);
            this.lb_time.TabIndex = 7;
            this.lb_time.Text = "0:0:0.0";
            // 
            // lb_elapse
            // 
            this.lb_elapse.AutoSize = true;
            this.lb_elapse.Location = new System.Drawing.Point(39, 144);
            this.lb_elapse.Name = "lb_elapse";
            this.lb_elapse.Size = new System.Drawing.Size(76, 14);
            this.lb_elapse.TabIndex = 6;
            this.lb_elapse.Text = "Elapse Time";
            // 
            // numericUpDown1
            // 
            this.numericUpDown1.Location = new System.Drawing.Point(127, 114);
            this.numericUpDown1.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.numericUpDown1.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numericUpDown1.Name = "numericUpDown1";
            this.numericUpDown1.Size = new System.Drawing.Size(47, 22);
            this.numericUpDown1.TabIndex = 5;
            this.numericUpDown1.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numericUpDown1.ValueChanged += new System.EventHandler(this.numericUpDown1_ValueChanged);
            // 
            // lb_data_bad
            // 
            this.lb_data_bad.AutoSize = true;
            this.lb_data_bad.ForeColor = System.Drawing.Color.Red;
            this.lb_data_bad.Location = new System.Drawing.Point(66, 126);
            this.lb_data_bad.Name = "lb_data_bad";
            this.lb_data_bad.Size = new System.Drawing.Size(14, 14);
            this.lb_data_bad.TabIndex = 4;
            this.lb_data_bad.Text = "0";
            // 
            // lb_timeout
            // 
            this.lb_timeout.AutoSize = true;
            this.lb_timeout.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(192)))));
            this.lb_timeout.Location = new System.Drawing.Point(66, 108);
            this.lb_timeout.Name = "lb_timeout";
            this.lb_timeout.Size = new System.Drawing.Size(14, 14);
            this.lb_timeout.TabIndex = 4;
            this.lb_timeout.Text = "0";
            // 
            // lb_rate
            // 
            this.lb_rate.AutoSize = true;
            this.lb_rate.Location = new System.Drawing.Point(66, 90);
            this.lb_rate.Name = "lb_rate";
            this.lb_rate.Size = new System.Drawing.Size(14, 14);
            this.lb_rate.TabIndex = 4;
            this.lb_rate.Text = "0";
            // 
            // lb_retry
            // 
            this.lb_retry.AutoSize = true;
            this.lb_retry.ForeColor = System.Drawing.Color.Black;
            this.lb_retry.Location = new System.Drawing.Point(66, 70);
            this.lb_retry.Name = "lb_retry";
            this.lb_retry.Size = new System.Drawing.Size(14, 14);
            this.lb_retry.TabIndex = 4;
            this.lb_retry.Text = "0";
            // 
            // lb_pass
            // 
            this.lb_pass.AutoSize = true;
            this.lb_pass.ForeColor = System.Drawing.Color.Green;
            this.lb_pass.Location = new System.Drawing.Point(66, 48);
            this.lb_pass.Name = "lb_pass";
            this.lb_pass.Size = new System.Drawing.Size(14, 14);
            this.lb_pass.TabIndex = 4;
            this.lb_pass.Text = "0";
            // 
            // lb_count
            // 
            this.lb_count.AutoSize = true;
            this.lb_count.Location = new System.Drawing.Point(66, 26);
            this.lb_count.Name = "lb_count";
            this.lb_count.Size = new System.Drawing.Size(14, 14);
            this.lb_count.TabIndex = 4;
            this.lb_count.Text = "0";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(180, 122);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(24, 14);
            this.label5.TabIndex = 3;
            this.label5.Text = "ms";
            // 
            // bt_clear
            // 
            this.bt_clear.Location = new System.Drawing.Point(127, 70);
            this.bt_clear.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.bt_clear.Name = "bt_clear";
            this.bt_clear.Size = new System.Drawing.Size(79, 22);
            this.bt_clear.TabIndex = 1;
            this.bt_clear.Text = "RESET";
            this.bt_clear.UseVisualStyleBackColor = true;
            this.bt_clear.Click += new System.EventHandler(this.bt_clear_Click);
            // 
            // btn_stop
            // 
            this.btn_stop.Location = new System.Drawing.Point(127, 44);
            this.btn_stop.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.btn_stop.Name = "btn_stop";
            this.btn_stop.Size = new System.Drawing.Size(79, 22);
            this.btn_stop.TabIndex = 1;
            this.btn_stop.Text = "STOP";
            this.btn_stop.UseVisualStyleBackColor = true;
            this.btn_stop.Click += new System.EventHandler(this.btn_stop_Click);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Font = new System.Drawing.Font("휴먼모음T", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.label1.Location = new System.Drawing.Point(21, 26);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(42, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "COUNT:";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Font = new System.Drawing.Font("휴먼모음T", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.label3.Location = new System.Drawing.Point(34, 48);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(29, 12);
            this.label3.TabIndex = 0;
            this.label3.Text = "ACK:";
            // 
            // btn_start
            // 
            this.btn_start.Location = new System.Drawing.Point(127, 18);
            this.btn_start.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.btn_start.Name = "btn_start";
            this.btn_start.Size = new System.Drawing.Size(79, 22);
            this.btn_start.TabIndex = 1;
            this.btn_start.Text = "START";
            this.btn_start.UseVisualStyleBackColor = true;
            this.btn_start.Click += new System.EventHandler(this.btn_start_Click);
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Font = new System.Drawing.Font("휴먼모음T", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.label6.Location = new System.Drawing.Point(125, 98);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(45, 12);
            this.label6.TabIndex = 0;
            this.label6.Text = "PERIOD:";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Font = new System.Drawing.Font("휴먼모음T", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.label8.Location = new System.Drawing.Point(1, 126);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(62, 12);
            this.label8.TabIndex = 0;
            this.label8.Text = "DATA BAD:";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Font = new System.Drawing.Font("휴먼모음T", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.label7.Location = new System.Drawing.Point(11, 108);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(52, 12);
            this.label7.TabIndex = 0;
            this.label7.Text = "TIMEOUT:";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Font = new System.Drawing.Font("휴먼모음T", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.label2.Location = new System.Drawing.Point(6, 90);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(56, 12);
            this.label2.TabIndex = 0;
            this.label2.Text = "HIT RATE:";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Font = new System.Drawing.Font("휴먼모음T", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.label4.Location = new System.Drawing.Point(21, 70);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(41, 12);
            this.label4.TabIndex = 0;
            this.label4.Text = "RETRY:";
            // 
            // tabPage3
            // 
            this.tabPage3.Controls.Add(this.tableLayoutPanel1);
            this.tabPage3.Controls.Add(this.label13);
            this.tabPage3.Font = new System.Drawing.Font("휴먼모음T", 10F, System.Drawing.FontStyle.Bold);
            this.tabPage3.Location = new System.Drawing.Point(4, 22);
            this.tabPage3.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tabPage3.Name = "tabPage3";
            this.tabPage3.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tabPage3.Size = new System.Drawing.Size(1356, 719);
            this.tabPage3.TabIndex = 2;
            this.tabPage3.Text = "DSC";
            this.tabPage3.UseVisualStyleBackColor = true;
            // 
            // tableLayoutPanel1
            // 
            this.tableLayoutPanel1.ColumnCount = 2;
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 83.23553F));
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 16.76446F));
            this.tableLayoutPanel1.Controls.Add(this.dgvDscSend, 0, 3);
            this.tableLayoutPanel1.Controls.Add(this.dgvDscRsv, 0, 1);
            this.tableLayoutPanel1.Controls.Add(this.panel1, 0, 0);
            this.tableLayoutPanel1.Controls.Add(this.panel2, 0, 2);
            this.tableLayoutPanel1.Controls.Add(this.tableLayoutPanel2, 1, 1);
            this.tableLayoutPanel1.Controls.Add(this.tableLayoutPanel3, 1, 3);
            this.tableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel1.Location = new System.Drawing.Point(3, 2);
            this.tableLayoutPanel1.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tableLayoutPanel1.Name = "tableLayoutPanel1";
            this.tableLayoutPanel1.RowCount = 4;
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.tableLayoutPanel1.Size = new System.Drawing.Size(1350, 715);
            this.tableLayoutPanel1.TabIndex = 3;
            // 
            // dgvDscSend
            // 
            this.dgvDscSend.AllowUserToAddRows = false;
            this.dgvDscSend.AllowUserToResizeRows = false;
            dataGridViewCellStyle13.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            this.dgvDscSend.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle13;
            this.dgvDscSend.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            dataGridViewCellStyle14.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle14.BackColor = System.Drawing.SystemColors.Control;
            dataGridViewCellStyle14.Font = new System.Drawing.Font("휴먼모음T", 10F, System.Drawing.FontStyle.Bold);
            dataGridViewCellStyle14.ForeColor = System.Drawing.SystemColors.WindowText;
            dataGridViewCellStyle14.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle14.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle14.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgvDscSend.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle14;
            this.dgvDscSend.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgvDscSend.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.DscTxType,
            this.DscTxFormat,
            this.DscTxAddr,
            this.DscTxCategory,
            this.DscTxSelfID,
            this.DscTxTeleCom,
            this.DscTxTCom1,
            this.DscTxTCom2,
            this.DscTxSubCom,
            this.DscTxFreq,
            this.DscTxDistID,
            this.DscTxNature,
            this.DscTxLatitude,
            this.DscTxLongitude,
            this.DscTxUtc,
            this.DscTxEos,
            this.DscTxExpn,
            this.SendDsc});
            dataGridViewCellStyle15.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle15.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle15.Font = new System.Drawing.Font("휴먼모음T", 10F, System.Drawing.FontStyle.Bold);
            dataGridViewCellStyle15.ForeColor = System.Drawing.SystemColors.ControlText;
            dataGridViewCellStyle15.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle15.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle15.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgvDscSend.DefaultCellStyle = dataGridViewCellStyle15;
            this.dgvDscSend.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dgvDscSend.Location = new System.Drawing.Point(3, 384);
            this.dgvDscSend.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.dgvDscSend.Name = "dgvDscSend";
            dataGridViewCellStyle16.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle16.BackColor = System.Drawing.SystemColors.Control;
            dataGridViewCellStyle16.Font = new System.Drawing.Font("휴먼모음T", 10F, System.Drawing.FontStyle.Bold);
            dataGridViewCellStyle16.ForeColor = System.Drawing.SystemColors.WindowText;
            dataGridViewCellStyle16.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle16.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle16.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgvDscSend.RowHeadersDefaultCellStyle = dataGridViewCellStyle16;
            this.dgvDscSend.RowHeadersVisible = false;
            this.dgvDscSend.RowHeadersWidth = 62;
            dataGridViewCellStyle17.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            this.dgvDscSend.RowsDefaultCellStyle = dataGridViewCellStyle17;
            this.dgvDscSend.RowTemplate.Height = 21;
            this.dgvDscSend.Size = new System.Drawing.Size(1117, 329);
            this.dgvDscSend.TabIndex = 9;
            this.dgvDscSend.CellClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.CellClick_dgvDscSend);
            this.dgvDscSend.SelectionChanged += new System.EventHandler(this.SelectionChanged_DscTx);
            // 
            // DscTxType
            // 
            this.DscTxType.FillWeight = 91.49928F;
            this.DscTxType.HeaderText = "TYPE";
            this.DscTxType.MinimumWidth = 8;
            this.DscTxType.Name = "DscTxType";
            this.DscTxType.ReadOnly = true;
            // 
            // DscTxFormat
            // 
            this.DscTxFormat.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.DscTxFormat.FillWeight = 91.49928F;
            this.DscTxFormat.HeaderText = "FORMAT";
            this.DscTxFormat.MinimumWidth = 8;
            this.DscTxFormat.Name = "DscTxFormat";
            this.DscTxFormat.ReadOnly = true;
            this.DscTxFormat.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
            // 
            // DscTxAddr
            // 
            this.DscTxAddr.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.DscTxAddr.FillWeight = 91.49928F;
            this.DscTxAddr.HeaderText = "ADDR";
            this.DscTxAddr.MinimumWidth = 8;
            this.DscTxAddr.Name = "DscTxAddr";
            this.DscTxAddr.ReadOnly = true;
            // 
            // DscTxCategory
            // 
            this.DscTxCategory.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.DscTxCategory.FillWeight = 91.49928F;
            this.DscTxCategory.HeaderText = "CATEGORY";
            this.DscTxCategory.MinimumWidth = 8;
            this.DscTxCategory.Name = "DscTxCategory";
            this.DscTxCategory.ReadOnly = true;
            // 
            // DscTxSelfID
            // 
            this.DscTxSelfID.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.DscTxSelfID.FillWeight = 91.49928F;
            this.DscTxSelfID.HeaderText = "Self ID";
            this.DscTxSelfID.MinimumWidth = 8;
            this.DscTxSelfID.Name = "DscTxSelfID";
            this.DscTxSelfID.ReadOnly = true;
            // 
            // DscTxTeleCom
            // 
            this.DscTxTeleCom.FillWeight = 99.63344F;
            this.DscTxTeleCom.HeaderText = "TeleCom";
            this.DscTxTeleCom.MinimumWidth = 8;
            this.DscTxTeleCom.Name = "DscTxTeleCom";
            this.DscTxTeleCom.ReadOnly = true;
            // 
            // DscTxTCom1
            // 
            this.DscTxTCom1.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.DscTxTCom1.FillWeight = 91.49928F;
            this.DscTxTCom1.HeaderText = "TCom1";
            this.DscTxTCom1.MinimumWidth = 8;
            this.DscTxTCom1.Name = "DscTxTCom1";
            this.DscTxTCom1.ReadOnly = true;
            // 
            // DscTxTCom2
            // 
            this.DscTxTCom2.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.DscTxTCom2.FillWeight = 91.49928F;
            this.DscTxTCom2.HeaderText = "TCom2";
            this.DscTxTCom2.MinimumWidth = 8;
            this.DscTxTCom2.Name = "DscTxTCom2";
            this.DscTxTCom2.ReadOnly = true;
            // 
            // DscTxSubCom
            // 
            this.DscTxSubCom.FillWeight = 99.63344F;
            this.DscTxSubCom.HeaderText = "SubCom";
            this.DscTxSubCom.MinimumWidth = 8;
            this.DscTxSubCom.Name = "DscTxSubCom";
            // 
            // DscTxFreq
            // 
            this.DscTxFreq.FillWeight = 91.49928F;
            this.DscTxFreq.HeaderText = "FREQ/POS";
            this.DscTxFreq.MinimumWidth = 8;
            this.DscTxFreq.Name = "DscTxFreq";
            this.DscTxFreq.ReadOnly = true;
            // 
            // DscTxDistID
            // 
            this.DscTxDistID.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.DscTxDistID.FillWeight = 138.071F;
            this.DscTxDistID.HeaderText = "Dist ID/NUM";
            this.DscTxDistID.MinimumWidth = 8;
            this.DscTxDistID.Name = "DscTxDistID";
            this.DscTxDistID.ReadOnly = true;
            // 
            // DscTxNature
            // 
            this.DscTxNature.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.DscTxNature.FillWeight = 91.49928F;
            this.DscTxNature.HeaderText = "Nature";
            this.DscTxNature.MinimumWidth = 8;
            this.DscTxNature.Name = "DscTxNature";
            this.DscTxNature.ReadOnly = true;
            // 
            // DscTxLatitude
            // 
            this.DscTxLatitude.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.DscTxLatitude.FillWeight = 130.6409F;
            this.DscTxLatitude.HeaderText = "Latitude";
            this.DscTxLatitude.MinimumWidth = 8;
            this.DscTxLatitude.Name = "DscTxLatitude";
            this.DscTxLatitude.ReadOnly = true;
            // 
            // DscTxLongitude
            // 
            this.DscTxLongitude.FillWeight = 125.8955F;
            this.DscTxLongitude.HeaderText = "Longitude";
            this.DscTxLongitude.MinimumWidth = 8;
            this.DscTxLongitude.Name = "DscTxLongitude";
            this.DscTxLongitude.ReadOnly = true;
            // 
            // DscTxUtc
            // 
            this.DscTxUtc.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.DscTxUtc.FillWeight = 91.49928F;
            this.DscTxUtc.HeaderText = "UTC";
            this.DscTxUtc.MinimumWidth = 8;
            this.DscTxUtc.Name = "DscTxUtc";
            this.DscTxUtc.ReadOnly = true;
            // 
            // DscTxEos
            // 
            this.DscTxEos.FillWeight = 99.63344F;
            this.DscTxEos.HeaderText = "EOS";
            this.DscTxEos.MinimumWidth = 8;
            this.DscTxEos.Name = "DscTxEos";
            this.DscTxEos.ReadOnly = true;
            // 
            // DscTxExpn
            // 
            this.DscTxExpn.HeaderText = "Expan";
            this.DscTxExpn.MinimumWidth = 8;
            this.DscTxExpn.Name = "DscTxExpn";
            // 
            // SendDsc
            // 
            this.SendDsc.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.SendDsc.FillWeight = 91.49928F;
            this.SendDsc.FlatStyle = System.Windows.Forms.FlatStyle.Popup;
            this.SendDsc.HeaderText = "Send";
            this.SendDsc.MinimumWidth = 8;
            this.SendDsc.Name = "SendDsc";
            this.SendDsc.ReadOnly = true;
            this.SendDsc.Resizable = System.Windows.Forms.DataGridViewTriState.True;
            // 
            // dgvDscRsv
            // 
            this.dgvDscRsv.AllowUserToAddRows = false;
            this.dgvDscRsv.AllowUserToResizeRows = false;
            dataGridViewCellStyle18.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            this.dgvDscRsv.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle18;
            this.dgvDscRsv.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgvDscRsv.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableWithoutHeaderText;
            dataGridViewCellStyle19.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle19.BackColor = System.Drawing.SystemColors.Control;
            dataGridViewCellStyle19.Font = new System.Drawing.Font("휴먼모음T", 10F, System.Drawing.FontStyle.Bold);
            dataGridViewCellStyle19.ForeColor = System.Drawing.SystemColors.WindowText;
            dataGridViewCellStyle19.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle19.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle19.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgvDscRsv.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle19;
            this.dgvDscRsv.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgvDscRsv.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.RsvTime,
            this.DscFormat,
            this.DscAddr,
            this.DscCategory,
            this.DscSelfID,
            this.DscTeleCom1,
            this.DscTeleCom2,
            this.DscFreq,
            this.DscDistID,
            this.DscNature,
            this.DscPOS,
            this.DscUTC,
            this.DscExpnPos,
            this.DscEOS});
            dataGridViewCellStyle20.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle20.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle20.Font = new System.Drawing.Font("휴먼모음T", 10F, System.Drawing.FontStyle.Bold);
            dataGridViewCellStyle20.ForeColor = System.Drawing.SystemColors.ControlText;
            dataGridViewCellStyle20.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle20.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle20.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgvDscRsv.DefaultCellStyle = dataGridViewCellStyle20;
            this.dgvDscRsv.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dgvDscRsv.Location = new System.Drawing.Point(3, 32);
            this.dgvDscRsv.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.dgvDscRsv.Name = "dgvDscRsv";
            dataGridViewCellStyle21.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle21.BackColor = System.Drawing.SystemColors.Control;
            dataGridViewCellStyle21.Font = new System.Drawing.Font("휴먼모음T", 10F, System.Drawing.FontStyle.Bold);
            dataGridViewCellStyle21.ForeColor = System.Drawing.SystemColors.WindowText;
            dataGridViewCellStyle21.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle21.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle21.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgvDscRsv.RowHeadersDefaultCellStyle = dataGridViewCellStyle21;
            this.dgvDscRsv.RowHeadersVisible = false;
            this.dgvDscRsv.RowHeadersWidth = 62;
            dataGridViewCellStyle22.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            this.dgvDscRsv.RowsDefaultCellStyle = dataGridViewCellStyle22;
            this.dgvDscRsv.RowTemplate.Height = 21;
            this.dgvDscRsv.Size = new System.Drawing.Size(1117, 328);
            this.dgvDscRsv.TabIndex = 3;
            // 
            // RsvTime
            // 
            this.RsvTime.HeaderText = "TIME";
            this.RsvTime.MinimumWidth = 8;
            this.RsvTime.Name = "RsvTime";
            this.RsvTime.ReadOnly = true;
            // 
            // DscFormat
            // 
            this.DscFormat.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.DscFormat.HeaderText = "FORMAT";
            this.DscFormat.MinimumWidth = 8;
            this.DscFormat.Name = "DscFormat";
            this.DscFormat.ReadOnly = true;
            this.DscFormat.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
            // 
            // DscAddr
            // 
            this.DscAddr.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.DscAddr.HeaderText = "ADDR";
            this.DscAddr.MinimumWidth = 8;
            this.DscAddr.Name = "DscAddr";
            this.DscAddr.ReadOnly = true;
            // 
            // DscCategory
            // 
            this.DscCategory.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.DscCategory.HeaderText = "CATEGORY";
            this.DscCategory.MinimumWidth = 8;
            this.DscCategory.Name = "DscCategory";
            this.DscCategory.ReadOnly = true;
            // 
            // DscSelfID
            // 
            this.DscSelfID.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.DscSelfID.HeaderText = "Self ID";
            this.DscSelfID.MinimumWidth = 8;
            this.DscSelfID.Name = "DscSelfID";
            this.DscSelfID.ReadOnly = true;
            // 
            // DscTeleCom1
            // 
            this.DscTeleCom1.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.DscTeleCom1.HeaderText = "TCom1";
            this.DscTeleCom1.MinimumWidth = 8;
            this.DscTeleCom1.Name = "DscTeleCom1";
            this.DscTeleCom1.ReadOnly = true;
            // 
            // DscTeleCom2
            // 
            this.DscTeleCom2.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.DscTeleCom2.HeaderText = "TCom2";
            this.DscTeleCom2.MinimumWidth = 8;
            this.DscTeleCom2.Name = "DscTeleCom2";
            this.DscTeleCom2.ReadOnly = true;
            // 
            // DscFreq
            // 
            this.DscFreq.HeaderText = "FREQ";
            this.DscFreq.MinimumWidth = 8;
            this.DscFreq.Name = "DscFreq";
            // 
            // DscDistID
            // 
            this.DscDistID.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.DscDistID.HeaderText = "Dist ID";
            this.DscDistID.MinimumWidth = 8;
            this.DscDistID.Name = "DscDistID";
            this.DscDistID.ReadOnly = true;
            // 
            // DscNature
            // 
            this.DscNature.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.DscNature.HeaderText = "Nature";
            this.DscNature.MinimumWidth = 8;
            this.DscNature.Name = "DscNature";
            this.DscNature.ReadOnly = true;
            // 
            // DscPOS
            // 
            this.DscPOS.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.DscPOS.HeaderText = "Pos/TelNo";
            this.DscPOS.MinimumWidth = 8;
            this.DscPOS.Name = "DscPOS";
            this.DscPOS.ReadOnly = true;
            // 
            // DscUTC
            // 
            this.DscUTC.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.DscUTC.HeaderText = "UTC";
            this.DscUTC.MinimumWidth = 8;
            this.DscUTC.Name = "DscUTC";
            this.DscUTC.ReadOnly = true;
            // 
            // DscExpnPos
            // 
            this.DscExpnPos.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.DscExpnPos.HeaderText = "Expn POS";
            this.DscExpnPos.MinimumWidth = 8;
            this.DscExpnPos.Name = "DscExpnPos";
            this.DscExpnPos.ReadOnly = true;
            // 
            // DscEOS
            // 
            this.DscEOS.HeaderText = "EOS";
            this.DscEOS.MinimumWidth = 8;
            this.DscEOS.Name = "DscEOS";
            this.DscEOS.ReadOnly = true;
            // 
            // panel1
            // 
            this.tableLayoutPanel1.SetColumnSpan(this.panel1, 2);
            this.panel1.Controls.Add(this.lb_rx_dsc);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panel1.Location = new System.Drawing.Point(3, 2);
            this.panel1.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(1344, 26);
            this.panel1.TabIndex = 6;
            // 
            // lb_rx_dsc
            // 
            this.lb_rx_dsc.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.lb_rx_dsc.AutoSize = true;
            this.lb_rx_dsc.BackColor = System.Drawing.Color.Transparent;
            this.lb_rx_dsc.Font = new System.Drawing.Font("휴먼모음T", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.lb_rx_dsc.ForeColor = System.Drawing.SystemColors.ControlText;
            this.lb_rx_dsc.Location = new System.Drawing.Point(3, 6);
            this.lb_rx_dsc.Name = "lb_rx_dsc";
            this.lb_rx_dsc.Size = new System.Drawing.Size(83, 13);
            this.lb_rx_dsc.TabIndex = 4;
            this.lb_rx_dsc.Text = "RX DSC (0)";
            this.lb_rx_dsc.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // panel2
            // 
            this.tableLayoutPanel1.SetColumnSpan(this.panel2, 2);
            this.panel2.Controls.Add(this.lb_tx_dsc);
            this.panel2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panel2.Location = new System.Drawing.Point(3, 364);
            this.panel2.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.panel2.Name = "panel2";
            this.panel2.Size = new System.Drawing.Size(1344, 16);
            this.panel2.TabIndex = 7;
            // 
            // lb_tx_dsc
            // 
            this.lb_tx_dsc.Anchor = System.Windows.Forms.AnchorStyles.Left;
            this.lb_tx_dsc.AutoSize = true;
            this.lb_tx_dsc.BackColor = System.Drawing.Color.Transparent;
            this.lb_tx_dsc.Font = new System.Drawing.Font("휴먼모음T", 9F, System.Drawing.FontStyle.Bold);
            this.lb_tx_dsc.ForeColor = System.Drawing.SystemColors.ControlText;
            this.lb_tx_dsc.Location = new System.Drawing.Point(3, 3);
            this.lb_tx_dsc.Name = "lb_tx_dsc";
            this.lb_tx_dsc.Size = new System.Drawing.Size(81, 13);
            this.lb_tx_dsc.TabIndex = 5;
            this.lb_tx_dsc.Text = "TX DSC (0)";
            this.lb_tx_dsc.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // tableLayoutPanel2
            // 
            this.tableLayoutPanel2.BackColor = System.Drawing.Color.Transparent;
            this.tableLayoutPanel2.ColumnCount = 2;
            this.tableLayoutPanel2.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.tableLayoutPanel2.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.tableLayoutPanel2.Controls.Add(this.btn_rx_clear, 0, 0);
            this.tableLayoutPanel2.Controls.Add(this.btn_import, 0, 1);
            this.tableLayoutPanel2.Controls.Add(this.btn_export, 1, 1);
            this.tableLayoutPanel2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel2.Location = new System.Drawing.Point(1126, 32);
            this.tableLayoutPanel2.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tableLayoutPanel2.Name = "tableLayoutPanel2";
            this.tableLayoutPanel2.RowCount = 4;
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30F));
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30F));
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20F));
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20F));
            this.tableLayoutPanel2.Size = new System.Drawing.Size(221, 328);
            this.tableLayoutPanel2.TabIndex = 8;
            // 
            // btn_rx_clear
            // 
            this.btn_rx_clear.BackColor = System.Drawing.Color.Silver;
            this.btn_rx_clear.Dock = System.Windows.Forms.DockStyle.Fill;
            this.btn_rx_clear.Font = new System.Drawing.Font("휴먼모음T", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.btn_rx_clear.Location = new System.Drawing.Point(1, 2);
            this.btn_rx_clear.Margin = new System.Windows.Forms.Padding(1, 2, 1, 2);
            this.btn_rx_clear.Name = "btn_rx_clear";
            this.btn_rx_clear.Size = new System.Drawing.Size(108, 26);
            this.btn_rx_clear.TabIndex = 0;
            this.btn_rx_clear.Text = "CLEAR";
            this.btn_rx_clear.UseVisualStyleBackColor = false;
            this.btn_rx_clear.Click += new System.EventHandler(this.btn_rx_clear_Click);
            // 
            // btn_import
            // 
            this.btn_import.BackColor = System.Drawing.Color.Silver;
            this.btn_import.Dock = System.Windows.Forms.DockStyle.Fill;
            this.btn_import.Font = new System.Drawing.Font("휴먼모음T", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.btn_import.Location = new System.Drawing.Point(1, 32);
            this.btn_import.Margin = new System.Windows.Forms.Padding(1, 2, 1, 2);
            this.btn_import.Name = "btn_import";
            this.btn_import.Size = new System.Drawing.Size(108, 26);
            this.btn_import.TabIndex = 0;
            this.btn_import.Text = "IMPORT";
            this.btn_import.UseVisualStyleBackColor = false;
            this.btn_import.Click += new System.EventHandler(this.btn_import_Click);
            // 
            // btn_export
            // 
            this.btn_export.BackColor = System.Drawing.Color.Silver;
            this.btn_export.Dock = System.Windows.Forms.DockStyle.Fill;
            this.btn_export.Font = new System.Drawing.Font("휴먼모음T", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.btn_export.Location = new System.Drawing.Point(111, 32);
            this.btn_export.Margin = new System.Windows.Forms.Padding(1, 2, 1, 2);
            this.btn_export.Name = "btn_export";
            this.btn_export.Size = new System.Drawing.Size(109, 26);
            this.btn_export.TabIndex = 1;
            this.btn_export.Text = "EXPORT";
            this.btn_export.UseVisualStyleBackColor = false;
            this.btn_export.Click += new System.EventHandler(this.btn_export_Click);
            // 
            // tableLayoutPanel3
            // 
            this.tableLayoutPanel3.BackColor = System.Drawing.Color.Transparent;
            this.tableLayoutPanel3.ColumnCount = 2;
            this.tableLayoutPanel3.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.tableLayoutPanel3.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.tableLayoutPanel3.Controls.Add(this.btnDSCClear, 1, 2);
            this.tableLayoutPanel3.Controls.Add(this.btn_tx_import, 0, 3);
            this.tableLayoutPanel3.Controls.Add(this.btnDSCAdd, 0, 0);
            this.tableLayoutPanel3.Controls.Add(this.btnDSCDelete, 0, 2);
            this.tableLayoutPanel3.Controls.Add(this.btnDscEdit, 0, 1);
            this.tableLayoutPanel3.Controls.Add(this.btn_tx_export, 1, 3);
            this.tableLayoutPanel3.Controls.Add(this.label31, 0, 5);
            this.tableLayoutPanel3.Controls.Add(this.tb_dsc_send_auto_intval, 1, 5);
            this.tableLayoutPanel3.Controls.Add(this.btn_dsc_auto_send, 1, 7);
            this.tableLayoutPanel3.Controls.Add(this.label48, 0, 6);
            this.tableLayoutPanel3.Controls.Add(this.tb_num_of_runs, 1, 6);
            this.tableLayoutPanel3.Controls.Add(this.cb_keep_run, 0, 7);
            this.tableLayoutPanel3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel3.Location = new System.Drawing.Point(1126, 384);
            this.tableLayoutPanel3.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tableLayoutPanel3.Name = "tableLayoutPanel3";
            this.tableLayoutPanel3.RowCount = 9;
            this.tableLayoutPanel3.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30F));
            this.tableLayoutPanel3.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30F));
            this.tableLayoutPanel3.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30F));
            this.tableLayoutPanel3.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30F));
            this.tableLayoutPanel3.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30F));
            this.tableLayoutPanel3.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30F));
            this.tableLayoutPanel3.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30F));
            this.tableLayoutPanel3.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30F));
            this.tableLayoutPanel3.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30F));
            this.tableLayoutPanel3.Size = new System.Drawing.Size(221, 329);
            this.tableLayoutPanel3.TabIndex = 10;
            // 
            // btnDSCClear
            // 
            this.btnDSCClear.BackColor = System.Drawing.Color.Silver;
            this.btnDSCClear.Dock = System.Windows.Forms.DockStyle.Fill;
            this.btnDSCClear.Font = new System.Drawing.Font("휴먼모음T", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.btnDSCClear.Location = new System.Drawing.Point(111, 62);
            this.btnDSCClear.Margin = new System.Windows.Forms.Padding(1, 2, 1, 2);
            this.btnDSCClear.Name = "btnDSCClear";
            this.btnDSCClear.Size = new System.Drawing.Size(109, 26);
            this.btnDSCClear.TabIndex = 2;
            this.btnDSCClear.Text = "CLEAR";
            this.btnDSCClear.UseVisualStyleBackColor = false;
            this.btnDSCClear.Click += new System.EventHandler(this.btnDSCClear_Click);
            // 
            // btn_tx_import
            // 
            this.btn_tx_import.BackColor = System.Drawing.Color.Silver;
            this.btn_tx_import.Dock = System.Windows.Forms.DockStyle.Fill;
            this.btn_tx_import.Font = new System.Drawing.Font("휴먼모음T", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.btn_tx_import.Location = new System.Drawing.Point(1, 92);
            this.btn_tx_import.Margin = new System.Windows.Forms.Padding(1, 2, 1, 2);
            this.btn_tx_import.Name = "btn_tx_import";
            this.btn_tx_import.Size = new System.Drawing.Size(108, 26);
            this.btn_tx_import.TabIndex = 2;
            this.btn_tx_import.Text = "IMPORT";
            this.btn_tx_import.UseVisualStyleBackColor = false;
            this.btn_tx_import.Click += new System.EventHandler(this.btn_tx_import_Click);
            // 
            // btnDSCAdd
            // 
            this.btnDSCAdd.BackColor = System.Drawing.Color.Silver;
            this.btnDSCAdd.Dock = System.Windows.Forms.DockStyle.Fill;
            this.btnDSCAdd.Font = new System.Drawing.Font("휴먼모음T", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.btnDSCAdd.Location = new System.Drawing.Point(1, 2);
            this.btnDSCAdd.Margin = new System.Windows.Forms.Padding(1, 2, 1, 2);
            this.btnDSCAdd.Name = "btnDSCAdd";
            this.btnDSCAdd.Size = new System.Drawing.Size(108, 26);
            this.btnDSCAdd.TabIndex = 13;
            this.btnDSCAdd.Text = "ADD";
            this.btnDSCAdd.UseVisualStyleBackColor = false;
            this.btnDSCAdd.Click += new System.EventHandler(this.btnDSCAdd_Click);
            // 
            // btnDSCDelete
            // 
            this.btnDSCDelete.BackColor = System.Drawing.Color.Silver;
            this.btnDSCDelete.Dock = System.Windows.Forms.DockStyle.Fill;
            this.btnDSCDelete.Font = new System.Drawing.Font("휴먼모음T", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.btnDSCDelete.Location = new System.Drawing.Point(1, 62);
            this.btnDSCDelete.Margin = new System.Windows.Forms.Padding(1, 2, 1, 2);
            this.btnDSCDelete.Name = "btnDSCDelete";
            this.btnDSCDelete.Size = new System.Drawing.Size(108, 26);
            this.btnDSCDelete.TabIndex = 14;
            this.btnDSCDelete.Text = "DEL";
            this.btnDSCDelete.UseVisualStyleBackColor = false;
            this.btnDSCDelete.Click += new System.EventHandler(this.btnDSCDelete_Click);
            // 
            // btnDscEdit
            // 
            this.btnDscEdit.BackColor = System.Drawing.Color.Silver;
            this.btnDscEdit.Dock = System.Windows.Forms.DockStyle.Fill;
            this.btnDscEdit.Font = new System.Drawing.Font("휴먼모음T", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.btnDscEdit.Location = new System.Drawing.Point(1, 32);
            this.btnDscEdit.Margin = new System.Windows.Forms.Padding(1, 2, 1, 2);
            this.btnDscEdit.Name = "btnDscEdit";
            this.btnDscEdit.Size = new System.Drawing.Size(108, 26);
            this.btnDscEdit.TabIndex = 15;
            this.btnDscEdit.Text = "EDIT";
            this.btnDscEdit.UseVisualStyleBackColor = false;
            this.btnDscEdit.Click += new System.EventHandler(this.btnDscEdit_Click);
            // 
            // btn_tx_export
            // 
            this.btn_tx_export.BackColor = System.Drawing.Color.Silver;
            this.btn_tx_export.Dock = System.Windows.Forms.DockStyle.Fill;
            this.btn_tx_export.Font = new System.Drawing.Font("휴먼모음T", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.btn_tx_export.Location = new System.Drawing.Point(111, 92);
            this.btn_tx_export.Margin = new System.Windows.Forms.Padding(1, 2, 1, 2);
            this.btn_tx_export.Name = "btn_tx_export";
            this.btn_tx_export.Size = new System.Drawing.Size(109, 26);
            this.btn_tx_export.TabIndex = 3;
            this.btn_tx_export.Text = "EXPORT";
            this.btn_tx_export.UseVisualStyleBackColor = false;
            this.btn_tx_export.Click += new System.EventHandler(this.btn_tx_export_Click);
            // 
            // label31
            // 
            this.label31.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.label31.AutoSize = true;
            this.label31.Font = new System.Drawing.Font("휴먼모음T", 9.75F);
            this.label31.Location = new System.Drawing.Point(3, 150);
            this.label31.Name = "label31";
            this.label31.Size = new System.Drawing.Size(104, 30);
            this.label31.TabIndex = 16;
            this.label31.Text = "INTERVAL(ms)";
            this.label31.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            // 
            // tb_dsc_send_auto_intval
            // 
            this.tb_dsc_send_auto_intval.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tb_dsc_send_auto_intval.Font = new System.Drawing.Font("휴먼모음T", 9.75F);
            this.tb_dsc_send_auto_intval.Location = new System.Drawing.Point(111, 152);
            this.tb_dsc_send_auto_intval.Margin = new System.Windows.Forms.Padding(1, 2, 1, 2);
            this.tb_dsc_send_auto_intval.Name = "tb_dsc_send_auto_intval";
            this.tb_dsc_send_auto_intval.Size = new System.Drawing.Size(109, 22);
            this.tb_dsc_send_auto_intval.TabIndex = 17;
            this.tb_dsc_send_auto_intval.Text = "1000";
            this.tb_dsc_send_auto_intval.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.tb_dsc_send_auto_intval.KeyDown += new System.Windows.Forms.KeyEventHandler(this.tb_dsc_test_interval_keydown);
            // 
            // btn_dsc_auto_send
            // 
            this.btn_dsc_auto_send.Dock = System.Windows.Forms.DockStyle.Fill;
            this.btn_dsc_auto_send.Font = new System.Drawing.Font("휴먼모음T", 9.75F);
            this.btn_dsc_auto_send.Location = new System.Drawing.Point(111, 212);
            this.btn_dsc_auto_send.Margin = new System.Windows.Forms.Padding(1, 2, 1, 2);
            this.btn_dsc_auto_send.Name = "btn_dsc_auto_send";
            this.btn_dsc_auto_send.Size = new System.Drawing.Size(109, 26);
            this.btn_dsc_auto_send.TabIndex = 18;
            this.btn_dsc_auto_send.Text = "RUN";
            this.btn_dsc_auto_send.UseVisualStyleBackColor = true;
            this.btn_dsc_auto_send.Click += new System.EventHandler(this.btn_dsc_auto_send_Click);
            // 
            // label48
            // 
            this.label48.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.label48.AutoSize = true;
            this.label48.Font = new System.Drawing.Font("휴먼모음T", 9.75F);
            this.label48.Location = new System.Drawing.Point(3, 180);
            this.label48.Name = "label48";
            this.label48.Size = new System.Drawing.Size(104, 30);
            this.label48.TabIndex = 19;
            this.label48.Text = "Num Of Run";
            this.label48.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            // 
            // tb_num_of_runs
            // 
            this.tb_num_of_runs.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tb_num_of_runs.Font = new System.Drawing.Font("휴먼모음T", 9.75F);
            this.tb_num_of_runs.Location = new System.Drawing.Point(111, 182);
            this.tb_num_of_runs.Margin = new System.Windows.Forms.Padding(1, 2, 1, 2);
            this.tb_num_of_runs.Name = "tb_num_of_runs";
            this.tb_num_of_runs.Size = new System.Drawing.Size(109, 22);
            this.tb_num_of_runs.TabIndex = 20;
            this.tb_num_of_runs.Text = "100";
            this.tb_num_of_runs.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.tb_num_of_runs.KeyDown += new System.Windows.Forms.KeyEventHandler(this.tb_dsc_test_num_of_run_keydown);
            // 
            // cb_keep_run
            // 
            this.cb_keep_run.AutoSize = true;
            this.cb_keep_run.Dock = System.Windows.Forms.DockStyle.Right;
            this.cb_keep_run.Font = new System.Drawing.Font("휴먼모음T", 9.75F);
            this.cb_keep_run.Location = new System.Drawing.Point(25, 212);
            this.cb_keep_run.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.cb_keep_run.Name = "cb_keep_run";
            this.cb_keep_run.Size = new System.Drawing.Size(82, 26);
            this.cb_keep_run.TabIndex = 21;
            this.cb_keep_run.Text = "Keep Run";
            this.cb_keep_run.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.cb_keep_run.UseVisualStyleBackColor = true;
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(42, 4);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(0, 16);
            this.label13.TabIndex = 1;
            // 
            // tabPage4
            // 
            this.tabPage4.Controls.Add(this.tableLayoutPanel4);
            this.tabPage4.Location = new System.Drawing.Point(4, 22);
            this.tabPage4.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tabPage4.Name = "tabPage4";
            this.tabPage4.Size = new System.Drawing.Size(1356, 719);
            this.tabPage4.TabIndex = 3;
            this.tabPage4.Text = "ALERT";
            this.tabPage4.UseVisualStyleBackColor = true;
            // 
            // tableLayoutPanel4
            // 
            this.tableLayoutPanel4.ColumnCount = 2;
            this.tableLayoutPanel4.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 30F));
            this.tableLayoutPanel4.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 70F));
            this.tableLayoutPanel4.Controls.Add(this.tableLayoutPanel5, 1, 0);
            this.tableLayoutPanel4.Controls.Add(this.bt_notify, 0, 1);
            this.tableLayoutPanel4.Controls.Add(this.tableLayoutPanel6, 0, 0);
            this.tableLayoutPanel4.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel4.Location = new System.Drawing.Point(0, 0);
            this.tableLayoutPanel4.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tableLayoutPanel4.Name = "tableLayoutPanel4";
            this.tableLayoutPanel4.RowCount = 2;
            this.tableLayoutPanel4.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 42.99835F));
            this.tableLayoutPanel4.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 21.91104F));
            this.tableLayoutPanel4.Size = new System.Drawing.Size(1356, 719);
            this.tableLayoutPanel4.TabIndex = 7;
            // 
            // tableLayoutPanel5
            // 
            this.tableLayoutPanel5.ColumnCount = 4;
            this.tableLayoutPanel5.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 20F));
            this.tableLayoutPanel5.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 30F));
            this.tableLayoutPanel5.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 20F));
            this.tableLayoutPanel5.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 30F));
            this.tableLayoutPanel5.Controls.Add(this.tb_title, 1, 2);
            this.tableLayoutPanel5.Controls.Add(this.tb_category, 3, 1);
            this.tableLayoutPanel5.Controls.Add(this.tb_priority, 1, 1);
            this.tableLayoutPanel5.Controls.Add(this.tb_instance, 3, 0);
            this.tableLayoutPanel5.Controls.Add(this.lb_ID, 0, 0);
            this.tableLayoutPanel5.Controls.Add(this.label14, 2, 1);
            this.tableLayoutPanel5.Controls.Add(this.lb_priority, 0, 1);
            this.tableLayoutPanel5.Controls.Add(this.lb_instance, 2, 0);
            this.tableLayoutPanel5.Controls.Add(this.label15, 0, 2);
            this.tableLayoutPanel5.Controls.Add(this.tb_id, 1, 0);
            this.tableLayoutPanel5.Controls.Add(this.tb_desc, 3, 2);
            this.tableLayoutPanel5.Controls.Add(this.label16, 2, 2);
            this.tableLayoutPanel5.Dock = System.Windows.Forms.DockStyle.Left;
            this.tableLayoutPanel5.Location = new System.Drawing.Point(409, 10);
            this.tableLayoutPanel5.Margin = new System.Windows.Forms.Padding(3, 10, 3, 2);
            this.tableLayoutPanel5.Name = "tableLayoutPanel5";
            this.tableLayoutPanel5.RowCount = 4;
            this.tableLayoutPanel5.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 17.74635F));
            this.tableLayoutPanel5.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 17.74635F));
            this.tableLayoutPanel5.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 17.74635F));
            this.tableLayoutPanel5.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 46.76095F));
            this.tableLayoutPanel5.Size = new System.Drawing.Size(785, 464);
            this.tableLayoutPanel5.TabIndex = 1;
            // 
            // tb_title
            // 
            this.tb_title.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tb_title.Enabled = false;
            this.tb_title.Font = new System.Drawing.Font("휴먼모음T", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.tb_title.Location = new System.Drawing.Point(160, 164);
            this.tb_title.Margin = new System.Windows.Forms.Padding(3, 0, 3, 2);
            this.tb_title.Name = "tb_title";
            this.tb_title.Size = new System.Drawing.Size(229, 26);
            this.tb_title.TabIndex = 11;
            // 
            // tb_category
            // 
            this.tb_category.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tb_category.Enabled = false;
            this.tb_category.Font = new System.Drawing.Font("휴먼모음T", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.tb_category.Location = new System.Drawing.Point(552, 82);
            this.tb_category.Margin = new System.Windows.Forms.Padding(3, 0, 3, 2);
            this.tb_category.Name = "tb_category";
            this.tb_category.Size = new System.Drawing.Size(230, 26);
            this.tb_category.TabIndex = 10;
            // 
            // tb_priority
            // 
            this.tb_priority.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tb_priority.Enabled = false;
            this.tb_priority.Font = new System.Drawing.Font("휴먼모음T", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.tb_priority.Location = new System.Drawing.Point(160, 82);
            this.tb_priority.Margin = new System.Windows.Forms.Padding(3, 0, 3, 2);
            this.tb_priority.Name = "tb_priority";
            this.tb_priority.Size = new System.Drawing.Size(229, 26);
            this.tb_priority.TabIndex = 9;
            // 
            // tb_instance
            // 
            this.tb_instance.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tb_instance.Enabled = false;
            this.tb_instance.Font = new System.Drawing.Font("휴먼모음T", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.tb_instance.Location = new System.Drawing.Point(552, 0);
            this.tb_instance.Margin = new System.Windows.Forms.Padding(3, 0, 3, 2);
            this.tb_instance.Name = "tb_instance";
            this.tb_instance.Size = new System.Drawing.Size(230, 26);
            this.tb_instance.TabIndex = 8;
            // 
            // lb_ID
            // 
            this.lb_ID.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lb_ID.AutoSize = true;
            this.lb_ID.Font = new System.Drawing.Font("휴먼모음T", 11.25F);
            this.lb_ID.Location = new System.Drawing.Point(120, 0);
            this.lb_ID.Name = "lb_ID";
            this.lb_ID.Size = new System.Drawing.Size(34, 17);
            this.lb_ID.TabIndex = 1;
            this.lb_ID.Text = "ID :";
            this.lb_ID.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // label14
            // 
            this.label14.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.label14.AutoSize = true;
            this.label14.Font = new System.Drawing.Font("휴먼모음T", 11.25F);
            this.label14.Location = new System.Drawing.Point(453, 82);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(93, 17);
            this.label14.TabIndex = 4;
            this.label14.Text = "CATEGORY :";
            this.label14.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // lb_priority
            // 
            this.lb_priority.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lb_priority.AutoSize = true;
            this.lb_priority.Font = new System.Drawing.Font("휴먼모음T", 11.25F);
            this.lb_priority.Location = new System.Drawing.Point(69, 82);
            this.lb_priority.Name = "lb_priority";
            this.lb_priority.Size = new System.Drawing.Size(85, 17);
            this.lb_priority.TabIndex = 3;
            this.lb_priority.Text = "PRIORITY :";
            this.lb_priority.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // lb_instance
            // 
            this.lb_instance.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lb_instance.AutoSize = true;
            this.lb_instance.Font = new System.Drawing.Font("휴먼모음T", 11.25F);
            this.lb_instance.Location = new System.Drawing.Point(459, 0);
            this.lb_instance.Name = "lb_instance";
            this.lb_instance.Size = new System.Drawing.Size(87, 17);
            this.lb_instance.TabIndex = 2;
            this.lb_instance.Text = "INSTANCE :";
            this.lb_instance.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // label15
            // 
            this.label15.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.label15.AutoSize = true;
            this.label15.Font = new System.Drawing.Font("휴먼모음T", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.label15.Location = new System.Drawing.Point(98, 164);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(56, 17);
            this.label15.TabIndex = 5;
            this.label15.Text = "TITLE :";
            this.label15.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            // 
            // tb_id
            // 
            this.tb_id.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tb_id.Enabled = false;
            this.tb_id.Font = new System.Drawing.Font("휴먼모음T", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.tb_id.Location = new System.Drawing.Point(160, 0);
            this.tb_id.Margin = new System.Windows.Forms.Padding(3, 0, 3, 2);
            this.tb_id.Name = "tb_id";
            this.tb_id.Size = new System.Drawing.Size(229, 26);
            this.tb_id.TabIndex = 7;
            // 
            // tb_desc
            // 
            this.tb_desc.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tb_desc.Enabled = false;
            this.tb_desc.Font = new System.Drawing.Font("휴먼모음T", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.tb_desc.Location = new System.Drawing.Point(552, 164);
            this.tb_desc.Margin = new System.Windows.Forms.Padding(3, 0, 3, 2);
            this.tb_desc.Name = "tb_desc";
            this.tb_desc.Size = new System.Drawing.Size(230, 26);
            this.tb_desc.TabIndex = 13;
            // 
            // label16
            // 
            this.label16.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.label16.AutoSize = true;
            this.label16.Font = new System.Drawing.Font("휴먼모음T", 11.25F);
            this.label16.Location = new System.Drawing.Point(434, 164);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(112, 17);
            this.label16.TabIndex = 6;
            this.label16.Text = "DESCRIPTION :";
            this.label16.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // bt_notify
            // 
            this.bt_notify.Dock = System.Windows.Forms.DockStyle.Fill;
            this.bt_notify.Font = new System.Drawing.Font("휴먼모음T", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.bt_notify.Location = new System.Drawing.Point(3, 478);
            this.bt_notify.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.bt_notify.Name = "bt_notify";
            this.bt_notify.Size = new System.Drawing.Size(400, 239);
            this.bt_notify.TabIndex = 14;
            this.bt_notify.Text = "NOTIFY ALERT TO DISPLAY";
            this.bt_notify.UseVisualStyleBackColor = true;
            this.bt_notify.Click += new System.EventHandler(this.bt_notify_Click);
            // 
            // tableLayoutPanel6
            // 
            this.tableLayoutPanel6.ColumnCount = 1;
            this.tableLayoutPanel6.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel6.Controls.Add(this.gb_state, 0, 1);
            this.tableLayoutPanel6.Controls.Add(this.gb_alert_list, 0, 0);
            this.tableLayoutPanel6.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel6.Location = new System.Drawing.Point(3, 2);
            this.tableLayoutPanel6.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tableLayoutPanel6.Name = "tableLayoutPanel6";
            this.tableLayoutPanel6.RowCount = 2;
            this.tableLayoutPanel6.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel6.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 60F));
            this.tableLayoutPanel6.Size = new System.Drawing.Size(400, 472);
            this.tableLayoutPanel6.TabIndex = 15;
            // 
            // gb_state
            // 
            this.gb_state.Controls.Add(this.rb_ractifiy);
            this.gb_state.Controls.Add(this.rb_raise);
            this.gb_state.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gb_state.Location = new System.Drawing.Point(3, 414);
            this.gb_state.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.gb_state.Name = "gb_state";
            this.gb_state.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.gb_state.Size = new System.Drawing.Size(394, 56);
            this.gb_state.TabIndex = 15;
            this.gb_state.TabStop = false;
            this.gb_state.Text = "Alert State";
            // 
            // rb_ractifiy
            // 
            this.rb_ractifiy.AutoSize = true;
            this.rb_ractifiy.Location = new System.Drawing.Point(75, 28);
            this.rb_ractifiy.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.rb_ractifiy.Name = "rb_ractifiy";
            this.rb_ractifiy.Size = new System.Drawing.Size(70, 17);
            this.rb_ractifiy.TabIndex = 1;
            this.rb_ractifiy.TabStop = true;
            this.rb_ractifiy.Text = "RACTIFY";
            this.rb_ractifiy.UseVisualStyleBackColor = true;
            // 
            // rb_raise
            // 
            this.rb_raise.AutoSize = true;
            this.rb_raise.Location = new System.Drawing.Point(8, 28);
            this.rb_raise.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.rb_raise.Name = "rb_raise";
            this.rb_raise.Size = new System.Drawing.Size(57, 17);
            this.rb_raise.TabIndex = 0;
            this.rb_raise.TabStop = true;
            this.rb_raise.Text = "RAISE";
            this.rb_raise.UseVisualStyleBackColor = true;
            // 
            // gb_alert_list
            // 
            this.gb_alert_list.Controls.Add(this.lb_alert);
            this.gb_alert_list.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gb_alert_list.Location = new System.Drawing.Point(3, 2);
            this.gb_alert_list.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.gb_alert_list.Name = "gb_alert_list";
            this.gb_alert_list.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.gb_alert_list.Size = new System.Drawing.Size(394, 408);
            this.gb_alert_list.TabIndex = 1;
            this.gb_alert_list.TabStop = false;
            this.gb_alert_list.Text = "Alert List";
            // 
            // lb_alert
            // 
            this.lb_alert.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lb_alert.Font = new System.Drawing.Font("휴먼모음T", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.lb_alert.FormattingEnabled = true;
            this.lb_alert.ItemHeight = 18;
            this.lb_alert.Items.AddRange(new object[] {
            "3008(1) : TX POWER:INHIBIT",
            "3008(2) : TX POWER:UNLOCK",
            "3115(1) : ANTENNA:FAILURE",
            "3115(2) : RX:FAILURE",
            "3115(3) : TU CONN LOST"});
            this.lb_alert.Location = new System.Drawing.Point(3, 16);
            this.lb_alert.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.lb_alert.Name = "lb_alert";
            this.lb_alert.Size = new System.Drawing.Size(388, 390);
            this.lb_alert.TabIndex = 1;
            this.lb_alert.SelectedIndexChanged += new System.EventHandler(this.SelectedIndexChanged_lbAlert);
            // 
            // tabPage5
            // 
            this.tabPage5.Controls.Add(this.groupBox10);
            this.tabPage5.Controls.Add(this.bt_rx_ext_full_save);
            this.tabPage5.Controls.Add(this.bt_rx_ext_full_clear);
            this.tabPage5.Controls.Add(this.rich_tb_full_sentence);
            this.tabPage5.Controls.Add(this.label37);
            this.tabPage5.Location = new System.Drawing.Point(4, 22);
            this.tabPage5.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tabPage5.Name = "tabPage5";
            this.tabPage5.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tabPage5.Size = new System.Drawing.Size(1356, 719);
            this.tabPage5.TabIndex = 4;
            this.tabPage5.Text = "EXTERNAL COM";
            this.tabPage5.UseVisualStyleBackColor = true;
            // 
            // groupBox10
            // 
            this.groupBox10.Controls.Add(this.tb_lz_mins);
            this.groupBox10.Controls.Add(this.tb_lz_hours);
            this.groupBox10.Controls.Add(this.label45);
            this.groupBox10.Controls.Add(this.label44);
            this.groupBox10.Controls.Add(this.tb_latitude);
            this.groupBox10.Controls.Add(this.tb_logitude);
            this.groupBox10.Controls.Add(this.tb_time);
            this.groupBox10.Controls.Add(this.tb_date);
            this.groupBox10.Controls.Add(this.label38);
            this.groupBox10.Controls.Add(this.label41);
            this.groupBox10.Controls.Add(this.label39);
            this.groupBox10.Controls.Add(this.label40);
            this.groupBox10.Location = new System.Drawing.Point(734, 50);
            this.groupBox10.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox10.Name = "groupBox10";
            this.groupBox10.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox10.Size = new System.Drawing.Size(347, 158);
            this.groupBox10.TabIndex = 7;
            this.groupBox10.TabStop = false;
            this.groupBox10.Text = "INFOMATION";
            // 
            // tb_lz_mins
            // 
            this.tb_lz_mins.Enabled = false;
            this.tb_lz_mins.Location = new System.Drawing.Point(269, 60);
            this.tb_lz_mins.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tb_lz_mins.Name = "tb_lz_mins";
            this.tb_lz_mins.Size = new System.Drawing.Size(67, 21);
            this.tb_lz_mins.TabIndex = 14;
            // 
            // tb_lz_hours
            // 
            this.tb_lz_hours.Enabled = false;
            this.tb_lz_hours.Location = new System.Drawing.Point(269, 30);
            this.tb_lz_hours.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tb_lz_hours.Name = "tb_lz_hours";
            this.tb_lz_hours.Size = new System.Drawing.Size(67, 21);
            this.tb_lz_hours.TabIndex = 13;
            // 
            // label45
            // 
            this.label45.AutoSize = true;
            this.label45.Location = new System.Drawing.Point(219, 62);
            this.label45.Name = "label45";
            this.label45.Size = new System.Drawing.Size(47, 13);
            this.label45.TabIndex = 12;
            this.label45.Text = "LZ M :";
            // 
            // label44
            // 
            this.label44.AutoSize = true;
            this.label44.Location = new System.Drawing.Point(219, 34);
            this.label44.Name = "label44";
            this.label44.Size = new System.Drawing.Size(45, 13);
            this.label44.TabIndex = 11;
            this.label44.Text = "LZ H :";
            // 
            // tb_latitude
            // 
            this.tb_latitude.Enabled = false;
            this.tb_latitude.Location = new System.Drawing.Point(83, 86);
            this.tb_latitude.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tb_latitude.Name = "tb_latitude";
            this.tb_latitude.Size = new System.Drawing.Size(130, 21);
            this.tb_latitude.TabIndex = 10;
            // 
            // tb_logitude
            // 
            this.tb_logitude.Enabled = false;
            this.tb_logitude.Location = new System.Drawing.Point(83, 114);
            this.tb_logitude.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tb_logitude.Name = "tb_logitude";
            this.tb_logitude.Size = new System.Drawing.Size(130, 21);
            this.tb_logitude.TabIndex = 9;
            // 
            // tb_time
            // 
            this.tb_time.Enabled = false;
            this.tb_time.Location = new System.Drawing.Point(83, 58);
            this.tb_time.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tb_time.Name = "tb_time";
            this.tb_time.Size = new System.Drawing.Size(130, 21);
            this.tb_time.TabIndex = 8;
            // 
            // tb_date
            // 
            this.tb_date.Enabled = false;
            this.tb_date.Location = new System.Drawing.Point(83, 30);
            this.tb_date.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tb_date.Name = "tb_date";
            this.tb_date.Size = new System.Drawing.Size(130, 21);
            this.tb_date.TabIndex = 7;
            // 
            // label38
            // 
            this.label38.AutoSize = true;
            this.label38.Location = new System.Drawing.Point(8, 90);
            this.label38.Name = "label38";
            this.label38.Size = new System.Drawing.Size(69, 13);
            this.label38.TabIndex = 4;
            this.label38.Text = "LATITUDE :";
            // 
            // label41
            // 
            this.label41.AutoSize = true;
            this.label41.Location = new System.Drawing.Point(34, 62);
            this.label41.Name = "label41";
            this.label41.Size = new System.Drawing.Size(43, 13);
            this.label41.TabIndex = 6;
            this.label41.Text = "TIME :";
            // 
            // label39
            // 
            this.label39.AutoSize = true;
            this.label39.Location = new System.Drawing.Point(5, 116);
            this.label39.Name = "label39";
            this.label39.Size = new System.Drawing.Size(72, 13);
            this.label39.TabIndex = 4;
            this.label39.Text = "LOGITUDE :";
            // 
            // label40
            // 
            this.label40.AutoSize = true;
            this.label40.Location = new System.Drawing.Point(33, 34);
            this.label40.Name = "label40";
            this.label40.Size = new System.Drawing.Size(44, 13);
            this.label40.TabIndex = 5;
            this.label40.Text = "DATE :";
            // 
            // bt_rx_ext_full_save
            // 
            this.bt_rx_ext_full_save.Location = new System.Drawing.Point(141, 22);
            this.bt_rx_ext_full_save.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.bt_rx_ext_full_save.Name = "bt_rx_ext_full_save";
            this.bt_rx_ext_full_save.Size = new System.Drawing.Size(79, 22);
            this.bt_rx_ext_full_save.TabIndex = 3;
            this.bt_rx_ext_full_save.Text = "SAVE";
            this.bt_rx_ext_full_save.UseVisualStyleBackColor = true;
            this.bt_rx_ext_full_save.Click += new System.EventHandler(this.bt_rx_ext_full_save_Click);
            // 
            // bt_rx_ext_full_clear
            // 
            this.bt_rx_ext_full_clear.Location = new System.Drawing.Point(226, 22);
            this.bt_rx_ext_full_clear.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.bt_rx_ext_full_clear.Name = "bt_rx_ext_full_clear";
            this.bt_rx_ext_full_clear.Size = new System.Drawing.Size(79, 22);
            this.bt_rx_ext_full_clear.TabIndex = 2;
            this.bt_rx_ext_full_clear.Text = "CLEAR";
            this.bt_rx_ext_full_clear.UseVisualStyleBackColor = true;
            this.bt_rx_ext_full_clear.Click += new System.EventHandler(this.bt_rx_ext_full_clear_Click);
            // 
            // rich_tb_full_sentence
            // 
            this.rich_tb_full_sentence.Location = new System.Drawing.Point(22, 50);
            this.rich_tb_full_sentence.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.rich_tb_full_sentence.Name = "rich_tb_full_sentence";
            this.rich_tb_full_sentence.ScrollBars = System.Windows.Forms.RichTextBoxScrollBars.Vertical;
            this.rich_tb_full_sentence.Size = new System.Drawing.Size(706, 158);
            this.rich_tb_full_sentence.TabIndex = 1;
            this.rich_tb_full_sentence.Text = "";
            // 
            // label37
            // 
            this.label37.AutoSize = true;
            this.label37.Location = new System.Drawing.Point(19, 26);
            this.label37.Name = "label37";
            this.label37.Size = new System.Drawing.Size(104, 13);
            this.label37.TabIndex = 0;
            this.label37.Text = "RX Full Sentence";
            // 
            // tabPage6
            // 
            this.tabPage6.Controls.Add(this.bt_record);
            this.tabPage6.Controls.Add(this.lb_playback_time);
            this.tabPage6.Controls.Add(this.tb_audiotrack);
            this.tabPage6.Controls.Add(this.lb_rec_time);
            this.tabPage6.Controls.Add(this.bt_delete_track);
            this.tabPage6.Controls.Add(this.bt_delete_all);
            this.tabPage6.Controls.Add(this.bt_playback);
            this.tabPage6.Controls.Add(this.lb_audiotrack);
            this.tabPage6.Controls.Add(this.bt_get_audiotrack);
            this.tabPage6.Location = new System.Drawing.Point(4, 22);
            this.tabPage6.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tabPage6.Name = "tabPage6";
            this.tabPage6.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tabPage6.Size = new System.Drawing.Size(1356, 719);
            this.tabPage6.TabIndex = 5;
            this.tabPage6.Text = "AUDIO TRACK";
            this.tabPage6.UseVisualStyleBackColor = true;
            // 
            // bt_record
            // 
            this.bt_record.Location = new System.Drawing.Point(260, 78);
            this.bt_record.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.bt_record.Name = "bt_record";
            this.bt_record.Size = new System.Drawing.Size(109, 30);
            this.bt_record.TabIndex = 9;
            this.bt_record.Text = "REC OFF";
            this.bt_record.UseVisualStyleBackColor = true;
            this.bt_record.Click += new System.EventHandler(this.bt_record_Click);
            // 
            // lb_playback_time
            // 
            this.lb_playback_time.AutoSize = true;
            this.lb_playback_time.Location = new System.Drawing.Point(210, 302);
            this.lb_playback_time.Name = "lb_playback_time";
            this.lb_playback_time.Size = new System.Drawing.Size(134, 13);
            this.lb_playback_time.TabIndex = 8;
            this.lb_playback_time.Text = "Playback Time: 0.000s";
            // 
            // tb_audiotrack
            // 
            this.tb_audiotrack.BackColor = System.Drawing.SystemColors.Window;
            this.tb_audiotrack.LargeChange = 10;
            this.tb_audiotrack.Location = new System.Drawing.Point(26, 318);
            this.tb_audiotrack.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tb_audiotrack.Maximum = 60000;
            this.tb_audiotrack.Name = "tb_audiotrack";
            this.tb_audiotrack.Size = new System.Drawing.Size(325, 45);
            this.tb_audiotrack.SmallChange = 10;
            this.tb_audiotrack.TabIndex = 7;
            this.tb_audiotrack.Scroll += new System.EventHandler(this.audiotrack_trackbar_scroll);
            // 
            // lb_rec_time
            // 
            this.lb_rec_time.AutoSize = true;
            this.lb_rec_time.Location = new System.Drawing.Point(33, 302);
            this.lb_rec_time.Name = "lb_rec_time";
            this.lb_rec_time.Size = new System.Drawing.Size(78, 13);
            this.lb_rec_time.TabIndex = 6;
            this.lb_rec_time.Text = "Record Time";
            // 
            // bt_delete_track
            // 
            this.bt_delete_track.Enabled = false;
            this.bt_delete_track.Location = new System.Drawing.Point(260, 178);
            this.bt_delete_track.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.bt_delete_track.Name = "bt_delete_track";
            this.bt_delete_track.Size = new System.Drawing.Size(109, 30);
            this.bt_delete_track.TabIndex = 4;
            this.bt_delete_track.Text = "DELETE TRACK";
            this.bt_delete_track.UseVisualStyleBackColor = true;
            this.bt_delete_track.Click += new System.EventHandler(this.bt_delete_track_Click);
            // 
            // bt_delete_all
            // 
            this.bt_delete_all.Location = new System.Drawing.Point(260, 212);
            this.bt_delete_all.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.bt_delete_all.Name = "bt_delete_all";
            this.bt_delete_all.Size = new System.Drawing.Size(109, 30);
            this.bt_delete_all.TabIndex = 3;
            this.bt_delete_all.Text = "DELETE ALL";
            this.bt_delete_all.UseVisualStyleBackColor = true;
            this.bt_delete_all.Click += new System.EventHandler(this.bt_delete_all_Click);
            // 
            // bt_playback
            // 
            this.bt_playback.Enabled = false;
            this.bt_playback.Location = new System.Drawing.Point(260, 126);
            this.bt_playback.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.bt_playback.Name = "bt_playback";
            this.bt_playback.Size = new System.Drawing.Size(109, 30);
            this.bt_playback.TabIndex = 2;
            this.bt_playback.Text = "PLAY";
            this.bt_playback.UseVisualStyleBackColor = true;
            this.bt_playback.Click += new System.EventHandler(this.bt_playback_Click);
            // 
            // lb_audiotrack
            // 
            this.lb_audiotrack.FormattingEnabled = true;
            this.lb_audiotrack.Location = new System.Drawing.Point(26, 78);
            this.lb_audiotrack.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.lb_audiotrack.Name = "lb_audiotrack";
            this.lb_audiotrack.Size = new System.Drawing.Size(228, 160);
            this.lb_audiotrack.TabIndex = 1;
            this.lb_audiotrack.SelectedIndexChanged += new System.EventHandler(this.SelectedIndexChanged_Track);
            // 
            // bt_get_audiotrack
            // 
            this.bt_get_audiotrack.Location = new System.Drawing.Point(26, 28);
            this.bt_get_audiotrack.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.bt_get_audiotrack.Name = "bt_get_audiotrack";
            this.bt_get_audiotrack.Size = new System.Drawing.Size(228, 28);
            this.bt_get_audiotrack.TabIndex = 0;
            this.bt_get_audiotrack.Text = "AUDIO TRACK LIST";
            this.bt_get_audiotrack.UseVisualStyleBackColor = true;
            this.bt_get_audiotrack.Click += new System.EventHandler(this.bt_get_audiotrack_Click);
            // 
            // tick_timer
            // 
            this.tick_timer.Interval = 1;
            this.tick_timer.Tick += new System.EventHandler(this.TickTimerHandler);
            // 
            // timer_connect
            // 
            this.timer_connect.Interval = 1000;
            this.timer_connect.Tick += new System.EventHandler(this.TimerConnect);
            // 
            // AudioTrack_timer
            // 
            this.AudioTrack_timer.Interval = 10;
            this.AudioTrack_timer.Tick += new System.EventHandler(this.AudioTrack_timer_Tick);
            // 
            // dsc_send_timer
            // 
            this.dsc_send_timer.Tick += new System.EventHandler(this.DscSendTimerHandler);
            // 
            // IV2500A_Form
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackgroundImageLayout = System.Windows.Forms.ImageLayout.None;
            this.ClientSize = new System.Drawing.Size(1137, 658);
            this.Controls.Add(this.tabControl1);
            this.Controls.Add(this.toolStrip1);
            this.HideOnClose = true;
            this.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.Name = "IV2500A_Form";
            this.TabText = "IV2500A";
            this.Text = "IV2500A";
            this.Load += new System.EventHandler(this.Load_VhfSimulator);
            this.toolStrip1.ResumeLayout(false);
            this.toolStrip1.PerformLayout();
            this.tabControl1.ResumeLayout(false);
            this.tabPage1.ResumeLayout(false);
            this.groupBox6.ResumeLayout(false);
            this.groupBox9.ResumeLayout(false);
            this.groupBox9.PerformLayout();
            this.groupBox8.ResumeLayout(false);
            this.groupBox8.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tb_wkr_rx_pwr)).EndInit();
            this.groupBox7.ResumeLayout(false);
            this.groupBox7.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tb_main_tx_pwr)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tb_main_rx_pwr)).EndInit();
            this.gb_audio_codec.ResumeLayout(false);
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tb_out_rx_bb)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tb_front_ext_spk)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tb_rear_wing_h_spk)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tb_front_h_spk)).EndInit();
            this.groupBox4.ResumeLayout(false);
            this.groupBox4.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tb_rx_bb)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tb_wing_mic)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tb_rear_mic)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tb_front_mic)).EndInit();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.att_trackBar)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gain_trackBar)).EndInit();
            this.gb_OpMode.ResumeLayout(false);
            this.gb_OpMode.PerformLayout();
            this.tabPage2.ResumeLayout(false);
            this.groupBox5.ResumeLayout(false);
            this.groupBox5.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgv_selftest)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDown1)).EndInit();
            this.tabPage3.ResumeLayout(false);
            this.tabPage3.PerformLayout();
            this.tableLayoutPanel1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgvDscSend)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dgvDscRsv)).EndInit();
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            this.panel2.ResumeLayout(false);
            this.panel2.PerformLayout();
            this.tableLayoutPanel2.ResumeLayout(false);
            this.tableLayoutPanel3.ResumeLayout(false);
            this.tableLayoutPanel3.PerformLayout();
            this.tabPage4.ResumeLayout(false);
            this.tableLayoutPanel4.ResumeLayout(false);
            this.tableLayoutPanel5.ResumeLayout(false);
            this.tableLayoutPanel5.PerformLayout();
            this.tableLayoutPanel6.ResumeLayout(false);
            this.gb_state.ResumeLayout(false);
            this.gb_state.PerformLayout();
            this.gb_alert_list.ResumeLayout(false);
            this.tabPage5.ResumeLayout(false);
            this.tabPage5.PerformLayout();
            this.groupBox10.ResumeLayout(false);
            this.groupBox10.PerformLayout();
            this.tabPage6.ResumeLayout(false);
            this.tabPage6.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tb_audiotrack)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.ToolStrip toolStrip1;
        private System.Windows.Forms.ToolStripLabel toolStripLabel1;
        private System.Windows.Forms.ToolStripComboBox cbCom;
        private System.Windows.Forms.ToolStripButton btnConnect;
        private System.Windows.Forms.TabControl tabControl1;
        private System.Windows.Forms.TabPage tabPage1;
        private System.Windows.Forms.TabPage tabPage2;
        private System.Windows.Forms.GroupBox gb_OpMode;
        private System.Windows.Forms.RadioButton rb_TxMode;
        private System.Windows.Forms.RadioButton rb_RxMode;
        private System.Windows.Forms.RadioButton rb_MarkMode;
        private System.Windows.Forms.RadioButton rb_DotMode;
        private System.Windows.Forms.RadioButton rb_SpaceMode;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Button btn_stop;
        private System.Windows.Forms.Button btn_start;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label lb_rate;
        private System.Windows.Forms.Label lb_retry;
        private System.Windows.Forms.Label lb_pass;
        private System.Windows.Forms.Label lb_count;
        private System.Windows.Forms.Timer tick_timer;
        private System.Windows.Forms.NumericUpDown numericUpDown1;
        private System.Windows.Forms.Label lb_elapse;
        private System.Windows.Forms.Label lb_time;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label lb_timeout;
        private System.Windows.Forms.Button bt_clear;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Label lb_data_bad;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.TextBox tb_att;
        private System.Windows.Forms.TextBox tb_gain;
        private System.Windows.Forms.TrackBar att_trackBar;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.TrackBar gain_trackBar;
        private System.Windows.Forms.Label lb_att_value;
        private System.Windows.Forms.Label lb_gain_value;
        private System.Windows.Forms.TextBox tb_limiter;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.Timer timer_connect;
        private System.Windows.Forms.ToolStripLabel lb_bb_ver;
        private System.Windows.Forms.TabPage tabPage3;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel1;
        private System.Windows.Forms.DataGridView dgvDscRsv;
        private System.Windows.Forms.Label lb_rx_dsc;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.Label lb_tx_dsc;
        private System.Windows.Forms.Panel panel2;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel2;
        private System.Windows.Forms.Button btn_rx_clear;
        private System.Windows.Forms.Button btn_import;
        private System.Windows.Forms.Button btn_export;
        private System.Windows.Forms.DataGridView dgvDscSend;
        private System.Windows.Forms.DataGridViewTextBoxColumn RsvTime;
        private System.Windows.Forms.DataGridViewTextBoxColumn DscFormat;
        private System.Windows.Forms.DataGridViewTextBoxColumn DscAddr;
        private System.Windows.Forms.DataGridViewTextBoxColumn DscCategory;
        private System.Windows.Forms.DataGridViewTextBoxColumn DscSelfID;
        private System.Windows.Forms.DataGridViewTextBoxColumn DscTeleCom1;
        private System.Windows.Forms.DataGridViewTextBoxColumn DscTeleCom2;
        private System.Windows.Forms.DataGridViewTextBoxColumn DscFreq;
        private System.Windows.Forms.DataGridViewTextBoxColumn DscDistID;
        private System.Windows.Forms.DataGridViewTextBoxColumn DscNature;
        private System.Windows.Forms.DataGridViewTextBoxColumn DscPOS;
        private System.Windows.Forms.DataGridViewTextBoxColumn DscUTC;
        private System.Windows.Forms.DataGridViewTextBoxColumn DscExpnPos;
        private System.Windows.Forms.DataGridViewTextBoxColumn DscEOS;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel3;
        private System.Windows.Forms.Button btnDSCAdd;
        private System.Windows.Forms.Button btnDSCDelete;
        private System.Windows.Forms.Button btnDscEdit;
        private System.Windows.Forms.Button btn_tx_export;
        private System.Windows.Forms.Button btn_tx_import;
        private System.Windows.Forms.Button btnDSCClear;
        private System.Windows.Forms.DataGridViewTextBoxColumn DscTxType;
        private System.Windows.Forms.DataGridViewTextBoxColumn DscTxFormat;
        private System.Windows.Forms.DataGridViewTextBoxColumn DscTxAddr;
        private System.Windows.Forms.DataGridViewTextBoxColumn DscTxCategory;
        private System.Windows.Forms.DataGridViewTextBoxColumn DscTxSelfID;
        private System.Windows.Forms.DataGridViewTextBoxColumn DscTxTeleCom;
        private System.Windows.Forms.DataGridViewTextBoxColumn DscTxTCom1;
        private System.Windows.Forms.DataGridViewTextBoxColumn DscTxTCom2;
        private System.Windows.Forms.DataGridViewTextBoxColumn DscTxSubCom;
        private System.Windows.Forms.DataGridViewTextBoxColumn DscTxFreq;
        private System.Windows.Forms.DataGridViewTextBoxColumn DscTxDistID;
        private System.Windows.Forms.DataGridViewTextBoxColumn DscTxNature;
        private System.Windows.Forms.DataGridViewTextBoxColumn DscTxLatitude;
        private System.Windows.Forms.DataGridViewTextBoxColumn DscTxLongitude;
        private System.Windows.Forms.DataGridViewTextBoxColumn DscTxUtc;
        private System.Windows.Forms.DataGridViewTextBoxColumn DscTxEos;
        private System.Windows.Forms.DataGridViewTextBoxColumn DscTxExpn;
        private System.Windows.Forms.DataGridViewButtonColumn SendDsc;
        private System.Windows.Forms.TabPage tabPage4;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel4;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel5;
        private System.Windows.Forms.Label lb_ID;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.Label label16;
        private System.Windows.Forms.Label lb_priority;
        private System.Windows.Forms.Label lb_instance;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.TextBox tb_title;
        private System.Windows.Forms.TextBox tb_category;
        private System.Windows.Forms.TextBox tb_priority;
        private System.Windows.Forms.TextBox tb_instance;
        private System.Windows.Forms.TextBox tb_id;
        private System.Windows.Forms.TextBox tb_desc;
        private System.Windows.Forms.Button bt_notify;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel6;
        private System.Windows.Forms.GroupBox gb_state;
        private System.Windows.Forms.RadioButton rb_ractifiy;
        private System.Windows.Forms.RadioButton rb_raise;
        private System.Windows.Forms.GroupBox gb_alert_list;
        private System.Windows.Forms.ListBox lb_alert;
        private System.Windows.Forms.GroupBox gb_audio_codec;
        private System.Windows.Forms.GroupBox groupBox4;
        private System.Windows.Forms.TrackBar tb_rx_bb;
        private System.Windows.Forms.Label label20;
        private System.Windows.Forms.TrackBar tb_wing_mic;
        private System.Windows.Forms.Label label19;
        private System.Windows.Forms.TrackBar tb_rear_mic;
        private System.Windows.Forms.Label label18;
        private System.Windows.Forms.TrackBar tb_front_mic;
        private System.Windows.Forms.Label label17;
        private System.Windows.Forms.Label lb_rx_bb;
        private System.Windows.Forms.Label lb_wing_mic;
        private System.Windows.Forms.Label lb_rear_mic;
        private System.Windows.Forms.Label lb_front_mic;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.Label lb_out_rx_bb;
        private System.Windows.Forms.Label lb_front_ext_spk;
        private System.Windows.Forms.Label lb_rear_wing_h_spk;
        private System.Windows.Forms.Label lb_front_h_spk;
        private System.Windows.Forms.TrackBar tb_out_rx_bb;
        private System.Windows.Forms.Label label25;
        private System.Windows.Forms.TrackBar tb_front_ext_spk;
        private System.Windows.Forms.Label label26;
        private System.Windows.Forms.TrackBar tb_rear_wing_h_spk;
        private System.Windows.Forms.Label label27;
        private System.Windows.Forms.TrackBar tb_front_h_spk;
        private System.Windows.Forms.Label label28;
        private System.Windows.Forms.GroupBox groupBox5;
        private System.Windows.Forms.Button btn_run;
        private System.Windows.Forms.GroupBox groupBox6;
        private System.Windows.Forms.TextBox tb_wkr_freq;
        private System.Windows.Forms.Label label21;
        private System.Windows.Forms.TextBox tb_main_rx_freq;
        private System.Windows.Forms.Label label30;
        private System.Windows.Forms.Label label23;
        private System.Windows.Forms.Label label22;
        private System.Windows.Forms.GroupBox groupBox8;
        private System.Windows.Forms.GroupBox groupBox7;
        private System.Windows.Forms.Label label29;
        private System.Windows.Forms.TrackBar tb_main_tx_pwr;
        private System.Windows.Forms.Label label24;
        private System.Windows.Forms.TrackBar tb_main_rx_pwr;
        private System.Windows.Forms.Label label33;
        private System.Windows.Forms.TrackBar tb_wkr_rx_pwr;
        private System.Windows.Forms.Label lb_wkr_rx_pwr;
        private System.Windows.Forms.Label lb_main_tx_pwr;
        private System.Windows.Forms.Label lb_main_rx_pwr;
        private System.Windows.Forms.GroupBox groupBox9;
        private System.Windows.Forms.TextBox tb_pwr_cnt;
        private System.Windows.Forms.Label label34;
        private System.Windows.Forms.Label label36;
        private System.Windows.Forms.Label label35;
        private System.Windows.Forms.TextBox tb_dds_adj;
        private System.Windows.Forms.Label label32;
        private System.Windows.Forms.TabPage tabPage5;
        private System.Windows.Forms.RichTextBox rich_tb_full_sentence;
        private System.Windows.Forms.Label label37;
        private System.Windows.Forms.Button bt_rx_ext_full_save;
        private System.Windows.Forms.Button bt_rx_ext_full_clear;
        private System.Windows.Forms.GroupBox groupBox10;
        private System.Windows.Forms.Label label38;
        private System.Windows.Forms.Label label41;
        private System.Windows.Forms.Label label39;
        private System.Windows.Forms.Label label40;
        private System.Windows.Forms.TextBox tb_latitude;
        private System.Windows.Forms.TextBox tb_logitude;
        private System.Windows.Forms.TextBox tb_time;
        private System.Windows.Forms.TextBox tb_date;
        private System.Windows.Forms.Label label43;
        private System.Windows.Forms.Label label42;
        private System.Windows.Forms.TextBox tb_main_tx_freq;
        private System.Windows.Forms.TextBox tb_lz_mins;
        private System.Windows.Forms.TextBox tb_lz_hours;
        private System.Windows.Forms.Label label45;
        private System.Windows.Forms.Label label44;
        private System.Windows.Forms.Label label47;
        private System.Windows.Forms.TextBox tb_dcoffset;
        private System.Windows.Forms.Label label46;
        private System.Windows.Forms.TabPage tabPage6;
        private System.Windows.Forms.Button bt_get_audiotrack;
        private System.Windows.Forms.ListBox lb_audiotrack;
        private System.Windows.Forms.Button bt_playback;
        private System.Windows.Forms.Button bt_delete_all;
        private System.Windows.Forms.Button bt_delete_track;
        private System.Windows.Forms.Timer AudioTrack_timer;
        private System.Windows.Forms.Label lb_rec_time;
        private System.Windows.Forms.TrackBar tb_audiotrack;
        private System.Windows.Forms.Label lb_playback_time;
        private System.Windows.Forms.Button bt_record;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripButton btnSetController;
        private System.Windows.Forms.Button btn_clean_selftest;
        private System.Windows.Forms.DataGridView dgv_selftest;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column1;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column2;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column3;
        private System.Windows.Forms.DataGridViewTextBoxColumn TimeCol;
        private System.Windows.Forms.DataGridViewTextBoxColumn ItemCol;
        private System.Windows.Forms.DataGridViewTextBoxColumn ResultCol;
        private System.Windows.Forms.Label lb_selftest_tot_fail;
        private System.Windows.Forms.Label lb_selftest_tot_pass;
        private System.Windows.Forms.Button bt_export;
        private System.Windows.Forms.Label label31;
        private System.Windows.Forms.TextBox tb_dsc_send_auto_intval;
        private System.Windows.Forms.Button btn_dsc_auto_send;
        private System.Windows.Forms.Label label48;
        private System.Windows.Forms.TextBox tb_num_of_runs;
        private System.Windows.Forms.CheckBox cb_keep_run;
        private System.Windows.Forms.Timer dsc_send_timer;
    }
}
