﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AssistPro.ais
{
    internal class AisOwnShip
    {
        private uint m_MMSI = 0;
        private uint m_IMO = 0;
        private string m_ShipName = "@@@@@@@@@@@@@@@@@@@@";
        private string m_CallSign = "@@@@@@@";

        private int m_IntAntPosA = 0;
        private int m_IntAntPosB = 0;
        private int m_IntAntPosC = 0;
        private int m_IntAntPosD = 0;

        private int m_ExtAntPosA = 0;
        private int m_ExtAntPosB = 0;
        private int m_ExtAntPosC = 0;
        private int m_ExtAntPosD = 0;

        private int m_ShipCargoType = 0;
        private float m_Draught = 0;
        private int m_PersonOnBoard = 0;
        private string m_Destination = "@@@@@@@@@@@@@@@@@@@@";
        private int m_EstHour = 24;
        private int m_EstMinute = 60;
        private int m_EstDay = 0;
        private int m_EstMonth = 0;
        private int m_NavStatus = 0;
        private int m_RegionalAppFlags = 0;

        public UInt32 GetMMSI()
        {
            return m_MMSI;
        }
        public UInt32 GetIMO()
        {
            return m_IMO;
        }
        public string GetShipName()
        {
            return m_ShipName;
        }
        public string GetCallSign()
        {
            return m_CallSign;
        }
        public int GetIntAntPosA()
        {
            return m_IntAntPosA;
        }
        public int GetIntAntPosB()
        {
            return m_IntAntPosB;
        }
        public int GetIntAntPosC()
        {
            return m_IntAntPosC;
        }
        public int GetIntAntPosD()
        {
            return m_IntAntPosD;
        }
        public int GetExtAntPosA()
        {
            return m_ExtAntPosA;
        }
        public int GetExtAntPosB()
        {
            return m_ExtAntPosB;
        }
        public int GetExtAntPosC()
        {
            return m_ExtAntPosC;
        }
        public int GetExtAntPosD()
        {
            return m_ExtAntPosD;
        }
        public int GetShipCargoType()
        {
            return m_ShipCargoType;
        }
        public float GetDraught()
        {
            return m_Draught;
        }
        public int GetPersonOnBoard()
        {
            return m_PersonOnBoard;
        }
        public string GetDestination()
        {
            return m_Destination;
        }
        public int GetEstHour()
        {
            return m_EstHour;
        }
        public int GetEstMinute()
        {
            return m_EstMinute;
        }
        public int GetEstDay()
        {
            return m_EstDay;
        }
        public int GetEstMonth()
        {
            return m_EstMonth;
        }
        public int GetNavStatus()
        {
            return m_NavStatus;
        }
        public int GetRegionalAppFlags()
        {
            return m_RegionalAppFlags;
        }

        public void SetMMSI(uint nMMSI)
        {
            m_MMSI = nMMSI;
        }
        public void SetIMO(uint nIMO)
        {
            m_IMO = nIMO;
        }
        public void SetShipName(string strName)
        {
            m_ShipName = strName;
        }
        public void SetCallSign(string strCallSign)
        {
            m_CallSign = strCallSign;
        }

        public void SetIntAntPos(int nA, int nB, int nC, int nD)
        {
            m_IntAntPosA = nA;
            m_IntAntPosB = nB;
            m_IntAntPosC = nC;
            m_IntAntPosD = nD;
        }

        public void SetExtAntPos(int nA, int nB, int nC, int nD)
        {
            m_ExtAntPosA = nA;
            m_ExtAntPosB = nB;
            m_ExtAntPosC = nC;
            m_ExtAntPosD = nD;
        }
        public void SetShipCargoType(int nCargoType)
        {
            m_ShipCargoType = nCargoType;
        }
        public void SetDraught(float fDraught)
        {
            m_Draught = fDraught;
        }
        public void SetPersonOnBoard(int nPerson)
        {
            m_PersonOnBoard = nPerson;
        }
        public void SetDestination(string strDest)
        {
            m_Destination = strDest;
        }
        public void SetEstHour(int nHour)
        {
            m_EstHour = nHour;
        }
        public void SetEstMinute(int nMinute)
        {
            m_EstMinute = nMinute;
        }
        public void SetEstDay(int nDay)
        {
            m_EstDay = nDay;
        }
        public void SetEstMonth(int nMonth)
        {
            m_EstMonth = nMonth;
        }
        public void SetNavStatus(int nStatus)
        {
            m_NavStatus = nStatus;
        }
        public void SetRegionalAppFlags(int nFlags)
        {
            m_RegionalAppFlags = nFlags;
        }
    }
}
