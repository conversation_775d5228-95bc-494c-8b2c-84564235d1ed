﻿using AssistPro.Lib;
using AssistPro.navtex.communication;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using WeifenLuo.WinFormsUI;
using static AssistPro.navtex.communication.CommPort;

namespace AssistPro.navtex.sentence
{
    public partial class Sentence_NRM_Form : DockContent
    {
        public static Sentence_NRM_Form _sentence_nrm_form;
        private static Sentence_NRM_Form instance;

        private CommPort m_CommPort = CommPort.Instance;
        private Common _Common = Common.Instance;
        private UdpPort m_UdpPort = UdpPort.Instance;

        public static Sentence_NRM_Form Instance
        {
            get
            {
                return instance;
            }
        }

        public Sentence_NRM_Form()
        {
            InitializeComponent();

            rb_61162_1_2.Checked = true;
            tb_450_line_count.Text = string.Format("1");
        }
        private byte CalcurateChecksum(string nmeaStr)
        {
            string n = nmeaStr.StartsWith("$") ? nmeaStr.Substring(1) : nmeaStr;

            byte chk = 0;
            int index = 0;

            while ((index < n.Length) && (n[index] != '*') && (n[index] != '\n'))
            {
                if (index > 70)
                {
                    // Sentence too long
                    return 0;
                }
                chk ^= (byte)n[index++];
            }
            return chk;
        }

        private string ComposeTagblock()
        {
            string tag_body = string.Empty;

            if (rb_61162_1_2.Checked == false)
            {
                string header = "UdPbC" + "\0";

                if (cb_s_enable.Checked == true)
                {
                    string tag_body_src = string.IsNullOrEmpty(tb_450_src.Text) ? ("s:") : ("s:" + tb_450_src.Text);
                    tag_body += tag_body_src;
                }

                if (cb_d_enable.Checked == true)
                {
                    string tag_body_dst = string.IsNullOrEmpty(tb_450_dst.Text) ? ("d:") : ("d:" + tb_450_dst.Text);
                    if(tag_body == "")
                    {
                        tag_body += tag_body_dst;
                    }
                    else
                    {
                        tag_body += "," + tag_body_dst;
                    }
                }

                if (cb_n_enable.Checked == true)
                {
                    int n;
                    try
                    {
                        n = int.Parse(tb_450_line_count.Text);
                    }
                    catch (Exception e)
                    {
                        tb_450_line_count.Text = "1";
                        n = 1;
                    }

                    string tag_body_line_count = string.Format("n:{0}", n);
                    if (tag_body == "")
                    {
                        tag_body += tag_body_line_count;
                    }
                    else
                    {
                        tag_body += "," + tag_body_line_count;
                    }

                    tb_450_line_count.Text = string.Format("{0}", n + 1);
                }

                byte checksum = CalcurateChecksum(tag_body);
                string m_tag_block = header + "\\" + tag_body + "*" + checksum.ToString("X2") + "\\";

                return m_tag_block;
            }

            return "";
        }
        private int get_purpose_radio_number()
        {
            int ret = 0;

            if (m_given_radio.Checked == true)
            {
                ret = 1;
            }
            else if (m_storage_radio.Checked == true)
            {
                ret = 2;
            }
            else if (m_printer_radio.Checked == true)
            {
                ret = 3;
            }
            else if (m_ins_radio.Checked == true)
            {
                ret = 4;
            }
            else
            {
                ret = 0;
                MessageBox.Show("Purpose 선택되지 않음");
            }
            return ret;
        }

        private int get_frequency_radio_number()
        {
            int ret = 0;

            if (m_490KHz_radio.Checked == true)
            {
                ret = 1;
            }
            else if (m_518KHz_radio.Checked == true)
            {
                ret = 2;
            }
            else if (m_4209_5KHz_radio.Checked == true)
            {
                ret = 3;
            }
            else
            {
                ret = 0;
                MessageBox.Show("Frequency 선택되지 않음");
            }
            return ret;
        }

        private int get_checkbox_coverage()
        {
            int ret = 0;

            ret |= (Convert.ToInt32(m_coverage_A_radio.Checked));
            ret |= (Convert.ToInt32(m_coverage_B_radio.Checked)) << 1;
            ret |= (Convert.ToInt32(m_coverage_C_radio.Checked)) << 2;
            ret |= (Convert.ToInt32(m_coverage_D_radio.Checked)) << 3;
            ret |= (Convert.ToInt32(m_coverage_E_radio.Checked)) << 4;
            ret |= (Convert.ToInt32(m_coverage_F_radio.Checked)) << 5;
            ret |= (Convert.ToInt32(m_coverage_G_radio.Checked)) << 6;
            ret |= (Convert.ToInt32(m_coverage_H_radio.Checked)) << 7;
            ret |= (Convert.ToInt32(m_coverage_I_radio.Checked)) << 8;
            ret |= (Convert.ToInt32(m_coverage_J_radio.Checked)) << 9;
            ret |= (Convert.ToInt32(m_coverage_K_radio.Checked)) << 10;
            ret |= (Convert.ToInt32(m_coverage_L_radio.Checked)) << 11;
            ret |= (Convert.ToInt32(m_coverage_M_radio.Checked)) << 12;
            ret |= (Convert.ToInt32(m_coverage_N_radio.Checked)) << 13;
            ret |= (Convert.ToInt32(m_coverage_O_radio.Checked)) << 14;
            ret |= (Convert.ToInt32(m_coverage_P_radio.Checked)) << 15;
            ret |= (Convert.ToInt32(m_coverage_Q_radio.Checked)) << 16;
            ret |= (Convert.ToInt32(m_coverage_R_radio.Checked)) << 17;
            ret |= (Convert.ToInt32(m_coverage_S_radio.Checked)) << 18;
            ret |= (Convert.ToInt32(m_coverage_T_radio.Checked)) << 19;
            ret |= (Convert.ToInt32(m_coverage_U_radio.Checked)) << 20;
            ret |= (Convert.ToInt32(m_coverage_V_radio.Checked)) << 21;
            ret |= (Convert.ToInt32(m_coverage_W_radio.Checked)) << 22;
            ret |= (Convert.ToInt32(m_coverage_X_radio.Checked)) << 23;
            ret |= (Convert.ToInt32(m_coverage_Y_radio.Checked)) << 24;
            ret |= (Convert.ToInt32(m_coverage_Z_radio.Checked)) << 25;

            return ret;
        }

        private int get_checkbox_msg()
        {
            int ret = 0;

            ret |= (Convert.ToInt32(m_message_A_radio.Checked));
            ret |= (Convert.ToInt32(m_message_B_radio.Checked)) << 1;
            ret |= (Convert.ToInt32(m_message_C_radio.Checked)) << 2;
            ret |= (Convert.ToInt32(m_message_D_radio.Checked)) << 3;
            ret |= (Convert.ToInt32(m_message_E_radio.Checked)) << 4;
            ret |= (Convert.ToInt32(m_message_F_radio.Checked)) << 5;
            ret |= (Convert.ToInt32(m_message_G_radio.Checked)) << 6;
            ret |= (Convert.ToInt32(m_message_H_radio.Checked)) << 7;
            ret |= (Convert.ToInt32(m_message_I_radio.Checked)) << 8;
            ret |= (Convert.ToInt32(m_message_J_radio.Checked)) << 9;
            ret |= (Convert.ToInt32(m_message_K_radio.Checked)) << 10;
            ret |= (Convert.ToInt32(m_message_L_radio.Checked)) << 11;
            ret |= (Convert.ToInt32(m_message_M_radio.Checked)) << 12;
            ret |= (Convert.ToInt32(m_message_N_radio.Checked)) << 13;
            ret |= (Convert.ToInt32(m_message_O_radio.Checked)) << 14;
            ret |= (Convert.ToInt32(m_message_P_radio.Checked)) << 15;
            ret |= (Convert.ToInt32(m_message_Q_radio.Checked)) << 16;
            ret |= (Convert.ToInt32(m_message_R_radio.Checked)) << 17;
            ret |= (Convert.ToInt32(m_message_S_radio.Checked)) << 18;
            ret |= (Convert.ToInt32(m_message_T_radio.Checked)) << 19;
            ret |= (Convert.ToInt32(m_message_U_radio.Checked)) << 20;
            ret |= (Convert.ToInt32(m_message_V_radio.Checked)) << 21;
            ret |= (Convert.ToInt32(m_message_W_radio.Checked)) << 22;
            ret |= (Convert.ToInt32(m_message_X_radio.Checked)) << 23;
            ret |= (Convert.ToInt32(m_message_Y_radio.Checked)) << 24;
            ret |= (Convert.ToInt32(m_message_Z_radio.Checked)) << 25;

            return ret;
        }

        private void set_coverage_checkbox_all_ctrl(int n)
        {
            if(n == 1)
            {
                m_coverage_A_radio.Checked = true;
                m_coverage_B_radio.Checked = true;
                m_coverage_C_radio.Checked = true;
                m_coverage_D_radio.Checked = true;
                m_coverage_E_radio.Checked = true;
                m_coverage_F_radio.Checked = true;
                m_coverage_G_radio.Checked = true;
                m_coverage_H_radio.Checked = true;
                m_coverage_I_radio.Checked = true;
                m_coverage_J_radio.Checked = true;
                m_coverage_K_radio.Checked = true;
                m_coverage_L_radio.Checked = true;
                m_coverage_M_radio.Checked = true;
                m_coverage_N_radio.Checked = true;
                m_coverage_O_radio.Checked = true;
                m_coverage_P_radio.Checked = true;
                m_coverage_Q_radio.Checked = true;
                m_coverage_R_radio.Checked = true;
                m_coverage_S_radio.Checked = true;
                m_coverage_T_radio.Checked = true;
                m_coverage_U_radio.Checked = true;
                m_coverage_V_radio.Checked = true;
                m_coverage_W_radio.Checked = true;
                m_coverage_X_radio.Checked = true;
                m_coverage_Y_radio.Checked = true;
                m_coverage_Z_radio.Checked = true;
            }
            else
            {
                m_coverage_A_radio.Checked = false;
                m_coverage_B_radio.Checked = false;
                m_coverage_C_radio.Checked = false;
                m_coverage_D_radio.Checked = false;
                m_coverage_E_radio.Checked = false;
                m_coverage_F_radio.Checked = false;
                m_coverage_G_radio.Checked = false;
                m_coverage_H_radio.Checked = false;
                m_coverage_I_radio.Checked = false;
                m_coverage_J_radio.Checked = false;
                m_coverage_K_radio.Checked = false;
                m_coverage_L_radio.Checked = false;
                m_coverage_M_radio.Checked = false;
                m_coverage_N_radio.Checked = false;
                m_coverage_O_radio.Checked = false;
                m_coverage_P_radio.Checked = false;
                m_coverage_Q_radio.Checked = false;
                m_coverage_R_radio.Checked = false;
                m_coverage_S_radio.Checked = false;
                m_coverage_T_radio.Checked = false;
                m_coverage_U_radio.Checked = false;
                m_coverage_V_radio.Checked = false;
                m_coverage_W_radio.Checked = false;
                m_coverage_X_radio.Checked = false;
                m_coverage_Y_radio.Checked = false;
                m_coverage_Z_radio.Checked = false;
            }
        }

        private void set_msg_checkbox_all_ctrl(int n)
        {
            if (n == 1)
            {
                m_message_A_radio.Checked = true;
                m_message_B_radio.Checked = true;
                m_message_C_radio.Checked = true;
                m_message_D_radio.Checked = true;
                m_message_E_radio.Checked = true;
                m_message_F_radio.Checked = true;
                m_message_G_radio.Checked = true;
                m_message_H_radio.Checked = true;
                m_message_I_radio.Checked = true;
                m_message_J_radio.Checked = true;
                m_message_K_radio.Checked = true;
                m_message_L_radio.Checked = true;
                m_message_M_radio.Checked = true;
                m_message_N_radio.Checked = true;
                m_message_O_radio.Checked = true;
                m_message_P_radio.Checked = true;
                m_message_Q_radio.Checked = true;
                m_message_R_radio.Checked = true;
                m_message_S_radio.Checked = true;
                m_message_T_radio.Checked = true;
                m_message_U_radio.Checked = true;
                m_message_V_radio.Checked = true;
                m_message_W_radio.Checked = true;
                m_message_X_radio.Checked = true;
                m_message_Y_radio.Checked = true;
                m_message_Z_radio.Checked = true;
            }
            else
            {
                m_message_A_radio.Checked = false;
                m_message_B_radio.Checked = false;
                m_message_C_radio.Checked = false;
                m_message_D_radio.Checked = false;
                m_message_E_radio.Checked = false;
                m_message_F_radio.Checked = false;
                m_message_G_radio.Checked = false;
                m_message_H_radio.Checked = false;
                m_message_I_radio.Checked = false;
                m_message_J_radio.Checked = false;
                m_message_K_radio.Checked = false;
                m_message_L_radio.Checked = false;
                m_message_M_radio.Checked = false;
                m_message_N_radio.Checked = false;
                m_message_O_radio.Checked = false;
                m_message_P_radio.Checked = false;
                m_message_Q_radio.Checked = false;
                m_message_R_radio.Checked = false;
                m_message_S_radio.Checked = false;
                m_message_T_radio.Checked = false;
                m_message_U_radio.Checked = false;
                m_message_V_radio.Checked = false;
                m_message_W_radio.Checked = false;
                m_message_X_radio.Checked = false;
                m_message_Y_radio.Checked = false;
                m_message_Z_radio.Checked = false;
            }
        }

        private void m_coverage_all_enable_btn_Click(object sender, EventArgs e)
        {
            set_coverage_checkbox_all_ctrl(1);
        }

        private void m_coverage_all_disable_btn_Click(object sender, EventArgs e)
        {
            set_coverage_checkbox_all_ctrl(0);
        }

        private void m_message_all_enable_btn_Click(object sender, EventArgs e)
        {
            set_msg_checkbox_all_ctrl(1);
        }

        private void m_message_all_disable_btn_Click(object sender, EventArgs e)
        {
            set_msg_checkbox_all_ctrl(0);
        }

        private void m_send_btn_Click(object sender, EventArgs e)
        {
            string str = string.Empty;
            string msg = string.Empty;
            string send_msg = string.Empty;
            string talker_id = string.Empty;
            int purpose_radio_num = 0;
            int freq_radio_num = 0;
            int coverage_data = 0;
            int msg_data = 0;
            byte checksum = 0;

            talker_id = m_NRM_talk_id_textbox.Text;
            if(talker_id == string.Empty)
            {
                talker_id = "--";
            }

            purpose_radio_num = get_purpose_radio_number();
            freq_radio_num = get_frequency_radio_number();

            coverage_data = get_checkbox_coverage();
            msg_data = get_checkbox_msg();

            msg = string.Format("${0}NRM,{1:D1},{2:D1},{3:X8},{4:X8},C", talker_id, purpose_radio_num, freq_radio_num, coverage_data, msg_data);
            checksum = _Common.check_sum(msg.Substring(1));
            msg += string.Format("*{0:X2}\r\n", checksum);

            if(rb_61162_1_2.Checked)
            {
                send_msg = msg;

                m_CommPort.StrSend(send_msg, (int)Serial_t.SERIAL_TYPE_INS);
                m_NRM_monitor.AppendText(string.Format("Send : ") + send_msg);
            }
            else
            {
                send_msg = ComposeTagblock() + msg;
                m_UdpPort.StrSend(send_msg);

                send_msg = send_msg.Replace("\0", "\\0");
                m_NRM_monitor.AppendText(string.Format("Send : ") + send_msg);
            }
        }
    }
}
