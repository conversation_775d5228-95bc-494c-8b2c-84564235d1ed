﻿namespace AssistPro.navtex.sentence
{
    partial class Sentence_NRM_Form
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(Sentence_NRM_Form));
            this.m_send_btn = new System.Windows.Forms.Button();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.m_message_all_disable_btn = new System.Windows.Forms.Button();
            this.m_message_all_enable_btn = new System.Windows.Forms.Button();
            this.m_message_Z_radio = new System.Windows.Forms.CheckBox();
            this.m_message_Y_radio = new System.Windows.Forms.CheckBox();
            this.m_message_X_radio = new System.Windows.Forms.CheckBox();
            this.m_message_W_radio = new System.Windows.Forms.CheckBox();
            this.m_message_V_radio = new System.Windows.Forms.CheckBox();
            this.m_message_U_radio = new System.Windows.Forms.CheckBox();
            this.m_message_T_radio = new System.Windows.Forms.CheckBox();
            this.m_message_S_radio = new System.Windows.Forms.CheckBox();
            this.m_message_R_radio = new System.Windows.Forms.CheckBox();
            this.m_message_Q_radio = new System.Windows.Forms.CheckBox();
            this.m_message_P_radio = new System.Windows.Forms.CheckBox();
            this.m_message_O_radio = new System.Windows.Forms.CheckBox();
            this.m_message_N_radio = new System.Windows.Forms.CheckBox();
            this.m_message_M_radio = new System.Windows.Forms.CheckBox();
            this.m_message_L_radio = new System.Windows.Forms.CheckBox();
            this.m_message_K_radio = new System.Windows.Forms.CheckBox();
            this.m_message_J_radio = new System.Windows.Forms.CheckBox();
            this.m_message_I_radio = new System.Windows.Forms.CheckBox();
            this.m_message_H_radio = new System.Windows.Forms.CheckBox();
            this.m_message_G_radio = new System.Windows.Forms.CheckBox();
            this.m_message_F_radio = new System.Windows.Forms.CheckBox();
            this.m_message_E_radio = new System.Windows.Forms.CheckBox();
            this.m_message_D_radio = new System.Windows.Forms.CheckBox();
            this.m_message_C_radio = new System.Windows.Forms.CheckBox();
            this.m_message_B_radio = new System.Windows.Forms.CheckBox();
            this.m_message_A_radio = new System.Windows.Forms.CheckBox();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.m_coverage_all_disable_btn = new System.Windows.Forms.Button();
            this.m_coverage_all_enable_btn = new System.Windows.Forms.Button();
            this.m_coverage_Z_radio = new System.Windows.Forms.CheckBox();
            this.m_coverage_Y_radio = new System.Windows.Forms.CheckBox();
            this.m_coverage_X_radio = new System.Windows.Forms.CheckBox();
            this.m_coverage_W_radio = new System.Windows.Forms.CheckBox();
            this.m_coverage_V_radio = new System.Windows.Forms.CheckBox();
            this.m_coverage_U_radio = new System.Windows.Forms.CheckBox();
            this.m_coverage_T_radio = new System.Windows.Forms.CheckBox();
            this.m_coverage_S_radio = new System.Windows.Forms.CheckBox();
            this.m_coverage_R_radio = new System.Windows.Forms.CheckBox();
            this.m_coverage_Q_radio = new System.Windows.Forms.CheckBox();
            this.m_coverage_P_radio = new System.Windows.Forms.CheckBox();
            this.m_coverage_O_radio = new System.Windows.Forms.CheckBox();
            this.m_coverage_N_radio = new System.Windows.Forms.CheckBox();
            this.m_coverage_M_radio = new System.Windows.Forms.CheckBox();
            this.m_coverage_L_radio = new System.Windows.Forms.CheckBox();
            this.m_coverage_K_radio = new System.Windows.Forms.CheckBox();
            this.m_coverage_J_radio = new System.Windows.Forms.CheckBox();
            this.m_coverage_I_radio = new System.Windows.Forms.CheckBox();
            this.m_coverage_H_radio = new System.Windows.Forms.CheckBox();
            this.m_coverage_G_radio = new System.Windows.Forms.CheckBox();
            this.m_coverage_F_radio = new System.Windows.Forms.CheckBox();
            this.m_coverage_E_radio = new System.Windows.Forms.CheckBox();
            this.m_coverage_D_radio = new System.Windows.Forms.CheckBox();
            this.m_coverage_C_radio = new System.Windows.Forms.CheckBox();
            this.m_coverage_B_radio = new System.Windows.Forms.CheckBox();
            this.m_coverage_A_radio = new System.Windows.Forms.CheckBox();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.m_4209_5KHz_radio = new System.Windows.Forms.RadioButton();
            this.m_518KHz_radio = new System.Windows.Forms.RadioButton();
            this.m_490KHz_radio = new System.Windows.Forms.RadioButton();
            this.Purpose = new System.Windows.Forms.GroupBox();
            this.m_ins_radio = new System.Windows.Forms.RadioButton();
            this.m_printer_radio = new System.Windows.Forms.RadioButton();
            this.m_storage_radio = new System.Windows.Forms.RadioButton();
            this.m_given_radio = new System.Windows.Forms.RadioButton();
            this.m_NRM_monitor = new System.Windows.Forms.TextBox();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.m_NRM_talk_id_textbox = new System.Windows.Forms.TextBox();
            this.gb_interface = new System.Windows.Forms.GroupBox();
            this.tb_450_line_count = new System.Windows.Forms.TextBox();
            this.label13 = new System.Windows.Forms.Label();
            this.cb_n_enable = new System.Windows.Forms.CheckBox();
            this.cb_d_enable = new System.Windows.Forms.CheckBox();
            this.cb_s_enable = new System.Windows.Forms.CheckBox();
            this.tb_450_dst = new System.Windows.Forms.TextBox();
            this.tb_450_src = new System.Windows.Forms.TextBox();
            this.label12 = new System.Windows.Forms.Label();
            this.label11 = new System.Windows.Forms.Label();
            this.rb_61162_450 = new System.Windows.Forms.RadioButton();
            this.rb_61162_1_2 = new System.Windows.Forms.RadioButton();
            this.groupBox3.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.Purpose.SuspendLayout();
            this.groupBox4.SuspendLayout();
            this.gb_interface.SuspendLayout();
            this.SuspendLayout();
            // 
            // m_send_btn
            // 
            resources.ApplyResources(this.m_send_btn, "m_send_btn");
            this.m_send_btn.Name = "m_send_btn";
            this.m_send_btn.UseVisualStyleBackColor = true;
            this.m_send_btn.Click += new System.EventHandler(this.m_send_btn_Click);
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.m_message_all_disable_btn);
            this.groupBox3.Controls.Add(this.m_message_all_enable_btn);
            this.groupBox3.Controls.Add(this.m_message_Z_radio);
            this.groupBox3.Controls.Add(this.m_message_Y_radio);
            this.groupBox3.Controls.Add(this.m_message_X_radio);
            this.groupBox3.Controls.Add(this.m_message_W_radio);
            this.groupBox3.Controls.Add(this.m_message_V_radio);
            this.groupBox3.Controls.Add(this.m_message_U_radio);
            this.groupBox3.Controls.Add(this.m_message_T_radio);
            this.groupBox3.Controls.Add(this.m_message_S_radio);
            this.groupBox3.Controls.Add(this.m_message_R_radio);
            this.groupBox3.Controls.Add(this.m_message_Q_radio);
            this.groupBox3.Controls.Add(this.m_message_P_radio);
            this.groupBox3.Controls.Add(this.m_message_O_radio);
            this.groupBox3.Controls.Add(this.m_message_N_radio);
            this.groupBox3.Controls.Add(this.m_message_M_radio);
            this.groupBox3.Controls.Add(this.m_message_L_radio);
            this.groupBox3.Controls.Add(this.m_message_K_radio);
            this.groupBox3.Controls.Add(this.m_message_J_radio);
            this.groupBox3.Controls.Add(this.m_message_I_radio);
            this.groupBox3.Controls.Add(this.m_message_H_radio);
            this.groupBox3.Controls.Add(this.m_message_G_radio);
            this.groupBox3.Controls.Add(this.m_message_F_radio);
            this.groupBox3.Controls.Add(this.m_message_E_radio);
            this.groupBox3.Controls.Add(this.m_message_D_radio);
            this.groupBox3.Controls.Add(this.m_message_C_radio);
            this.groupBox3.Controls.Add(this.m_message_B_radio);
            this.groupBox3.Controls.Add(this.m_message_A_radio);
            resources.ApplyResources(this.groupBox3, "groupBox3");
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.TabStop = false;
            // 
            // m_message_all_disable_btn
            // 
            resources.ApplyResources(this.m_message_all_disable_btn, "m_message_all_disable_btn");
            this.m_message_all_disable_btn.Name = "m_message_all_disable_btn";
            this.m_message_all_disable_btn.UseVisualStyleBackColor = true;
            this.m_message_all_disable_btn.Click += new System.EventHandler(this.m_message_all_disable_btn_Click);
            // 
            // m_message_all_enable_btn
            // 
            resources.ApplyResources(this.m_message_all_enable_btn, "m_message_all_enable_btn");
            this.m_message_all_enable_btn.Name = "m_message_all_enable_btn";
            this.m_message_all_enable_btn.UseVisualStyleBackColor = true;
            this.m_message_all_enable_btn.Click += new System.EventHandler(this.m_message_all_enable_btn_Click);
            // 
            // m_message_Z_radio
            // 
            resources.ApplyResources(this.m_message_Z_radio, "m_message_Z_radio");
            this.m_message_Z_radio.Name = "m_message_Z_radio";
            this.m_message_Z_radio.UseVisualStyleBackColor = true;
            // 
            // m_message_Y_radio
            // 
            resources.ApplyResources(this.m_message_Y_radio, "m_message_Y_radio");
            this.m_message_Y_radio.Name = "m_message_Y_radio";
            this.m_message_Y_radio.UseVisualStyleBackColor = true;
            // 
            // m_message_X_radio
            // 
            resources.ApplyResources(this.m_message_X_radio, "m_message_X_radio");
            this.m_message_X_radio.Name = "m_message_X_radio";
            this.m_message_X_radio.UseVisualStyleBackColor = true;
            // 
            // m_message_W_radio
            // 
            resources.ApplyResources(this.m_message_W_radio, "m_message_W_radio");
            this.m_message_W_radio.Name = "m_message_W_radio";
            this.m_message_W_radio.UseVisualStyleBackColor = true;
            // 
            // m_message_V_radio
            // 
            resources.ApplyResources(this.m_message_V_radio, "m_message_V_radio");
            this.m_message_V_radio.Name = "m_message_V_radio";
            this.m_message_V_radio.UseVisualStyleBackColor = true;
            // 
            // m_message_U_radio
            // 
            resources.ApplyResources(this.m_message_U_radio, "m_message_U_radio");
            this.m_message_U_radio.Name = "m_message_U_radio";
            this.m_message_U_radio.UseVisualStyleBackColor = true;
            // 
            // m_message_T_radio
            // 
            resources.ApplyResources(this.m_message_T_radio, "m_message_T_radio");
            this.m_message_T_radio.Name = "m_message_T_radio";
            this.m_message_T_radio.UseVisualStyleBackColor = true;
            // 
            // m_message_S_radio
            // 
            resources.ApplyResources(this.m_message_S_radio, "m_message_S_radio");
            this.m_message_S_radio.Name = "m_message_S_radio";
            this.m_message_S_radio.UseVisualStyleBackColor = true;
            // 
            // m_message_R_radio
            // 
            resources.ApplyResources(this.m_message_R_radio, "m_message_R_radio");
            this.m_message_R_radio.Name = "m_message_R_radio";
            this.m_message_R_radio.UseVisualStyleBackColor = true;
            // 
            // m_message_Q_radio
            // 
            resources.ApplyResources(this.m_message_Q_radio, "m_message_Q_radio");
            this.m_message_Q_radio.Name = "m_message_Q_radio";
            this.m_message_Q_radio.UseVisualStyleBackColor = true;
            // 
            // m_message_P_radio
            // 
            resources.ApplyResources(this.m_message_P_radio, "m_message_P_radio");
            this.m_message_P_radio.Name = "m_message_P_radio";
            this.m_message_P_radio.UseVisualStyleBackColor = true;
            // 
            // m_message_O_radio
            // 
            resources.ApplyResources(this.m_message_O_radio, "m_message_O_radio");
            this.m_message_O_radio.Name = "m_message_O_radio";
            this.m_message_O_radio.UseVisualStyleBackColor = true;
            // 
            // m_message_N_radio
            // 
            resources.ApplyResources(this.m_message_N_radio, "m_message_N_radio");
            this.m_message_N_radio.Name = "m_message_N_radio";
            this.m_message_N_radio.UseVisualStyleBackColor = true;
            // 
            // m_message_M_radio
            // 
            resources.ApplyResources(this.m_message_M_radio, "m_message_M_radio");
            this.m_message_M_radio.Name = "m_message_M_radio";
            this.m_message_M_radio.UseVisualStyleBackColor = true;
            // 
            // m_message_L_radio
            // 
            resources.ApplyResources(this.m_message_L_radio, "m_message_L_radio");
            this.m_message_L_radio.Name = "m_message_L_radio";
            this.m_message_L_radio.UseVisualStyleBackColor = true;
            // 
            // m_message_K_radio
            // 
            resources.ApplyResources(this.m_message_K_radio, "m_message_K_radio");
            this.m_message_K_radio.Name = "m_message_K_radio";
            this.m_message_K_radio.UseVisualStyleBackColor = true;
            // 
            // m_message_J_radio
            // 
            resources.ApplyResources(this.m_message_J_radio, "m_message_J_radio");
            this.m_message_J_radio.Name = "m_message_J_radio";
            this.m_message_J_radio.UseVisualStyleBackColor = true;
            // 
            // m_message_I_radio
            // 
            resources.ApplyResources(this.m_message_I_radio, "m_message_I_radio");
            this.m_message_I_radio.Name = "m_message_I_radio";
            this.m_message_I_radio.UseVisualStyleBackColor = true;
            // 
            // m_message_H_radio
            // 
            resources.ApplyResources(this.m_message_H_radio, "m_message_H_radio");
            this.m_message_H_radio.Name = "m_message_H_radio";
            this.m_message_H_radio.UseVisualStyleBackColor = true;
            // 
            // m_message_G_radio
            // 
            resources.ApplyResources(this.m_message_G_radio, "m_message_G_radio");
            this.m_message_G_radio.Name = "m_message_G_radio";
            this.m_message_G_radio.UseVisualStyleBackColor = true;
            // 
            // m_message_F_radio
            // 
            resources.ApplyResources(this.m_message_F_radio, "m_message_F_radio");
            this.m_message_F_radio.Name = "m_message_F_radio";
            this.m_message_F_radio.UseVisualStyleBackColor = true;
            // 
            // m_message_E_radio
            // 
            resources.ApplyResources(this.m_message_E_radio, "m_message_E_radio");
            this.m_message_E_radio.Name = "m_message_E_radio";
            this.m_message_E_radio.UseVisualStyleBackColor = true;
            // 
            // m_message_D_radio
            // 
            resources.ApplyResources(this.m_message_D_radio, "m_message_D_radio");
            this.m_message_D_radio.Name = "m_message_D_radio";
            this.m_message_D_radio.UseVisualStyleBackColor = true;
            // 
            // m_message_C_radio
            // 
            resources.ApplyResources(this.m_message_C_radio, "m_message_C_radio");
            this.m_message_C_radio.Name = "m_message_C_radio";
            this.m_message_C_radio.UseVisualStyleBackColor = true;
            // 
            // m_message_B_radio
            // 
            resources.ApplyResources(this.m_message_B_radio, "m_message_B_radio");
            this.m_message_B_radio.Name = "m_message_B_radio";
            this.m_message_B_radio.UseVisualStyleBackColor = true;
            // 
            // m_message_A_radio
            // 
            resources.ApplyResources(this.m_message_A_radio, "m_message_A_radio");
            this.m_message_A_radio.Name = "m_message_A_radio";
            this.m_message_A_radio.UseVisualStyleBackColor = true;
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.m_coverage_all_disable_btn);
            this.groupBox2.Controls.Add(this.m_coverage_all_enable_btn);
            this.groupBox2.Controls.Add(this.m_coverage_Z_radio);
            this.groupBox2.Controls.Add(this.m_coverage_Y_radio);
            this.groupBox2.Controls.Add(this.m_coverage_X_radio);
            this.groupBox2.Controls.Add(this.m_coverage_W_radio);
            this.groupBox2.Controls.Add(this.m_coverage_V_radio);
            this.groupBox2.Controls.Add(this.m_coverage_U_radio);
            this.groupBox2.Controls.Add(this.m_coverage_T_radio);
            this.groupBox2.Controls.Add(this.m_coverage_S_radio);
            this.groupBox2.Controls.Add(this.m_coverage_R_radio);
            this.groupBox2.Controls.Add(this.m_coverage_Q_radio);
            this.groupBox2.Controls.Add(this.m_coverage_P_radio);
            this.groupBox2.Controls.Add(this.m_coverage_O_radio);
            this.groupBox2.Controls.Add(this.m_coverage_N_radio);
            this.groupBox2.Controls.Add(this.m_coverage_M_radio);
            this.groupBox2.Controls.Add(this.m_coverage_L_radio);
            this.groupBox2.Controls.Add(this.m_coverage_K_radio);
            this.groupBox2.Controls.Add(this.m_coverage_J_radio);
            this.groupBox2.Controls.Add(this.m_coverage_I_radio);
            this.groupBox2.Controls.Add(this.m_coverage_H_radio);
            this.groupBox2.Controls.Add(this.m_coverage_G_radio);
            this.groupBox2.Controls.Add(this.m_coverage_F_radio);
            this.groupBox2.Controls.Add(this.m_coverage_E_radio);
            this.groupBox2.Controls.Add(this.m_coverage_D_radio);
            this.groupBox2.Controls.Add(this.m_coverage_C_radio);
            this.groupBox2.Controls.Add(this.m_coverage_B_radio);
            this.groupBox2.Controls.Add(this.m_coverage_A_radio);
            resources.ApplyResources(this.groupBox2, "groupBox2");
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.TabStop = false;
            // 
            // m_coverage_all_disable_btn
            // 
            resources.ApplyResources(this.m_coverage_all_disable_btn, "m_coverage_all_disable_btn");
            this.m_coverage_all_disable_btn.Name = "m_coverage_all_disable_btn";
            this.m_coverage_all_disable_btn.UseVisualStyleBackColor = true;
            this.m_coverage_all_disable_btn.Click += new System.EventHandler(this.m_coverage_all_disable_btn_Click);
            // 
            // m_coverage_all_enable_btn
            // 
            resources.ApplyResources(this.m_coverage_all_enable_btn, "m_coverage_all_enable_btn");
            this.m_coverage_all_enable_btn.Name = "m_coverage_all_enable_btn";
            this.m_coverage_all_enable_btn.UseVisualStyleBackColor = true;
            this.m_coverage_all_enable_btn.Click += new System.EventHandler(this.m_coverage_all_enable_btn_Click);
            // 
            // m_coverage_Z_radio
            // 
            resources.ApplyResources(this.m_coverage_Z_radio, "m_coverage_Z_radio");
            this.m_coverage_Z_radio.Name = "m_coverage_Z_radio";
            this.m_coverage_Z_radio.UseVisualStyleBackColor = true;
            // 
            // m_coverage_Y_radio
            // 
            resources.ApplyResources(this.m_coverage_Y_radio, "m_coverage_Y_radio");
            this.m_coverage_Y_radio.Name = "m_coverage_Y_radio";
            this.m_coverage_Y_radio.UseVisualStyleBackColor = true;
            // 
            // m_coverage_X_radio
            // 
            resources.ApplyResources(this.m_coverage_X_radio, "m_coverage_X_radio");
            this.m_coverage_X_radio.Name = "m_coverage_X_radio";
            this.m_coverage_X_radio.UseVisualStyleBackColor = true;
            // 
            // m_coverage_W_radio
            // 
            resources.ApplyResources(this.m_coverage_W_radio, "m_coverage_W_radio");
            this.m_coverage_W_radio.Name = "m_coverage_W_radio";
            this.m_coverage_W_radio.UseVisualStyleBackColor = true;
            // 
            // m_coverage_V_radio
            // 
            resources.ApplyResources(this.m_coverage_V_radio, "m_coverage_V_radio");
            this.m_coverage_V_radio.Name = "m_coverage_V_radio";
            this.m_coverage_V_radio.UseVisualStyleBackColor = true;
            // 
            // m_coverage_U_radio
            // 
            resources.ApplyResources(this.m_coverage_U_radio, "m_coverage_U_radio");
            this.m_coverage_U_radio.Name = "m_coverage_U_radio";
            this.m_coverage_U_radio.UseVisualStyleBackColor = true;
            // 
            // m_coverage_T_radio
            // 
            resources.ApplyResources(this.m_coverage_T_radio, "m_coverage_T_radio");
            this.m_coverage_T_radio.Name = "m_coverage_T_radio";
            this.m_coverage_T_radio.UseVisualStyleBackColor = true;
            // 
            // m_coverage_S_radio
            // 
            resources.ApplyResources(this.m_coverage_S_radio, "m_coverage_S_radio");
            this.m_coverage_S_radio.Name = "m_coverage_S_radio";
            this.m_coverage_S_radio.UseVisualStyleBackColor = true;
            // 
            // m_coverage_R_radio
            // 
            resources.ApplyResources(this.m_coverage_R_radio, "m_coverage_R_radio");
            this.m_coverage_R_radio.Name = "m_coverage_R_radio";
            this.m_coverage_R_radio.UseVisualStyleBackColor = true;
            // 
            // m_coverage_Q_radio
            // 
            resources.ApplyResources(this.m_coverage_Q_radio, "m_coverage_Q_radio");
            this.m_coverage_Q_radio.Name = "m_coverage_Q_radio";
            this.m_coverage_Q_radio.UseVisualStyleBackColor = true;
            // 
            // m_coverage_P_radio
            // 
            resources.ApplyResources(this.m_coverage_P_radio, "m_coverage_P_radio");
            this.m_coverage_P_radio.Name = "m_coverage_P_radio";
            this.m_coverage_P_radio.UseVisualStyleBackColor = true;
            // 
            // m_coverage_O_radio
            // 
            resources.ApplyResources(this.m_coverage_O_radio, "m_coverage_O_radio");
            this.m_coverage_O_radio.Name = "m_coverage_O_radio";
            this.m_coverage_O_radio.UseVisualStyleBackColor = true;
            // 
            // m_coverage_N_radio
            // 
            resources.ApplyResources(this.m_coverage_N_radio, "m_coverage_N_radio");
            this.m_coverage_N_radio.Name = "m_coverage_N_radio";
            this.m_coverage_N_radio.UseVisualStyleBackColor = true;
            // 
            // m_coverage_M_radio
            // 
            resources.ApplyResources(this.m_coverage_M_radio, "m_coverage_M_radio");
            this.m_coverage_M_radio.Name = "m_coverage_M_radio";
            this.m_coverage_M_radio.UseVisualStyleBackColor = true;
            // 
            // m_coverage_L_radio
            // 
            resources.ApplyResources(this.m_coverage_L_radio, "m_coverage_L_radio");
            this.m_coverage_L_radio.Name = "m_coverage_L_radio";
            this.m_coverage_L_radio.UseVisualStyleBackColor = true;
            // 
            // m_coverage_K_radio
            // 
            resources.ApplyResources(this.m_coverage_K_radio, "m_coverage_K_radio");
            this.m_coverage_K_radio.Name = "m_coverage_K_radio";
            this.m_coverage_K_radio.UseVisualStyleBackColor = true;
            // 
            // m_coverage_J_radio
            // 
            resources.ApplyResources(this.m_coverage_J_radio, "m_coverage_J_radio");
            this.m_coverage_J_radio.Name = "m_coverage_J_radio";
            this.m_coverage_J_radio.UseVisualStyleBackColor = true;
            // 
            // m_coverage_I_radio
            // 
            resources.ApplyResources(this.m_coverage_I_radio, "m_coverage_I_radio");
            this.m_coverage_I_radio.Name = "m_coverage_I_radio";
            this.m_coverage_I_radio.UseVisualStyleBackColor = true;
            // 
            // m_coverage_H_radio
            // 
            resources.ApplyResources(this.m_coverage_H_radio, "m_coverage_H_radio");
            this.m_coverage_H_radio.Name = "m_coverage_H_radio";
            this.m_coverage_H_radio.UseVisualStyleBackColor = true;
            // 
            // m_coverage_G_radio
            // 
            resources.ApplyResources(this.m_coverage_G_radio, "m_coverage_G_radio");
            this.m_coverage_G_radio.Name = "m_coverage_G_radio";
            this.m_coverage_G_radio.UseVisualStyleBackColor = true;
            // 
            // m_coverage_F_radio
            // 
            resources.ApplyResources(this.m_coverage_F_radio, "m_coverage_F_radio");
            this.m_coverage_F_radio.Name = "m_coverage_F_radio";
            this.m_coverage_F_radio.UseVisualStyleBackColor = true;
            // 
            // m_coverage_E_radio
            // 
            resources.ApplyResources(this.m_coverage_E_radio, "m_coverage_E_radio");
            this.m_coverage_E_radio.Name = "m_coverage_E_radio";
            this.m_coverage_E_radio.UseVisualStyleBackColor = true;
            // 
            // m_coverage_D_radio
            // 
            resources.ApplyResources(this.m_coverage_D_radio, "m_coverage_D_radio");
            this.m_coverage_D_radio.Name = "m_coverage_D_radio";
            this.m_coverage_D_radio.UseVisualStyleBackColor = true;
            // 
            // m_coverage_C_radio
            // 
            resources.ApplyResources(this.m_coverage_C_radio, "m_coverage_C_radio");
            this.m_coverage_C_radio.Name = "m_coverage_C_radio";
            this.m_coverage_C_radio.UseVisualStyleBackColor = true;
            // 
            // m_coverage_B_radio
            // 
            resources.ApplyResources(this.m_coverage_B_radio, "m_coverage_B_radio");
            this.m_coverage_B_radio.Name = "m_coverage_B_radio";
            this.m_coverage_B_radio.UseVisualStyleBackColor = true;
            // 
            // m_coverage_A_radio
            // 
            resources.ApplyResources(this.m_coverage_A_radio, "m_coverage_A_radio");
            this.m_coverage_A_radio.Name = "m_coverage_A_radio";
            this.m_coverage_A_radio.UseVisualStyleBackColor = true;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.m_4209_5KHz_radio);
            this.groupBox1.Controls.Add(this.m_518KHz_radio);
            this.groupBox1.Controls.Add(this.m_490KHz_radio);
            resources.ApplyResources(this.groupBox1, "groupBox1");
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.TabStop = false;
            // 
            // m_4209_5KHz_radio
            // 
            resources.ApplyResources(this.m_4209_5KHz_radio, "m_4209_5KHz_radio");
            this.m_4209_5KHz_radio.Name = "m_4209_5KHz_radio";
            this.m_4209_5KHz_radio.TabStop = true;
            this.m_4209_5KHz_radio.UseVisualStyleBackColor = true;
            // 
            // m_518KHz_radio
            // 
            resources.ApplyResources(this.m_518KHz_radio, "m_518KHz_radio");
            this.m_518KHz_radio.Name = "m_518KHz_radio";
            this.m_518KHz_radio.TabStop = true;
            this.m_518KHz_radio.UseVisualStyleBackColor = true;
            // 
            // m_490KHz_radio
            // 
            resources.ApplyResources(this.m_490KHz_radio, "m_490KHz_radio");
            this.m_490KHz_radio.Name = "m_490KHz_radio";
            this.m_490KHz_radio.TabStop = true;
            this.m_490KHz_radio.UseVisualStyleBackColor = true;
            // 
            // Purpose
            // 
            this.Purpose.Controls.Add(this.m_ins_radio);
            this.Purpose.Controls.Add(this.m_printer_radio);
            this.Purpose.Controls.Add(this.m_storage_radio);
            this.Purpose.Controls.Add(this.m_given_radio);
            resources.ApplyResources(this.Purpose, "Purpose");
            this.Purpose.Name = "Purpose";
            this.Purpose.TabStop = false;
            // 
            // m_ins_radio
            // 
            resources.ApplyResources(this.m_ins_radio, "m_ins_radio");
            this.m_ins_radio.Name = "m_ins_radio";
            this.m_ins_radio.TabStop = true;
            this.m_ins_radio.UseVisualStyleBackColor = true;
            // 
            // m_printer_radio
            // 
            resources.ApplyResources(this.m_printer_radio, "m_printer_radio");
            this.m_printer_radio.Name = "m_printer_radio";
            this.m_printer_radio.TabStop = true;
            this.m_printer_radio.UseVisualStyleBackColor = true;
            // 
            // m_storage_radio
            // 
            resources.ApplyResources(this.m_storage_radio, "m_storage_radio");
            this.m_storage_radio.Name = "m_storage_radio";
            this.m_storage_radio.TabStop = true;
            this.m_storage_radio.UseVisualStyleBackColor = true;
            // 
            // m_given_radio
            // 
            resources.ApplyResources(this.m_given_radio, "m_given_radio");
            this.m_given_radio.Name = "m_given_radio";
            this.m_given_radio.TabStop = true;
            this.m_given_radio.UseVisualStyleBackColor = true;
            // 
            // m_NRM_monitor
            // 
            this.m_NRM_monitor.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            resources.ApplyResources(this.m_NRM_monitor, "m_NRM_monitor");
            this.m_NRM_monitor.Name = "m_NRM_monitor";
            // 
            // groupBox4
            // 
            this.groupBox4.Controls.Add(this.m_NRM_talk_id_textbox);
            resources.ApplyResources(this.groupBox4, "groupBox4");
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.TabStop = false;
            // 
            // m_NRM_talk_id_textbox
            // 
            resources.ApplyResources(this.m_NRM_talk_id_textbox, "m_NRM_talk_id_textbox");
            this.m_NRM_talk_id_textbox.Name = "m_NRM_talk_id_textbox";
            // 
            // gb_interface
            // 
            this.gb_interface.Controls.Add(this.tb_450_line_count);
            this.gb_interface.Controls.Add(this.label13);
            this.gb_interface.Controls.Add(this.cb_n_enable);
            this.gb_interface.Controls.Add(this.cb_d_enable);
            this.gb_interface.Controls.Add(this.cb_s_enable);
            this.gb_interface.Controls.Add(this.tb_450_dst);
            this.gb_interface.Controls.Add(this.tb_450_src);
            this.gb_interface.Controls.Add(this.label12);
            this.gb_interface.Controls.Add(this.label11);
            this.gb_interface.Controls.Add(this.rb_61162_450);
            this.gb_interface.Controls.Add(this.rb_61162_1_2);
            resources.ApplyResources(this.gb_interface, "gb_interface");
            this.gb_interface.Name = "gb_interface";
            this.gb_interface.TabStop = false;
            // 
            // tb_450_line_count
            // 
            resources.ApplyResources(this.tb_450_line_count, "tb_450_line_count");
            this.tb_450_line_count.Name = "tb_450_line_count";
            // 
            // label13
            // 
            resources.ApplyResources(this.label13, "label13");
            this.label13.Name = "label13";
            // 
            // cb_n_enable
            // 
            resources.ApplyResources(this.cb_n_enable, "cb_n_enable");
            this.cb_n_enable.Name = "cb_n_enable";
            this.cb_n_enable.UseVisualStyleBackColor = true;
            // 
            // cb_d_enable
            // 
            resources.ApplyResources(this.cb_d_enable, "cb_d_enable");
            this.cb_d_enable.Name = "cb_d_enable";
            this.cb_d_enable.UseVisualStyleBackColor = true;
            // 
            // cb_s_enable
            // 
            resources.ApplyResources(this.cb_s_enable, "cb_s_enable");
            this.cb_s_enable.Name = "cb_s_enable";
            this.cb_s_enable.UseVisualStyleBackColor = true;
            // 
            // tb_450_dst
            // 
            resources.ApplyResources(this.tb_450_dst, "tb_450_dst");
            this.tb_450_dst.Name = "tb_450_dst";
            // 
            // tb_450_src
            // 
            resources.ApplyResources(this.tb_450_src, "tb_450_src");
            this.tb_450_src.Name = "tb_450_src";
            // 
            // label12
            // 
            resources.ApplyResources(this.label12, "label12");
            this.label12.Name = "label12";
            // 
            // label11
            // 
            resources.ApplyResources(this.label11, "label11");
            this.label11.Name = "label11";
            // 
            // rb_61162_450
            // 
            resources.ApplyResources(this.rb_61162_450, "rb_61162_450");
            this.rb_61162_450.Name = "rb_61162_450";
            this.rb_61162_450.TabStop = true;
            this.rb_61162_450.UseVisualStyleBackColor = true;
            // 
            // rb_61162_1_2
            // 
            resources.ApplyResources(this.rb_61162_1_2, "rb_61162_1_2");
            this.rb_61162_1_2.Name = "rb_61162_1_2";
            this.rb_61162_1_2.TabStop = true;
            this.rb_61162_1_2.UseVisualStyleBackColor = true;
            // 
            // Sentence_NRM_Form
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.gb_interface);
            this.Controls.Add(this.groupBox4);
            this.Controls.Add(this.m_NRM_monitor);
            this.Controls.Add(this.m_send_btn);
            this.Controls.Add(this.groupBox3);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.Purpose);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
            this.Name = "Sentence_NRM_Form";
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.Purpose.ResumeLayout(false);
            this.Purpose.PerformLayout();
            this.groupBox4.ResumeLayout(false);
            this.groupBox4.PerformLayout();
            this.gb_interface.ResumeLayout(false);
            this.gb_interface.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Button m_send_btn;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.Button m_message_all_disable_btn;
        private System.Windows.Forms.Button m_message_all_enable_btn;
        private System.Windows.Forms.CheckBox m_message_Z_radio;
        private System.Windows.Forms.CheckBox m_message_Y_radio;
        private System.Windows.Forms.CheckBox m_message_X_radio;
        private System.Windows.Forms.CheckBox m_message_W_radio;
        private System.Windows.Forms.CheckBox m_message_V_radio;
        private System.Windows.Forms.CheckBox m_message_U_radio;
        private System.Windows.Forms.CheckBox m_message_T_radio;
        private System.Windows.Forms.CheckBox m_message_S_radio;
        private System.Windows.Forms.CheckBox m_message_R_radio;
        private System.Windows.Forms.CheckBox m_message_Q_radio;
        private System.Windows.Forms.CheckBox m_message_P_radio;
        private System.Windows.Forms.CheckBox m_message_O_radio;
        private System.Windows.Forms.CheckBox m_message_N_radio;
        private System.Windows.Forms.CheckBox m_message_M_radio;
        private System.Windows.Forms.CheckBox m_message_L_radio;
        private System.Windows.Forms.CheckBox m_message_K_radio;
        private System.Windows.Forms.CheckBox m_message_J_radio;
        private System.Windows.Forms.CheckBox m_message_I_radio;
        private System.Windows.Forms.CheckBox m_message_H_radio;
        private System.Windows.Forms.CheckBox m_message_G_radio;
        private System.Windows.Forms.CheckBox m_message_F_radio;
        private System.Windows.Forms.CheckBox m_message_E_radio;
        private System.Windows.Forms.CheckBox m_message_D_radio;
        private System.Windows.Forms.CheckBox m_message_C_radio;
        private System.Windows.Forms.CheckBox m_message_B_radio;
        private System.Windows.Forms.CheckBox m_message_A_radio;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Button m_coverage_all_disable_btn;
        private System.Windows.Forms.Button m_coverage_all_enable_btn;
        private System.Windows.Forms.CheckBox m_coverage_Z_radio;
        private System.Windows.Forms.CheckBox m_coverage_Y_radio;
        private System.Windows.Forms.CheckBox m_coverage_X_radio;
        private System.Windows.Forms.CheckBox m_coverage_W_radio;
        private System.Windows.Forms.CheckBox m_coverage_V_radio;
        private System.Windows.Forms.CheckBox m_coverage_U_radio;
        private System.Windows.Forms.CheckBox m_coverage_T_radio;
        private System.Windows.Forms.CheckBox m_coverage_S_radio;
        private System.Windows.Forms.CheckBox m_coverage_R_radio;
        private System.Windows.Forms.CheckBox m_coverage_Q_radio;
        private System.Windows.Forms.CheckBox m_coverage_P_radio;
        private System.Windows.Forms.CheckBox m_coverage_O_radio;
        private System.Windows.Forms.CheckBox m_coverage_N_radio;
        private System.Windows.Forms.CheckBox m_coverage_M_radio;
        private System.Windows.Forms.CheckBox m_coverage_L_radio;
        private System.Windows.Forms.CheckBox m_coverage_K_radio;
        private System.Windows.Forms.CheckBox m_coverage_J_radio;
        private System.Windows.Forms.CheckBox m_coverage_I_radio;
        private System.Windows.Forms.CheckBox m_coverage_H_radio;
        private System.Windows.Forms.CheckBox m_coverage_G_radio;
        private System.Windows.Forms.CheckBox m_coverage_F_radio;
        private System.Windows.Forms.CheckBox m_coverage_E_radio;
        private System.Windows.Forms.CheckBox m_coverage_D_radio;
        private System.Windows.Forms.CheckBox m_coverage_C_radio;
        private System.Windows.Forms.CheckBox m_coverage_B_radio;
        private System.Windows.Forms.CheckBox m_coverage_A_radio;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.RadioButton m_4209_5KHz_radio;
        private System.Windows.Forms.RadioButton m_518KHz_radio;
        private System.Windows.Forms.RadioButton m_490KHz_radio;
        private System.Windows.Forms.GroupBox Purpose;
        private System.Windows.Forms.RadioButton m_ins_radio;
        private System.Windows.Forms.RadioButton m_printer_radio;
        private System.Windows.Forms.RadioButton m_storage_radio;
        private System.Windows.Forms.RadioButton m_given_radio;
        private System.Windows.Forms.TextBox m_NRM_monitor;
        private System.Windows.Forms.GroupBox groupBox4;
        private System.Windows.Forms.TextBox m_NRM_talk_id_textbox;
        private System.Windows.Forms.GroupBox gb_interface;
        private System.Windows.Forms.TextBox tb_450_line_count;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.CheckBox cb_n_enable;
        private System.Windows.Forms.CheckBox cb_d_enable;
        private System.Windows.Forms.CheckBox cb_s_enable;
        private System.Windows.Forms.TextBox tb_450_dst;
        private System.Windows.Forms.TextBox tb_450_src;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.RadioButton rb_61162_450;
        private System.Windows.Forms.RadioButton rb_61162_1_2;
    }
}