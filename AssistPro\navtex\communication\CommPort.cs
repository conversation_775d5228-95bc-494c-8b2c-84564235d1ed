﻿using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Ports;
using System.Linq;
using System.Net.Sockets;
using System.Net;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using AssistPro.Lib;
using AssistPro.navtex;

namespace AssistPro.navtex.communication
{
    public sealed class CommPort
    {
        static readonly CommPort instance = new CommPort();

        private UdpClient udpclient;
        private IPEndPoint remoteEP;
        public static CommPort Instance
        {
            get
            {
                return instance;
            }
        }
        public enum Serial_t
        {
            SERIAL_TYPE_MAIN = 0x00,
            SERIAL_TYPE_INS,
            SERIAL_TYPE_BAM,
            SERIAL_TYPE_MON_1,
            SERIAL_TYPE_MON_2,
            SERIAL_TYPE_MON_3,

            SERIAL_TYPE_MAX,
        };

        // @brief : Setting name component
        public string[] SettingName =
        {
            "Serial Main",
            "Serial INS",
            "Serial BAM",
            "NMEA Monitor 1",
            "NMEA Monitor 2",
            "NMEA Monitor 3",
        };

        public class Serial_c
        {
            public SerialPort _serialPort;
            public int _recoverCount;
            public string isStatus = "Close";
        }
        Serial_c[] m_serial_st = new Serial_c[(int)Serial_t.SERIAL_TYPE_MAX];
        private Thread _readThread = null;
        public bool _keepReading = false;

        public class Serial_Port_Info_c
        {
            public string PortName = "COM1";
            public int BaudRate = 115200;
            public int DataBits = 8;
            public System.IO.Ports.Parity Parity = System.IO.Ports.Parity.None;
            public System.IO.Ports.StopBits StopBits = System.IO.Ports.StopBits.One;
            public System.IO.Ports.Handshake Handshake = System.IO.Ports.Handshake.None;
        }

        CommPort()
        {
            for (int i=0; i<m_serial_st.Length; i++)
            {
                m_serial_st[i] = new Serial_c();
                m_serial_st[i]._serialPort = new SerialPort();
                m_serial_st[i]._recoverCount = new int();

                m_serial_st[i]._serialPort.Handshake = Handshake.None;
                m_serial_st[i]._serialPort.DtrEnable = true;
                m_serial_st[i]._serialPort.RtsEnable = true;
                m_serial_st[i]._serialPort.ReadBufferSize = 40960;
                m_serial_st[i]._serialPort.WriteBufferSize = 40960;
            }
        }

        private void StartReading()
        {
            if(_keepReading == false)
            {
                _keepReading = true;
                _readThread = new Thread(ReadThread);
                _readThread.IsBackground = true;
                _readThread.Start();
            }
        }

        private void StopReading()
        {
            if(_keepReading == true)
            {
                _keepReading = false;
                _readThread.Join();
                _readThread.Abort();
                _readThread = null;
            }
        }

        private async void ReadThread()
        {
            while(_keepReading)
            {
                for(int i=0; i<(int)Serial_t.SERIAL_TYPE_MAX; i++)
                {
                    if (IsOpen(i))
                    {
                        try
                        {
                            int recv_size = m_serial_st[i]._serialPort.BytesToRead;
                            if(recv_size != 0)
                            {
                                byte[] buff = new byte[recv_size];
                                await m_serial_st[i]._serialPort.BaseStream.ReadAsync(buff, 0, buff.Length);

                                if(i == (int)Serial_t.SERIAL_TYPE_MAIN)
                                {
                                    Received_data_Main(buff);
                                }
                                else if(i == (int)Serial_t.SERIAL_TYPE_INS)
                                {
                                    Received_data_INS(buff);
                                }
                                else if(i == (int)Serial_t.SERIAL_TYPE_BAM)
                                {
                                    Received_data_BAM(buff);
                                }
                                else if(i == (int)Serial_t.SERIAL_TYPE_MON_1)
                                {
                                    Received_data_Mon_1(buff);
                                }
                                else if (i == (int)Serial_t.SERIAL_TYPE_MON_2)
                                {
                                    Received_data_Mon_2(buff);
                                }
                                else if (i == (int)Serial_t.SERIAL_TYPE_MON_3)
                                {
                                    Received_data_Mon_3(buff);
                                }
                            }
                        }
                        catch (TimeoutException)
                        {
                            string s = System.Reflection.MethodBase.GetCurrentMethod().Name + ' ' + "Timeout Exception" + string.Format("{0}", i);
                            MessageBox.Show(s);
                        }
                        catch (Exception ex)
                        {
                            string s = System.Reflection.MethodBase.GetCurrentMethod().Name + ' ' + ex.Message + string.Format("{0}", i);
                            MessageBox.Show(s);
                        }
                    }
                    await Task.Delay(10);
                }
            }
        }

        // @brief : RTS output port control
        public void SetRtsIO(int num, int onoff)
        {
            if (onoff == 1)
            {
                m_serial_st[num]._serialPort.RtsEnable = true;
            }
            else
            {
                m_serial_st[num]._serialPort.RtsEnable = false;
            }
        }

        // @brief : CTS Input port control
        public int GetCtsIO(int num)
        {
            int ret = 0;
            if (m_serial_st[num]._serialPort.CtsHolding == true)
            {
                ret = 1;
            }
            else
            {
                ret = 0;
            }

            return ret;
        }

        // @brief : Available ports
        public string[] GetAvailablePorts()
        {
            return SerialPort.GetPortNames();
        }

        // @brief : Received data
        public delegate void ReceivedHandler(byte[] dataIn);
        public ReceivedHandler Received_data_Main;
        public ReceivedHandler Received_data_INS;
        public ReceivedHandler Received_data_BAM;
        public ReceivedHandler Received_data_Mon_1;
        public ReceivedHandler Received_data_Mon_2;
        public ReceivedHandler Received_data_Mon_3;

        // @brief : Open function
        public void Open(int n, Serial_Port_Info_c info)
        {
            if(IsOpen(n) == true)
            {
                Close(n);
            }

            try
            {
                m_serial_st[n]._serialPort.PortName = info.PortName;
                m_serial_st[n]._serialPort.BaudRate = info.BaudRate;
                m_serial_st[n]._serialPort.Parity = info.Parity;
                m_serial_st[n]._serialPort.DataBits = info.DataBits;
                m_serial_st[n]._serialPort.StopBits = info.StopBits;
                m_serial_st[n]._serialPort.Handshake = Handshake.None;

                // Set the read/write timeouts
                m_serial_st[n]._serialPort.ReadTimeout = int.MaxValue;
                m_serial_st[n]._serialPort.WriteTimeout = int.MaxValue;

                m_serial_st[n]._serialPort.Open();
                StartReading();
            }
            catch (IOException)
            {
                string str = String.Format("{0} does not exist", m_serial_st[n]._serialPort.PortName);
                MessageBox.Show(str);
            }
            catch (UnauthorizedAccessException)
            {
                string str = String.Format("{0} already in use", m_serial_st[n]._serialPort.PortName);
                MessageBox.Show(str);
            }
            catch (Exception ex)
            {
                string str = String.Format("{0}", ex.ToString());
                MessageBox.Show(str);
            }

            // Update the status
            if (m_serial_st[n]._serialPort.IsOpen)
            {
                string str = String.Format("{0}", m_serial_st[n]._serialPort.PortName);
                m_serial_st[n].isStatus = str;
            }
            else
            {
                string str = String.Format("{0} already in use", m_serial_st[n]._serialPort.PortName);
                MessageBox.Show(str);
            }
        }

        // @brief : Close function
        public void Close(int n)
        {
            if(IsOpen(n) == true)
            {
                m_serial_st[n]._serialPort.Close();

                string str = String.Format("Close");
                m_serial_st[n].isStatus = str;

                int cnt = 0;
                for (int i = 0; i < (int)Serial_t.SERIAL_TYPE_MAX; i++)
                {
                    if (IsOpen(i) == true)
                    {
                        cnt += 1;
                    }
                }
                if (cnt == 0)
                {
                    StopReading();
                }
            }
        }

        // @brief : IsStatus function
        public string IsStatus(int n)
        {
            return m_serial_st[n].isStatus;
        }

        // @brief : IsOpen function
        public bool IsOpen(int n)
        {
            return m_serial_st[n]._serialPort.IsOpen;
        }

        public void StrSend(string data, int n)
        {
            if (IsOpen(n))
            {
                try
                {
                    m_serial_st[n]._serialPort.Write(data);
                }
                catch (TimeoutException)
                {
                    MessageBox.Show(System.Reflection.MethodBase.GetCurrentMethod().Name + ' ' + "Timeout Exception");
                }
                catch (Exception ex)
                {
                    MessageBox.Show(System.Reflection.MethodBase.GetCurrentMethod().Name + ' ' + ex.Message);
                }
            }
        }

        public void ArrSend(byte[] arrayData, int n)
        {
            if (IsOpen(n))
            {
                try
                {
                    m_serial_st[n]._serialPort.Write(arrayData, 0, arrayData.Length);
                }
                catch (TimeoutException)
                {
                    MessageBox.Show(System.Reflection.MethodBase.GetCurrentMethod().Name + ' ' + "Timeout Exception");
                }
                catch (Exception ex)
                {
                    MessageBox.Show(System.Reflection.MethodBase.GetCurrentMethod().Name + ' ' + ex.Message);
                }
            }
        }
    }
}
