﻿using AssistPro.navtex.communication;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using WeifenLuo.WinFormsUI;
using AssistPro.navtex;
using static AssistPro.navtex.communication.Serial_Setting_Form;
using static AssistPro.navtex.communication.CommPort;
using AssistPro.Lib;
using static AssistPro.navtex.msg.Msg_Setting_Ini;

namespace AssistPro.navtex.msg
{
    public partial class Nav_Msg_Form : DockContent
    {
        public static Nav_Msg_Form _nav_msg_form;

        private CommPort m_CommPort = CommPort.Instance;
        private Audio_Wave_Control m_Audio_Wave_Control = Audio_Wave_Control.Instance;
        private Serial_RTS_Send_Mgr m_Serial_RTS_Send_Mgr = Serial_RTS_Send_Mgr.Instance;

        private Navtex_Message m_Navtex_Message = new Navtex_Message();
        public Nav_Msg_Form()
        {
            InitializeComponent();

            _nav_msg_form = this;

            Msg_Setting_Ini ini = new Msg_Setting_Ini();
            Msg_Setting_Info_c info = new Msg_Setting_Info_c();
            info = ini.StoreRead();
            textBox_FSK_Space.Text = info.strSpace;
            textBox_FSK_Mark.Text = info.strMark;
            textBox_FSK_BPS.Text = info.strBPS;
            TextBox_FSK_Data.Text = info.strMsg;
        }   

        private void button_Serial_Mark_Click(object sender, EventArgs e)
        {
            m_Serial_RTS_Send_Mgr.MarkOut();
        }

        private void button_Serial_Space_Click(object sender, EventArgs e)
        {
            m_Serial_RTS_Send_Mgr.SpaceOut();
        }

        private void button_Serial_Dot_Click(object sender, EventArgs e)
        {
            m_Serial_RTS_Send_Mgr.DotOut();
        }

        private void _Serial_Stop_Click(object sender, EventArgs e)
        {
            m_Serial_RTS_Send_Mgr.Stop();
        }

        private void Nav_Msg_Form_FormClosing(object sender, FormClosingEventArgs e)
        {
            this.Dispose();
            m_CommPort.Close((int)Serial_t.SERIAL_TYPE_MAIN);
            m_Serial_RTS_Send_Mgr.Stop();
        }

        private void button_FSK_DOT_Click(object sender, EventArgs e)
        {
            try
            {
                int space = Int32.Parse(textBox_FSK_Space.Text);
                int mark = Int32.Parse(textBox_FSK_Mark.Text);
                int bps = Int32.Parse(textBox_FSK_BPS.Text);

                if ((space >= 0 && space <= int.MaxValue) &&
                (mark >= 0 && mark <= int.MaxValue) &&
                (bps >= 0 && bps <= int.MaxValue))
                {
                    if (m_Audio_Wave_Control.isProcessing == false)
                    {
                        m_Audio_Wave_Control.DOT_Out(mark, space, bps);
                    }
                }
            }
            catch (Exception ex)
            {
                string s = string.Format("오류 발생 : {0}", ex.Message);
                MessageBox.Show(s);

                m_Audio_Wave_Control.WaveStop();
            }
        }

        private void button_FSK_Mark_Click(object sender, EventArgs e)
        {
            try
            {
                int mark = Int32.Parse(textBox_FSK_Mark.Text);

                if ((mark >= 0 && mark <= int.MaxValue))
                {
                    if (m_Audio_Wave_Control.isProcessing == false)
                    {
                        m_Audio_Wave_Control.Frequency_Out(mark);
                    }
                }
            }
            catch (Exception ex)
            {
                string s = string.Format("오류 발생 : {0}", ex.Message);
                MessageBox.Show(s);

                m_Audio_Wave_Control.WaveStop();
            }
        }

        private void button_FSK_Space_Click(object sender, EventArgs e)
        {
            try
            {
                int space = Int32.Parse(textBox_FSK_Space.Text);

                if ((space >= 0 && space <= int.MaxValue))
                {
                    if (m_Audio_Wave_Control.isProcessing == false)
                    {
                        m_Audio_Wave_Control.Frequency_Out(space);
                    }
                }
            }
            catch (Exception ex)
            {
                string s = string.Format("오류 발생 : {0}", ex.Message);
                MessageBox.Show(s);

                m_Audio_Wave_Control.WaveStop();
            }
        }

        private void button_FSK_Repeat_Click(object sender, EventArgs e)
        {
            try
            {
                int space = Int32.Parse(textBox_FSK_Space.Text);
                int mark = Int32.Parse(textBox_FSK_Mark.Text);
                int bps = Int32.Parse(textBox_FSK_BPS.Text);
                string strMsg = TextBox_FSK_Data.Text;

                if ((space >= 0 && space <= int.MaxValue) &&
                    (mark >= 0 && mark <= int.MaxValue) &&
                    (bps >= 0 && bps <= int.MaxValue) &&
                    (strMsg != null))
                {
                    if (m_Audio_Wave_Control.isProcessing == false)
                    {
                        m_Navtex_Message.Audio_MessageSend_repeat(mark, space, bps, 10, strMsg);
                    }
                }
            }
            catch (Exception ex)
            {
                string s = string.Format("오류 발생 : {0}", ex.Message);
                MessageBox.Show(s);

                m_Audio_Wave_Control.WaveStop();
            }
        }

        private void button_FSK_Msg_Click(object sender, EventArgs e)
        {
            try
            {
                int space = Int32.Parse(textBox_FSK_Space.Text);
                int mark = Int32.Parse(textBox_FSK_Mark.Text);
                int bps = Int32.Parse(textBox_FSK_BPS.Text);
                string strMsg = TextBox_FSK_Data.Text;

                if ((space >= 0 && space <= int.MaxValue) &&
                    (mark >= 0 && mark <= int.MaxValue) &&
                    (bps >= 0 && bps <= int.MaxValue) &&
                    (strMsg != null) )
                {
                    if (m_Audio_Wave_Control.isProcessing == false)
                    {
                        m_Navtex_Message.Audio_MessageSend(mark, space, bps, strMsg);
                    }
                }
            }
            catch (Exception ex)
            {
                string s = string.Format("오류 발생 : {0}", ex.Message);
                MessageBox.Show(s);

                m_Audio_Wave_Control.WaveStop();
            }
        }

        private void button_FSK_STOP_Click(object sender, EventArgs e)
        {
            m_Audio_Wave_Control.WaveStop();
        }

        private void DataCallBtn_Click(object sender, EventArgs e)
        {
            Msg_Setting_Ini ini = new Msg_Setting_Ini();
            Msg_Setting_Info_c info = new Msg_Setting_Info_c();

            info = ini.StoreRead();
            textBox_FSK_Space.Text = info.strSpace;
            textBox_FSK_Mark.Text = info.strMark;
            textBox_FSK_BPS.Text = info.strBPS;
            TextBox_FSK_Data.Text = info.strMsg;
        }

        private void DataSaveBtn_Click(object sender, EventArgs e)
        {
            Msg_Setting_Ini ini = new Msg_Setting_Ini();
            Msg_Setting_Info_c info = new Msg_Setting_Info_c();

            info.strSpace = textBox_FSK_Space.Text;
            info.strMark = textBox_FSK_Mark.Text;
            info.strBPS = textBox_FSK_BPS.Text;
            info.strMsg = TextBox_FSK_Data.Text;
            ini.StoreWrite(info);
        }

        private void button_Serial_Msg_Click(object sender, EventArgs e)
        {
            try
            {
                int bps = Int32.Parse(textBox_FSK_BPS.Text);
                string strMsg = TextBox_FSK_Data.Text;

                if ((bps >= 0 && bps <= int.MaxValue) &&
                    (strMsg != null))
                {
                    m_Navtex_Message.Serial_MessageSend(bps, strMsg);
                }
            }
            catch (Exception ex)
            {
                string s = string.Format("오류 발생 : {0}", ex.Message);
                MessageBox.Show(s);

                m_Serial_RTS_Send_Mgr.Stop();
            }
        }

        private void _Serial_Repeat_Click(object sender, EventArgs e)
        {
            try
            {
                int bps = Int32.Parse(textBox_FSK_BPS.Text);
                string strMsg = TextBox_FSK_Data.Text;

                if ((bps >= 0 && bps <= int.MaxValue) &&
                    (strMsg != null))
                {
                    m_Navtex_Message.Serial_MessageSend_repeat(bps, 10, strMsg);
                }
            }
            catch (Exception ex)
            {
                string s = string.Format("오류 발생 : {0}", ex.Message);
                MessageBox.Show(s);

                m_Serial_RTS_Send_Mgr.Stop();
            }
        }
    }
}
