﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using WeifenLuo.WinFormsUI;
using AssistPro.navtex.communication;
using static System.Windows.Forms.VisualStyles.VisualStyleElement;
using AssistPro.Lib;
using System.Threading;
using static AssistPro.navtex.communication.CommPort;

namespace AssistPro.navtex.monitor
{
    public partial class Interface_Monitor_Form : DockContent
    {
        static readonly Interface_Monitor_Form instance = new Interface_Monitor_Form();

        public static Interface_Monitor_Form Instance
        {
            get
            {
                return instance;
            }
        }

        CommProcess m_CommProc = CommProcess.Instance;
        Common _Common = Common.Instance;
        CommPort _CommPort = CommPort.Instance;
        ATimer m_gps_timer;
        ATimer m_print_timer;
        int Gps_On;
        int Print_On;
        int Print_tick;
        public Interface_Monitor_Form()
        {
            InitializeComponent();

            m_CommProc.Raw_Received_INS = Raw_Received_INS;
            m_CommProc.Raw_Received_BAM = Raw_Received_BAM;
            comboBox_If_Gps_Set.SelectedIndex = 0;
            m_gps_timer = new ATimer(2, 1000, Timer_GPS_Callback);

            comboBox_If_Print_Set.SelectedIndex = 0;
            m_print_timer = new ATimer(2, 1000, Timer_Print_Callback);

            Gps_On = 0;
            Print_On = 0;
            Print_tick = 0;
        }

        //private string Get_Combobox_If_Gps_Set()
        //{
        //    if(this.IsHandleCreated && !this.IsDisposed)
        //    {
        //    }
        //}

        public void Timer_Print_Callback()
        {
            DateTime date = DateTime.Today;
            DateTime time = DateTime.Now;
            string time_added_str = string.Empty;
            string combobox_str = string.Empty;
            byte[] print_init_arr = { 0x1B, 0x40 };
            byte[] print_font_set = { 0x1b, 0x21, 0x00 };
            byte[] print_data = Encoding.UTF8.GetBytes("0123456789012345678901234567890123456789");
            string print_data_str = Encoding.UTF8.GetString(print_data);

            if (InvokeRequired) // UI 스레드가 아니라면
            {
                Invoke(new Action(Timer_Print_Callback)); // UI 스레드에서 실행하도록 요청
                return;
            }

            if(Print_On == 1)
            {
                combobox_str = comboBox_If_Print_Set.Text.ToUpper();

                time_added_str = string.Format("({0:00}-{1:00}-{2:00} {3:00}:{4:00}:{5:00}) {6}\r\n", date.Year % 2000, date.Month, date.Day, time.Hour, time.Minute, time.Second, print_data_str);

                if (combobox_str == "INS")
                {
                    _CommPort.ArrSend(print_init_arr, (int)Serial_t.SERIAL_TYPE_INS);
                    _CommPort.ArrSend(print_font_set, (int)Serial_t.SERIAL_TYPE_INS);
                    _CommPort.ArrSend(print_data, (int)Serial_t.SERIAL_TYPE_INS);
                }
                else if (combobox_str == "BAM")
                {
                    _CommPort.ArrSend(print_init_arr, (int)Serial_t.SERIAL_TYPE_BAM);
                    _CommPort.ArrSend(print_font_set, (int)Serial_t.SERIAL_TYPE_BAM);
                    _CommPort.ArrSend(print_data, (int)Serial_t.SERIAL_TYPE_BAM);
                }
                else if (combobox_str == "MAIN")
                {
                    //_CommPort.ArrSend(print_init_arr, (int)Serial_t.SERIAL_TYPE_MAIN);
                    //_CommPort.ArrSend(print_font_set, (int)Serial_t.SERIAL_TYPE_MAIN);
                    _CommPort.ArrSend(print_data, (int)Serial_t.SERIAL_TYPE_MAIN);
                }

                textBox_If_Print_Out_Mon.AppendText(time_added_str);
            }
            else
            {
                Print_tick = 0;
            }
        }

        public void Timer_GPS_Callback()
        {
            DateTime date = DateTime.Today;
            DateTime time = DateTime.Now;
            string time_added_str = string.Empty;
            string combobox_str = string.Empty;
            string str = string.Empty;
            byte checksum = 0x00;

            if (InvokeRequired) // UI 스레드가 아니라면
            {
                Invoke(new Action(Timer_GPS_Callback)); // UI 스레드에서 실행하도록 요청
                return;
            }

            if (Gps_On == 1)
            {
                combobox_str = comboBox_If_Gps_Set.Text.ToUpper();

                str = string.Format("$GNRMC,{0:D6}.00,A,3510.46784,N,12907.62132,E,0.093,,{1:D6},,,D",  string.Format("{0:D2}{1:D2}{2:D2}", time.Hour, time.Minute, time.Second),
                                                                                                        string.Format("{0:D2}{1:D2}{2:D2}", date.Day, date.Month, date.Year % 2000) );
                checksum = _Common.check_sum(str.Substring(1));
                str += string.Format("*{0:X2}\r\n", checksum);
                time_added_str = string.Format("({0:00}-{1:00}-{2:00} {3:00}:{4:00}:{5:00}) {6}\r\n", date.Year % 2000, date.Month, date.Day, time.Hour, time.Minute, time.Second, str);

                if (combobox_str == "INS")
                {
                    _CommPort.StrSend(str, (int)Serial_t.SERIAL_TYPE_INS);
                }
                else if(combobox_str == "BAM")
                {
                    _CommPort.StrSend(str, (int)Serial_t.SERIAL_TYPE_BAM);
                }
                textBox_If_Gps_Out_Mon.AppendText(time_added_str);
            }
        }

        internal delegate void StringDelegate(string str);

        private void Raw_Received_INS(string str)
        {
            // Handle multi-threading
            if(this.IsHandleCreated && !this.IsDisposed)
            {
                if(InvokeRequired)
                {
                    Invoke(new StringDelegate(Raw_Received_INS), new object[] { str });
                    return;
                }

                DateTime date = DateTime.Today;
                DateTime time = DateTime.Now;
                string s = string.Format("({0:00}-{1:00}-{2:00} {3:00}:{4:00}:{5:00}) {6}\r\n", date.Year%2000, date.Month, date.Day, time.Hour, time.Minute, time.Second, str);
                textBox_If_INS_Out_Mon.AppendText(s);
            }
        }
        private void textBox_If_INS_Out_Mon_TextChanged(object sender, EventArgs e)
        {
            long len = textBox_If_INS_Out_Mon.Text.Trim().Length;

            textBox_If_INS_Out_Mon.SelectionStart = textBox_If_INS_Out_Mon.Text.Length;
            textBox_If_INS_Out_Mon.ScrollToCaret();

            if(len >= 220000)
            {
                textBox_If_INS_Out_Mon.Text = string.Empty;
            }
        }

        private void Raw_Received_BAM(string str)
        {
            // Handle multi-threading
            if (this.IsHandleCreated && !this.IsDisposed)
            {
                if (InvokeRequired)
                {
                    Invoke(new StringDelegate(Raw_Received_BAM), new object[] { str });
                    return;
                }

                DateTime date = DateTime.Today;
                DateTime time = DateTime.Now;
                string s = string.Format("({0:00}-{1:00}-{2:00} {3:00}:{4:00}:{5:00}) {6}\r\n", date.Year%2000, date.Month, date.Day, time.Hour, time.Minute, time.Second, str);
                textBox_If_BAM_Out_Mon.AppendText(s);
            }
        }
        private void textBox_If_BAM_Out_Mon_TextChanged(object sender, EventArgs e)
        {
            long len = textBox_If_BAM_Out_Mon.Text.Trim().Length;

            textBox_If_BAM_Out_Mon.SelectionStart = textBox_If_BAM_Out_Mon.Text.Length;
            textBox_If_BAM_Out_Mon.ScrollToCaret();

            if (len >= 220000)
            {
                textBox_If_BAM_Out_Mon.Text = string.Empty;
            }
        }

        private void textBox_If_Gps_Out_Mon_TextChanged(object sender, EventArgs e)
        {
            long len = textBox_If_Gps_Out_Mon.Text.Trim().Length;

            textBox_If_Gps_Out_Mon.SelectionStart = textBox_If_Gps_Out_Mon.Text.Length;
            textBox_If_Gps_Out_Mon.ScrollToCaret();

            if (len >= 220000)
            {
                textBox_If_Gps_Out_Mon.Text = string.Empty;
            }
        }

        private void button_If_Gps_Out_Click(object sender, EventArgs e)
        {
            if(Gps_On == 0)
            {
                button_If_Gps_Out.Text = "Running";
                button_If_Gps_Out.BackColor = Color.FromArgb(0, 255, 0);
                Gps_On = 1;
                m_gps_timer.Start();
            }
            else
            {
                button_If_Gps_Out.Text = "GPS Out";
                button_If_Gps_Out.BackColor = Color.FromArgb(192, 192, 192);
                Gps_On = 0;
            }
            
        }

        private void button_If_Print_Out_Click(object sender, EventArgs e)
        {
            if(Print_On == 0)
            {
                button_If_Print_Out.Text = "Running";
                button_If_Print_Out.BackColor = Color.FromArgb(0, 255, 0);
                Print_On = 1;
                m_print_timer.Start();
            }
            else
            {
                button_If_Print_Out.Text = "Print Data Out";
                button_If_Print_Out.BackColor = Color.FromArgb(192, 192, 192);
                Print_On = 0;
            }
        }
    }
}
