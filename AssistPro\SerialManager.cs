﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.IO.Ports;


namespace AssistPro
{
    internal class SerialManager
    {
        public enum BUFF_STATUS
        {
            BUFF_OK = 0,
            BUFF_FULL = -1,
            BUFF_EMPTY = -2,
            BUFF_NULL_DATA = -3,
        }

        private SerialPort m_serialPort;
        private int m_RxBufHeadIdx;
        private int m_RxBufTailIdx;
        private byte[] m_ComRxBuf;    // COM Rx 버퍼

        public SerialManager(string portName, int baudRate)
        {
            m_serialPort = new SerialPort(portName, baudRate);
            m_serialPort.ReadTimeout = 5000;
            m_serialPort.WriteTimeout = 5000;
            m_serialPort.ReadBufferSize = 8192;
            m_serialPort.WriteBufferSize = 8192;
            m_serialPort.Parity = Parity.None;
            m_serialPort.StopBits = StopBits.One;
            m_serialPort.DataBits = 8;
            m_serialPort.Handshake = Handshake.None;

            m_RxBufHeadIdx = 0;
            m_RxBufTailIdx = 0;

            m_ComRxBuf = new byte[4096];
        }

        public void init()
        {
            Array.Clear(m_ComRxBuf, 0, m_ComRxBuf.Length);
            m_RxBufHeadIdx = 0;
            m_RxBufTailIdx = 0;
        }

        public int GetSerialDataSize()
        {
            return m_serialPort.BytesToRead;
        }

        public BUFF_STATUS CheckRxBuffFull()
        {
            if (m_RxBufTailIdx == m_RxBufHeadIdx +1)
            {
                return BUFF_STATUS.BUFF_FULL;
            }
            return BUFF_STATUS.BUFF_OK;
        }

        private BUFF_STATUS CheckRxBuffEmpty()
        {
            if (m_RxBufTailIdx == m_RxBufHeadIdx)
            {
                return BUFF_STATUS.BUFF_EMPTY;
            }
            return BUFF_STATUS.BUFF_OK;
        }

        public void Enqueue()
        {
            int size = m_serialPort.BytesToRead;

            if (CheckRxBuffFull() != BUFF_STATUS.BUFF_FULL)
            {
                if (m_ComRxBuf.Length > (size + m_RxBufHeadIdx))
                {
                    m_serialPort.Read(m_ComRxBuf, m_RxBufHeadIdx, size);
                    m_RxBufHeadIdx += size;
                }
                else
                {
                    for (int i = 0; i < size; i++)
                    {
                        m_serialPort.Read(m_ComRxBuf, m_RxBufHeadIdx, 1);
                        m_RxBufHeadIdx++;

                        if (m_RxBufHeadIdx >= m_ComRxBuf.Length)
                        {
                            m_RxBufHeadIdx = 0;
                        }
                    }
                }
            }
            else
            {
                Console.WriteLine("Rx Buffer is Full");
            }
        }

        public BUFF_STATUS Dequeue(ref byte data)
        {
            BUFF_STATUS ret = BUFF_STATUS.BUFF_NULL_DATA;
            if (CheckRxBuffEmpty() != BUFF_STATUS.BUFF_EMPTY)
            {
                data = m_ComRxBuf[m_RxBufTailIdx];
                m_RxBufTailIdx++;
                if (m_RxBufTailIdx >= m_ComRxBuf.Length)
                {
                    m_RxBufTailIdx = 0;
                }
                ret = BUFF_STATUS.BUFF_OK;
            }
            return ret;
        }

        public void Start()
        {
            m_serialPort.Open();
        }

        public void Stop()
        {
            m_serialPort.Close();
        }

        public SerialPort GetInstance()
        {
            return m_serialPort;
        }

    }
}
