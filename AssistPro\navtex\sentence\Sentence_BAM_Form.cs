﻿using AssistPro.Lib;
using AssistPro.navtex.communication;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using WeifenLuo.WinFormsUI;
using static AssistPro.navtex.communication.CommPort;

namespace AssistPro.navtex.sentence
{
    public partial class Sentence_BAM_Form : DockContent
    {
        static readonly Sentence_BAM_Form instance = new Sentence_BAM_Form();

        private CommPort m_CommPort = CommPort.Instance;
        private CommProcess m_CommProc = CommProcess.Instance;
        private Common m_Common = Common.Instance;
        private UdpPort m_UdpPort = UdpPort.Instance;

        public static Sentence_BAM_Form Instance
        {
            get
            {
                return instance;
            }
        }

        public Sentence_BAM_Form()
        {
            InitializeComponent();

            rb_61162_1_2.Checked = true;
            tb_450_line_count.Text = string.Format("1");

            m_CommProc.Raw_Received_BAM_Mon = Raw_Received_BAM_Mon;
        }

        internal delegate void StringDelegate(string str);
        private void Raw_Received_BAM_Mon(string str)
        {
            if (this.IsHandleCreated && !this.IsDisposed)
            {
                if (InvokeRequired)
                {
                    Invoke(new StringDelegate(Raw_Received_BAM_Mon), new object[] { str });
                    return;
                }
            }

            m_BAM_monitor.AppendText(string.Format("Recv : ") + str);
        }

        private byte CalcurateChecksum(string nmeaStr)
        {
            string n = nmeaStr.StartsWith("$") ? nmeaStr.Substring(1) : nmeaStr;

            byte chk = 0;
            int index = 0;

            while ( (index < n.Length) && (n[index] != '*') && (n[index] != '\n') )
            {
                if(index > 70)
                {
                    // Sentence too long
                    return 0;
                }
                chk ^= (byte)n[index++];
            }
            return chk;
        }

        private string ComposeTagblock()
        {
            string tag_body = string.Empty;

            if(rb_61162_1_2.Checked == false)
            {
                string header = "UdPbC" + "\0";

                if(cb_s_enable.Checked == true)
                {
                    string tag_body_src = string.IsNullOrEmpty(tb_450_src.Text) ? ("s:") : ("s:" + tb_450_src.Text);
                    tag_body += tag_body_src;
                }

                if(cb_d_enable.Checked == true)
                {
                    string tag_body_dst = string.IsNullOrEmpty(tb_450_dst.Text) ? ("d:") : ("d:" + tb_450_dst.Text);
                    if(tag_body == "")
                    {
                        tag_body += tag_body_dst;
                    }
                    else
                    {
                        tag_body += "," + tag_body_dst;
                    }
                }

                if(cb_n_enable.Checked == true)
                {
                    int n;
                    try
                    {
                        n = int.Parse(tb_450_line_count.Text);
                    }
                    catch (Exception e)
                    {
                        tb_450_line_count.Text = "1";
                        n = 1;
                    }

                    string tag_body_line_count = string.Format("n:{0}", n);
                    if(tag_body == "")
                    {
                        tag_body += tag_body_line_count;
                    }
                    else
                    {
                        tag_body += "," + tag_body_line_count;
                    }

                    tb_450_line_count.Text = string.Format("{0}", n + 1);
                }

                byte checksum = CalcurateChecksum(tag_body);
                string m_tag_block = header + "\\" + tag_body + "*" + checksum.ToString("X2") + "\\";

                return m_tag_block;
            }

            return "";
        }

        private void bt_send_ack_Click(object sender, EventArgs e)
        {
            string talker_id = string.Empty;
            string msg = string.Empty;
            string send_msg = string.Empty;

            talker_id = m_BAM_talk_id_textbox.Text;
            if (talker_id == string.Empty)
            {
                talker_id = "--";
            }

            msg = string.Format("${0}ACK,{1}", talker_id, tb_alert_id.Text);
            byte checksum = m_Common.check_sum(msg.Substring(1));
            msg += string.Format("*{0:X2}\r\n", checksum);

            if (rb_61162_1_2.Checked)
            {
                send_msg = msg;
                m_CommPort.StrSend(send_msg, (int)Serial_t.SERIAL_TYPE_BAM);

                m_BAM_monitor.AppendText(string.Format("Send : ") + send_msg);
            }
            else
            {
                send_msg = ComposeTagblock() + msg;
                m_UdpPort.StrSend(send_msg);

                send_msg = send_msg.Replace("\0", "\\0");
                m_BAM_monitor.AppendText(string.Format("Send : ") + send_msg);
            }
        }

        private void bt_run_Click(object sender, EventArgs e)
        {

        }

        private void bt_acn_send_Click(object sender, EventArgs e)
        {
            DateTime utc_now = DateTime.UtcNow;
            string send_msg = string.Empty;
            string msg = string.Empty;
            string cmd = string.Empty;
            string talker_id = string.Empty;

            talker_id = m_BAM_talk_id_textbox.Text;
            if (talker_id == string.Empty)
            {
                talker_id = "--";
            }

            // intellian's mnemonic code is INL
            msg = string.Format("${0}ACN,{1},{2},{3},{4},{5},{6}",
                                talker_id,
                                string.Format("{0:D2}{1:D2}{2:D2}.{3:D2}", utc_now.Hour, utc_now.Minute, utc_now.Second, utc_now.Millisecond/10),
                                "INL",
                                tb_acn_alert_id.Text,
                                tb_acn_alert_inst.Text,
                                cb_cmd.Text[0],
                                "C");
            byte checksum = m_Common.check_sum(msg.Substring(1));
            msg += string.Format("*{0:X2}\r\n", checksum);

            if (rb_61162_1_2.Checked)
            {
                send_msg = msg;
                m_CommPort.StrSend(send_msg, (int)Serial_t.SERIAL_TYPE_BAM);

                m_BAM_monitor.AppendText(string.Format("Send : ") + send_msg);
            }
            else
            {
                send_msg = ComposeTagblock() + msg;
                m_UdpPort.StrSend(send_msg);

                send_msg = send_msg.Replace("\0", "\\0");
                m_BAM_monitor.AppendText(string.Format("Send : ") + send_msg);
            }
        }

        private void bt_alr_send_Click(object sender, EventArgs e)
        {
            DateTime utc_now = DateTime.UtcNow;
            string send_msg = string.Empty;
            string msg = string.Empty;
            string talker_id = string.Empty;

            talker_id = m_BAM_talk_id_textbox.Text;
            if(talker_id == string.Empty)
            {
                talker_id = "--";
            }

            // intellian's mnemonic code is INL
            msg = string.Format("${0}ALR,{1},{2},{3},{4},{5}",
                                talker_id,
                                string.Format("{0:D2}{1:D2}{2:D2}.{3:D2}", utc_now.Hour, utc_now.Minute, utc_now.Second, utc_now.Millisecond / 10),
                                tb_alr_alert_id.Text,
                                cb_alr_status.Text[0],
                                cb_alr_ack.Text[0],
                                tb_alr_desc.Text);
            byte checksum = m_Common.check_sum(msg.Substring(1));
            msg += string.Format("*{0:X2}\r\n", checksum);

            if (rb_61162_1_2.Checked)
            {
                send_msg = msg;
                m_CommPort.StrSend(send_msg, (int)Serial_t.SERIAL_TYPE_BAM);

                m_BAM_monitor.AppendText(string.Format("Send : ") + send_msg);
            }
            else
            {
                send_msg = ComposeTagblock() + msg;
                m_UdpPort.StrSend(send_msg);

                send_msg = send_msg.Replace("\0", "\\0");
                m_BAM_monitor.AppendText(string.Format("Send : ") + send_msg);
            }
        }
    }
}