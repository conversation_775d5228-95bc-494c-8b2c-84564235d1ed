﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO.Ports;
using System.Linq;
using System.Net.Sockets;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using WeifenLuo.WinFormsUI;
using AssistPro.Lib;
using AssistPro.navtex.communication;
using static AssistPro.SerialManager;
using AssistPro.navtex.msg;
using static AssistPro.navtex.communication.CommProcess_Map;
using System.Threading;

namespace AssistPro
{
    public partial class BerTestForm : DockContent
    {
        private SerialManager m_SerialManager;
        private SerialPort m_SerialPort;
        private UdpClient udpclient;
        IPEndPoint remoteEP;

        private Common _Common = Common.Instance;

        public class Packet_c
        {
            public int RxCnt = 0;
            public int TxCnt = 0;
            public byte[] TxBuffer = new byte[50000];
            public byte[] RxBuffer = new byte[50000];
            public int len = 0;
            public int sn = 0;
            public int cmd = 0;
            public int param = 0;
        }
        public Packet_c CommData = new Packet_c();

        public BerTestForm()
        {
            InitializeComponent();

            cb_CommList.Enabled = true;
            tb_sync_clock_offset_cnt.Text = string.Format("0");
            tb_multi_send_cnt.Text = string.Format("0");
        }

        private void ts_ip_connect_Click(object sender, EventArgs e)
        {
            try
            {
                if (ts_ip_addr.Text == "")
                {
                    MessageBox.Show(" Please enter IP address. ");
                    return;
                }

                if (ts_port.Text == "")
                {
                    MessageBox.Show(" Please enter port number. ");
                    return;
                }

                if (ts_ip_connect.Text == "CONNECT")
                {
                    udpclient = new UdpClient();
                    IPAddress multicastaddress = IPAddress.Parse(ts_ip_addr.Text);
                    udpclient.JoinMulticastGroup(multicastaddress);
                    remoteEP = new IPEndPoint(multicastaddress, int.Parse(ts_port.Text));
                    ts_ip_connect.Text = "DISCONNECT";
                    ts_ip_connect.BackColor = Color.PaleGreen;
                }
                else
                {
                    udpclient.Close();
                    ts_ip_connect.Text = "CONNECT";
                    ts_ip_connect.BackColor = Color.Silver;
                }
            }
            catch (Exception ea)
            {
                MessageBox.Show(ea.Message);
                ts_ip_connect.BackColor = Color.Silver;
                ts_ip_connect.Text = "CONNECT";
            }
        }

        private void btn_ConnectIO_Click(object sender, EventArgs e)
        {
            try
            {
                if (cb_CommList.Text == "")
                {
                    MessageBox.Show(" You need select port. ");
                    return;
                }

                if (cb_CommList.Enabled)
                {
                    m_SerialManager = new SerialManager(cb_CommList.Text, int.Parse(cb_baudrate.Text));

                    m_SerialPort = m_SerialManager.GetInstance();
                    m_SerialPort.DataReceived += serialPort_DataReceived;

                    m_SerialPort.Open();
                    btn_ConnectIO.Text = "DISCONNECT";
                    cb_CommList.Enabled = false;
                    btn_ConnectIO.BackColor = Color.PaleGreen;
                }
                else
                {
                    m_SerialPort.Close();
                    btn_ConnectIO.Text = "CONNECT";
                    cb_CommList.Enabled = true;
                    if (ts_ip_connect.Text == "CONNECT")
                    btn_ConnectIO.BackColor = Color.Silver;
                }
            }
            catch (Exception ea)
            {
                MessageBox.Show(ea.Message);
                cb_CommList.Enabled = true;
                btn_ConnectIO.BackColor = Color.Silver;
                btn_ConnectIO.Text = "CONNECT";
            }
        }

        private void serialPort_DataReceived(object sender, SerialDataReceivedEventArgs e)
        {
            this.Invoke(new EventHandler(SerialData_Received));
        }

        private async void SerialData_Received(object s, EventArgs e)
        {
            int size = m_SerialPort.BytesToRead;
            byte[] buff = new byte[size];

            await m_SerialPort.BaseStream.ReadAsync(buff, 0, buff.Length);

            try
            {
                for (int i = 0; i < buff.Length; i++)
                {
                    CommData.RxBuffer[CommData.RxCnt++] = buff[i];
                    if (CommData.RxCnt >= CommData.RxBuffer.Length)
                    {
                        CommData.RxCnt = 0;
                        Array.Clear(CommData.RxBuffer, 0, CommData.RxBuffer.Length);
                    }

                    if (CommData.RxBuffer[0] == '!' && CommData.RxBuffer[1] == '@' && CommData.RxBuffer[2] == '#' && CommData.RxBuffer[3] == '$')
                    {
                        if (CommData.RxCnt == 12)
                        {
                            CommData.len = CommData.RxBuffer[4];
                            CommData.len += CommData.RxBuffer[5] << 8;

                            CommData.sn = CommData.RxBuffer[6];
                            CommData.sn += CommData.RxBuffer[7] << 8;

                            CommData.cmd = CommData.RxBuffer[8];
                            CommData.cmd += CommData.RxBuffer[9] << 8;

                            CommData.param = CommData.RxBuffer[10];
                            CommData.param += CommData.RxBuffer[11] << 8;
                        }
                        else if(CommData.RxCnt > 12)
                        { 
                            if (CommData.RxCnt >= (CommData.len + 6))
                            {
                                ushort crc = _Common.nav_crc16_calc(0xFFFF, CommData.RxBuffer, CommData.len);
                                byte crc_L = Convert.ToByte((crc) & 0x00FF);
                                byte crc_H = Convert.ToByte((crc >> 8) & 0x00FF);
                                int len = CommData.len;

                                if (CommData.RxBuffer[len + 4] == crc_L && CommData.RxBuffer[len + 5] == crc_H)
                                {
                                    procCommRx(CommData.RxBuffer);
                                }

                                CommData.RxCnt = 0;
                                Array.Clear(CommData.RxBuffer, 0, CommData.RxBuffer.Length);
                            }
                        }
                    }
                    else
                    {
                        if(CommData.RxCnt >= 4)
                        {
                            CommData.RxBuffer[0] = CommData.RxBuffer[1];
                            CommData.RxBuffer[1] = CommData.RxBuffer[2];
                            CommData.RxBuffer[2] = CommData.RxBuffer[3];
                            CommData.RxCnt = 3;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(System.Reflection.MethodBase.GetCurrentMethod().Name + ' ' + ex.Message);
            }
        }

        private void procCommRx(byte[] dataIn)
        {
            long total_bit = 0;
            long error_bit = 0;
            long loss = 0;

            byte[] recvByte = new byte[CommData.len];
            try
            {
                Array.Copy(CommData.RxBuffer, 12, recvByte, 0, CommData.len - 8);
            }
            catch (Exception ex)
            {
                return;
            }

            if(CommData.len == 8)
            {
                return;
            }

            switch (CommData.cmd)
            {
                case (int)0x01:
                    long data_len = (recvByte[0]) + (recvByte[1] << 8);
                    long error_rate = (recvByte[2]) + (recvByte[3] << 8);
                    byte[] data_bit = new byte[data_len];
                    Array.Copy(recvByte, 4, data_bit, 0, data_len);

                    string data_bit_str = string.Join("", data_bit);
                    string datetime_str = DateTime.Now.ToString("HH:mm:ss");
                    string data_len_str = string.Format("{0}", data_len);
                    string error_rate_str = string.Format("{0}.{1}%", error_rate/10 , error_rate%10);

                    int a = 0;
                    a = 5;

                    try
                    {
                        int new_row_index = dgvRxBer.Rows.Add(  datetime_str,
                                                                "",
                                                                data_len_str,
                                                                error_rate_str,
                                                                data_bit_str);
                        dgvRxBer.FirstDisplayedScrollingRowIndex = new_row_index;
                        m_monitor.AppendText(string.Format("Recv : Single tested\r\n"));
                    }
                    catch (Exception ex) 
                    {
                        m_monitor.AppendText(string.Format("Recv : [Single] {0}\r\n", ex.Message));
                        return; 
                    }

                break;

                case (int)0x02:
                    try
                    {
                        total_bit = (recvByte[0]) + (recvByte[1] << 8) + (recvByte[2] << 16) + (recvByte[3] << 24);
                        error_bit = (recvByte[4]) + (recvByte[5] << 8) + (recvByte[6] << 16) + (recvByte[7] << 24);
                        loss = (error_bit * 1000) / total_bit;
                        m_monitor.AppendText(string.Format("Recv : Repeat tested\r\n"));
                    }
                    catch (Exception ex)
                    {
                        m_monitor.AppendText(string.Format("Recv : [Repeat] {0}\r\n", ex.Message));
                        return; 
                    }

                    lb_total_bit.Text = string.Format("{0}", total_bit);
                    lb_error_bit.Text = string.Format("{0}", error_bit);
                    lb_loss.Text = string.Format("{0}.{1}%", loss/10, loss%10);
                    break;

                case (int)0x03:
                    try
                    {
                        total_bit = (recvByte[0]) + (recvByte[1] << 8) + (recvByte[2] << 16) + (recvByte[3] << 24);
                        error_bit = (recvByte[4]) + (recvByte[5] << 8) + (recvByte[6] << 16) + (recvByte[7] << 24);
                        loss = (error_bit * 1000) / total_bit;
                        m_monitor.AppendText(string.Format("Recv : Multi tested\r\n"));
                    }
                    catch (Exception ex)
                    {
                        m_monitor.AppendText(string.Format("Recv : [Multi] {0}\r\n", ex.Message));
                        return;
                    }

                    lb_total_bit.Text = string.Format("{0}", total_bit);
                    lb_error_bit.Text = string.Format("{0}", error_bit);
                    lb_loss.Text = string.Format("{0}.{1}%", loss / 10, loss % 10);
                    break;


            }
        }

        private void BerTestForm_Load(object sender, EventArgs e)
        {
            int rowIndex = 0;

            rb_mfhf.Checked = true;
            rb_vhf.Checked = false;

            foreach (string name in SerialPort.GetPortNames())
            {
                cb_CommList.Items.Add(name);
            }

            //this.dgvRxBer.Font = new Font("휴먼모음T", 10, FontStyle.Regular);
        }

        private bool HasInvalidCharacters(string str_data)
        {
            int cnt = 0;
            foreach(char c in str_data)
            {
                if(c != '0' && c != '1')
                {
                    MessageBox.Show("Check if there is data other than 0 or 1\r\nAlso check if there are invalid characters (including spaces)");
                    return false;
                }
                cnt++;
            }

            if(cnt == 0)
            {
                MessageBox.Show("Data not found");
                return false;
            }
            else if(cnt > 1000)
            {
                MessageBox.Show("Character limit exceeded (1000)");
                return false;
            }

            return true;
        }

        private bool HasInvalidNumber(string str_data)
        {
            int num = 0;

            try
            {
                num = int.Parse(str_data);
            }
            catch(Exception ex)
            {
                MessageBox.Show(string.Format("{0}", ex));
                return false;                
            }
            return true;
        }

        private void ber_protocol_send(int cmd, byte[] data, string log)
        {
            if (m_SerialPort != null && m_SerialPort.IsOpen)
            {
                int param = 0x0;
                int i = 0;
                ushort crc = 0;

                byte[] txdata = new byte[4 + 8 + 2 + data.Length]; // start, head, crc, data

                // start
                txdata[0] = Convert.ToByte('!');
                txdata[1] = Convert.ToByte('@');
                txdata[2] = Convert.ToByte('#');
                txdata[3] = Convert.ToByte('$');

                // header
                txdata[4] = Convert.ToByte((txdata.Length - 6) & 0xFF);
                txdata[5] = Convert.ToByte(((txdata.Length - 6) << 8) & 0xFF);
                txdata[6] = 0;
                txdata[7] = 0;
                txdata[8] = Convert.ToByte((cmd) & 0xFF);
                txdata[9] = Convert.ToByte(((cmd) << 8) & 0xFF);
                txdata[10] = Convert.ToByte((param) & 0xFF);
                txdata[11] = Convert.ToByte(((param) << 8) & 0xFF);

                // data
                for (i = 0; i < data.Length; i++)
                {
                    txdata[i + 12] = data[i];
                }

                // crc
                crc = _Common.nav_crc16_calc(0xFFFF, txdata, (txdata.Length - 6));
                txdata[4 + 8 + data.Length] = Convert.ToByte((crc) & 0x00FF);
                txdata[4 + 8 + data.Length + 1] = Convert.ToByte((crc >> 8) & 0x00FF);

                m_SerialPort.Write(txdata, 0, txdata.Length);

                m_monitor.AppendText(string.Format("Send : {0}\r\n", log));
            }
        }


        private void bt_dot_out_Click(object sender, EventArgs e)
        {
            lb_total_bit.Text = "0";
            lb_error_bit.Text = "0";
            lb_loss.Text = "0%";

            byte[] data = new byte[4];
            Array.Clear(data, 0, data.Length);
            data[0] = 3;

            ber_protocol_send(0x03, data, "Dot out");
        }

        private void bt_space_out_Click(object sender, EventArgs e)
        {
            lb_total_bit.Text = "0";
            lb_error_bit.Text = "0";
            lb_loss.Text = "0%";

            byte[] data = new byte[4];
            Array.Clear(data, 0, data.Length);
            data[0] = 2;

            ber_protocol_send(0x03, data, "Space out");
        }

        private void bt_mark_out_Click(object sender, EventArgs e)
        {
            lb_total_bit.Text = "0";
            lb_error_bit.Text = "0";
            lb_loss.Text = "0%";

            byte[] data = new byte[4];
            Array.Clear(data, 0, data.Length);
            data[0] = 1;

            ber_protocol_send(0x03, data, "Mark out");
        }

        private void bt_stop_Click(object sender, EventArgs e)
        {
            lb_total_bit.Text = "0";
            lb_error_bit.Text = "0";
            lb_loss.Text = "0%";

            byte[] data = new byte[4];
            Array.Clear(data, 0, data.Length);
            data[0] = 0;

            ber_protocol_send(0x03, data, "Stop");
        }

        private void bt_single_send_Click(object sender, EventArgs e)
        {
            lb_total_bit.Text = "0";
            lb_error_bit.Text = "0";
            lb_loss.Text = "0%";

            if (HasInvalidCharacters(tb_pattern_data.Text) == false)
            {
                return;
            }

            byte[] data = new byte[6 + tb_pattern_data.Text.Length];
            Array.Clear(data, 0, data.Length);

            data[0] = 1;
            data[4] = (byte) (tb_pattern_data.Text.Length        & 0x00FF);
            data[5] = (byte) ((tb_pattern_data.Text.Length >> 8) & 0x00FF);

            for(int i=0; i<tb_pattern_data.Text.Length; i++)
            {
                data[i + 6] = (byte)(tb_pattern_data.Text[i]-'0');
            }

            ber_protocol_send(0x04, data, "Single Send");
        }

        private void bt_repeat_send_Click(object sender, EventArgs e)
        {
            lb_total_bit.Text = "0";
            lb_error_bit.Text = "0";
            lb_loss.Text = "0%";

            if (HasInvalidCharacters(tb_pattern_data.Text) == false)
            {
                return;
            }

            byte[] data = new byte[6 + tb_pattern_data.Text.Length];
            Array.Clear(data, 0, data.Length);

            data[0] = 1;
            data[4] = (byte)(tb_pattern_data.Text.Length & 0x00FF);
            data[5] = (byte)((tb_pattern_data.Text.Length >> 8) & 0x00FF);

            for (int i = 0; i < tb_pattern_data.Text.Length; i++)
            {
                data[i + 6] = (byte)(tb_pattern_data.Text[i] - '0');
            }

            ber_protocol_send(0x05, data, "Repeat Send");
        }

        private void bt_pn9_gen_Click(object sender, EventArgs e)
        {
            int num = 9;
            Random random = new Random();
            int[] binary_data = new int[num];

            for (int i = 0; i < num; i++)
            {
                binary_data[i] = random.Next(0, 2);
            }
            tb_pattern_data.Text = string.Join("", binary_data);
        }

        private void bt_pn11_gen_Click(object sender, EventArgs e)
        {
            int num = 11;
            Random random = new Random();
            int[] binary_data = new int[num];

            for (int i = 0; i < num; i++)
            {
                binary_data[i] = random.Next(0, 2);
            }
            tb_pattern_data.Text = string.Join("", binary_data);
        }

        private void bt_pn23_gen_Click(object sender, EventArgs e)
        {
            int num = 23;
            Random random = new Random();
            int[] binary_data = new int[num];

            for (int i = 0; i < num; i++)
            {
                binary_data[i] = random.Next(0, 2);
            }
            tb_pattern_data.Text = string.Join("", binary_data);
        }

        private void bt_pn100_gen_Click(object sender, EventArgs e)
        {
            int num = 100;
            Random random = new Random();
            int[] binary_data = new int[num];

            for (int i = 0; i < num; i++)
            {
                binary_data[i] = random.Next(0, 2);
            }
            tb_pattern_data.Text = string.Join("", binary_data);
        }

        private void bt_pn200_gen_Click(object sender, EventArgs e)
        {
            int num = 200;
            Random random = new Random();
            int[] binary_data = new int[num];

            for (int i = 0; i < num; i++)
            {
                binary_data[i] = random.Next(0, 2);
            }
            tb_pattern_data.Text = string.Join("", binary_data);
        }

        private void bt_model_select_Click(object sender, EventArgs e)
        {
            byte[] data = new byte[12];
            Array.Clear(data, 0, data.Length);

            if(rb_mfhf.Checked)
            {
                data[0] = 0;
            }
            else if(rb_vhf.Checked)
            {
                data[0] = 1;
            }

            if(HasInvalidNumber(tb_sync_clock_offset_cnt.Text) == false)
            {
                return;
            }
            if(HasInvalidNumber(tb_multi_send_cnt.Text) == false)
            {
                return;
            }

            int offset_cnt = int.Parse(tb_sync_clock_offset_cnt.Text);
            int multi_send_cnt = int.Parse(tb_multi_send_cnt.Text);

            data[4] = (byte) (offset_cnt & 0x000000FF);
            data[5] = (byte)((offset_cnt >> 8) & 0x000000FF);
            data[6] = (byte)((offset_cnt >> 16) & 0x000000FF);
            data[7] = (byte)((offset_cnt >> 24) & 0x000000FF);

            data[8] = (byte)(multi_send_cnt & 0x000000FF);
            data[9] = (byte)((multi_send_cnt >> 8) & 0x000000FF);
            data[10] = (byte)((multi_send_cnt >> 16) & 0x000000FF);
            data[11] = (byte)((multi_send_cnt >> 24) & 0x000000FF);

            ber_protocol_send(0x06, data, "Config data");
        }

        private void bt_multi_send_Click(object sender, EventArgs e)
        {
            lb_total_bit.Text = "0";
            lb_error_bit.Text = "0";
            lb_loss.Text = "0%";

            if (HasInvalidCharacters(tb_pattern_data.Text) == false)
            {
                return;
            }

            byte[] data = new byte[6 + tb_pattern_data.Text.Length];
            Array.Clear(data, 0, data.Length);

            data[0] = 1;
            data[4] = (byte)(tb_pattern_data.Text.Length & 0x00FF);
            data[5] = (byte)((tb_pattern_data.Text.Length >> 8) & 0x00FF);

            for (int i = 0; i < tb_pattern_data.Text.Length; i++)
            {
                data[i + 6] = (byte)(tb_pattern_data.Text[i] - '0');
            }

            ber_protocol_send(0x07, data, "Multi Send");
        }
    }
}

