﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using AssistPro.Lib;
using AssistPro.navtex;
using AssistPro.navtex.monitor;
using AssistPro.navtex.msg;
using AssistPro.navtex.sentence;
using static AssistPro.navtex.communication.CommProcess_Map;

namespace AssistPro.navtex.communication
{
    public sealed class CommProcess
    {
        static readonly CommProcess instance = new CommProcess();
        CommPort com = CommPort.Instance;
        CommProcess_Map m_CommData = CommProcess_Map.Instance;
        Common m_Common = Common.Instance;

        public static CommProcess Instance
        {
            get
            {
                return instance;
            }
        }

        CommProcess()
        {
            com.Received_data_Main += Received_data_Main;
            com.Received_data_INS += Received_data_INS;
            com.Received_data_BAM += Received_data_BAM;
            com.Received_data_Mon_1 += Received_data_Mon_1;
            com.Received_data_Mon_2 += Received_data_Mon_2;
            com.Received_data_Mon_3 += Received_data_Mon_3;
        }

        public delegate void StringHandler(string str);
        public StringHandler NMEA_Received_INS;
        public StringHandler NMEA_Received_Mon_1;
        public StringHandler NMEA_Received_Mon_2;
        public StringHandler NMEA_Received_Mon_3;

        public StringHandler Raw_Received_INS;
        public StringHandler Raw_Received_CRQ_Mon;
        public StringHandler Raw_Received_BAM;
        public StringHandler Raw_Received_BAM_Mon;

        // @brief : Received main port packet processing
        private void Received_data_Main(byte[] dataIn)
        {
            try
            {
                for(int i=0; i<dataIn.Length; i++)
                {
                    m_CommData.Debug.RxBuffer[m_CommData.Debug.RxCnt++] = dataIn[i];
                    if(m_CommData.Debug.RxCnt >= m_CommData.Debug.RxBuffer.Length)
                    {
                        m_CommData.Debug.RxCnt = 0;
                        Array.Clear(m_CommData.Debug.RxBuffer, 0, m_CommData.Debug.RxBuffer.Length);
                    }

                    if (m_CommData.Debug.RxBuffer[0] == '$')
                    {
                        if(m_CommData.Debug.RxCnt >= 9)
                        {
                            m_CommData.Debug.len = m_CommData.Debug.RxBuffer[1];
                            m_CommData.Debug.len += m_CommData.Debug.RxBuffer[2] << 8;

                            m_CommData.Debug.sn = m_CommData.Debug.RxBuffer[3];
                            m_CommData.Debug.sn += m_CommData.Debug.RxBuffer[4] << 8;

                            m_CommData.Debug.cmd = m_CommData.Debug.RxBuffer[5];
                            m_CommData.Debug.cmd += m_CommData.Debug.RxBuffer[6] << 8;

                            m_CommData.Debug.param = m_CommData.Debug.RxBuffer[7];
                            m_CommData.Debug.param += m_CommData.Debug.RxBuffer[8] << 8;

                            if(m_CommData.Debug.RxCnt >= (m_CommData.Debug.len + 3))
                            {
                                ushort crc = m_Common.crc16_calc(0xFFFF, m_CommData.Debug.RxBuffer, m_CommData.Debug.len);
                                byte crc_L = Convert.ToByte((crc) & 0x00FF);
                                byte crc_H = Convert.ToByte((crc >> 8) & 0x00FF);
                                int len = m_CommData.Debug.len;

                                if (m_CommData.Debug.RxBuffer[len + 1] == crc_L && m_CommData.Debug.RxBuffer[len + 2] == crc_H )
                                {
                                    procCommRx_Main(m_CommData.Debug.RxBuffer);
                                }

                                m_CommData.Debug.RxCnt = 0;
                                Array.Clear(m_CommData.Debug.RxBuffer, 0, m_CommData.Debug.RxBuffer.Length);
                            }
                        }
                    }
                    else
                    {
                        m_CommData.Debug.RxCnt = 0;
                        Array.Clear(m_CommData.Debug.RxBuffer, 0, m_CommData.Debug.RxBuffer.Length);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(System.Reflection.MethodBase.GetCurrentMethod().Name + ' ' + ex.Message);
            }
        }
        // @brief : Received main port packet parsing
        private void procCommRx_Main(byte[] dataIn)
        {
            String str = string.Empty;
            byte[] msg_data = new byte[dataIn.Length - 8];        // start, cmd ... byte delete
            byte[] msg_hex_data = new byte[dataIn.Length - 8];    // start, cmd ... byte delete

            byte[] recvByte = new byte[m_CommData.Debug.len];
            Array.Copy(m_CommData.Debug.RxBuffer, 9, recvByte, 0, m_CommData.Debug.len - 10);

            switch (m_CommData.Debug.cmd)
            {
                case (int)DebugCmd_t.RX_TYPE_MSG_LOG:
                    Nav_Msg_Log _Nav_Msg_Log = new Nav_Msg_Log();
                    _Nav_Msg_Log.Navtex_Message_Data_Write(recvByte);
                    _Nav_Msg_Log.Navtex_Message_ProcessData_Write(recvByte);
                break;
            }
        }

        // @brief : Received ins port packet processing
        private void Received_data_INS(byte[] dataIn)
        {
            try
            {
                procCommRx_Raw_INS(dataIn);

                for (int i = 0; i < dataIn.Length; i++)
                {
                    m_CommData.INS.RxBuffer[m_CommData.INS.RxCnt++] = dataIn[i];

                    if (m_CommData.INS.RxBuffer[0] == '$' || m_CommData.INS.RxBuffer[0] == '!')
                    {
                        if (m_CommData.INS.RxCnt > 83)
                        {
                            m_CommData.INS.RxCnt = 0;
                            Array.Clear(m_CommData.INS.RxBuffer, 0, m_CommData.INS.RxBuffer.Length);
                        }
                        else
                        {
                            if (m_CommData.INS.RxBuffer[m_CommData.INS.RxCnt - 1] == 0x0A)
                            {
                                if (m_CommData.INS.RxCnt >= 8 && m_CommData.INS.RxBuffer[m_CommData.INS.RxCnt - 2] == 0x0D)
                                {
                                    procCommRx_INS(m_CommData.INS.RxBuffer);
                                }
                                m_CommData.INS.RxCnt = 0;
                                Array.Clear(m_CommData.INS.RxBuffer, 0, m_CommData.INS.RxBuffer.Length);
                            }
                        }
                    }
                    else
                    {
                        m_CommData.INS.RxCnt = 0;
                        Array.Clear(m_CommData.INS.RxBuffer, 0, m_CommData.INS.RxBuffer.Length);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(System.Reflection.MethodBase.GetCurrentMethod().Name + ' ' + ex.Message);
            }
        }

        // @brief : Received ins port Raw data send
        private void procCommRx_Raw_INS(byte[] dataIn)
        {
            string str = Encoding.Default.GetString(dataIn);
            if(Interface_Monitor_Form.Instance != null)
            {
                Raw_Received_INS(str);
            }

            if(Sentence_CRQ_Form.Instance != null)
            {
                Raw_Received_CRQ_Mon(str);
            }
        }
        // @brief : Received ins port packet parsing
        private void procCommRx_INS(byte[] dataIn)
        {
            string str = Encoding.Default.GetString(dataIn);
            if(Nmea_Monitor_Form.Instance != null)
            {
                NMEA_Received_INS(str);
            }
        }

        // @brief : Received bam port packet processing
        private void Received_data_BAM(byte[] dataIn)
        {
            try
            {
                procCommRx_Raw_BAM(dataIn);

                for (int i = 0; i < dataIn.Length; i++)
                {
                    m_CommData.BAM.RxBuffer[m_CommData.BAM.RxCnt++] = dataIn[i];

                    if (m_CommData.BAM.RxBuffer[0] == '$' || m_CommData.BAM.RxBuffer[0] == '!')
                    {
                        if (m_CommData.BAM.RxCnt > 83)
                        {
                            m_CommData.BAM.RxCnt = 0;
                            Array.Clear(m_CommData.BAM.RxBuffer, 0, m_CommData.BAM.RxBuffer.Length);
                        }
                        else
                        {
                            if (m_CommData.BAM.RxBuffer[m_CommData.BAM.RxCnt - 1] == 0x0A)
                            {
                                if (m_CommData.BAM.RxCnt >= 8 && m_CommData.BAM.RxBuffer[m_CommData.BAM.RxCnt - 2] == 0x0D)
                                {
                                    procCommRx_BAM(m_CommData.BAM.RxBuffer);
                                }
                                m_CommData.BAM.RxCnt = 0;
                                Array.Clear(m_CommData.BAM.RxBuffer, 0, m_CommData.BAM.RxBuffer.Length);
                            }
                        }
                    }
                    else
                    {
                        m_CommData.BAM.RxCnt = 0;
                        Array.Clear(m_CommData.BAM.RxBuffer, 0, m_CommData.BAM.RxBuffer.Length);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(System.Reflection.MethodBase.GetCurrentMethod().Name + ' ' + ex.Message);
            }
        }

        // @brief : Received bam port Raw data send
        private void procCommRx_Raw_BAM(byte[] dataIn)
        {
            string str = Encoding.Default.GetString(dataIn);
            if (Nmea_Monitor_Form.Instance != null)
            {
                Raw_Received_BAM(str);
            }

            if(Sentence_BAM_Form.Instance != null)
            {
                Raw_Received_BAM_Mon(str);
            }
        }

        // @brief : Received bam port packet parsing
        private void procCommRx_BAM(byte[] dataIn)
        {

        }


        // @brief : Received ins monitor 1 port packet processing
        private void Received_data_Mon_1(byte[] dataIn)
        {
            try
            {
                for (int i = 0; i < dataIn.Length; i++)
                {
                    m_CommData.Mon_1.RxBuffer[m_CommData.Mon_1.RxCnt++] = dataIn[i];

                    if (m_CommData.Mon_1.RxBuffer[0] == '$' || m_CommData.Mon_1.RxBuffer[0] == '!')
                    {
                        if (m_CommData.Mon_1.RxCnt > 83)
                        {
                            m_CommData.Mon_1.RxCnt = 0;
                            Array.Clear(m_CommData.Mon_1.RxBuffer, 0, m_CommData.Mon_1.RxBuffer.Length);
                        }
                        else
                        {
                            if (m_CommData.Mon_1.RxBuffer[m_CommData.Mon_1.RxCnt - 1] == 0x0A)
                            {
                                if (m_CommData.Mon_1.RxCnt >= 8 && m_CommData.Mon_1.RxBuffer[m_CommData.Mon_1.RxCnt - 2] == 0x0D)
                                {
                                    procCommRx_Mon_1(m_CommData.Mon_1.RxBuffer);
                                }
                                m_CommData.Mon_1.RxCnt = 0;
                                Array.Clear(m_CommData.Mon_1.RxBuffer, 0, m_CommData.Mon_1.RxBuffer.Length);
                            }
                        }
                    }
                    else
                    {
                        m_CommData.Mon_1.RxCnt = 0;
                        Array.Clear(m_CommData.Mon_1.RxBuffer, 0, m_CommData.Mon_1.RxBuffer.Length);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(System.Reflection.MethodBase.GetCurrentMethod().Name + ' ' + ex.Message);
            }
        }
        // @brief : Received ins monitor 1 port packet parsing
        private void procCommRx_Mon_1(byte[] dataIn)
        {
            if (Nmea_Monitor_Form.Instance != null)
            {
                string str = Encoding.Default.GetString(dataIn);
                NMEA_Received_Mon_1(str);
            }
        }

        // @brief : Received ins monitor 2 port packet processing
        private void Received_data_Mon_2(byte[] dataIn)
        {
            try
            {
                for (int i = 0; i < dataIn.Length; i++)
                {
                    m_CommData.Mon_2.RxBuffer[m_CommData.Mon_2.RxCnt++] = dataIn[i];

                    if (m_CommData.Mon_2.RxBuffer[0] == '$' || m_CommData.Mon_2.RxBuffer[0] == '!')
                    {
                        if (m_CommData.Mon_2.RxCnt > 83)
                        {
                            m_CommData.Mon_2.RxCnt = 0;
                            Array.Clear(m_CommData.Mon_2.RxBuffer, 0, m_CommData.Mon_2.RxBuffer.Length);
                        }
                        else
                        {
                            if (m_CommData.Mon_2.RxBuffer[m_CommData.Mon_2.RxCnt - 1] == 0x0A)
                            {
                                if (m_CommData.Mon_2.RxCnt >= 8 && m_CommData.Mon_2.RxBuffer[m_CommData.Mon_2.RxCnt - 2] == 0x0D)
                                {
                                    procCommRx_Mon_2(m_CommData.Mon_2.RxBuffer);
                                }
                                m_CommData.Mon_2.RxCnt = 0;
                                Array.Clear(m_CommData.Mon_2.RxBuffer, 0, m_CommData.Mon_2.RxBuffer.Length);
                            }
                        }
                    }
                    else
                    {
                        m_CommData.Mon_2.RxCnt = 0;
                        Array.Clear(m_CommData.Mon_2.RxBuffer, 0, m_CommData.Mon_2.RxBuffer.Length);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(System.Reflection.MethodBase.GetCurrentMethod().Name + ' ' + ex.Message);
            }
        }
        // @brief : Received ins monitor 2 port packet parsing
        private void procCommRx_Mon_2(byte[] dataIn)
        {
            if (Nmea_Monitor_Form.Instance != null)
            {
                string str = Encoding.Default.GetString(dataIn);
                NMEA_Received_Mon_2(str);
            }
        }

        // @brief : Received ins monitor 3 port packet processing
        private void Received_data_Mon_3(byte[] dataIn)
        {
            try
            {
                for (int i = 0; i < dataIn.Length; i++)
                {
                    m_CommData.Mon_3.RxBuffer[m_CommData.Mon_3.RxCnt++] = dataIn[i];

                    if (m_CommData.Mon_3.RxBuffer[0] == '$' || m_CommData.Mon_3.RxBuffer[0] == '!')
                    {
                        if (m_CommData.Mon_3.RxCnt > 83)
                        {
                            m_CommData.Mon_3.RxCnt = 0;
                            Array.Clear(m_CommData.Mon_3.RxBuffer, 0, m_CommData.Mon_3.RxBuffer.Length);
                        }
                        else
                        {
                            if (m_CommData.Mon_3.RxBuffer[m_CommData.Mon_3.RxCnt - 1] == 0x0A)
                            {
                                if (m_CommData.Mon_3.RxCnt >= 8 && m_CommData.Mon_3.RxBuffer[m_CommData.Mon_3.RxCnt - 2] == 0x0D)
                                {
                                    procCommRx_Mon_3(m_CommData.Mon_3.RxBuffer);
                                }
                                m_CommData.Mon_3.RxCnt = 0;
                                Array.Clear(m_CommData.Mon_3.RxBuffer, 0, m_CommData.Mon_3.RxBuffer.Length);
                            }
                        }
                    }
                    else
                    {
                        m_CommData.Mon_3.RxCnt = 0;
                        Array.Clear(m_CommData.Mon_3.RxBuffer, 0, m_CommData.Mon_3.RxBuffer.Length);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(System.Reflection.MethodBase.GetCurrentMethod().Name + ' ' + ex.Message);
            }
        }
        // @brief : Received ins monitor 3 port packet parsing
        private void procCommRx_Mon_3(byte[] dataIn)
        {
            if (Nmea_Monitor_Form.Instance != null)
            {
                string str = Encoding.Default.GetString(dataIn);
                NMEA_Received_Mon_3(str);
            }
        }
    }
}
