﻿namespace AssistPro
{
    partial class FirmwareUpdateForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.groupBoxSelectFile = new System.Windows.Forms.GroupBox();
            this.buttonOpenFW = new System.Windows.Forms.Button();
            this.textBoxHexFilePath = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.groupBoxSearchDevice = new System.Windows.Forms.GroupBox();
            this.listBoxSearch = new System.Windows.Forms.ListBox();
            this.buttonSearchDevice = new System.Windows.Forms.Button();
            this.groupBoxDevInfo = new System.Windows.Forms.GroupBox();
            this.label8 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.textBoxSearchDevice = new System.Windows.Forms.TextBox();
            this.textBoxSearchSubDevice = new System.Windows.Forms.TextBox();
            this.textBoxSearchDeviceNum = new System.Windows.Forms.TextBox();
            this.textBoxSearchDeviceVer = new System.Windows.Forms.TextBox();
            this.textBoxSearchIPAddr = new System.Windows.Forms.TextBox();
            this.textBoxSearchSubnet = new System.Windows.Forms.TextBox();
            this.label3 = new System.Windows.Forms.Label();
            this.groupBoxFileInfo = new System.Windows.Forms.GroupBox();
            this.label12 = new System.Windows.Forms.Label();
            this.label13 = new System.Windows.Forms.Label();
            this.label14 = new System.Windows.Forms.Label();
            this.textBoxFileDevInfo = new System.Windows.Forms.TextBox();
            this.textBoxFileVer = new System.Windows.Forms.TextBox();
            this.textBoxFileDate = new System.Windows.Forms.TextBox();
            this.textBoxFileSize = new System.Windows.Forms.TextBox();
            this.label15 = new System.Windows.Forms.Label();
            this.groupBoxUpdate = new System.Windows.Forms.GroupBox();
            this.buttonClearLog = new System.Windows.Forms.Button();
            this.progressBarUpdate = new System.Windows.Forms.ProgressBar();
            this.listBoxUpdateLog = new System.Windows.Forms.ListBox();
            this.buttonStopUpdate = new System.Windows.Forms.Button();
            this.buttonStartUpdate = new System.Windows.Forms.Button();
            this.toolStrip1 = new System.Windows.Forms.ToolStrip();
            this.toolStripLabel1 = new System.Windows.Forms.ToolStripLabel();
            this.toolStripComboBoxIP = new System.Windows.Forms.ToolStripComboBox();
            this.toolStripButtonSelectIP = new System.Windows.Forms.ToolStripButton();
            this.groupBoxSelectFile.SuspendLayout();
            this.groupBoxSearchDevice.SuspendLayout();
            this.groupBoxDevInfo.SuspendLayout();
            this.groupBoxFileInfo.SuspendLayout();
            this.groupBoxUpdate.SuspendLayout();
            this.toolStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // groupBoxSelectFile
            // 
            this.groupBoxSelectFile.Controls.Add(this.buttonOpenFW);
            this.groupBoxSelectFile.Controls.Add(this.textBoxHexFilePath);
            this.groupBoxSelectFile.Controls.Add(this.label1);
            this.groupBoxSelectFile.Enabled = false;
            this.groupBoxSelectFile.Location = new System.Drawing.Point(532, 28);
            this.groupBoxSelectFile.Name = "groupBoxSelectFile";
            this.groupBoxSelectFile.Size = new System.Drawing.Size(440, 55);
            this.groupBoxSelectFile.TabIndex = 1;
            this.groupBoxSelectFile.TabStop = false;
            this.groupBoxSelectFile.Text = "Select Firmware";
            // 
            // buttonOpenFW
            // 
            this.buttonOpenFW.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.buttonOpenFW.Location = new System.Drawing.Point(359, 20);
            this.buttonOpenFW.Name = "buttonOpenFW";
            this.buttonOpenFW.Size = new System.Drawing.Size(75, 23);
            this.buttonOpenFW.TabIndex = 2;
            this.buttonOpenFW.Text = "Open";
            this.buttonOpenFW.UseVisualStyleBackColor = true;
            this.buttonOpenFW.Click += new System.EventHandler(this.buttonSelectHexFile_Click);
            // 
            // textBoxHexFilePath
            // 
            this.textBoxHexFilePath.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.textBoxHexFilePath.Location = new System.Drawing.Point(56, 20);
            this.textBoxHexFilePath.Name = "textBoxHexFilePath";
            this.textBoxHexFilePath.Size = new System.Drawing.Size(297, 21);
            this.textBoxHexFilePath.TabIndex = 1;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(17, 24);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(33, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "File :";
            // 
            // groupBoxSearchDevice
            // 
            this.groupBoxSearchDevice.Controls.Add(this.listBoxSearch);
            this.groupBoxSearchDevice.Controls.Add(this.buttonSearchDevice);
            this.groupBoxSearchDevice.Enabled = false;
            this.groupBoxSearchDevice.Location = new System.Drawing.Point(12, 28);
            this.groupBoxSearchDevice.Name = "groupBoxSearchDevice";
            this.groupBoxSearchDevice.Size = new System.Drawing.Size(240, 220);
            this.groupBoxSearchDevice.TabIndex = 3;
            this.groupBoxSearchDevice.TabStop = false;
            this.groupBoxSearchDevice.Text = "Search Device";
            // 
            // listBoxSearch
            // 
            this.listBoxSearch.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.listBoxSearch.FormattingEnabled = true;
            this.listBoxSearch.ItemHeight = 12;
            this.listBoxSearch.Location = new System.Drawing.Point(6, 65);
            this.listBoxSearch.Name = "listBoxSearch";
            this.listBoxSearch.Size = new System.Drawing.Size(228, 136);
            this.listBoxSearch.TabIndex = 1;
            this.listBoxSearch.SelectedIndexChanged += new System.EventHandler(this.searchListBox_SelectedIndexChanged);
            // 
            // buttonSearchDevice
            // 
            this.buttonSearchDevice.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.buttonSearchDevice.Font = new System.Drawing.Font("굴림", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.buttonSearchDevice.Location = new System.Drawing.Point(6, 20);
            this.buttonSearchDevice.Name = "buttonSearchDevice";
            this.buttonSearchDevice.Size = new System.Drawing.Size(228, 39);
            this.buttonSearchDevice.TabIndex = 0;
            this.buttonSearchDevice.Text = "Search Device";
            this.buttonSearchDevice.UseVisualStyleBackColor = true;
            this.buttonSearchDevice.Click += new System.EventHandler(this.searchDeviceButton_Click);
            // 
            // groupBoxDevInfo
            // 
            this.groupBoxDevInfo.Controls.Add(this.label8);
            this.groupBoxDevInfo.Controls.Add(this.label7);
            this.groupBoxDevInfo.Controls.Add(this.label6);
            this.groupBoxDevInfo.Controls.Add(this.label5);
            this.groupBoxDevInfo.Controls.Add(this.label4);
            this.groupBoxDevInfo.Controls.Add(this.textBoxSearchDevice);
            this.groupBoxDevInfo.Controls.Add(this.textBoxSearchSubDevice);
            this.groupBoxDevInfo.Controls.Add(this.textBoxSearchDeviceNum);
            this.groupBoxDevInfo.Controls.Add(this.textBoxSearchDeviceVer);
            this.groupBoxDevInfo.Controls.Add(this.textBoxSearchIPAddr);
            this.groupBoxDevInfo.Controls.Add(this.textBoxSearchSubnet);
            this.groupBoxDevInfo.Controls.Add(this.label3);
            this.groupBoxDevInfo.Enabled = false;
            this.groupBoxDevInfo.Location = new System.Drawing.Point(258, 28);
            this.groupBoxDevInfo.Name = "groupBoxDevInfo";
            this.groupBoxDevInfo.Size = new System.Drawing.Size(268, 220);
            this.groupBoxDevInfo.TabIndex = 3;
            this.groupBoxDevInfo.TabStop = false;
            this.groupBoxDevInfo.Text = "Device Info";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(17, 182);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(87, 12);
            this.label8.TabIndex = 3;
            this.label8.Text = "Subnet Mask :";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(17, 151);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(75, 12);
            this.label7.TabIndex = 3;
            this.label7.Text = "IP Address :";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(17, 120);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(56, 12);
            this.label6.TabIndex = 3;
            this.label6.Text = "Version :";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(17, 89);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(100, 12);
            this.label5.TabIndex = 2;
            this.label5.Text = "Device Number :";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(17, 58);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(77, 12);
            this.label4.TabIndex = 2;
            this.label4.Text = "Sub Device :";
            // 
            // textBoxSearchDevice
            // 
            this.textBoxSearchDevice.Location = new System.Drawing.Point(137, 24);
            this.textBoxSearchDevice.Name = "textBoxSearchDevice";
            this.textBoxSearchDevice.Size = new System.Drawing.Size(112, 21);
            this.textBoxSearchDevice.TabIndex = 1;
            // 
            // textBoxSearchSubDevice
            // 
            this.textBoxSearchSubDevice.Location = new System.Drawing.Point(137, 55);
            this.textBoxSearchSubDevice.Name = "textBoxSearchSubDevice";
            this.textBoxSearchSubDevice.Size = new System.Drawing.Size(112, 21);
            this.textBoxSearchSubDevice.TabIndex = 1;
            // 
            // textBoxSearchDeviceNum
            // 
            this.textBoxSearchDeviceNum.Location = new System.Drawing.Point(137, 86);
            this.textBoxSearchDeviceNum.Name = "textBoxSearchDeviceNum";
            this.textBoxSearchDeviceNum.Size = new System.Drawing.Size(112, 21);
            this.textBoxSearchDeviceNum.TabIndex = 1;
            // 
            // textBoxSearchDeviceVer
            // 
            this.textBoxSearchDeviceVer.Location = new System.Drawing.Point(137, 117);
            this.textBoxSearchDeviceVer.Name = "textBoxSearchDeviceVer";
            this.textBoxSearchDeviceVer.Size = new System.Drawing.Size(112, 21);
            this.textBoxSearchDeviceVer.TabIndex = 1;
            // 
            // textBoxSearchIPAddr
            // 
            this.textBoxSearchIPAddr.Location = new System.Drawing.Point(137, 148);
            this.textBoxSearchIPAddr.Name = "textBoxSearchIPAddr";
            this.textBoxSearchIPAddr.Size = new System.Drawing.Size(112, 21);
            this.textBoxSearchIPAddr.TabIndex = 1;
            // 
            // textBoxSearchSubnet
            // 
            this.textBoxSearchSubnet.Location = new System.Drawing.Point(137, 179);
            this.textBoxSearchSubnet.Name = "textBoxSearchSubnet";
            this.textBoxSearchSubnet.Size = new System.Drawing.Size(112, 21);
            this.textBoxSearchSubnet.TabIndex = 1;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(17, 27);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(51, 12);
            this.label3.TabIndex = 0;
            this.label3.Text = "Device :";
            // 
            // groupBoxFileInfo
            // 
            this.groupBoxFileInfo.Controls.Add(this.label12);
            this.groupBoxFileInfo.Controls.Add(this.label13);
            this.groupBoxFileInfo.Controls.Add(this.label14);
            this.groupBoxFileInfo.Controls.Add(this.textBoxFileDevInfo);
            this.groupBoxFileInfo.Controls.Add(this.textBoxFileVer);
            this.groupBoxFileInfo.Controls.Add(this.textBoxFileDate);
            this.groupBoxFileInfo.Controls.Add(this.textBoxFileSize);
            this.groupBoxFileInfo.Controls.Add(this.label15);
            this.groupBoxFileInfo.Enabled = false;
            this.groupBoxFileInfo.Location = new System.Drawing.Point(532, 93);
            this.groupBoxFileInfo.Name = "groupBoxFileInfo";
            this.groupBoxFileInfo.Size = new System.Drawing.Size(440, 155);
            this.groupBoxFileInfo.TabIndex = 7;
            this.groupBoxFileInfo.TabStop = false;
            this.groupBoxFileInfo.Text = "Firmware File Info";
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(17, 120);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(62, 12);
            this.label12.TabIndex = 3;
            this.label12.Text = "File Size :";
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(17, 89);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(88, 12);
            this.label13.TabIndex = 2;
            this.label13.Text = "Release Date :";
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(17, 58);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(56, 12);
            this.label14.TabIndex = 2;
            this.label14.Text = "Version :";
            // 
            // textBoxFileDevInfo
            // 
            this.textBoxFileDevInfo.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.textBoxFileDevInfo.Location = new System.Drawing.Point(137, 24);
            this.textBoxFileDevInfo.Name = "textBoxFileDevInfo";
            this.textBoxFileDevInfo.Size = new System.Drawing.Size(284, 21);
            this.textBoxFileDevInfo.TabIndex = 1;
            // 
            // textBoxFileVer
            // 
            this.textBoxFileVer.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.textBoxFileVer.Location = new System.Drawing.Point(137, 55);
            this.textBoxFileVer.Name = "textBoxFileVer";
            this.textBoxFileVer.Size = new System.Drawing.Size(284, 21);
            this.textBoxFileVer.TabIndex = 1;
            // 
            // textBoxFileDate
            // 
            this.textBoxFileDate.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.textBoxFileDate.Location = new System.Drawing.Point(137, 86);
            this.textBoxFileDate.Name = "textBoxFileDate";
            this.textBoxFileDate.Size = new System.Drawing.Size(284, 21);
            this.textBoxFileDate.TabIndex = 1;
            // 
            // textBoxFileSize
            // 
            this.textBoxFileSize.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.textBoxFileSize.Location = new System.Drawing.Point(137, 117);
            this.textBoxFileSize.Name = "textBoxFileSize";
            this.textBoxFileSize.Size = new System.Drawing.Size(284, 21);
            this.textBoxFileSize.TabIndex = 1;
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(17, 27);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(51, 12);
            this.label15.TabIndex = 0;
            this.label15.Text = "Device :";
            // 
            // groupBoxUpdate
            // 
            this.groupBoxUpdate.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.groupBoxUpdate.Controls.Add(this.buttonClearLog);
            this.groupBoxUpdate.Controls.Add(this.progressBarUpdate);
            this.groupBoxUpdate.Controls.Add(this.listBoxUpdateLog);
            this.groupBoxUpdate.Controls.Add(this.buttonStopUpdate);
            this.groupBoxUpdate.Controls.Add(this.buttonStartUpdate);
            this.groupBoxUpdate.Location = new System.Drawing.Point(12, 254);
            this.groupBoxUpdate.Name = "groupBoxUpdate";
            this.groupBoxUpdate.Size = new System.Drawing.Size(960, 255);
            this.groupBoxUpdate.TabIndex = 4;
            this.groupBoxUpdate.TabStop = false;
            this.groupBoxUpdate.Text = "Update";
            // 
            // buttonClearLog
            // 
            this.buttonClearLog.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.buttonClearLog.Font = new System.Drawing.Font("굴림", 10F, System.Drawing.FontStyle.Bold);
            this.buttonClearLog.Location = new System.Drawing.Point(834, 62);
            this.buttonClearLog.Name = "buttonClearLog";
            this.buttonClearLog.Size = new System.Drawing.Size(120, 29);
            this.buttonClearLog.TabIndex = 3;
            this.buttonClearLog.Text = "Clear Log";
            this.buttonClearLog.UseVisualStyleBackColor = false;
            this.buttonClearLog.Click += new System.EventHandler(this.buttonClearLog_Click);
            // 
            // progressBarUpdate
            // 
            this.progressBarUpdate.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.progressBarUpdate.Location = new System.Drawing.Point(8, 65);
            this.progressBarUpdate.Name = "progressBarUpdate";
            this.progressBarUpdate.Size = new System.Drawing.Size(820, 23);
            this.progressBarUpdate.TabIndex = 2;
            // 
            // listBoxUpdateLog
            // 
            this.listBoxUpdateLog.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.listBoxUpdateLog.FormattingEnabled = true;
            this.listBoxUpdateLog.ItemHeight = 12;
            this.listBoxUpdateLog.Location = new System.Drawing.Point(6, 99);
            this.listBoxUpdateLog.Name = "listBoxUpdateLog";
            this.listBoxUpdateLog.Size = new System.Drawing.Size(948, 148);
            this.listBoxUpdateLog.TabIndex = 1;
            // 
            // buttonStopUpdate
            // 
            this.buttonStopUpdate.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.buttonStopUpdate.Enabled = false;
            this.buttonStopUpdate.Font = new System.Drawing.Font("굴림", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.buttonStopUpdate.Location = new System.Drawing.Point(711, 20);
            this.buttonStopUpdate.Name = "buttonStopUpdate";
            this.buttonStopUpdate.Size = new System.Drawing.Size(243, 39);
            this.buttonStopUpdate.TabIndex = 0;
            this.buttonStopUpdate.Text = "Stop";
            this.buttonStopUpdate.UseVisualStyleBackColor = false;
            this.buttonStopUpdate.Click += new System.EventHandler(this.buttonStopUpdate_Click);
            // 
            // buttonStartUpdate
            // 
            this.buttonStartUpdate.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.buttonStartUpdate.Enabled = false;
            this.buttonStartUpdate.Font = new System.Drawing.Font("굴림", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.buttonStartUpdate.Location = new System.Drawing.Point(6, 20);
            this.buttonStartUpdate.Name = "buttonStartUpdate";
            this.buttonStartUpdate.Size = new System.Drawing.Size(699, 39);
            this.buttonStartUpdate.TabIndex = 0;
            this.buttonStartUpdate.Text = "Start Firmware Upload";
            this.buttonStartUpdate.UseVisualStyleBackColor = false;
            this.buttonStartUpdate.Click += new System.EventHandler(this.buttonStartUpdate_Click);
            // 
            // toolStrip1
            // 
            this.toolStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripLabel1,
            this.toolStripComboBoxIP,
            this.toolStripButtonSelectIP});
            this.toolStrip1.Location = new System.Drawing.Point(0, 0);
            this.toolStrip1.Name = "toolStrip1";
            this.toolStrip1.Size = new System.Drawing.Size(984, 25);
            this.toolStrip1.TabIndex = 0;
            this.toolStrip1.Text = "toolStrip1";
            // 
            // toolStripLabel1
            // 
            this.toolStripLabel1.Name = "toolStripLabel1";
            this.toolStripLabel1.Size = new System.Drawing.Size(43, 22);
            this.toolStripLabel1.Text = "PC IP :";
            // 
            // toolStripComboBoxIP
            // 
            this.toolStripComboBoxIP.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.toolStripComboBoxIP.Name = "toolStripComboBoxIP";
            this.toolStripComboBoxIP.Size = new System.Drawing.Size(120, 25);
            this.toolStripComboBoxIP.SelectedIndexChanged += new System.EventHandler(this.toolStripComboBoxIP_SelectedIndexChanged);
            // 
            // toolStripButtonSelectIP
            // 
            this.toolStripButtonSelectIP.AutoSize = false;
            this.toolStripButtonSelectIP.BackColor = System.Drawing.Color.Silver;
            this.toolStripButtonSelectIP.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            this.toolStripButtonSelectIP.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.toolStripButtonSelectIP.Name = "toolStripButtonSelectIP";
            this.toolStripButtonSelectIP.Overflow = System.Windows.Forms.ToolStripItemOverflow.Never;
            this.toolStripButtonSelectIP.Size = new System.Drawing.Size(80, 22);
            this.toolStripButtonSelectIP.Text = "Open";
            this.toolStripButtonSelectIP.Click += new System.EventHandler(this.toolStripButtonSelectIP_Click);
            // 
            // FirmwareUpdateForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(984, 521);
            this.Controls.Add(this.groupBoxUpdate);
            this.Controls.Add(this.groupBoxFileInfo);
            this.Controls.Add(this.groupBoxDevInfo);
            this.Controls.Add(this.groupBoxSearchDevice);
            this.Controls.Add(this.groupBoxSelectFile);
            this.Controls.Add(this.toolStrip1);
            this.Name = "FirmwareUpdateForm";
            this.TabText = "Firmware Update";
            this.Text = "Firmware Update";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.FirmwareUpdateForm_FormClosing);
            this.Load += new System.EventHandler(this.FirmwareUpdateForm_Load);
            this.groupBoxSelectFile.ResumeLayout(false);
            this.groupBoxSelectFile.PerformLayout();
            this.groupBoxSearchDevice.ResumeLayout(false);
            this.groupBoxDevInfo.ResumeLayout(false);
            this.groupBoxDevInfo.PerformLayout();
            this.groupBoxFileInfo.ResumeLayout(false);
            this.groupBoxFileInfo.PerformLayout();
            this.groupBoxUpdate.ResumeLayout(false);
            this.toolStrip1.ResumeLayout(false);
            this.toolStrip1.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion
        private System.Windows.Forms.GroupBox groupBoxSelectFile;
        private System.Windows.Forms.Button buttonOpenFW;
        private System.Windows.Forms.TextBox textBoxHexFilePath;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.GroupBox groupBoxSearchDevice;
        private System.Windows.Forms.ListBox listBoxSearch;
        private System.Windows.Forms.Button buttonSearchDevice;
        private System.Windows.Forms.GroupBox groupBoxDevInfo;
        private System.Windows.Forms.TextBox textBoxSearchSubnet;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.TextBox textBoxSearchDevice;
        private System.Windows.Forms.TextBox textBoxSearchSubDevice;
        private System.Windows.Forms.TextBox textBoxSearchDeviceNum;
        private System.Windows.Forms.TextBox textBoxSearchDeviceVer;
        private System.Windows.Forms.TextBox textBoxSearchIPAddr;
        private System.Windows.Forms.GroupBox groupBoxFileInfo;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.TextBox textBoxFileDevInfo;
        private System.Windows.Forms.TextBox textBoxFileVer;
        private System.Windows.Forms.TextBox textBoxFileDate;
        private System.Windows.Forms.TextBox textBoxFileSize;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.GroupBox groupBoxUpdate;
        private System.Windows.Forms.ListBox listBoxUpdateLog;
        private System.Windows.Forms.Button buttonStartUpdate;
        private System.Windows.Forms.ProgressBar progressBarUpdate;
        private System.Windows.Forms.Button buttonStopUpdate;
        private System.Windows.Forms.Button buttonClearLog;
        private System.Windows.Forms.ToolStrip toolStrip1;
        private System.Windows.Forms.ToolStripLabel toolStripLabel1;
        private System.Windows.Forms.ToolStripComboBox toolStripComboBoxIP;
        private System.Windows.Forms.ToolStripButton toolStripButtonSelectIP;
    }
}