<?xml version="1.0"?>
<doc>
    <assembly>
        <name>NAudio</name>
    </assembly>
    <members>
        <member name="T:NAudio.Wave.AudioFileReader">
            <summary>
            AudioFileReader simplifies opening an audio file in NAudio
            Simply pass in the filename, and it will attempt to open the
            file and set up a conversion path that turns into PCM IEEE float.
            ACM codecs will be used for conversion.
            It provides a volume property and implements both WaveStream and
            ISampleProvider, making it possibly the only stage in your audio
            pipeline necessary for simple playback scenarios
            </summary>
        </member>
        <member name="M:NAudio.Wave.AudioFileReader.#ctor(System.String)">
            <summary>
            Initializes a new instance of AudioFileReader
            </summary>
            <param name="fileName">The file to open</param>
        </member>
        <member name="M:NAudio.Wave.AudioFileReader.CreateReaderStream(System.String)">
            <summary>
            Creates the reader stream, supporting all filetypes in the core NAudio library,
            and ensuring we are in PCM format
            </summary>
            <param name="fileName">File Name</param>
        </member>
        <member name="P:NAudio.Wave.AudioFileReader.FileName">
            <summary>
            File Name
            </summary>
        </member>
        <member name="P:NAudio.Wave.AudioFileReader.WaveFormat">
            <summary>
            WaveFormat of this stream
            </summary>
        </member>
        <member name="P:NAudio.Wave.AudioFileReader.Length">
            <summary>
            Length of this stream (in bytes)
            </summary>
        </member>
        <member name="P:NAudio.Wave.AudioFileReader.Position">
            <summary>
            Position of this stream (in bytes)
            </summary>
        </member>
        <member name="M:NAudio.Wave.AudioFileReader.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Reads from this wave stream
            </summary>
            <param name="buffer">Audio buffer</param>
            <param name="offset">Offset into buffer</param>
            <param name="count">Number of bytes required</param>
            <returns>Number of bytes read</returns>
        </member>
        <member name="M:NAudio.Wave.AudioFileReader.Read(System.Single[],System.Int32,System.Int32)">
            <summary>
            Reads audio from this sample provider
            </summary>
            <param name="buffer">Sample buffer</param>
            <param name="offset">Offset into sample buffer</param>
            <param name="count">Number of samples required</param>
            <returns>Number of samples read</returns>
        </member>
        <member name="P:NAudio.Wave.AudioFileReader.Volume">
            <summary>
            Gets or Sets the Volume of this AudioFileReader. 1.0f is full volume
            </summary>
        </member>
        <member name="M:NAudio.Wave.AudioFileReader.SourceToDest(System.Int64)">
            <summary>
            Helper to convert source to dest bytes
            </summary>
        </member>
        <member name="M:NAudio.Wave.AudioFileReader.DestToSource(System.Int64)">
            <summary>
            Helper to convert dest to source bytes
            </summary>
        </member>
        <member name="M:NAudio.Wave.AudioFileReader.Dispose(System.Boolean)">
            <summary>
            Disposes this AudioFileReader
            </summary>
            <param name="disposing">True if called from Dispose</param>
        </member>
        <member name="T:NAudio.Wave.Mp3FileReader">
            <summary>
            Class for reading from MP3 files
            </summary>
        </member>
        <member name="M:NAudio.Wave.Mp3FileReader.#ctor(System.String)">
            <summary>Supports opening a MP3 file</summary>
        </member>
        <member name="M:NAudio.Wave.Mp3FileReader.#ctor(System.IO.Stream)">
            <summary>
            Opens MP3 from a stream rather than a file
            Will not dispose of this stream itself
            </summary>
            <param name="inputStream">The incoming stream containing MP3 data</param>
        </member>
        <member name="M:NAudio.Wave.Mp3FileReader.CreateAcmFrameDecompressor(NAudio.Wave.WaveFormat)">
            <summary>
            Creates an ACM MP3 Frame decompressor. This is the default with NAudio
            </summary>
            <param name="mp3Format">A WaveFormat object based </param>
            <returns></returns>
        </member>
    </members>
</doc>
