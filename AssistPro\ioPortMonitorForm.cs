﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.IO.Ports;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using WeifenLuo.WinFormsUI;

namespace AssistPro
{
    public partial class ioPortMonitorForm : DockContent
    {
        private SerialManager m_SerialManager;
        private SerialPort m_SerialPort;
        private string m_StrDate;

        public ioPortMonitorForm()
        {
            InitializeComponent();

            cb_CommList.Enabled = true;
        }

        public void DataReceived(string data)
        {
            //m_StrDate = DateTime.Now.ToString("HH:mm:ss.fff : ");

            richtb_IOMonitor.SelectionColor = Color.Green;
            //richtb_IOMonitor.AppendText(m_StrDate);
            richtb_IOMonitor.AppendText(data);
            richtb_IOMonitor.ScrollToCaret();
        }

        private void Load_ioPortMonitor(object sender, EventArgs e)
        {

            foreach (string name in SerialPort.GetPortNames())
            {
                cb_CommList.Items.Add(name);
            }
        }

        private void IO_Monitor_DataReceived(object sender, SerialDataReceivedEventArgs e)
        {
            if (this.InvokeRequired)
                this.Invoke(new EventHandler(Received_IO_Monitor));
        }

        private void Received_IO_Monitor(object s, EventArgs e)
        {
            string data = m_SerialPort.ReadExisting();
            //string data = m_SerialPort.ReadLine();
            DataReceived(data);
        }

        private void btn_ConnectIO_Click(object sender, EventArgs e)
        {
            try
            {
                if (cb_CommList.Text == "")
                {
                    MessageBox.Show(" You need select port. ");
                    return;
                }

                if (cb_CommList.Enabled)
                {
                    m_SerialManager = new SerialManager(cb_CommList.Text, 115200);

                    m_SerialPort= m_SerialManager.GetInstance();

                    m_SerialPort.DataReceived += IO_Monitor_DataReceived;
                    m_SerialPort.Open();
                    btn_ConnectIO.Text = "DISCONNECT";
                    cb_CommList.Enabled = false;
                    btn_ConnectIO.BackColor = Color.PaleGreen;

                }
                else
                {
                    m_SerialPort.Close();
                    btn_ConnectIO.Text = "CONNECT";
                    cb_CommList.Enabled = true;
                    btn_ConnectIO.BackColor = Color.Silver;
                }
            }
            catch (Exception ea)
            {
                MessageBox.Show(ea.Message);
                cb_CommList.Enabled = true;
                btn_ConnectIO.BackColor = Color.Silver;
                btn_ConnectIO.Text = "CONNECT";
            }

        }

        private void toolStripButton_clear_Click(object sender, EventArgs e)
        {
            richtb_IOMonitor.Text = String.Empty;
        }

        private void toolStripButton_save_Click(object sender, EventArgs e)
        {
            SaveFileDialog saveFileDialog = new SaveFileDialog();
            saveFileDialog.Filter = "Text Files (*.txt)|*.txt";
            saveFileDialog.DefaultExt = "txt";
            saveFileDialog.AddExtension = true;

            if (saveFileDialog.ShowDialog() == DialogResult.OK)
            {
                richtb_IOMonitor.SaveFile(saveFileDialog.FileName, RichTextBoxStreamType.PlainText);
            }
        }
    }
}
