﻿using AssistPro.vhf;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.IO.Ports;
using System.Linq;
using System.Net;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using WeifenLuo.WinFormsUI;

using static AssistPro.SerialManager;
using static AssistPro.vhf.DataProtocol;

namespace AssistPro
{
    public partial class ProtocolAnalyzerForm : DockContent
    {

        private SerialManager m_SerialManager;
        private SerialPort m_SerialPort;
        private Stopwatch m_Stopwatch;
        private Thread m_SerialThread;
        private bool m_isCapturing;
        private DataProtocol m_DataProtocol;

        private List<Thread> threads = new List<Thread>();

        public delegate void UpdateDatGridView();

        public UpdateDatGridView UpdateDGV;

        public ProtocolAnalyzerForm()
        {
            InitializeComponent();

            cb_CommList.Enabled = true;
            m_Stopwatch = new Stopwatch();
            m_isCapturing = false;
            toolStripComboBox_raw_mode.SelectedIndex = 0;

            UpdateDGV += AddDataGridViewItem;
        }

        private void AddDataGridViewItem()
        {

            ushort SeqNum = m_DataProtocol.m_DataFrame.SeqNum;
            ushort Length = m_DataProtocol.m_DataFrame.Length;
            ushort Cmd = m_DataProtocol.m_DataFrame.CMD;
            ushort SubCmd = m_DataProtocol.m_DataFrame.SubCmd;


            string Sender, Receiver, CmdTypeStr, ErrStat, RawData;

            //Console.WriteLine(SeqNumStr);

            //long elapsedMilliseconds = m_Stopwatch.ElapsedMilliseconds;

            //long seconds = elapsedMilliseconds / 1000;
            //long milliseconds = elapsedMilliseconds % 1000;

            //TimeStr = string.Format("{0:D}.{1:D4}", seconds, milliseconds);

            string StrDate = DateTime.Now.ToString("HH:mm:ss.fff");

            // cmd type 
            // req : 0
            // rsp : 1
            // set : 2
            // ack : 3

            // target type
            // trans : 0
            // ctrl  : 1

            string strSubCmd = m_DataProtocol.UShortToHex(SubCmd);
            ushort txtcmd;
            ushort subcmd;

            int cmd_type = (Cmd & 0xC0) >> 6;
            int sender = (Cmd & 0x38) >> 3;
            int receiver = (Cmd & 0x07);

            if (sender == 0) Sender = "TRANCEIVER";
            else Sender = "DISPLAY";

            if (receiver == 0) Receiver = "TRANCEIVER";
            else Receiver = "DISPLAY";


            if (cmd_type == 3) CmdTypeStr = "ACK";
            else if (cmd_type == 2) CmdTypeStr = "SET / NOTI";
            else if (cmd_type == 1) CmdTypeStr = "RESPOND";
            else CmdTypeStr = "REQUEST";

            if (m_DataProtocol.m_DataFrame.ErrorStat == ERROR.ERR_SFD) ErrStat = "SFD ERROR";
            else if (m_DataProtocol.m_DataFrame.ErrorStat == ERROR.ERR_LEN) ErrStat = "LENGTH ERROR";
            else if (m_DataProtocol.m_DataFrame.ErrorStat == ERROR.ERR_CRC) ErrStat = "CRC ERROR";
            else ErrStat = "OK";

            if (toolStripComboBox_raw_mode.SelectedIndex == 0) // hex mode
            {
                RawData = m_DataProtocol.ConvertRawDataToString(m_DataProtocol.m_DataFrame.data_buf, Length - 8, true);
            }
            else
            {
                RawData = m_DataProtocol.ConvertRawDataToString(m_DataProtocol.m_DataFrame.data_buf, Length - 8, false);
            }


            if (tb_filter_cmd.Text != string.Empty)
            {

                txtcmd = ushort.Parse(tb_filter_cmd.Text, System.Globalization.NumberStyles.HexNumber);
                subcmd = ushort.Parse(strSubCmd, System.Globalization.NumberStyles.HexNumber);

                if (txtcmd == subcmd)
                {

                    int rowIndex = DataGridView.Rows.Add(SeqNum.ToString(), StrDate, Sender, Receiver, CmdTypeStr, SubCmd.ToString("X2"), Length.ToString(), ErrStat, RawData);

                    if (m_DataProtocol.m_DataFrame.ErrorStat != ERROR.ERR_OK)
                    {
                        DataGridView.Rows[rowIndex].DefaultCellStyle.BackColor = Color.OrangeRed;
                    }
                    DataGridView.FirstDisplayedScrollingRowIndex = rowIndex;
                }
                else
                {
                    return;
                }
            }
            else
            {
                int rowIndex = DataGridView.Rows.Add(SeqNum.ToString(), StrDate, Sender, Receiver, CmdTypeStr, SubCmd.ToString("X2"), Length.ToString(), ErrStat, RawData); ;

                if (m_DataProtocol.m_DataFrame.ErrorStat != ERROR.ERR_OK)
                {
                    DataGridView.Rows[rowIndex].DefaultCellStyle.BackColor = Color.OrangeRed;
                }

                DataGridView.FirstDisplayedScrollingRowIndex = rowIndex;

            }

        }


        private void SerialReceived_IO_Monitor(object s, EventArgs e)
        {
            m_SerialManager.Enqueue();
        }

        private void serialPort1_DataReceived(object sender, SerialDataReceivedEventArgs e)
        {
            // 시리얼통신 수신 시 쓰레드 충돌을 피하기 위해 Invoke를 사용한다.
            if (this.InvokeRequired)
                this.Invoke(new EventHandler(SerialReceived_IO_Monitor));
        }

        private void toolStripButton1_Click(object sender, EventArgs e)
        {
            try
            {
                if (cb_CommList.Text == "")
                {
                    MessageBox.Show(" You need select port. ");
                    return;
                }

                if (cb_CommList.Enabled)
                {
                    m_SerialManager = new SerialManager(cb_CommList.Text, 115200);

                    m_SerialPort = m_SerialManager.GetInstance();

                    m_SerialPort.DataReceived += serialPort1_DataReceived;
                    m_SerialPort.Open();
                    toolStripButton1.Text = "DISCONNECT";
                    cb_CommList.Enabled = false;
                    toolStripButton1.BackColor = Color.PaleGreen;

                    m_DataProtocol = new DataProtocol(m_SerialPort);
                    toolStripButton_Capture.Enabled = true;

                }
                else
                {
                    m_SerialPort.Close();
                    toolStripButton1.Text = "CONNECT";
                    cb_CommList.Enabled = true;
                    toolStripButton1.BackColor = Color.Silver;
                    toolStripButton_Capture.Enabled = false;
                    Invoke(UpdateDGV);
                }
            }
            catch (Exception ea)
            {
                MessageBox.Show(ea.Message);
                cb_CommList.Enabled = true;
                toolStripButton1.BackColor = Color.Silver;
                toolStripButton1.Text = "CONNECT";
            }
        }

        private void Load_DataProtocalAnalyzer(object sender, EventArgs e)
        {
            foreach (string name in SerialPort.GetPortNames())
            {
                cb_CommList.Items.Add(name);
            }

            toolStripButton_Capture.Enabled = false;
        }

        private void toolStripButton_Capture_Click(object sender, EventArgs e)
        {
            if (m_isCapturing == false)
            {
                m_isCapturing = true;
                toolStripButton_Capture.BackColor = Color.PaleGreen;

                m_Stopwatch.Start();

                m_SerialThread = new Thread(SerialDataThread);
                m_SerialThread.Priority = ThreadPriority.Highest;

                threads.Add(m_SerialThread);
                m_SerialThread.Start();
            }
            else
            {
                m_isCapturing = false;
                toolStripButton_Capture.BackColor = Color.Silver;
                m_Stopwatch.Stop();
                m_SerialThread.Abort();
            }
        }

        private void SerialDataThread()
        {
            byte TempData = 0;

            while (true)
            {
                while (m_SerialManager.Dequeue(ref TempData) != BUFF_STATUS.BUFF_NULL_DATA)
                {
                    if (m_DataProtocol.m_RcvFrame.bReceiving == false)
                    {
                        if (TempData == DataProtocol.FRM_SFD)
                        {
                            m_DataProtocol.ResetReceiveFrame();
                            m_DataProtocol.m_RcvFrame.rx_data[m_DataProtocol.m_RcvFrame.rcv_cnt++] = TempData;
                            m_DataProtocol.m_RcvFrame.bReceiving = true;
                        }
                    }
                    else
                    {
                        m_DataProtocol.m_RcvFrame.rx_data[m_DataProtocol.m_RcvFrame.rcv_cnt++] = TempData;
                        if (m_DataProtocol.m_RcvFrame.rcv_cnt == 3)
                        {
                            m_DataProtocol.m_RcvFrame.frame_len = (ushort)((m_DataProtocol.m_RcvFrame.rx_data[1])
                                                                          | ((m_DataProtocol.m_RcvFrame.rx_data[2] << 8) & 0xFF00));

                            if (m_DataProtocol.m_RcvFrame.frame_len < DataProtocol.FRAME_HEADER_SIZE ||
                                m_DataProtocol.m_RcvFrame.frame_len > DataProtocol.MAX_FRAME_LEN)
                            {
                                m_DataProtocol.ResetReceiveFrame();
                            }
                        }
                        else if (m_DataProtocol.m_RcvFrame.rcv_cnt >= m_DataProtocol.m_RcvFrame.frame_len + DataProtocol.FRAME_CRC_SIZE + 1)  // CRC(2) + FRM_SFD(1)
                        {
                            switch (m_DataProtocol.ParseCommand(m_DataProtocol.m_RcvFrame.rx_data, m_DataProtocol.m_RcvFrame.frame_len, m_DataProtocol.m_RcvFrame.rcv_cnt))
                            {
                                case ERROR.ERR_CRC:
                                    {
                                        Console.WriteLine("ERROR.ERR_CRC");
                                        m_DataProtocol.m_DataFrame.ErrorStat = ERROR.ERR_CRC;
                                        m_DataProtocol.ResetReceiveFrame();

                                        break;
                                    }
                                case ERROR.ERR_LEN:
                                    {
                                        Console.WriteLine("ERROR.ERR_LEN");
                                        m_DataProtocol.m_DataFrame.ErrorStat = ERROR.ERR_LEN;
                                        m_DataProtocol.ResetReceiveFrame();
                                    }
                                    break;
                                case ERROR.ERR_SFD:
                                    {
                                        Console.WriteLine("ERROR.ERR_SFD");
                                        m_DataProtocol.m_DataFrame.ErrorStat = ERROR.ERR_SFD;
                                        m_DataProtocol.ResetReceiveFrame();
                                    }
                                    break;
                                case ERROR.ERR_OK:
                                    if (m_DataProtocol.m_DataFrame.RxFrameIdx > 0)
                                    {
                                        m_DataProtocol.m_DataFrame.ErrorStat = ERROR.ERR_OK;
                                        m_DataProtocol.m_DataFrame.RxFrameIdx = 0;
                                        //m_DataProtocol.ProcCommandHandler();
                                    }
                                    break;
                                default:
                                    break;
                            }
                            Invoke(UpdateDGV);
                            m_DataProtocol.ResetReceiveFrame();
                        }
                    }
                }
                //Console.WriteLine("Thread");
                Thread.Sleep(1);
            }
        }

        private void toolStripButton_Capture_Clear(object sender, EventArgs e)
        {
            DataGridView.Rows.Clear();
        }

        private void toolStripButton_Export_Click(object sender, EventArgs e)
        {
            using (SaveFileDialog dlg = new SaveFileDialog())
            {
                dlg.Filter = "csv (*.csv) | *.csv";
                if (dlg.ShowDialog() == DialogResult.OK)
                {
                    Utill.Save_Csv(dlg.FileName, DataGridView, true);
                }
            }
        }

        private void toolStripButton_Import_Click(object sender, EventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.InitialDirectory = @"D:\";
            dlg.Title = "Open CSV Files";
            dlg.CheckFileExists = true;
            dlg.CheckPathExists = true;
            dlg.DefaultExt = "CSV";
            dlg.Filter = "CSV files (*.csv)|*.csv|All Files (*.*)|*.*";
            dlg.FilterIndex = 1;
            dlg.RestoreDirectory = true;
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                string rowValue;
                string[] cellValue;
                DataGridView.Rows.Clear();

                if (System.IO.File.Exists(dlg.FileName))
                {
                    System.IO.StreamReader sr = new System.IO.StreamReader(dlg.FileName);

                    //Reading header
                    rowValue = sr.ReadLine();
                    cellValue = rowValue.Split(',');

                    while (sr.Peek() != -1)
                    {
                        rowValue = sr.ReadLine();
                        cellValue = rowValue.Split(',');
                        DataGridView.Rows.Add(cellValue);
                    }
                    sr.Close();
                }
                else
                {
                    MessageBox.Show("No File is Selected");
                }
            }

        }

        private void tb_filter_KeyPress(object sender, KeyPressEventArgs e)
        {
            // 16진수 값과 백스페이스만 허용
            if (!(char.IsDigit(e.KeyChar) || "ABCDEFabcdef".Contains(e.KeyChar)) && !char.IsControl(e.KeyChar))
            {
                e.Handled = true; // 입력 취소
                MessageBox.Show("16진수 값만 입력할 수 있습니다.");
            }
        }
    }
}
