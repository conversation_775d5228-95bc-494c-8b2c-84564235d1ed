﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Sockets;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using AssistPro.Lib;
using AssistPro.navtex;
using System.Windows;
using System.Drawing;
using System.Threading;

namespace AssistPro.navtex.communication
{
    public sealed class UdpPort
    {
        static readonly UdpPort instance = new UdpPort();

        private UdpClient udpclient;
        private IPEndPoint remoteEP;
        private int connect_status;
        private bool _keepReading = false;
        private Thread _readThread = null; 

        public static UdpPort Instance
        {
            get
            {
                return instance;
            }
        }

        UdpPort()
        {
            connect_status = 0;
        }

        private void StartReading()
        {
            if(_keepReading == false)
            {
                _keepReading = true;
                _readThread = new Thread(ReadThread);
                _readThread.IsBackground = true;
                _readThread.Start();
            }
        }

        private void StopReading()
        {
            if (_keepReading == true)
            {
                _keepReading = false;
                _readThread.Join();
                _readThread.Abort();
                _readThread = null;
            }
        }

        private async void ReadThread()
        {
            while(_keepReading)
            {
                if(IsOpen() == 1)
                {
                    if(udpclient.Available > 0)
                    {
                        try
                        {
                            udpclient.BeginReceive(new AsyncCallback(Receive), udpclient);
                        }
                        catch(Exception ex)
                        {

                        }
                    }
                }
                await Task.Delay(10);
            }
        }

        public delegate void ReceivedHandler(byte[] dataIn);
        public ReceivedHandler Received_Udp;
        private void Receive(IAsyncResult ar)
        {
            if (udpclient == null)
            {
                return;
            }

            try
            {
                byte[] bytes = udpclient.EndReceive(ar, ref remoteEP);
                Received_Udp(bytes);

                MessageBox.Show(Encoding.Default.GetString(bytes));
            }
            catch (Exception ex)
            {
                return;
            }
        }

        public int IsOpen()
        {
            return connect_status;
        }

        public void Open()
        {
            if(IsOpen() == 0)
            {
                try
                {
                    if (IN3000R_Form._in3000r_form.toolStripTextBox_LAN.Text == "" || IN3000R_Form._in3000r_form.toolStripTextBox_LAN.Text == null)
                    {
                        MessageBox.Show(" Please enter IP address. ");
                        return;
                    }

                    if(IN3000R_Form._in3000r_form.toolStripTextBox_Port.Text == "" || IN3000R_Form._in3000r_form.toolStripTextBox_Port.Text == null)
                    {
                        MessageBox.Show(" Please enter port number. ");
                        return;
                    }

                    udpclient = new UdpClient();
                    IPAddress multicast_addr = IPAddress.Parse(IN3000R_Form._in3000r_form.toolStripTextBox_LAN.Text);
                    udpclient.JoinMulticastGroup(multicast_addr);
                    remoteEP = new IPEndPoint(multicast_addr, int.Parse(IN3000R_Form._in3000r_form.toolStripTextBox_Port.Text));

                    StartReading();

                    IN3000R_Form._in3000r_form.toolStripTextBox_LAN.BackColor = Color.FromArgb(0, 255, 0);
                    IN3000R_Form._in3000r_form.toolStripTextBox_Port.BackColor = Color.FromArgb(0, 255, 0);

                    connect_status = 1;
                }
                catch (Exception e)
                {
                    MessageBox.Show(e.Message);
                }
            }
        }

        public void Close()
        {
            udpclient.Close();
            StopReading();

            IN3000R_Form._in3000r_form.toolStripTextBox_LAN.BackColor = Color.FromArgb(255, 0, 0);
            IN3000R_Form._in3000r_form.toolStripTextBox_Port.BackColor = Color.FromArgb(255, 0, 0);

            connect_status = 0;
        }

        public void StrSend(string data)
        {
            if(IsOpen() == 1)
            {
                try
                {
                    byte[] bytes = Encoding.UTF8.GetBytes(data);
                    udpclient.Send(bytes, bytes.Length, remoteEP);
                }
                catch (TimeoutException)
                {
                    MessageBox.Show(System.Reflection.MethodBase.GetCurrentMethod().Name + ' ' + "Timeout Exception");
                }
                catch (Exception ex)
                {
                    MessageBox.Show(System.Reflection.MethodBase.GetCurrentMethod().Name + ' ' + ex.Message);
                }
            }
            else
            {
                MessageBox.Show("IP & Port not connected");
            }
        }

        public void ArrSend(byte[] arrData)
        {
            if(IsOpen() == 1)
            {
                try
                {
                    udpclient.Send(arrData, arrData.Length, remoteEP);
                }
                catch (TimeoutException)
                {
                    MessageBox.Show(System.Reflection.MethodBase.GetCurrentMethod().Name + ' ' + "Timeout Exception");
                }
                catch (Exception ex)
                {
                    MessageBox.Show(System.Reflection.MethodBase.GetCurrentMethod().Name + ' ' + ex.Message);
                }
            }
            else
            {
                MessageBox.Show("IP & Port not connected");
            }
        }
    }
}
