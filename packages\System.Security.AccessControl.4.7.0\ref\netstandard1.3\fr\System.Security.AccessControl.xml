﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Security.AccessControl</name>
  </assembly>
  <members>
    <member name="T:System.Security.AccessControl.AccessControlActions">
      <summary>Spécifie les actions autorisées avec les objets sécurisables.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlActions.Change">
      <summary>Spécifie un accès en écriture seule.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlActions.None">
      <summary>Ne spécifie aucun accès.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlActions.View">
      <summary>Spécifie un accès en lecture seule.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AccessControlModification">
      <summary>Spécifie le type de modification de contrôle d'accès à effectuer.Cette énumération est utilisée par les méthodes de la classe <see cref="T:System.Security.AccessControl.ObjectSecurity" /> et ses descendants.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlModification.Add">
      <summary>Ajoute la règle d'autorisation spécifiée à la liste de contrôle d'accès.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlModification.Remove">
      <summary>Supprime les règles d'autorisation qui contiennent les mêmes identificateur de sécurité et masque d'accès que la règle d'autorisation spécifiée dans la liste de contrôle d'accès.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlModification.RemoveAll">
      <summary>Supprime les règles d'autorisation qui contiennent le même identificateur de sécurité que la règle d'autorisation spécifiée dans la liste de contrôle d'accès.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlModification.RemoveSpecific">
      <summary>Supprime les règles d'autorisation qui correspondent exactement à la règle d'autorisation spécifiée dans la liste de contrôle d'accès.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlModification.Reset">
      <summary>Supprime les règles d'autorisation qui contiennent le même identificateur de sécurité que la règle d'autorisation spécifiée dans la liste de contrôle d'accès, puis ajoute la règle d'autorisation spécifiée à cette liste.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlModification.Set">
      <summary>Supprime toutes les règles d'autorisation de la liste de contrôle d'accès, puis ajoute la règle d'autorisation spécifiée à cette liste.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AccessControlSections">
      <summary>Spécifie quelles sections d'un descripteur de sécurité enregistrer ou charger.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlSections.Access">
      <summary>Liste de contrôle d'accès discrétionnaire.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlSections.All">
      <summary>Descripteur de sécurité entier.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlSections.Audit">
      <summary>Liste de contrôle d'accès système.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlSections.Group">
      <summary>Groupe principal.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlSections.None">
      <summary>Aucune section.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlSections.Owner">
      <summary>Propriétaire.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AccessControlType">
      <summary>Spécifie si un objet <see cref="T:System.Security.AccessControl.AccessRule" /> est utilisé pour autoriser ou refuser l'accès.Ces valeurs ne sont pas des indicateurs et elles ne peuvent pas être combinées.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlType.Allow">
      <summary>L'objet <see cref="T:System.Security.AccessControl.AccessRule" /> sert à autoriser l'accès à un objet sécurisé.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlType.Deny">
      <summary>L'objet <see cref="T:System.Security.AccessControl.AccessRule" /> sert à refuser l'accès à un objet sécurisé.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AccessRule">
      <summary>Représente une combinaison entre identité d'un utilisateur, masque d'accès et type de contrôle d'accès (autorisation ou refus).Un objet <see cref="T:System.Security.AccessControl.AccessRule" /> contient également des informations relatives à la façon dont les objets enfants héritent de la règle et dont cet héritage est propagé.</summary>
    </member>
    <member name="M:System.Security.AccessControl.AccessRule.#ctor(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.AccessRule" /> à l'aide des valeurs spécifiées.</summary>
      <param name="identity">Identité à laquelle la règle d'accès s'applique.Ce paramètre doit désigner un objet pouvant être casté en tant que <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <param name="accessMask">Masque d'accès de cette règle.Le masque d'accès est une collection 32 bits de bits anonymes, dont la signification est définie par les intégrateurs individuels.</param>
      <param name="isInherited">true si cette règle est héritée d'un conteneur parent.</param>
      <param name="inheritanceFlags">Propriétés d'héritage de la règle d'accès.</param>
      <param name="propagationFlags">Indique si les règles d'accès héritées sont automatiquement propagées.Les indicateurs de propagation sont ignorés si <paramref name="inheritanceFlags" /> a la valeur <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</param>
      <param name="type">Type de contrôle d'accès valide.</param>
      <exception cref="T:System.ArgumentException">La valeur du paramètre <paramref name="identity" /> ne peut pas être castée en tant que <see cref="T:System.Security.Principal.SecurityIdentifier" /> ou le paramètre <paramref name="type" /> contient une valeur non valide.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Le paramètre <paramref name="accessMask" /> a la valeur zéro ou les paramètres <paramref name="inheritanceFlags" /> ou <paramref name="propagationFlags" /> contiennent des valeurs d'indicateur non reconnues.</exception>
    </member>
    <member name="P:System.Security.AccessControl.AccessRule.AccessControlType">
      <summary>Obtient la valeur <see cref="T:System.Security.AccessControl.AccessControlType" /> associée à cet objet <see cref="T:System.Security.AccessControl.AccessRule" />.</summary>
      <returns>Valeur <see cref="T:System.Security.AccessControl.AccessControlType" /> associée à cet objet <see cref="T:System.Security.AccessControl.AccessRule" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.AccessRule`1">
      <summary>Représente une combinaison entre identité d'un utilisateur, masque d'accès et type de contrôle d'accès (autorisation ou refus).Un objet AccessRule`1 contient également des informations relatives à la façon dont les objets enfants héritent de la règle et dont cet héritage est propagé.</summary>
      <typeparam name="T">Type de droits d'accès pour la règle d'accès.</typeparam>
    </member>
    <member name="M:System.Security.AccessControl.AccessRule`1.#ctor(System.Security.Principal.IdentityReference,`0,System.Security.AccessControl.AccessControlType)">
      <summary>Initialise une nouvelle instance de la classe AccessRule'1 à l'aide des valeurs spécifiées.</summary>
      <param name="identity">Identité à laquelle la règle d'accès s'applique.</param>
      <param name="rights">Droits de la règle d'accès.</param>
      <param name="type">Type de contrôle d'accès valide.</param>
    </member>
    <member name="M:System.Security.AccessControl.AccessRule`1.#ctor(System.Security.Principal.IdentityReference,`0,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
      <summary>Initialise une nouvelle instance de la classe AccessRule'1 à l'aide des valeurs spécifiées.</summary>
      <param name="identity">Identité à laquelle la règle d'accès s'applique.</param>
      <param name="rights">Droits de la règle d'accès.</param>
      <param name="inheritanceFlags">Propriétés d'héritage de la règle d'accès.</param>
      <param name="propagationFlags">Indique si les règles d'accès héritées sont automatiquement propagées.Les indicateurs de propagation sont ignorés si <paramref name="inheritanceFlags" /> a la valeur <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</param>
      <param name="type">Type de contrôle d'accès valide.</param>
    </member>
    <member name="M:System.Security.AccessControl.AccessRule`1.#ctor(System.String,`0,System.Security.AccessControl.AccessControlType)">
      <summary>Initialise une nouvelle instance de la classe AccessRule'1 à l'aide des valeurs spécifiées.</summary>
      <param name="identity">Identité à laquelle la règle d'accès s'applique.</param>
      <param name="rights">Droits de la règle d'accès.</param>
      <param name="type">Type de contrôle d'accès valide.</param>
    </member>
    <member name="M:System.Security.AccessControl.AccessRule`1.#ctor(System.String,`0,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
      <summary>Initialise une nouvelle instance de la classe AccessRule'1 à l'aide des valeurs spécifiées.</summary>
      <param name="identity">Identité à laquelle la règle d'accès s'applique.</param>
      <param name="rights">Droits de la règle d'accès.</param>
      <param name="inheritanceFlags">Propriétés d'héritage de la règle d'accès.</param>
      <param name="propagationFlags">Indique si les règles d'accès héritées sont automatiquement propagées.Les indicateurs de propagation sont ignorés si <paramref name="inheritanceFlags" /> a la valeur <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</param>
      <param name="type">Type de contrôle d'accès valide.</param>
    </member>
    <member name="P:System.Security.AccessControl.AccessRule`1.Rights">
      <summary>Obtient les droits de l'instance actuelle.</summary>
      <returns>Droits, castés en tant que type &lt;T&gt;, de l'instance actuelle.</returns>
    </member>
    <member name="T:System.Security.AccessControl.AceEnumerator">
      <summary>Donne la possibilité d'itérer au sein des entrées d'une liste de contrôle d'accès (ACL). </summary>
    </member>
    <member name="P:System.Security.AccessControl.AceEnumerator.Current">
      <summary>Obtient l'élément en cours dans la collection <see cref="T:System.Security.AccessControl.GenericAce" />.Cette propriété obtient la version de type convivial de l'objet.</summary>
      <returns>Élément en cours dans la collection <see cref="T:System.Security.AccessControl.GenericAce" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.AceEnumerator.MoveNext">
      <summary>Avance l'énumérateur à l'élément suivant de la collection <see cref="T:System.Security.AccessControl.GenericAce" />.</summary>
      <returns>true si l'énumérateur a pu avancer jusqu'à l'élément suivant ; false si l'énumérateur a dépassé la fin de la collection.</returns>
      <exception cref="T:System.InvalidOperationException">La collection a été modifiée après la création de l'énumérateur.</exception>
    </member>
    <member name="M:System.Security.AccessControl.AceEnumerator.Reset">
      <summary>Rétablit l'énumérateur à sa position initiale, qui précède le premier élément de la collection <see cref="T:System.Security.AccessControl.GenericAce" />.</summary>
      <exception cref="T:System.InvalidOperationException">La collection a été modifiée après la création de l'énumérateur.</exception>
    </member>
    <member name="P:System.Security.AccessControl.AceEnumerator.System#Collections#IEnumerator#Current"></member>
    <member name="T:System.Security.AccessControl.AceFlags">
      <summary>Spécifie le comportement, en matière d'héritage et d'audit, d'une entrée du contrôle d'accès.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.AuditFlags">
      <summary>Toutes les tentatives d'accès sont auditées.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.ContainerInherit">
      <summary>Le masque d'accès est propagé aux objets conteneurs enfants.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.FailedAccess">
      <summary>Les échecs de tentatives d'accès sont audités.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.InheritanceFlags">
      <summary>OR logique de <see cref="F:System.Security.AccessControl.AceFlags.ObjectInherit" />, <see cref="F:System.Security.AccessControl.AceFlags.ContainerInherit" />, <see cref="F:System.Security.AccessControl.AceFlags.NoPropagateInherit" /> et <see cref="F:System.Security.AccessControl.AceFlags.InheritOnly" />.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.Inherited">
      <summary>Une entrée du contrôle d'accès est héritée d'un conteneur parent plutôt que définie explicitement pour un objet.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.InheritOnly">
      <summary>Le masque d'accès ne se propage qu'aux objets enfants.Les objets enfants sont des objets conteneurs et descendants.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.None">
      <summary>Aucun indicateur d'entrée du contrôle d'accès n'est défini.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.NoPropagateInherit">
      <summary>Les vérifications d'accès ne s'appliquent pas à l'objet ; elles ne s'appliquent qu'à ses enfants.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.ObjectInherit">
      <summary>Le masque d'accès se propage sur les objets enfants descendants.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.SuccessfulAccess">
      <summary>Les tentatives d'accès réussies sont auditées.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AceQualifier">
      <summary>Spécifie la fonction d'une entrée du contrôle d'accès.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceQualifier.AccessAllowed">
      <summary>Autorise l'accès.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceQualifier.AccessDenied">
      <summary>Refuse l'accès.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceQualifier.SystemAlarm">
      <summary>Provoque une alerte système.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceQualifier.SystemAudit">
      <summary>Provoque un audit système.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AceType">
      <summary>Définit les types d'entrées du contrôle d'accès disponibles.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessAllowed">
      <summary>Autorise un client approuvé spécifique identifié par un objet <see cref="T:System.Security.Principal.IdentityReference" /> à accéder à un objet.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessAllowedCallback">
      <summary>Autorise un client approuvé spécifique identifié par un objet <see cref="T:System.Security.Principal.IdentityReference" /> à accéder à un objet.Ce type d'entrée du contrôle d'accès peut contenir des données de rappel facultatives.Les données de rappel représentent un BLOB, spécifique au gestionnaire des ressources, non interprété.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessAllowedCallbackObject">
      <summary>Autorise l'accès à un objet, un jeu de propriétés ou une propriété.L'entrée du contrôle d'accès contient un jeu de droits d'accès, un GUID qui identifie le type d'objet et un objet <see cref="T:System.Security.Principal.IdentityReference" /> qui identifie le client approuvé à qui le système accorde l'accès.L'entrée du contrôle d'accès contient également un GUID et un jeu d'indicateurs qui contrôlent la façon dont les objets enfants héritent de cette entrée.Ce type d'entrée du contrôle d'accès peut contenir des données de rappel facultatives.Les données de rappel représentent un BLOB, spécifique au gestionnaire des ressources, non interprété.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessAllowedCompound">
      <summary>Défini mais jamais utilisé.Inclus ici par souci d'intégrité.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessAllowedObject">
      <summary>Autorise l'accès à un objet, un jeu de propriétés ou une propriété.L'entrée du contrôle d'accès contient un jeu de droits d'accès, un GUID qui identifie le type d'objet et un objet <see cref="T:System.Security.Principal.IdentityReference" /> qui identifie le client approuvé à qui le système accorde l'accès.L'entrée du contrôle d'accès contient également un GUID et un jeu d'indicateurs qui contrôlent la façon dont les objets enfants héritent de cette entrée.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessDenied">
      <summary>Refuse à un client approuvé spécifique, identifié par un objet <see cref="T:System.Security.Principal.IdentityReference" />, l'accès à un objet.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessDeniedCallback">
      <summary>Refuse à un client approuvé spécifique, identifié par un objet <see cref="T:System.Security.Principal.IdentityReference" />, l'accès à un objet.Ce type d'entrée du contrôle d'accès peut contenir des données de rappel facultatives.Les données de rappel représentent un BLOB, spécifique au gestionnaire des ressources, non interprété.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessDeniedCallbackObject">
      <summary>Refuse l'accès à un objet, un jeu de propriétés ou une propriété.L'entrée du contrôle d'accès contient un jeu de droits d'accès, un GUID qui identifie le type d'objet et un objet <see cref="T:System.Security.Principal.IdentityReference" /> qui identifie le client approuvé à qui le système accorde l'accès.L'entrée du contrôle d'accès contient également un GUID et un jeu d'indicateurs qui contrôlent la façon dont les objets enfants héritent de cette entrée.Ce type d'entrée du contrôle d'accès peut contenir des données de rappel facultatives.Les données de rappel représentent un BLOB, spécifique au gestionnaire des ressources, non interprété.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessDeniedObject">
      <summary>Refuse l'accès à un objet, un jeu de propriétés ou une propriété.L'entrée du contrôle d'accès contient un jeu de droits d'accès, un GUID qui identifie le type d'objet et un objet <see cref="T:System.Security.Principal.IdentityReference" /> qui identifie le client approuvé à qui le système accorde l'accès.L'entrée du contrôle d'accès contient également un GUID et un jeu d'indicateurs qui contrôlent la façon dont les objets enfants héritent de cette entrée.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.MaxDefinedAceType">
      <summary>Suit le type d'entrée du contrôle d'accès maximal de l'énumération.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAlarm">
      <summary>Réservé à une utilisation future.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAlarmCallback">
      <summary>Réservé à une utilisation future.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAlarmCallbackObject">
      <summary>Réservé à une utilisation future.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAlarmObject">
      <summary>Réservé à une utilisation future.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAudit">
      <summary>Enregistre un message d'audit lorsqu'un client approuvé spécifié tente un accès à un objet.Le client approuvé est identifié par un objet <see cref="T:System.Security.Principal.IdentityReference" />.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAuditCallback">
      <summary>Enregistre un message d'audit lorsqu'un client approuvé spécifié tente un accès à un objet.Le client approuvé est identifié par un objet <see cref="T:System.Security.Principal.IdentityReference" />.Ce type d'entrée du contrôle d'accès peut contenir des données de rappel facultatives.Les données de rappel représentent un BLOB, spécifique au gestionnaire des ressources, non interprété.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAuditCallbackObject">
      <summary>Enregistre un message d'audit lorsqu'un client approuvé spécifié tente un accès à un objet ou à des sous-objets, tels que des jeux de propriétés ou propriétés.L'entrée du contrôle d'accès contient un jeu de droits d'accès, un GUID qui identifie le type d'objet ou sous-objet et un objet <see cref="T:System.Security.Principal.IdentityReference" /> qui identifie le client approuvé pour qui le système audite l'accès.L'entrée du contrôle d'accès contient également un GUID et un jeu d'indicateurs qui contrôlent la façon dont les objets enfants héritent de cette entrée.Ce type d'entrée du contrôle d'accès peut contenir des données de rappel facultatives.Les données de rappel représentent un BLOB, spécifique au gestionnaire des ressources, non interprété.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAuditObject">
      <summary>Enregistre un message d'audit lorsqu'un client approuvé spécifié tente un accès à un objet ou à des sous-objets, tels que des jeux de propriétés ou propriétés.L'entrée du contrôle d'accès contient un jeu de droits d'accès, un GUID qui identifie le type d'objet ou sous-objet et un objet <see cref="T:System.Security.Principal.IdentityReference" /> qui identifie le client approuvé pour qui le système audite l'accès.L'entrée du contrôle d'accès contient également un GUID et un jeu d'indicateurs qui contrôlent la façon dont les objets enfants héritent de cette entrée.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AuditFlags">
      <summary>Spécifie les conditions dans lesquelles l'audit tente d'accéder à un objet sécurisable.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AuditFlags.Failure">
      <summary>Les échecs des tentatives d'accès sont à auditer.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AuditFlags.None">
      <summary>Aucune tentative d'accès n'est à auditer.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AuditFlags.Success">
      <summary>Les tentatives d'accès réussies sont à auditer.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AuditRule">
      <summary>Représente une combinaison entre identité d'un utilisateur et masque d'accès.Un objet <see cref="T:System.Security.AccessControl.AuditRule" /> contient également des informations sur la façon dont les objets enfants héritent de la règle, sur la façon dont cet héritage est propagé et les conditions gouvernant à l'audit.</summary>
    </member>
    <member name="M:System.Security.AccessControl.AuditRule.#ctor(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.AuditRule" /> à l'aide des valeurs spécifiées.</summary>
      <param name="identity">Identité à laquelle la règle d'audit s'applique.Il doit s'agir d'un objet pouvant être casté en tant que <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <param name="accessMask">Masque d'accès de cette règle.Le masque d'accès est une collection 32 bits de bits anonymes, dont la signification est définie par les intégrateurs individuels.</param>
      <param name="isInherited">true pour hériter de cette règle à partir d'un conteneur parent.</param>
      <param name="inheritanceFlags">Propriétés d'héritage de la règle d'audit.</param>
      <param name="propagationFlags">Indique si les règles d'audit héritées sont automatiquement propagées.Les indicateurs de propagation sont ignorés si <paramref name="inheritanceFlags" /> a la valeur <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</param>
      <param name="auditFlags">Conditions gouvernant à l'audit de la règle.</param>
      <exception cref="T:System.ArgumentException">La valeur du paramètre <paramref name="identity" /> ne peut pas être castée en tant que <see cref="T:System.Security.Principal.SecurityIdentifier" /> ou le paramètre <paramref name="auditFlags" /> contient une valeur non valide.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Le paramètre <paramref name="accessMask" /> a la valeur zéro ou les paramètres <paramref name="inheritanceFlags" /> ou <paramref name="propagationFlags" /> contiennent des valeurs d'indicateur non reconnues.</exception>
    </member>
    <member name="P:System.Security.AccessControl.AuditRule.AuditFlags">
      <summary>Obtient les indicateurs d'audit de cette règle d'audit.</summary>
      <returns>Combinaison d'opérations de bits des valeurs d'énumération.Cette combinaison spécifie les conditions d'audit de cette règle d'audit.</returns>
    </member>
    <member name="T:System.Security.AccessControl.AuditRule`1">
      <summary>Représente une combinaison entre identité d'un utilisateur et masque d'accès.</summary>
      <typeparam name="T"></typeparam>
    </member>
    <member name="M:System.Security.AccessControl.AuditRule`1.#ctor(System.Security.Principal.IdentityReference,`0,System.Security.AccessControl.AuditFlags)">
      <summary>Initialise une nouvelle instance de la classe AuditRule'1 à l'aide des valeurs spécifiées.</summary>
      <param name="identity">Identité à laquelle cette règle d'audit s'applique.</param>
      <param name="rights">Droits de la règle d'audit.</param>
      <param name="flags">Conditions gouvernant à l'audit de la règle.</param>
    </member>
    <member name="M:System.Security.AccessControl.AuditRule`1.#ctor(System.Security.Principal.IdentityReference,`0,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
      <summary>Initialise une nouvelle instance de la classe AuditRule'1 à l'aide des valeurs spécifiées.</summary>
      <param name="identity">Identité à laquelle la règle d'audit s'applique. </param>
      <param name="rights">Droits de la règle d'audit.</param>
      <param name="inheritanceFlags">Propriétés d'héritage de la règle d'audit.</param>
      <param name="propagationFlags">Indique si les règles d'audit héritées sont automatiquement propagées.</param>
      <param name="flags">Conditions gouvernant à l'audit de la règle.</param>
    </member>
    <member name="M:System.Security.AccessControl.AuditRule`1.#ctor(System.String,`0,System.Security.AccessControl.AuditFlags)">
      <summary>Initialise une nouvelle instance de la classe AuditRule'1 à l'aide des valeurs spécifiées.</summary>
      <param name="identity">Identité à laquelle la règle d'audit s'applique.</param>
      <param name="rights">Droits de la règle d'audit.</param>
      <param name="flags">Propriétés de la règle d'audit.</param>
    </member>
    <member name="M:System.Security.AccessControl.AuditRule`1.#ctor(System.String,`0,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
      <summary>Initialise une nouvelle instance de la classe AuditRule'1 à l'aide des valeurs spécifiées.</summary>
      <param name="identity">Identité à laquelle la règle d'audit s'applique.</param>
      <param name="rights">Droits de la règle d'audit.</param>
      <param name="inheritanceFlags">Propriétés d'héritage de la règle d'audit.</param>
      <param name="propagationFlags">Indique si les règles d'audit héritées sont automatiquement propagées.</param>
      <param name="flags">Conditions gouvernant à l'audit de la règle.</param>
    </member>
    <member name="P:System.Security.AccessControl.AuditRule`1.Rights">
      <summary>Droits de la règle d'audit.</summary>
      <returns>retourne <see cref="{0}" /> ;</returns>
    </member>
    <member name="T:System.Security.AccessControl.AuthorizationRule">
      <summary>Détermine l'accès aux objets sécurisables.Les classes dérivées <see cref="T:System.Security.AccessControl.AccessRule" /> et <see cref="T:System.Security.AccessControl.AuditRule" /> permettent de spécialiser les fonctionnalités d'accès et d'audit.</summary>
    </member>
    <member name="M:System.Security.AccessControl.AuthorizationRule.#ctor(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.AuthorizationControl.AccessRule" /> à l'aide des valeurs spécifiées.</summary>
      <param name="identity">Identité à laquelle la règle d'accès s'applique.  Ce paramètre doit désigner un objet pouvant être casté en tant que <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <param name="accessMask">Masque d'accès de cette règle.Le masque d'accès est une collection 32 bits de bits anonymes, dont la signification est définie par les intégrateurs individuels.</param>
      <param name="isInherited">true pour hériter de cette règle à partir d'un conteneur parent.</param>
      <param name="inheritanceFlags">Propriétés d'héritage de la règle d'accès.</param>
      <param name="propagationFlags">Indique si les règles d'accès héritées sont automatiquement propagées.Les indicateurs de propagation sont ignorés si <paramref name="inheritanceFlags" /> a la valeur <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</param>
      <exception cref="T:System.ArgumentException">La valeur du paramètre <paramref name="identity" /> ne peut pas être castée en tant que <see cref="T:System.Security.Principal.SecurityIdentifier" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Le paramètre <paramref name="accessMask" /> a la valeur zéro ou les paramètres <paramref name="inheritanceFlags" /> ou <paramref name="propagationFlags" /> contiennent des valeurs d'indicateur non reconnues.</exception>
    </member>
    <member name="P:System.Security.AccessControl.AuthorizationRule.AccessMask">
      <summary>Obtient le masque d'accès de cette règle.</summary>
      <returns>Masque d'accès de cette règle.</returns>
    </member>
    <member name="P:System.Security.AccessControl.AuthorizationRule.IdentityReference">
      <summary>Obtient <see cref="T:System.Security.Principal.IdentityReference" /> auquel cette règle s'applique.</summary>
      <returns>
        <see cref="T:System.Security.Principal.IdentityReference" /> auquel cette règle s'applique.</returns>
    </member>
    <member name="P:System.Security.AccessControl.AuthorizationRule.InheritanceFlags">
      <summary>Obtient la valeur des indicateurs qui déterminent la façon dont les objets enfants héritent de cette règle.</summary>
      <returns>Combinaison d'opérations de bits des valeurs d'énumération.</returns>
    </member>
    <member name="P:System.Security.AccessControl.AuthorizationRule.IsInherited">
      <summary>Obtient une valeur qui indique si cette règle est établie explicitement ou si elle est héritée d'un objet conteneur parent.</summary>
      <returns>true si cette règle n'est pas établie explicitement mais héritée d'un conteneur parent.</returns>
    </member>
    <member name="P:System.Security.AccessControl.AuthorizationRule.PropagationFlags">
      <summary>Obtient la valeur des indicateurs de propagation, qui déterminent la façon dont l'héritage de cette règle se propage aux objets enfants.Cette propriété n'a de lieu d'être que si la valeur de l'énumération <see cref="T:System.Security.AccessControl.InheritanceFlags" /> n'est pas <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</summary>
      <returns>Combinaison d'opérations de bits des valeurs d'énumération.</returns>
    </member>
    <member name="T:System.Security.AccessControl.AuthorizationRuleCollection">
      <summary>Représente une collection d'objets <see cref="T:System.Security.AccessControl.AuthorizationRule" />.</summary>
    </member>
    <member name="M:System.Security.AccessControl.AuthorizationRuleCollection.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.AuthorizationRuleCollection" />.</summary>
    </member>
    <member name="M:System.Security.AccessControl.AuthorizationRuleCollection.AddRule(System.Security.AccessControl.AuthorizationRule)">
      <summary>Ajoute un objet <see cref="T:System.Web.Configuration.AuthorizationRule" /> à la collection.</summary>
      <param name="rule">Objet <see cref="T:System.Web.Configuration.AuthorizationRule" /> à ajouter à la collection.</param>
    </member>
    <member name="M:System.Security.AccessControl.AuthorizationRuleCollection.CopyTo(System.Security.AccessControl.AuthorizationRule[],System.Int32)">
      <summary>Copie le contenu de la collection dans un tableau.</summary>
      <param name="rules">Tableau dans lequel copier le contenu de la collection.</param>
      <param name="index">Index de base zéro à partir duquel commencer la copie.</param>
    </member>
    <member name="P:System.Security.AccessControl.AuthorizationRuleCollection.Count"></member>
    <member name="P:System.Security.AccessControl.AuthorizationRuleCollection.Item(System.Int32)">
      <summary>Obtient l'objet <see cref="T:System.Security.AccessControl.AuthorizationRule" /> à l'index spécifié dans la collection.</summary>
      <returns>Objet <see cref="T:System.Security.AccessControl.AuthorizationRule" /> à l'index spécifié.</returns>
      <param name="index">Index de base zéro de l'objet <see cref="T:System.Security.AccessControl.AuthorizationRule" /> à obtenir.</param>
    </member>
    <member name="M:System.Security.AccessControl.AuthorizationRuleCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)"></member>
    <member name="P:System.Security.AccessControl.AuthorizationRuleCollection.System#Collections#ICollection#IsSynchronized"></member>
    <member name="P:System.Security.AccessControl.AuthorizationRuleCollection.System#Collections#ICollection#SyncRoot"></member>
    <member name="M:System.Security.AccessControl.AuthorizationRuleCollection.System#Collections#IEnumerable#GetEnumerator"></member>
    <member name="T:System.Security.AccessControl.CommonAce">
      <summary>Représente une entrée du contrôle d'accès.</summary>
    </member>
    <member name="M:System.Security.AccessControl.CommonAce.#ctor(System.Security.AccessControl.AceFlags,System.Security.AccessControl.AceQualifier,System.Int32,System.Security.Principal.SecurityIdentifier,System.Boolean,System.Byte[])">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.CommonAce" />.</summary>
      <param name="flags">Indicateurs qui spécifient des informations relatives à l'héritage, à la propagation d'héritage et aux conditions d'audit de la nouvelle entrée du contrôle d'accès.</param>
      <param name="qualifier">Utilisation de la nouvelle entrée du contrôle d'accès.</param>
      <param name="accessMask">Masque d'accès de l'entrée du contrôle d'accès.</param>
      <param name="sid">
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> associé à la nouvelle entrée du contrôle d'accès.</param>
      <param name="isCallback">true pour spécifier que la nouvelle entrée du contrôle d'accès est de type rappel.</param>
      <param name="opaque">Données opaques associées à la nouvelle entrée du contrôle d'accès.Les données opaques ne sont autorisées que dans les entrées du contrôle d'accès de type rappel.Ce tableau ne doit pas avoir une longueur supérieure à la valeur de retour de la méthode <see cref="M:System.Security.AccessControl.CommonAce.MaxOpaqueLength(System.Boolean)" />.</param>
    </member>
    <member name="P:System.Security.AccessControl.CommonAce.BinaryLength">
      <summary>Obtient la longueur, en octets, de la représentation binaire de l'objet <see cref="T:System.Security.AccessControl.CommonAce" /> en cours.Utilisez cette durée avec la méthode <see cref="M:System.Security.AccessControl.CommonAce.GetBinaryForm(System.Byte[],System.Int32)" /> avant de marshaler l'entrée du contrôle d'accès dans un tableau binaire.</summary>
      <returns>Longueur, en octets, de la représentation binaire de l'objet <see cref="T:System.Security.AccessControl.CommonAce" /> en cours.</returns>
    </member>
    <member name="M:System.Security.AccessControl.CommonAce.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Marshale le contenu de l'objet <see cref="T:System.Security.AccessControl.CommonAce" /> dans le tableau d'octets spécifié en commençant à l'offset spécifié.</summary>
      <param name="binaryForm">Tableau d'octets dans lequel le contenu de l'objet <see cref="T:System.Security.AccessControl.CommonAce" /> est marshalé.</param>
      <param name="offset">Offset au niveau duquel commencer le marshaling.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> est négatif ou trop élevé pour que <see cref="T:System.Security.AccessControl.CommonAce" /> puisse être entièrement copié dans le tableau <paramref name="binaryForm" />.</exception>
    </member>
    <member name="M:System.Security.AccessControl.CommonAce.MaxOpaqueLength(System.Boolean)">
      <summary>Obtient la longueur maximale autorisée d'un blob de données opaque pour les entrées du contrôle d'accès de type rappel.</summary>
      <returns>Longueur autorisée d'un objet BLOB de données opaque.</returns>
      <param name="isCallback">true pour spécifier que l'objet <see cref="T:System.Security.AccessControl.CommonAce" /> est une entrée du contrôle d'accès de type rappel.</param>
    </member>
    <member name="T:System.Security.AccessControl.CommonAcl">
      <summary>Représente une liste de contrôle d'accès et constitue la classe de base des classes <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> et <see cref="T:System.Security.AccessControl.SystemAcl" />.</summary>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.BinaryLength">
      <summary>Obtient la longueur, en octets, de la représentation binaire de l'objet <see cref="T:System.Security.AccessControl.CommonAcl" /> actuel.Cette longueur doit être utilisée avant de marshaler la liste de contrôle d'accès dans un tableau binaire avec la méthode <see cref="M:System.Security.AccessControl.CommonAcl.GetBinaryForm(System.Byte[],System.Int32)" />.</summary>
      <returns>Longueur, en octets, de la représentation binaire de l'objet <see cref="T:System.Security.AccessControl.CommonAcl" /> actuel.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.Count">
      <summary>Obtient le nombre d'entrées du contrôle d'accès dans l'objet <see cref="T:System.Security.AccessControl.CommonAcl" /> actuel.</summary>
      <returns>Nombre d'entrées du contrôle d'accès dans l'objet <see cref="T:System.Security.AccessControl.CommonAcl" /> actuel.</returns>
    </member>
    <member name="M:System.Security.AccessControl.CommonAcl.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Marshale le contenu de l'objet <see cref="T:System.Security.AccessControl.CommonAcl" /> dans le tableau d'octets spécifié en commençant à l'offset spécifié.</summary>
      <param name="binaryForm">Tableau d'octets dans lequel le contenu de <see cref="T:System.Security.AccessControl.CommonAcl" /> est marshalé.</param>
      <param name="offset">Offset au niveau duquel commencer le marshaling.</param>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.IsCanonical">
      <summary>Obtient une valeur booléenne qui spécifie si les entrées du contrôle d'accès de l'objet <see cref="T:System.Security.AccessControl.CommonAcl" /> actuel suivent un ordre canonique.</summary>
      <returns>true si les entrées du contrôle d'accès de l'objet <see cref="T:System.Security.AccessControl.CommonAcl" /> actuel suivent un ordre canonique ; sinon, false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.IsContainer">
      <summary>Définit si l'objet <see cref="T:System.Security.AccessControl.CommonAcl" /> est un conteneur. </summary>
      <returns>true si l'objet actuel <see cref="T:System.Security.AccessControl.CommonAcl" /> est un conteneur.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.IsDS">
      <summary>Définit si l'objet <see cref="T:System.Security.AccessControl.CommonAcl" /> actuel est une liste de contrôle d'accès d'objets d'annuaire.</summary>
      <returns>true si l'objet <see cref="T:System.Security.AccessControl.CommonAcl" /> actuel est une liste de contrôle d'accès d'objets d'annuaire.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.Item(System.Int32)">
      <summary>Obtient ou définit le <see cref="T:System.Security.AccessControl.CommonAce" /> à l'index spécifié.</summary>
      <returns>Objet <see cref="T:System.Security.AccessControl.CommonAce" /> à l'index spécifié.</returns>
      <param name="index">Index de base zéro du <see cref="T:System.Security.AccessControl.CommonAce" /> à obtenir ou définir.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonAcl.Purge(System.Security.Principal.SecurityIdentifier)">
      <summary>Supprime toutes les entrées du contrôle d'accès contenues par cet objet <see cref="T:System.Security.AccessControl.CommonAcl" /> et associées à l'objet <see cref="T:System.Security.Principal.SecurityIdentifier" /> spécifié.</summary>
      <param name="sid">Objet <see cref="T:System.Security.Principal.SecurityIdentifier" /> à vérifier.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonAcl.RemoveInheritedAces">
      <summary>Supprime toutes les entrées du contrôle d'accès héritées de cet objet <see cref="T:System.Security.AccessControl.CommonAcl" />.</summary>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.Revision">
      <summary>Obtient le niveau de révision de <see cref="T:System.Security.AccessControl.CommonAcl" />.</summary>
      <returns>Valeur d'octet qui spécifie le niveau de révision de <see cref="T:System.Security.AccessControl.CommonAcl" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.CommonObjectSecurity">
      <summary>Contrôle l'accès aux objets sans manipulation directe de listes de contrôle d'accès.Cette classe est la classe de base abstraite de la classe <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</summary>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.#ctor(System.Boolean)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <param name="isContainer">true si le nouvel objet est un objet conteneur.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.AddAccessRule(System.Security.AccessControl.AccessRule)">
      <summary>Ajoute la règle d'accès spécifiée à la liste de contrôle d'accès discrétionnaire associée à cet objet <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <param name="rule">Règle d'accès à ajouter.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.AddAuditRule(System.Security.AccessControl.AuditRule)">
      <summary>Ajoute la règle d'audit spécifiée à la liste de contrôle d'accès système associée à cet objet <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <param name="rule">Règle d'audit à ajouter.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.GetAccessRules(System.Boolean,System.Boolean,System.Type)">
      <summary>Obtient une collection des règles d'accès associées à l'identificateur de sécurité spécifié.</summary>
      <returns>Collection des règles d'accès associées à l'objet <see cref="T:System.Security.Principal.SecurityIdentifier" /> spécifié.</returns>
      <param name="includeExplicit">true pour inclure des règles d'accès définies explicitement pour l'objet.</param>
      <param name="includeInherited">true pour inclure des règles d'accès héritées.</param>
      <param name="targetType">Spécifie si l'identificateur de sécurité pour lequel récupérer les règles d'accès est de type T:System.Security.Principal.SecurityIdentifier ou T:System.Security.Principal.NTAccount.La valeur de ce paramètre doit être un type qui peut être traduit vers le type <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.GetAuditRules(System.Boolean,System.Boolean,System.Type)">
      <summary>Obtient une collection des règles d'audit associées à l'identificateur de sécurité spécifié.</summary>
      <returns>Collection des règles d'audit associées à l'objet <see cref="T:System.Security.Principal.SecurityIdentifier" /> spécifié.</returns>
      <param name="includeExplicit">true pour inclure des règles d'audit définies explicitement pour l'objet.</param>
      <param name="includeInherited">true pour inclure des règles d'audit héritées.</param>
      <param name="targetType">Identificateur de sécurité pour lequel récupérer des règles d'audit.Il doit s'agir d'un objet pouvant être casté en tant qu'objet <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.ModifyAccess(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AccessRule,System.Boolean@)">
      <summary>Applique la modification spécifiée à la liste de contrôle d'accès discrétionnaire associée à cet objet <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <returns>true si la modification de la liste de contrôle d'accès discrétionnaire réussit ; sinon false.</returns>
      <param name="modification">Modification à appliquer à la liste de contrôle d'accès discrétionnaire.</param>
      <param name="rule">Règle d'accès à modifier.</param>
      <param name="modified">true si la modification de la liste de contrôle d'accès discrétionnaire réussit ; sinon false.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.ModifyAudit(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AuditRule,System.Boolean@)">
      <summary>Applique la modification spécifiée à la liste de contrôle d'accès système associée à cet objet <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <returns>true si la modification de la liste de contrôle d'accès système réussit ; sinon false.</returns>
      <param name="modification">Modification à appliquer à la liste de contrôle d'accès système.</param>
      <param name="rule">Règle d'audit à modifier.</param>
      <param name="modified">true si la modification de la liste de contrôle d'accès système réussit ; sinon false.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAccessRule(System.Security.AccessControl.AccessRule)">
      <summary>Supprime les règles d'accès qui contiennent les mêmes identificateur de sécurité et masque d'accès que la règle d'accès spécifiée dans la liste de contrôle d'accès discrétionnaire associée à cet objet <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <returns>true si la suppression de la règle d'accès a réussi ; sinon, false.</returns>
      <param name="rule">Règle d'accès à supprimer.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAccessRuleAll(System.Security.AccessControl.AccessRule)">
      <summary>Supprime toutes les règles d'accès qui ont le même identificateur de sécurité que la règle d'accès spécifiée dans la liste de contrôle d'accès discrétionnaire associée à cet objet <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <param name="rule">Règle d'accès à supprimer.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAccessRuleSpecific(System.Security.AccessControl.AccessRule)">
      <summary>Supprime toutes les règles d'accès qui correspondent exactement à la règle d'accès spécifiée dans la liste de contrôle d'accès discrétionnaire associée à cet objet <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <param name="rule">Règle d'accès à supprimer.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAuditRule(System.Security.AccessControl.AuditRule)">
      <summary>Supprime les règles d'audit qui contiennent les mêmes identificateur de sécurité et masque d'accès que la règle d'audit spécifiée dans la liste de contrôle d'accès système associée à cet objet <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <returns>true si la suppression de la règle d'audit a réussi ; sinon, false.</returns>
      <param name="rule">Règle d'audit à supprimer.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAuditRuleAll(System.Security.AccessControl.AuditRule)">
      <summary>Supprime toutes les règles d'audit qui ont le même identificateur de sécurité que la règle d'audit spécifiée dans la liste de contrôle d'accès système associée à cet objet <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <param name="rule">Règle d'audit à supprimer.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAuditRuleSpecific(System.Security.AccessControl.AuditRule)">
      <summary>Supprime toutes les règles d'audit qui correspondent exactement à la règle d'audit spécifiée dans la liste de contrôle d'accès système associée à cet objet <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</summary>
      <param name="rule">Règle d'audit à supprimer.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.ResetAccessRule(System.Security.AccessControl.AccessRule)">
      <summary>Supprime toutes les règles d'accès de la liste de contrôle d'accès discrétionnaire associée à cet objet <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />, puis ajoute la règle d'accès spécifiée.</summary>
      <param name="rule">Règle d'accès à réinitialiser.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.SetAccessRule(System.Security.AccessControl.AccessRule)">
      <summary>Supprime toutes les règles d'accès qui contiennent les mêmes identificateur de sécurité et qualificateur que la règle d'accès spécifiée dans la liste de contrôle d'accès discrétionnaire associée à cet objet <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />, puis ajoute la règle d'accès spécifiée.</summary>
      <param name="rule">Règle d'accès à définir.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.SetAuditRule(System.Security.AccessControl.AuditRule)">
      <summary>Supprime toutes les règles d'audit qui contiennent les mêmes identificateur de sécurité et qualificateur que la règle d'audit spécifiée dans la liste de contrôle d'accès système associée à cet objet <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />, puis ajoute la règle d'audit spécifiée.</summary>
      <param name="rule">Règle d'audit à définir.</param>
    </member>
    <member name="T:System.Security.AccessControl.CommonSecurityDescriptor">
      <summary>Représente un descripteur de sécurité.Un descripteur de sécurité comprend un propriétaire, un groupe principal, une liste de contrôle d'accès discrétionnaire et une liste de contrôle d'accès système.</summary>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.#ctor(System.Boolean,System.Boolean,System.Byte[],System.Int32)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> à partir du tableau spécifié de valeurs exprimées en octets.</summary>
      <param name="isContainer">true si le nouveau descripteur de sécurité est associé à un objet conteneur.</param>
      <param name="isDS">true si le nouveau descripteur de sécurité est associé à un objet annuaire.</param>
      <param name="binaryForm">Tableau de valeurs, en octets, à partir duquel créer le nouvel objet <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</param>
      <param name="offset">Dans le tableau <paramref name="binaryForm" />, offset auquel commencer la copie.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.#ctor(System.Boolean,System.Boolean,System.Security.AccessControl.ControlFlags,System.Security.Principal.SecurityIdentifier,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.SystemAcl,System.Security.AccessControl.DiscretionaryAcl)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> à partir des informations spécifiées.</summary>
      <param name="isContainer">true si le nouveau descripteur de sécurité est associé à un objet conteneur.</param>
      <param name="isDS">true si le nouveau descripteur de sécurité est associé à un objet annuaire.</param>
      <param name="flags">Indicateurs qui spécifient le comportement du nouvel objet <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</param>
      <param name="owner">Propriétaire du nouvel objet <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</param>
      <param name="group">Groupe principal du nouvel objet <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</param>
      <param name="systemAcl">Liste de contrôle d'accès système du nouvel objet <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</param>
      <param name="discretionaryAcl">Liste de contrôle d'accès discrétionnaire du nouvel objet <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.#ctor(System.Boolean,System.Boolean,System.Security.AccessControl.RawSecurityDescriptor)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> à partir de l'objet <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> spécifié.</summary>
      <param name="isContainer">true si le nouveau descripteur de sécurité est associé à un objet conteneur.</param>
      <param name="isDS">true si le nouveau descripteur de sécurité est associé à un objet annuaire.</param>
      <param name="rawSecurityDescriptor">Objet <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> à partir duquel créer le nouvel objet <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.#ctor(System.Boolean,System.Boolean,System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> à partir de la chaîne SDDL spécifiée.</summary>
      <param name="isContainer">true si le nouveau descripteur de sécurité est associé à un objet conteneur.</param>
      <param name="isDS">true si le nouveau descripteur de sécurité est associé à un objet annuaire.</param>
      <param name="sddlForm">Chaîne SDDL à partir de laquelle créer le nouvel objet <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.AddDiscretionaryAcl(System.Byte,System.Int32)">
      <summary>Définit les <see cref="P:System.Security.AccessControl.CommonSecurityDescriptor.DiscretionaryAcl" /> propriété pour ce <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> instance et définit les <see cref="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclPresent" /> indicateur.</summary>
      <param name="revision">Niveau de révision du nouvel objet <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />.</param>
      <param name="trusted">Nombre d'entrées du contrôle d'accès que cet objet <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> peut contenir.Ce nombre n'est à utiliser qu'à titre indicatif.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.AddSystemAcl(System.Byte,System.Int32)">
      <summary>Définit les <see cref="P:System.Security.AccessControl.CommonSecurityDescriptor.SystemAcl" /> propriété pour ce <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> instance et définit les <see cref="F:System.Security.AccessControl.ControlFlags.SystemAclPresent" /> indicateur.</summary>
      <param name="revision">Niveau de révision du nouvel objet <see cref="T:System.Security.AccessControl.SystemAcl" />.</param>
      <param name="trusted">Nombre d'entrées du contrôle d'accès que cet objet <see cref="T:System.Security.AccessControl.SystemAcl" /> peut contenir.Ce nombre n'est à utiliser qu'à titre indicatif.</param>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.ControlFlags">
      <summary>Obtient des valeurs qui spécifient le comportement de l'objet <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</summary>
      <returns>Une ou plusieurs valeurs de l'énumération <see cref="T:System.Security.AccessControl.ControlFlags" /> associées par une opération OR logique.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.DiscretionaryAcl">
      <summary>Obtient ou définit la liste de contrôle d'accès discrétionnaire de cet objet <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.La liste de contrôle d'accès discrétionnaire contient des règles d'accès.</summary>
      <returns>Liste de contrôle d'accès discrétionnaire de cet objet <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.Group">
      <summary>Obtient ou définit le groupe principal de cet objet <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</summary>
      <returns>Groupe principal de cet objet <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.IsContainer">
      <summary>Obtient une valeur booléenne qui spécifie si l'objet associé à cet objet <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> est un objet conteneur.</summary>
      <returns>true si l'objet associé à cet objet <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> est un objet conteneur ; sinon, false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.IsDiscretionaryAclCanonical">
      <summary>Obtient une valeur booléenne qui spécifie si la liste de contrôle d'accès discrétionnaire associée à cet objet <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> suit un ordre canonique.</summary>
      <returns>true si la liste de contrôle d'accès discrétionnaire associée à cet objet <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> suit un ordre canonique ; sinon, false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.IsDS">
      <summary>Obtient une valeur booléenne qui spécifie si l'objet associé à cet objet <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> est un objet annuaire.</summary>
      <returns>true si l'objet associé à cet objet <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> est un objet annuaire ; sinon, false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.IsSystemAclCanonical">
      <summary>Obtient une valeur booléenne qui spécifie si la liste de contrôle d'accès système associée à cet objet <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> suit un ordre canonique.</summary>
      <returns>true si la liste de contrôle d'accès système associée à cet objet <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> suit un ordre canonique ; sinon, false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.Owner">
      <summary>Obtient ou définit le propriétaire de l'objet associé à cet objet <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</summary>
      <returns>Propriétaire de l'objet associé à cet objet <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.PurgeAccessControl(System.Security.Principal.SecurityIdentifier)">
      <summary>Supprime toutes les règles d'accès de l'identificateur de sécurité spécifié dans la liste de contrôle d'accès discrétionnaire associée à cet objet <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</summary>
      <param name="sid">Identificateur de sécurité pour lequel supprimer des règles d'accès.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.PurgeAudit(System.Security.Principal.SecurityIdentifier)">
      <summary>Supprime toutes les règles d'audit de l'identificateur de sécurité spécifié dans la liste de contrôle d'accès système associée à cet objet <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</summary>
      <param name="sid">Identificateur de sécurité pour lequel supprimer des règles d'audit.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.SetDiscretionaryAclProtection(System.Boolean,System.Boolean)">
      <summary>Définit la protection héritée par la liste de contrôle d'accès discrétionnaire associée à cet objet <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.Les listes de contrôle d'accès discrétionnaires protégées n'héritent pas des règles d'accès des conteneurs parents.</summary>
      <param name="isProtected">true pour que la liste de contrôle d'accès discrétionnaire ne reçoive pas d'héritage.</param>
      <param name="preserveInheritance">true pour conserver dans la liste de contrôle d'accès discrétionnaire les règles d'accès héritées ; false pour supprimer de la liste les règles d'accès héritées.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.SetSystemAclProtection(System.Boolean,System.Boolean)">
      <summary>Définit la protection héritée par la liste de contrôle d'accès système associée à cet objet <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.Les listes de contrôle d'accès système protégées n'héritent pas des règles d'audit des conteneurs parents.</summary>
      <param name="isProtected">true pour que la liste de contrôle d'accès système ne reçoive pas d'héritage.</param>
      <param name="preserveInheritance">true pour conserver dans la liste de contrôle d'accès système les règles d'audit héritées ; false pour supprimer de la liste les règles d'audit héritées.</param>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.SystemAcl">
      <summary>Obtient ou définit la liste de contrôle d'accès système de cet objet <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.La liste de contrôle d'accès système contient des règles d'audit.</summary>
      <returns>Liste de contrôle d'accès système de cet objet <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.CompoundAce">
      <summary>Représente une entrée composée du contrôle d'accès.</summary>
    </member>
    <member name="M:System.Security.AccessControl.CompoundAce.#ctor(System.Security.AccessControl.AceFlags,System.Int32,System.Security.AccessControl.CompoundAceType,System.Security.Principal.SecurityIdentifier)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.CompoundAce" />.</summary>
      <param name="flags">Contient des indicateurs qui spécifient des informations relatives à l'héritage, la propagation d'héritage et les conditions d'audit de la nouvelle entrée du contrôle d'accès.</param>
      <param name="accessMask">Masque d'accès de l'entrée du contrôle d'accès.</param>
      <param name="compoundAceType">Valeur d'énumération <see cref="T:System.Security.AccessControl.CompoundAceType" />.</param>
      <param name="sid">
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> associé à la nouvelle entrée du contrôle d'accès.</param>
    </member>
    <member name="P:System.Security.AccessControl.CompoundAce.BinaryLength">
      <summary>Obtient la longueur, en octets, de la représentation binaire de l'objet <see cref="T:System.Security.AccessControl.CompoundAce" /> en cours.Cette longueur doit être utilisée avant de marshaler la liste de contrôle d'accès dans un tableau binaire avec la méthode <see cref="M:System.Security.AccessControl.CompoundAce.GetBinaryForm" />.</summary>
      <returns>Longueur, en octets, de la représentation binaire de l'objet <see cref="T:System.Security.AccessControl.CompoundAce" /> en cours.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CompoundAce.CompoundAceType">
      <summary>Obtient ou définit le type de cet objet <see cref="T:System.Security.AccessControl.CompoundAce" />.</summary>
      <returns>Type de cet objet <see cref="T:System.Security.AccessControl.CompoundAce" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.CompoundAce.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Marshale le contenu de l'objet <see cref="T:System.Security.AccessControl.CompoundAce" /> dans le tableau d'octets spécifié en commençant à l'offset spécifié.</summary>
      <param name="binaryForm">Tableau d'octets dans lequel le contenu de <see cref="T:System.Security.AccessControl.CompoundAce" /> est marshalé.</param>
      <param name="offset">Offset au niveau duquel commencer le marshaling.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> est négatif ou trop élevé pour que <see cref="T:System.Security.AccessControl.CompoundAce" /> puisse être entièrement copié dans <paramref name="array" />.</exception>
    </member>
    <member name="T:System.Security.AccessControl.CompoundAceType">
      <summary>Spécifie le type d'un objet <see cref="T:System.Security.AccessControl.CompoundAce" />.</summary>
    </member>
    <member name="F:System.Security.AccessControl.CompoundAceType.Impersonation">
      <summary>L'objet <see cref="T:System.Security.AccessControl.CompoundAce" /> est utilisé pour l'emprunt d'identité.</summary>
    </member>
    <member name="T:System.Security.AccessControl.ControlFlags">
      <summary>Ces indicateurs affectent le comportement du descripteur de sécurité.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclAutoInherited">
      <summary>Spécifie que la liste de contrôle d'accès discrétionnaire est automatiquement héritée du parent.Défini uniquement par les gestionnaires des ressources.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclAutoInheritRequired">
      <summary>Ignoré.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclDefaulted">
      <summary>Spécifie que la liste de contrôle d'accès discrétionnaire a été obtenue par un mécanisme par défaut.Défini uniquement par les gestionnaires des ressources.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclPresent">
      <summary>Spécifie que la liste de contrôle d'accès discrétionnaire n'est pas null.Défini par les gestionnaires des ressources ou les utilisateurs.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclProtected">
      <summary>Spécifie que le gestionnaire des ressources empêche l'héritage automatique.Défini par les gestionnaires des ressources ou les utilisateurs.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclUntrusted">
      <summary>Ignoré.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.GroupDefaulted">
      <summary>Spécifie que le <see cref="T:System.Security.Principal.SecurityIdentifier" /> de groupe a été obtenu par un mécanisme par défaut.Défini uniquement par les gestionnaires des ressources ; ne doit pas être défini par les appelants.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.None">
      <summary>Aucun indicateur de contrôle.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.OwnerDefaulted">
      <summary>Spécifie que le <see cref="T:System.Security.Principal.SecurityIdentifier" /> du propriétaire a été obtenu par un mécanisme par défaut.Défini uniquement par les gestionnaires des ressources ; ne doit pas être défini par les appelants.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.RMControlValid">
      <summary>Spécifie que le contenu du champ Réservé est valide.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.SelfRelative">
      <summary>Spécifie que la représentation binaire du descripteur de sécurité est au format auto-relatif.  Cet indicateur est toujours défini.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.ServerSecurity">
      <summary>Ignoré.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.SystemAclAutoInherited">
      <summary>Spécifie que la liste de contrôle d'accès système est automatiquement héritée du parent.Défini uniquement par les gestionnaires des ressources.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.SystemAclAutoInheritRequired">
      <summary>Ignoré.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.SystemAclDefaulted">
      <summary>Spécifie que la liste de contrôle d'accès système a été obtenue par un mécanisme par défaut.Défini uniquement par les gestionnaires des ressources.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.SystemAclPresent">
      <summary>Spécifie que la liste de contrôle d'accès système n'est pas null.Défini par les gestionnaires des ressources ou les utilisateurs.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.SystemAclProtected">
      <summary>Spécifie que le gestionnaire des ressources empêche l'héritage automatique.Défini par les gestionnaires des ressources ou les utilisateurs.</summary>
    </member>
    <member name="T:System.Security.AccessControl.CustomAce">
      <summary>Représente une entrée non définie du contrôle d'accès par l'un des membres de l'énumération <see cref="T:System.Security.AccessControl.AceType" />.</summary>
    </member>
    <member name="M:System.Security.AccessControl.CustomAce.#ctor(System.Security.AccessControl.AceType,System.Security.AccessControl.AceFlags,System.Byte[])">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.CustomAce" />.</summary>
      <param name="type">Type de la nouvelle entrée du contrôle d'accès.Cette valeur doit être supérieure à <see cref="F:System.Security.AccessControl.AceType.MaxDefinedAceType" />.</param>
      <param name="flags">Indicateurs donnant des informations sur l'héritage, la propagation d'héritage et les conditions d'audit de la nouvelle entrée du contrôle d'accès.</param>
      <param name="opaque">Tableau des valeurs d'octet qui contient les données de la nouvelle entrée du contrôle d'accès.Cette valeur peut être null.La longueur de ce tableau ne doit pas être supérieure à la valeur du champ <see cref="F:System.Security.AccessControl.CustomAce.MaxOpaqueLength" /> et doit être un multiple de quatre.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">La valeur du paramètre de <paramref name="type" /> n'est pas supérieure à <see cref="F:System.Security.AccessControl.AceType.MaxDefinedAceType" /> ou la longueur du tableau <paramref name="opaque" /> est supérieure à la valeur du champ <see cref="F:System.Security.AccessControl.CustomAce.MaxOpaqueLength" /> ou différente d'un multiple de quatre.</exception>
    </member>
    <member name="P:System.Security.AccessControl.CustomAce.BinaryLength">
      <summary>Obtient la longueur, en octets, de la représentation binaire de l'objet <see cref="T:System.Security.AccessControl.CustomAce" /> en cours.Cette longueur doit être utilisée avant de marshaler la liste de contrôle d'accès dans un tableau binaire avec la méthode <see cref="M:System.Security.AccessControl.CustomAce.GetBinaryForm" />.</summary>
      <returns>Longueur, en octets, de la représentation binaire de l'objet <see cref="T:System.Security.AccessControl.CustomAce" /> en cours.</returns>
    </member>
    <member name="M:System.Security.AccessControl.CustomAce.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Marshale le contenu de l'objet <see cref="T:System.Security.AccessControl.CustomAce" /> dans le tableau d'octets spécifié en commençant à l'offset spécifié.</summary>
      <param name="binaryForm">Tableau d'octets dans lequel le contenu de <see cref="T:System.Security.AccessControl.CustomAce" /> est marshalé.</param>
      <param name="offset">Offset au niveau duquel commencer le marshaling.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> est négatif ou trop élevé pour que <see cref="T:System.Security.AccessControl.CustomAce" /> puisse être entièrement copié dans <paramref name="array" />.</exception>
    </member>
    <member name="M:System.Security.AccessControl.CustomAce.GetOpaque">
      <summary>Retourne les données opaques associées à cet objet <see cref="T:System.Security.AccessControl.CustomAce" />. </summary>
      <returns>Tableau des valeurs d'octet qui représente les données opaques associées à cet objet <see cref="T:System.Security.AccessControl.CustomAce" />.</returns>
    </member>
    <member name="F:System.Security.AccessControl.CustomAce.MaxOpaqueLength">
      <summary>Retourne la longueur maximale autorisée d'un blob de données opaque pour cet objet <see cref="T:System.Security.AccessControl.CustomAce" />.</summary>
    </member>
    <member name="P:System.Security.AccessControl.CustomAce.OpaqueLength">
      <summary>Obtient la longueur des données opaques associées à cet objet <see cref="T:System.Security.AccessControl.CustomAce" />.</summary>
      <returns>Longueur des données de rappel opaques.</returns>
    </member>
    <member name="M:System.Security.AccessControl.CustomAce.SetOpaque(System.Byte[])">
      <summary>Définit les données de rappel opaques associées à cet objet <see cref="T:System.Security.AccessControl.CustomAce" />.</summary>
      <param name="opaque">Tableau de valeurs d'octet qui représente les données de rappel opaques de cet objet <see cref="T:System.Security.AccessControl.CustomAce" />.</param>
    </member>
    <member name="T:System.Security.AccessControl.DiscretionaryAcl">
      <summary>Représente une liste de contrôle d'accès discrétionnaire.</summary>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.#ctor(System.Boolean,System.Boolean,System.Byte,System.Int32)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> avec les valeurs spécifiées.</summary>
      <param name="isContainer">true si le nouvel objet <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> est un conteneur.</param>
      <param name="isDS">true si le nouvel objet <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> est une liste de contrôle d'accès d'un objet annuaire.</param>
      <param name="revision">Niveau de révision du nouvel objet <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />.</param>
      <param name="capacity">Nombre d'entrées du contrôle d'accès que cet objet <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> peut contenir.Ce nombre n'est à utiliser qu'à titre indicatif.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.#ctor(System.Boolean,System.Boolean,System.Int32)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> avec les valeurs spécifiées.</summary>
      <param name="isContainer">true si le nouvel objet <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> est un conteneur.</param>
      <param name="isDS">true si le nouvel objet <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> est une liste de contrôle d'accès d'un objet annuaire.</param>
      <param name="capacity">Nombre d'entrées du contrôle d'accès que cet objet <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> peut contenir.Ce nombre n'est à utiliser qu'à titre indicatif.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.#ctor(System.Boolean,System.Boolean,System.Security.AccessControl.RawAcl)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> avec les valeurs spécifiées à partir de l'objet <see cref="T:System.Security.AccessControl.RawAcl" /> spécifié.</summary>
      <param name="isContainer">true si le nouvel objet <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> est un conteneur.</param>
      <param name="isDS">true si le nouvel objet <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> est une liste de contrôle d'accès d'un objet annuaire.</param>
      <param name="rawAcl">Objet <see cref="T:System.Security.AccessControl.RawAcl" /> sous-jacent du nouvel objet <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />.Spécifiez null pour créer une liste de contrôle d'accès vide.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.AddAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Ajoute une entrée du contrôle d'accès, avec les paramètres spécifiés, à l'objet <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> en cours.</summary>
      <param name="accessType">Type de contrôle d'accès (autorisation ou refus) à ajouter.</param>
      <param name="sid">
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> pour lequel ajouter une entrée du contrôle d'accès.</param>
      <param name="accessMask">Règle d'accès de la nouvelle entrée de contrôle d'accès.</param>
      <param name="inheritanceFlags">Indicateurs qui spécifient les propriétés d'héritage de la nouvelle entrée de contrôle d'accès.</param>
      <param name="propagationFlags">Indicateurs qui spécifient les propriétés de propagation d'héritage de la nouvelle entrée de contrôle d'accès.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.AddAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>Ajoute une entrée du contrôle d'accès, avec les paramètres spécifiés, à l'objet <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> en cours.Utilisez cette méthode pour les listes de contrôle d'accès des objets d'annuaire quand vous spécifiez le type d'objet ou le type d'objet hérité pour la nouvelle entrée de contrôle d'accès.</summary>
      <param name="accessType">Type de contrôle d'accès (autorisation ou refus) à ajouter.</param>
      <param name="sid">
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> pour lequel ajouter une entrée du contrôle d'accès.</param>
      <param name="accessMask">Règle d'accès de la nouvelle entrée de contrôle d'accès.</param>
      <param name="inheritanceFlags">Indicateurs qui spécifient les propriétés d'héritage de la nouvelle entrée de contrôle d'accès.</param>
      <param name="propagationFlags">Indicateurs qui spécifient les propriétés de propagation d'héritage de la nouvelle entrée de contrôle d'accès.</param>
      <param name="objectFlags">Indicateurs qui spécifient si les paramètres <paramref name="objectType" /> et <paramref name="inheritedObjectType" /> contiennent des valeurs non null.</param>
      <param name="objectType">Identité de la classe des objets auxquels la nouvelle entrée de contrôle d'accès s'applique.</param>
      <param name="inheritedObjectType">Identité de la classe des objets enfants qui peuvent hériter de la nouvelle entrée de contrôle d'accès.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.AddAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAccessRule)">
      <summary>Ajoute une entrée du contrôle d'accès, avec les paramètres spécifiés, à l'objet <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> en cours.</summary>
      <param name="accessType">Type de contrôle d'accès (autorisation ou refus) à ajouter.</param>
      <param name="sid">
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> pour lequel ajouter une entrée du contrôle d'accès.</param>
      <param name="rule">Le <see cref="T:System.Security.AccessControl.ObjectAccessRule" /> pour l'accès à nouveau.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Supprime la règle de contrôle d'accès spécifiée de l'objet <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> en cours.</summary>
      <returns>true si cette méthode réussit à supprimer l'accès spécifié ; sinon, false.</returns>
      <param name="accessType">Type de contrôle d'accès (autorisation ou refus) à supprimer.</param>
      <param name="sid">
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> pour lequel supprimer une règle de contrôle d'accès.</param>
      <param name="accessMask">Masque d'accès de la règle à supprimer.</param>
      <param name="inheritanceFlags">Indicateurs qui spécifient les propriétés d'héritage de la règle à supprimer.</param>
      <param name="propagationFlags">Indicateurs qui spécifient les propriétés de propagation d'héritage de la règle à supprimer.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>Supprime la règle de contrôle d'accès spécifiée de l'objet <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> en cours.Utilisez cette méthode pour les listes de contrôle d'accès des objets d'annuaire quand vous spécifiez le type d'objet ou le type d'objet hérité.</summary>
      <returns>true si cette méthode réussit à supprimer l'accès spécifié ; sinon, false.</returns>
      <param name="accessType">Type de contrôle d'accès (autorisation ou refus) à supprimer.</param>
      <param name="sid">
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> pour lequel supprimer une règle de contrôle d'accès.</param>
      <param name="accessMask">Masque d'accès de la règle de contrôle d'accès à supprimer.</param>
      <param name="inheritanceFlags">Indicateurs qui spécifient les propriétés d'héritage de la règle de contrôle d'accès à supprimer.</param>
      <param name="propagationFlags">Indicateurs qui spécifient les propriétés de propagation d'héritage de la règle de contrôle d'accès à supprimer.</param>
      <param name="objectFlags">Indicateurs qui spécifient si les paramètres <paramref name="objectType" /> et <paramref name="inheritedObjectType" /> contiennent des valeurs non null.</param>
      <param name="objectType">Identité de la classe d'objets auxquels la règle de contrôle d'accès supprimée s'applique.</param>
      <param name="inheritedObjectType">Identité de la classe des objets enfants qui peuvent hériter de la règle de contrôle d'accès supprimée.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAccessRule)">
      <summary>Supprime la règle de contrôle d'accès spécifiée de l'objet <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> en cours.</summary>
      <returns>Retourne <see cref="T:System.Boolean" />.</returns>
      <param name="accessType">Type de contrôle d'accès (autorisation ou refus) à supprimer.</param>
      <param name="sid">
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> pour lequel supprimer une règle de contrôle d'accès.</param>
      <param name="rule">Le <see cref="T:System.Security.AccessControl.ObjectAccessRule" /> pour laquelle supprimer l'accès.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccessSpecific(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Supprime l'entrée du contrôle d'accès spécifiée de l'objet <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> en cours.</summary>
      <param name="accessType">Type de contrôle d'accès (autorisation ou refus) à supprimer.</param>
      <param name="sid">
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> pour lequel supprimer une entrée du contrôle d'accès.</param>
      <param name="accessMask">Masque d'accès de l'entrée de contrôle d'accès à supprimer.</param>
      <param name="inheritanceFlags">Indicateurs qui spécifient les propriétés d'héritage de l'entrée de contrôle d'accès à supprimer.</param>
      <param name="propagationFlags">Indicateurs qui spécifient les propriétés de propagation d'héritage de l'entrée de contrôle d'accès à supprimer.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccessSpecific(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>Supprime l'entrée du contrôle d'accès spécifiée de l'objet <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> en cours.Utilisez cette méthode pour les listes de contrôle d'accès des objets d'annuaire quand vous spécifiez le type d'objet ou le type d'objet hérité pour l'entrée de contrôle d'accès à supprimer.</summary>
      <param name="accessType">Type de contrôle d'accès (autorisation ou refus) à supprimer.</param>
      <param name="sid">
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> pour lequel supprimer une entrée du contrôle d'accès.</param>
      <param name="accessMask">Masque d'accès de l'entrée de contrôle d'accès à supprimer.</param>
      <param name="inheritanceFlags">Indicateurs qui spécifient les propriétés d'héritage de l'entrée de contrôle d'accès à supprimer.</param>
      <param name="propagationFlags">Indicateurs qui spécifient les propriétés de propagation d'héritage de l'entrée de contrôle d'accès à supprimer.</param>
      <param name="objectFlags">Indicateurs qui spécifient si les paramètres <paramref name="objectType" /> et <paramref name="inheritedObjectType" /> contiennent des valeurs non null.</param>
      <param name="objectType">Identité de la classe d'objets à laquelle l'entrée de contrôle d'accès supprimée s'applique.</param>
      <param name="inheritedObjectType">Identité de la classe des objets enfants qui peuvent hériter de l'entrée de contrôle d'accès supprimée.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccessSpecific(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAccessRule)">
      <summary>Supprime l'entrée du contrôle d'accès spécifiée de l'objet <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> en cours.</summary>
      <param name="accessType">Type de contrôle d'accès (autorisation ou refus) à supprimer.</param>
      <param name="sid">
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> pour lequel supprimer une entrée du contrôle d'accès.</param>
      <param name="rule">Le <see cref="T:System.Security.AccessControl.ObjectAccessRule" /> pour laquelle supprimer l'accès.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.SetAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Définit le contrôle d'accès spécifié pour l'objet <see cref="T:System.Security.Principal.SecurityIdentifier" /> spécifié.</summary>
      <param name="accessType">Type de contrôle d'accès (autorisation ou refus) à définir.</param>
      <param name="sid">
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> pour lequel définir une entrée du contrôle d'accès.</param>
      <param name="accessMask">Règle d'accès de la nouvelle entrée de contrôle d'accès.</param>
      <param name="inheritanceFlags">Indicateurs qui spécifient les propriétés d'héritage de la nouvelle entrée de contrôle d'accès.</param>
      <param name="propagationFlags">Indicateurs qui spécifient les propriétés de propagation d'héritage de la nouvelle entrée de contrôle d'accès.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.SetAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>Définit le contrôle d'accès spécifié pour l'objet <see cref="T:System.Security.Principal.SecurityIdentifier" /> spécifié.</summary>
      <param name="accessType">Type de contrôle d'accès (autorisation ou refus) à définir.</param>
      <param name="sid">
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> pour lequel définir une entrée du contrôle d'accès.</param>
      <param name="accessMask">Règle d'accès de la nouvelle entrée de contrôle d'accès.</param>
      <param name="inheritanceFlags">Indicateurs qui spécifient les propriétés d'héritage de la nouvelle entrée de contrôle d'accès.</param>
      <param name="propagationFlags">Indicateurs qui spécifient les propriétés de propagation d'héritage de la nouvelle entrée de contrôle d'accès.</param>
      <param name="objectFlags">Indicateurs qui spécifient si les paramètres <paramref name="objectType" /> et <paramref name="inheritedObjectType" /> contiennent des valeurs non null.</param>
      <param name="objectType">Identité de la classe des objets auxquels la nouvelle entrée de contrôle d'accès s'applique.</param>
      <param name="inheritedObjectType">Identité de la classe des objets enfants qui peuvent hériter de la nouvelle entrée de contrôle d'accès.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.SetAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAccessRule)">
      <summary>Définit le contrôle d'accès spécifié pour l'objet <see cref="T:System.Security.Principal.SecurityIdentifier" /> spécifié.</summary>
      <param name="accessType">Type de contrôle d'accès (autorisation ou refus) à définir.</param>
      <param name="sid">
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> pour lequel définir une entrée du contrôle d'accès.</param>
      <param name="rule">Le <see cref="T:System.Security.AccessControl.ObjectAccessRule" /> pour lequel vous configurez l'accès.</param>
    </member>
    <member name="T:System.Security.AccessControl.GenericAce">
      <summary>Représente une entrée du contrôle d'accès ; classe de base de toutes les autres classes d'entrées du contrôle d'accès.</summary>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.AceFlags">
      <summary>Obtient ou définit les <see cref="T:System.Security.AccessControl.AceFlags" /> associés à cet objet <see cref="T:System.Security.AccessControl.GenericAce" />.</summary>
      <returns>
        <see cref="T:System.Security.AccessControl.AceFlags" /> associés à cet objet <see cref="T:System.Security.AccessControl.GenericAce" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.AceType">
      <summary>Obtient le type de cette entrée du contrôle d'accès.</summary>
      <returns>Type de cette entrée du contrôle d'accès.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.AuditFlags">
      <summary>Obtient les informations d'audit associées à cette entrée du contrôle d'accès.</summary>
      <returns>Informations d'audit associées à cette entrée du contrôle d'accès.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.BinaryLength">
      <summary>Obtient la longueur, en octets, de la représentation binaire de l'objet <see cref="T:System.Security.AccessControl.GenericAce" /> en cours.Cette longueur doit être utilisée avant de marshaler la liste de contrôle d'accès dans un tableau binaire avec la méthode <see cref="M:System.Security.AccessControl.GenericAce.GetBinaryForm" />.</summary>
      <returns>Longueur, en octets, de la représentation binaire de l'objet <see cref="T:System.Security.AccessControl.GenericAce" /> en cours.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.Copy">
      <summary>Crée une copie complète de cette entrée du contrôle d'accès.</summary>
      <returns>Objet <see cref="T:System.Security.AccessControl.GenericAce" /> créé par cette méthode.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.CreateFromBinaryForm(System.Byte[],System.Int32)">
      <summary>Crée un objet <see cref="T:System.Security.AccessControl.GenericAce" /> à partir des données binaires spécifiées.</summary>
      <returns>Objet <see cref="T:System.Security.AccessControl.GenericAce" /> créé par cette méthode.</returns>
      <param name="binaryForm">Données binaires à partir desquelles créer le nouvel objet <see cref="T:System.Security.AccessControl.GenericAce" />.</param>
      <param name="offset">Offset auquel commencer l'unmarshaling.</param>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.Equals(System.Object)">
      <summary>Détermine si l'objet <see cref="T:System.Security.AccessControl.GenericAce" /> spécifié est égal à l'objet <see cref="T:System.Security.AccessControl.GenericAce" /> en cours.</summary>
      <returns>true si l'objet <see cref="T:System.Security.AccessControl.GenericAce" /> spécifié est égal à l'objet <see cref="T:System.Security.AccessControl.GenericAce" /> en cours ; sinon false.</returns>
      <param name="o">Objet <see cref="T:System.Security.AccessControl.GenericAce" /> à comparer à l'objet <see cref="T:System.Security.AccessControl.GenericAce" /> en cours.</param>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Marshale le contenu de l'objet <see cref="T:System.Security.AccessControl.GenericAce" /> dans le tableau d'octets spécifié en commençant à l'offset spécifié.</summary>
      <param name="binaryForm">Tableau d'octets dans lequel le contenu de <see cref="T:System.Security.AccessControl.GenericAce" /> est marshalé.</param>
      <param name="offset">Offset au niveau duquel commencer le marshaling.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> est négatif ou trop élevé pour que <see cref="T:System.Security.AccessControl.GenericAcl" /> puisse être entièrement copié dans <paramref name="array" />.</exception>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.GetHashCode">
      <summary>Sert de fonction de hachage pour la classe <see cref="T:System.Security.AccessControl.GenericAce" />.La méthode <see cref="M:System.Security.AccessControl.GenericAce.GetHashCode" /> peut être utilisée dans des algorithmes de hachage et des structures de données telles qu'une table de hachage.</summary>
      <returns>Code de hachage pour l'objet <see cref="T:System.Security.AccessControl.GenericAce" /> actuel.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.InheritanceFlags">
      <summary>Obtient des indicateurs qui spécifient les propriétés d'héritage de cette entrée du contrôle d'accès.</summary>
      <returns>Indicateurs qui spécifient les propriétés d'héritage de cette entrée du contrôle d'accès.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.IsInherited">
      <summary>Obtient une valeur booléenne qui spécifie si cette entrée du contrôle d'accès est héritée ou définie explicitement.</summary>
      <returns>true si cette entrée du contrôle d'accès est héritée ; sinon false.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.op_Equality(System.Security.AccessControl.GenericAce,System.Security.AccessControl.GenericAce)">
      <summary>Détermine si les objets <see cref="T:System.Security.AccessControl.GenericAce" /> spécifiés sont considérés comme égaux.</summary>
      <returns>true si les deux objets <see cref="T:System.Security.AccessControl.GenericAce" /> sont égaux ; sinon, false.</returns>
      <param name="left">Premier objet <see cref="T:System.Security.AccessControl.GenericAce" /> à comparer.</param>
      <param name="right">Deuxième <see cref="T:System.Security.AccessControl.GenericAce" /> à comparer.</param>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.op_Inequality(System.Security.AccessControl.GenericAce,System.Security.AccessControl.GenericAce)">
      <summary>Détermine si les objets <see cref="T:System.Security.AccessControl.GenericAce" /> spécifiés sont considérés comme inégaux.</summary>
      <returns>true si les deux objets <see cref="T:System.Security.AccessControl.GenericAce" /> sont inégaux ; sinon, false.</returns>
      <param name="left">Premier objet <see cref="T:System.Security.AccessControl.GenericAce" /> à comparer.</param>
      <param name="right">Deuxième <see cref="T:System.Security.AccessControl.GenericAce" /> à comparer.</param>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.PropagationFlags">
      <summary>Obtient des indicateurs qui spécifient les propriétés de propagation d'héritage de cette entrée du contrôle d'accès.</summary>
      <returns>Indicateurs qui spécifient les propriétés de propagation d'héritage de cette entrée du contrôle d'accès.</returns>
    </member>
    <member name="T:System.Security.AccessControl.GenericAcl">
      <summary>Représente une liste de contrôle d'accès (ACL) et constitue la classe de base des classes <see cref="T:System.Security.AccessControl.CommonAcl" />, <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />, <see cref="T:System.Security.AccessControl.RawAcl" /> et <see cref="T:System.Security.AccessControl.SystemAcl" />.</summary>
    </member>
    <member name="M:System.Security.AccessControl.GenericAcl.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.GenericAcl" />.</summary>
    </member>
    <member name="F:System.Security.AccessControl.GenericAcl.AclRevision">
      <summary>Niveau de révision de l'objet <see cref="T:System.Security.AccessControl.GenericAcl" /> en cours.Cette valeur est retournée par la propriété <see cref="P:System.Security.AccessControl.GenericAcl.Revision" /> des listes de contrôle d'accès non associées aux objets Services d'annuaire.</summary>
    </member>
    <member name="F:System.Security.AccessControl.GenericAcl.AclRevisionDS">
      <summary>Niveau de révision de l'objet <see cref="T:System.Security.AccessControl.GenericAcl" /> en cours.Cette valeur est retournée par la propriété <see cref="P:System.Security.AccessControl.GenericAcl.Revision" /> des listes de contrôle d'accès associées aux objets Services d'annuaire.</summary>
    </member>
    <member name="P:System.Security.AccessControl.GenericAcl.BinaryLength">
      <summary>Obtient la longueur, en octets, de la représentation binaire de l'objet <see cref="T:System.Security.AccessControl.GenericAcl" /> en cours.Cette longueur doit être utilisée avant de marshaler la liste de contrôle d'accès (ACL) dans un tableau binaire avec la méthode <see cref="M:System.Security.AccessControl.GenericAcl.GetBinaryForm" />.</summary>
      <returns>Longueur, en octets, de la représentation binaire de l'objet <see cref="T:System.Security.AccessControl.GenericAcl" /> en cours.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericAcl.CopyTo(System.Security.AccessControl.GenericAce[],System.Int32)">
      <summary>Copie chaque <see cref="T:System.Security.AccessControl.GenericAce" /> du <see cref="T:System.Security.AccessControl.GenericAcl" /> en cours vers le tableau spécifié.</summary>
      <param name="array">Tableau dans lequel les copies des objets <see cref="T:System.Security.AccessControl.GenericAce" /> contenus par le <see cref="T:System.Security.AccessControl.GenericAcl" /> en cours sont placées.</param>
      <param name="index">Index de base zéro du <paramref name="array" /> auquel la copie commence.</param>
    </member>
    <member name="P:System.Security.AccessControl.GenericAcl.Count">
      <summary>Obtient le nombre d'entrées du contrôle d'accès dans l'objet <see cref="T:System.Security.AccessControl.GenericAcl" /> en cours.</summary>
      <returns>Nombre d'entrées du contrôle d'accès dans l'objet <see cref="T:System.Security.AccessControl.GenericAcl" /> en cours.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericAcl.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Marshale le contenu de l'objet <see cref="T:System.Security.AccessControl.GenericAcl" /> dans le tableau d'octets spécifié en commençant à l'offset spécifié.</summary>
      <param name="binaryForm">Tableau d'octets dans lequel le contenu de <see cref="T:System.Security.AccessControl.GenericAcl" /> est marshalé.</param>
      <param name="offset">Offset au niveau duquel commencer le marshaling.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> est négatif ou trop élevé pour que <see cref="T:System.Security.AccessControl.GenericAcl" /> puisse être entièrement copié dans <paramref name="array" />.</exception>
    </member>
    <member name="M:System.Security.AccessControl.GenericAcl.GetEnumerator">
      <summary>Retourne une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.AceEnumerator" />.</summary>
      <returns>
        <see cref="T:Security.AccessControl.AceEnumerator" /> retourné par cette méthode.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAcl.IsSynchronized">
      <summary>La propriété a toujours la valeur false.Son implémentation est requise pour l'implémentation de l'interface <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Toujours false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAcl.Item(System.Int32)">
      <summary>Obtient ou définit <see cref="T:System.Security.AccessControl.GenericAce" /> à l'index spécifié.</summary>
      <returns>
        <see cref="T:System.Security.AccessControl.GenericAce" /> à l'index spécifié.</returns>
      <param name="index">Index de base zéro du <see cref="T:System.Security.AccessControl.GenericAce" /> à obtenir ou définir.</param>
    </member>
    <member name="F:System.Security.AccessControl.GenericAcl.MaxBinaryLength">
      <summary>Longueur binaire maximale autorisée d'un objet <see cref="T:System.Security.AccessControl.GenericAcl" />.</summary>
    </member>
    <member name="P:System.Security.AccessControl.GenericAcl.Revision">
      <summary>Obtient le niveau de révision de <see cref="T:System.Security.AccessControl.GenericAcl" />.</summary>
      <returns>Valeur d'octet qui spécifie le niveau de révision de <see cref="T:System.Security.AccessControl.GenericAcl" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAcl.SyncRoot">
      <summary>Cette propriété retourne toujours null.Son implémentation est requise pour l'implémentation de l'interface <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Retourne toujours null.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericAcl.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copie chaque <see cref="T:System.Security.AccessControl.GenericAce" /> du <see cref="T:System.Security.AccessControl.GenericAcl" /> en cours vers le tableau spécifié.</summary>
      <param name="array">Tableau dans lequel les copies des objets <see cref="T:System.Security.AccessControl.GenericAce" /> contenus par le <see cref="T:System.Security.AccessControl.GenericAcl" /> en cours sont placées.</param>
      <param name="index">Index de base zéro du <paramref name="array" /> auquel la copie commence.</param>
    </member>
    <member name="M:System.Security.AccessControl.GenericAcl.System#Collections#IEnumerable#GetEnumerator">
      <summary>Retourne une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.AceEnumerator" /> castée en instance de l'interface <see cref="T:System.Collections.IEnumerator" />.</summary>
      <returns>Nouvel objet <see cref="T:System.Security.AccessControl.AceEnumerator" />, casté en instance de l'interface <see cref="T:System.Collections.IEnumerator" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.GenericSecurityDescriptor">
      <summary>Représente un descripteur de sécurité.Un descripteur de sécurité inclut un propriétaire, un groupe principal, une liste de contrôle d'accès discrétionnaire et une liste de contrôle d'accès système.</summary>
    </member>
    <member name="M:System.Security.AccessControl.GenericSecurityDescriptor.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.GenericSecurity" />.</summary>
    </member>
    <member name="P:System.Security.AccessControl.GenericSecurityDescriptor.BinaryLength">
      <summary>Obtient la longueur, en octets, de la représentation binaire de l'objet <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> en cours.Cette longueur doit être utilisée avant de marshaler la liste de contrôle d'accès dans un tableau binaire avec la méthode <see cref="M:System.Security.AccessControl.GenericSecurityDescriptor.GetBinaryForm" />.</summary>
      <returns>Longueur, en octets, de la représentation binaire de l'objet <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> en cours.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericSecurityDescriptor.ControlFlags">
      <summary>Obtient des valeurs qui spécifient le comportement de l'objet <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />.</summary>
      <returns>Une ou plusieurs valeurs de l'énumération <see cref="T:System.Security.AccessControl.ControlFlags" /> associées par une opération OR logique.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericSecurityDescriptor.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Retourne un tableau de valeurs d'octets, qui représente les informations contenues dans cet objet <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />.</summary>
      <param name="binaryForm">Tableau d'octets dans lequel le contenu de <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> est marshalé.</param>
      <param name="offset">Offset au niveau duquel commencer le marshaling.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> est négatif ou trop élevé pour que <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> puisse être entièrement copié dans <paramref name="array" />.</exception>
    </member>
    <member name="M:System.Security.AccessControl.GenericSecurityDescriptor.GetSddlForm(System.Security.AccessControl.AccessControlSections)">
      <summary>Retourne la représentation en SDDL, langage de définition des descripteurs de sécurité, des sections spécifiées du descripteur de sécurité que cet objet <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> représente.</summary>
      <returns>Représentation SDDL des sections spécifiées du descripteur de sécurité associé à cet objet <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />.</returns>
      <param name="includeSections">Spécifie quelles sections (règles d'accès, règles d'audit, groupe principal, propriétaire) du descripteur de sécurité obtenir.</param>
    </member>
    <member name="P:System.Security.AccessControl.GenericSecurityDescriptor.Group">
      <summary>Obtient ou définit le groupe principal de cet objet <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />.</summary>
      <returns>Groupe principal de cet objet <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericSecurityDescriptor.IsSddlConversionSupported">
      <summary>Retourne une valeur booléenne qui spécifie si le descripteur de sécurité associé à cet objet <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> peut être converti au format SDDL.</summary>
      <returns>true si le descripteur de sécurité associé à cet objet <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> peut être converti au format SDDL ; sinon false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericSecurityDescriptor.Owner">
      <summary>Obtient ou définit le propriétaire de l'objet associé à cet objet <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />.</summary>
      <returns>Propriétaire de l'objet associé à cet objet <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericSecurityDescriptor.Revision">
      <summary>Obtient le numéro de révision de l'objet <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />.</summary>
      <returns>Valeur d'octet qui spécifie le niveau de révision du <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.InheritanceFlags">
      <summary>Les indicateurs d'héritage spécifient la sémantique d'héritage des entrées du contrôle d'accès.</summary>
    </member>
    <member name="F:System.Security.AccessControl.InheritanceFlags.ContainerInherit">
      <summary>Les objets conteneurs enfants héritent de l'entrée du contrôle d'accès.</summary>
    </member>
    <member name="F:System.Security.AccessControl.InheritanceFlags.None">
      <summary>Les objets enfants n'héritent pas de l'entrée du contrôle d'accès.</summary>
    </member>
    <member name="F:System.Security.AccessControl.InheritanceFlags.ObjectInherit">
      <summary>Les objets descendants du niveau enfant héritent de l'entrée du contrôle d'accès.</summary>
    </member>
    <member name="T:System.Security.AccessControl.KnownAce">
      <summary>Encapsule tous les types d'entrées du contrôle d'accès actuellement définis par Microsoft Corporation.Tous les objets <see cref="T:System.Security.AccessControl.KnownAce" /> contiennent un masque d'accès de 32 bits et un objet <see cref="T:System.Security.Principal.SecurityIdentifier" />.</summary>
    </member>
    <member name="P:System.Security.AccessControl.KnownAce.AccessMask">
      <summary>Obtient ou définit le masque d'accès de cet objet <see cref="T:System.Security.AccessControl.KnownAce" />.</summary>
      <returns>Masque d'accès de cet objet <see cref="T:System.Security.AccessControl.KnownAce" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.KnownAce.SecurityIdentifier">
      <summary>Obtient ou définit l'objet <see cref="T:System.Security.Principal.SecurityIdentifier" /> associé à cet objet <see cref="T:System.Security.AccessControl.KnownAce" />.</summary>
      <returns>Objet <see cref="T:System.Security.Principal.SecurityIdentifier" /> associé à cet objet <see cref="T:System.Security.AccessControl.KnownAce" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.NativeObjectSecurity">
      <summary>Donne la possibilité de contrôler l'accès aux objets natifs sans manipulation directe de listes de contrôle d'accès.Les types d'objets natifs sont définis par l'énumération <see cref="T:System.Security.AccessControl.ResourceType" />.</summary>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> avec les valeurs spécifiées.</summary>
      <param name="isContainer">true si le nouvel objet <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> est un objet conteneur.</param>
      <param name="resourceType">Type d'objet sécurisable auquel le nouvel objet <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> est associé.</param>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> avec les valeurs spécifiées.Nous recommandons d'attribuer des valeurs identiques aux paramètres <paramref name="includeSections" /> passés au constructeur et aux méthodes Persist.Pour plus d'informations, consultez la section Notes.</summary>
      <param name="isContainer">true si le nouvel objet <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> est un objet conteneur.</param>
      <param name="resourceType">Type d'objet sécurisable auquel le nouvel objet <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> est associé.</param>
      <param name="handle">Handle de l'objet sécurisable auquel le nouvel objet <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> est associé.</param>
      <param name="includeSections">Une des valeurs d'énumération <see cref="T:System.Security.AccessControl.AccessControlSections" /> qui spécifie les sections du descripteur de sécurité (règles d'accès, règles d'audit, propriétaire, groupe principal) de l'objet sécurisable à inclure dans cet objet <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections,System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode,System.Object)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> avec les valeurs spécifiées.Nous recommandons d'attribuer des valeurs identiques aux paramètres <paramref name="includeSections" /> passés au constructeur et aux méthodes Persist.Pour plus d'informations, consultez la section Notes.</summary>
      <param name="isContainer">true si le nouvel objet <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> est un objet conteneur.</param>
      <param name="resourceType">Type d'objet sécurisable auquel le nouvel objet <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> est associé.</param>
      <param name="handle">Handle de l'objet sécurisable auquel le nouvel objet <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> est associé.</param>
      <param name="includeSections">Une des valeurs d'énumération <see cref="T:System.Security.AccessControl.AccessControlSections" /> qui spécifie les sections du descripteur de sécurité (règles d'accès, règles d'audit, propriétaire, groupe principal) de l'objet sécurisable à inclure dans cet objet <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="exceptionFromErrorCode">Délégué implémenté par des intégrateurs et fournissant des exceptions personnalisées. </param>
      <param name="exceptionContext">Objet qui contient des informations contextuelles sur la source ou la destination de l'exception.</param>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode,System.Object)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> à l'aide des valeurs spécifiées.</summary>
      <param name="isContainer">true si le nouvel objet <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> est un objet conteneur.</param>
      <param name="resourceType">Type d'objet sécurisable auquel le nouvel objet <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> est associé.</param>
      <param name="exceptionFromErrorCode">Délégué implémenté par des intégrateurs et fournissant des exceptions personnalisées. </param>
      <param name="exceptionContext">Objet qui contient des informations contextuelles sur la source ou la destination de l'exception.</param>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> avec les valeurs spécifiées.Nous recommandons d'attribuer des valeurs identiques aux paramètres <paramref name="includeSections" /> passés au constructeur et aux méthodes Persist.Pour plus d'informations, consultez la section Notes.</summary>
      <param name="isContainer">true si le nouvel objet <see cref="T:System.Security.AccessControl.NativObjectSecurity" /> est un objet conteneur.</param>
      <param name="resourceType">Type d'objet sécurisable auquel le nouvel objet <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> est associé.</param>
      <param name="name">Nom de l'objet sécurisable auquel le nouvel objet <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> est associé.</param>
      <param name="includeSections">Une des valeurs d'énumération <see cref="T:System.Security.AccessControl.AccessControlSections" /> qui spécifie les sections du descripteur de sécurité (règles d'accès, règles d'audit, propriétaire, groupe principal) de l'objet sécurisable à inclure dans cet objet <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.String,System.Security.AccessControl.AccessControlSections,System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode,System.Object)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> avec les valeurs spécifiées.Nous recommandons d'attribuer des valeurs identiques aux paramètres <paramref name="includeSections" /> passés au constructeur et aux méthodes Persist.Pour plus d'informations, consultez la section Notes.</summary>
      <param name="isContainer">true si le nouvel objet <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> est un objet conteneur.</param>
      <param name="resourceType">Type d'objet sécurisable auquel le nouvel objet <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> est associé.</param>
      <param name="name">Nom de l'objet sécurisable auquel le nouvel objet <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> est associé.</param>
      <param name="includeSections">Une des valeurs d'énumération <see cref="T:System.Security.AccessControl.AccessControlSections" /> qui spécifie les sections du descripteur de sécurité (règles d'accès, règles d'audit, propriétaire, groupe principal) de l'objet sécurisable à inclure dans cet objet <see cref="T:System.Security.AccessControl.NativeObjectSecurity" />.</param>
      <param name="exceptionFromErrorCode">Délégué implémenté par des intégrateurs et fournissant des exceptions personnalisées. </param>
      <param name="exceptionContext">Objet qui contient des informations contextuelles sur la source ou la destination de l'exception.</param>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.Persist(System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections)">
      <summary>Enregistre les sections spécifiées du descripteur de sécurité associées à cet objet <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> dans un stockage permanent.Nous recommandons d'attribuer des valeurs identiques aux paramètres <paramref name="includeSections" /> passés au constructeur et aux méthodes Persist.Pour plus d'informations, consultez la section Notes.</summary>
      <param name="handle">Handle de l'objet sécurisable auquel cet objet <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> est associé.</param>
      <param name="includeSections">Une des valeurs d'énumération <see cref="T:System.Security.AccessControl.AccessControlSections" /> qui spécifie les sections du descripteur de sécurité (règles d'accès, règles d'audit, propriétaire, groupe principal) de l'objet sécurisable à enregistrer.</param>
      <exception cref="T:System.IO.FileNotFoundException">L'objet sécurisable auquel cet objet <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> est associé est un répertoire ou un fichier, et ce répertoire ou fichier est introuvable.</exception>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.Persist(System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections,System.Object)">
      <summary>Enregistre les sections spécifiées du descripteur de sécurité associées à cet objet <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> dans un stockage permanent.Nous recommandons d'attribuer des valeurs identiques aux paramètres <paramref name="includeSections" /> passés au constructeur et aux méthodes Persist.Pour plus d'informations, consultez la section Notes.</summary>
      <param name="handle">Handle de l'objet sécurisable auquel cet objet <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> est associé.</param>
      <param name="includeSections">Une des valeurs d'énumération <see cref="T:System.Security.AccessControl.AccessControlSections" /> qui spécifie les sections du descripteur de sécurité (règles d'accès, règles d'audit, propriétaire, groupe principal) de l'objet sécurisable à enregistrer.</param>
      <param name="exceptionContext">Objet qui contient des informations contextuelles sur la source ou la destination de l'exception.</param>
      <exception cref="T:System.IO.FileNotFoundException">L'objet sécurisable auquel cet objet <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> est associé est un répertoire ou un fichier, et ce répertoire ou fichier est introuvable.</exception>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.Persist(System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>Enregistre les sections spécifiées du descripteur de sécurité associées à cet objet <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> dans un stockage permanent.Nous recommandons d'attribuer des valeurs identiques aux paramètres <paramref name="includeSections" /> passés au constructeur et aux méthodes Persist.Pour plus d'informations, consultez la section Notes.</summary>
      <param name="name">Nom de l'objet sécurisable auquel cet objet <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> est associé.</param>
      <param name="includeSections">Une des valeurs d'énumération <see cref="T:System.Security.AccessControl.AccessControlSections" /> qui spécifie les sections du descripteur de sécurité (règles d'accès, règles d'audit, propriétaire, groupe principal) de l'objet sécurisable à enregistrer.</param>
      <exception cref="T:System.IO.FileNotFoundException">L'objet sécurisable auquel cet objet <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> est associé est un répertoire ou un fichier, et ce répertoire ou fichier est introuvable.</exception>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.Persist(System.String,System.Security.AccessControl.AccessControlSections,System.Object)">
      <summary>Enregistre les sections spécifiées du descripteur de sécurité associées à cet objet <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> dans un stockage permanent.Nous recommandons d'attribuer des valeurs identiques aux paramètres <paramref name="includeSections" /> passés au constructeur et aux méthodes Persist.Pour plus d'informations, consultez la section Notes.</summary>
      <param name="name">Nom de l'objet sécurisable auquel cet objet <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> est associé.</param>
      <param name="includeSections">Une des valeurs d'énumération <see cref="T:System.Security.AccessControl.AccessControlSections" /> qui spécifie les sections du descripteur de sécurité (règles d'accès, règles d'audit, propriétaire, groupe principal) de l'objet sécurisable à enregistrer.</param>
      <param name="exceptionContext">Objet qui contient des informations contextuelles sur la source ou la destination de l'exception.</param>
      <exception cref="T:System.IO.FileNotFoundException">L'objet sécurisable auquel cet objet <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> est associé est un répertoire ou un fichier, et ce répertoire ou fichier est introuvable.</exception>
    </member>
    <member name="T:System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode">
      <summary>Offre aux intégrateurs un moyen de mapper des codes d'erreur numériques aux exceptions spécifiques qu'ils créent.</summary>
      <returns>
        <see cref="T:System.Exception" /> que ce délégué crée.</returns>
      <param name="errorCode">Code d'erreur numérique.</param>
      <param name="name">Nom de l'objet sécurisable auquel le nouvel objet <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> est associé.</param>
      <param name="handle">Handle de l'objet sécurisable auquel le nouvel objet <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> est associé.</param>
      <param name="context">Objet qui contient des informations contextuelles sur la source ou la destination de l'exception.</param>
    </member>
    <member name="T:System.Security.AccessControl.ObjectAccessRule">
      <summary>Représente une combinaison entre identité d'un utilisateur, masque d'accès et type de contrôle d'accès (autorisation ou refus).Un objet <see cref="T:System.Security.AccessControl.ObjectAccessRule" /> contient également des informations sur le type d'objet auquel la règle s'applique, le type d'objet enfant qui peut hériter de la règle, ainsi que la façon dont les objets enfants héritent de la règle et dont cet héritage est propagé.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectAccessRule.#ctor(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Guid,System.Guid,System.Security.AccessControl.AccessControlType)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.ObjectAccessRule" /> avec les valeurs spécifiées.</summary>
      <param name="identity">Identité à laquelle la règle d'accès s'applique.  Il doit s'agir d'un objet pouvant être casté en tant que <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <param name="accessMask">Masque d'accès de cette règle.Le masque d'accès est une collection 32 bits de bits anonymes, dont la signification est définie par les intégrateurs individuels.</param>
      <param name="isInherited">true si cette règle est héritée d'un conteneur parent.</param>
      <param name="inheritanceFlags">Spécifie les propriétés d'héritage de la règle d'accès.</param>
      <param name="propagationFlags">Spécifie si les règles d'accès héritées sont automatiquement propagées.Les indicateurs de propagation sont ignorés si <paramref name="inheritanceFlags" /> a la valeur <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</param>
      <param name="objectType">Type d'objet auquel la règle s'applique.</param>
      <param name="inheritedObjectType">Type d'objet enfant qui peut hériter de la règle.</param>
      <param name="type">Spécifie si cette règle accorde ou refuse l'accès.</param>
      <exception cref="T:System.ArgumentException">La valeur du paramètre <paramref name="identity" /> ne peut pas être castée en tant que <see cref="T:System.Security.Principal.SecurityIdentifier" /> ou le paramètre <paramref name="type" /> contient une valeur non valide.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Le paramètre <paramref name="accessMask" /> a la valeur zéro ou les paramètres <paramref name="inheritanceFlags" /> ou <paramref name="propagationFlags" /> contiennent des valeurs d'indicateur non reconnues.</exception>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAccessRule.InheritedObjectType">
      <summary>Obtient le type d'objet enfant qui peut hériter de l'objet <see cref="System.Security.AccessControl.ObjectAccessRule" />.</summary>
      <returns>Type d'objet enfant qui peut hériter de l'objet <see cref="System.Security.AccessControl.ObjectAccessRule" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAccessRule.ObjectFlags">
      <summary>Obtient des indicateurs qui spécifient si les propriétés <see cref="P:System.Security.AccessControl.ObjectAccessRule.ObjectType" /> et <see cref="P:System.Security.AccessControl.ObjectAccessRule.InheritedObjectType" /> de l'objet <see cref="System.Security.AccessControl.ObjectAccessRule" /> contiennent des valeurs valides.</summary>
      <returns>
        <see cref="F:System.Security.AccessControl.ObjectAceFlags.ObjectAceTypePresent" /> spécifie que la propriété <see cref="P:System.Security.AccessControl.ObjectAccessRule.ObjectType" /> contient une valeur valide.<see cref="F:System.Security.AccessControl.ObjectAceFlags.InheritedObjectAceTypePresent" /> spécifie que la propriété <see cref="P:System.Security.AccessControl.ObjectAccessRule.InheritedObjectType" /> contient une valeur valide.Ces valeurs peuvent être combinées avec un OR logique.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAccessRule.ObjectType">
      <summary>Obtient le type d'objet auquel <see cref="System.Security.AccessControl.ObjectAccessRule" /> s'applique.</summary>
      <returns>Type d'objet auquel <see cref="System.Security.AccessControl.ObjectAccessRule" /> s'applique.</returns>
    </member>
    <member name="T:System.Security.AccessControl.ObjectAce">
      <summary>Contrôle l'accès aux objets Services d'annuaire.Cette classe représente une entrée du contrôle d'accès associée à un objet annuaire.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectAce.#ctor(System.Security.AccessControl.AceFlags,System.Security.AccessControl.AceQualifier,System.Int32,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid,System.Boolean,System.Byte[])">
      <summary>Initialise une instance de la classe <see cref="T:System.Security.AccessControl.ObjectAce" />.</summary>
      <param name="aceFlags">Conditions d'héritage, de propagation d'héritage et d'audit de la nouvelle entrée du contrôle d'accès.</param>
      <param name="qualifier">Utilisation de la nouvelle entrée du contrôle d'accès.</param>
      <param name="accessMask">Masque d'accès de l'entrée du contrôle d'accès.</param>
      <param name="sid">
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> associé à la nouvelle entrée du contrôle d'accès.</param>
      <param name="flags">Indique si les paramètres <paramref name="type" /> et <paramref name="inheritedType" /> contiennent des GUID d'objets valides.</param>
      <param name="type">GUID qui identifie le type d'objet auquel la nouvelle entrée du contrôle d'accès s'applique.</param>
      <param name="inheritedType">GUID qui identifie le type d'objet qui peut hériter de la nouvelle entrée du contrôle d'accès.</param>
      <param name="isCallback">true si la nouvelle entrée du contrôle d'accès est de type rappel.</param>
      <param name="opaque">Données opaques associées à la nouvelle entrée du contrôle d'accès.Cela n'est prévu que pour les entrées du contrôle d'accès de type rappel.Ce tableau ne doit pas avoir une longueur supérieure à la valeur de retour de la méthode <see cref="M:System.Security.AccessControl.ObjectAceMaxOpaqueLength" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Le paramètre qualificateur contient une valeur non valide ou la valeur du paramètre opaque a une longueur supérieure à la valeur de retour de la méthode <see cref="M:System.Security.AccessControl.ObjectAceMaxOpaqueLength" />.</exception>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAce.BinaryLength">
      <summary>Obtient la longueur, en octets, de la représentation binaire de l'objet <see cref="T:System.Security.AccessControl.ObjectAce" /> en cours.Cette longueur doit être utilisée avant de marshaler la liste de contrôle d'accès dans un tableau binaire avec la méthode <see cref="M:System.Security.AccessControl.ObjectAce.GetBinaryForm" />.</summary>
      <returns>Longueur, en octets, de la représentation binaire de l'objet <see cref="T:System.Security.AccessControl.ObjectAce" /> en cours.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectAce.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Marshale le contenu de l'objet <see cref="T:System.Security.AccessControl.ObjectAce" /> dans le tableau d'octets spécifié en commençant à l'offset spécifié.</summary>
      <param name="binaryForm">Tableau d'octets dans lequel le contenu de <see cref="T:System.Security.AccessControl.ObjectAce" /> est marshalé.</param>
      <param name="offset">Offset au niveau duquel commencer le marshaling.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> est négatif ou trop élevé pour que <see cref="T:System.Security.AccessControl.ObjectAce" /> puisse être entièrement copié dans <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAce.InheritedObjectAceType">
      <summary>Obtient ou définit le GUID du type d'objet qui peut hériter de l'entrée du contrôle d'accès que cet objet <see cref="T:System.Security.AccessControl.ObjectAce" /> représente.</summary>
      <returns>GUID du type d'objet qui peut hériter de l'entrée du contrôle d'accès que cet objet <see cref="T:System.Security.AccessControl.ObjectAce" /> représente.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectAce.MaxOpaqueLength(System.Boolean)">
      <summary>Retourne la longueur maximale autorisée, en octets, d'un blob de données opaque pour les entrées du contrôle d'accès de type rappel.</summary>
      <returns>Longueur maximale autorisée, en octets, d'un blob de données opaque pour les entrées du contrôle d'accès de type rappel.</returns>
      <param name="isCallback">True si <see cref="T:System.Security.AccessControl.ObjectAce" /> est une entrée du contrôle d'accès de type rappel.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAce.ObjectAceFlags">
      <summary>Obtient ou définit des indicateurs qui spécifient si les propriétés <see cref="P:System.Security.AccessControl.ObjectAce.ObjectAceType" /> et <see cref="P:System.Security.AccessControl.ObjectAce.InheritedObjectAceType" /> contiennent des valeurs qui identifient des types d'objet valides.</summary>
      <returns>Un ou plusieurs membres de l'énumération <see cref="T:System.Security.AccessControl.ObjectAceFlags" /> combinés avec une opération OR logique.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAce.ObjectAceType">
      <summary>Obtient ou définit le GUID du type d'objet associé à cet objet <see cref="T:System.Security.AccessControl.ObjectAce" />.</summary>
      <returns>GUID du type d'objet associé à cet objet <see cref="T:System.Security.AccessControl.ObjectAce" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.ObjectAceFlags">
      <summary>Spécifie la présence de types d'objets pour les entrées du contrôle d'accès.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ObjectAceFlags.InheritedObjectAceTypePresent">
      <summary>Type d'objet pouvant hériter de l'entrée du contrôle d'accès.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ObjectAceFlags.None">
      <summary>Aucun type d'objet n'est présent.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ObjectAceFlags.ObjectAceTypePresent">
      <summary>Le type d'objet associé à l'entrée du contrôle d'accès est présent.</summary>
    </member>
    <member name="T:System.Security.AccessControl.ObjectAuditRule">
      <summary>Représente une combinaison entre l'identité d'un utilisateur, un masque d'accès et des conditions d'audit.Un objet <see cref="T:System.Security.AccessControl.ObjectAuditRule" /> contient également des informations sur le type d'objet auquel la règle s'applique, le type d'objet enfant qui peut hériter de la règle, ainsi que la façon dont les objets enfants héritent de la règle et dont cet héritage est propagé.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectAuditRule.#ctor(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Guid,System.Guid,System.Security.AccessControl.AuditFlags)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.ObjectAuditRule" />.</summary>
      <param name="identity">Identité à laquelle la règle d'accès s'applique.  Il doit s'agir d'un objet pouvant être casté en tant que <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <param name="accessMask">Masque d'accès de cette règle.Le masque d'accès est une collection 32 bits de bits anonymes, dont la signification est définie par les intégrateurs individuels.</param>
      <param name="isInherited">true si cette règle est héritée d'un conteneur parent.</param>
      <param name="inheritanceFlags">Spécifie les propriétés d'héritage de la règle d'accès.</param>
      <param name="propagationFlags">Indique si les règles d'accès héritées sont automatiquement propagées.Les indicateurs de propagation sont ignorés si <paramref name="inheritanceFlags" /> a la valeur <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</param>
      <param name="objectType">Type d'objet auquel la règle s'applique.</param>
      <param name="inheritedObjectType">Type d'objet enfant qui peut hériter de la règle.</param>
      <param name="auditFlags">Conditions de l'audit.</param>
      <exception cref="T:System.ArgumentException">La valeur du paramètre <paramref name="identity" /> ne peut pas être castée en tant que <see cref="T:System.Security.Principal.SecurityIdentifier" /> ou le paramètre <paramref name="type" /> contient une valeur non valide.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Le paramètre <paramref name="accessMask" /> a la valeur zéro ou les paramètres <paramref name="inheritanceFlags" /> ou <paramref name="propagationFlags" /> contiennent des valeurs d'indicateur non reconnues.</exception>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAuditRule.InheritedObjectType">
      <summary>Obtient le type d'objet enfant qui peut hériter de l'objet <see cref="System.Security.AccessControl.ObjectAuditRule" />.</summary>
      <returns>Type d'objet enfant qui peut hériter de l'objet <see cref="System.Security.AccessControl.ObjectAuditRule" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAuditRule.ObjectFlags">
      <summary>Les propriétés <see cref="P:System.Security.AccessControl.ObjectAuditRule.ObjectType" /> et <see cref="P:System.Security.AccessControl.ObjectAuditRule.InheritedObjectType" /> de l'objet <see cref="System.Security.AccessControl.ObjectAuditRule" /> contiennent des valeurs valides.</summary>
      <returns>
        <see cref="F:System.Security.AccessControl.ObjectAceFlags.ObjectAceTypePresent" /> spécifie que la propriété <see cref="P:System.Security.AccessControl.ObjectAuditRule.ObjectType" /> contient une valeur valide.<see cref="F:System.Security.AccessControl.ObjectAceFlags.InheritedObjectAceTypePresent" /> spécifie que la propriété <see cref="P:System.Security.AccessControl.ObjectAuditRule.InheritedObjectType" /> contient une valeur valide.Ces valeurs peuvent être combinées avec un OR logique.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAuditRule.ObjectType">
      <summary>Obtient le type d'objet auquel <see cref="System.Security.AccessControl.ObjectAuditRule" /> s'applique.</summary>
      <returns>Type d'objet auquel <see cref="System.Security.AccessControl.ObjectAuditRule" /> s'applique.</returns>
    </member>
    <member name="T:System.Security.AccessControl.ObjectSecurity">
      <summary>Donne la possibilité de contrôler l'accès aux objets sans manipulation directe de listes de contrôle d'accès.Cette classe est la classe de base abstraite des classes <see cref="T:System.Security.AccessControl.CommonObjectSecurity" /> et <see cref="T:System.Security.AccessControl.DirectoryObjectSecurity" />.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.#ctor(System.Boolean,System.Boolean)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <param name="isContainer">true si le nouvel objet <see cref="T:System.Security.AccessControl.ObjectSecurity" /> est un objet conteneur.</param>
      <param name="isDS">True si le nouvel objet <see cref="T:System.Security.AccessControl.ObjectSecurity" /> est un objet annuaire.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.#ctor(System.Security.AccessControl.CommonSecurityDescriptor)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <param name="securityDescriptor">
        <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> de la nouvelle instance <see cref="T:System.Security.AccessControl.CommonObjectSecurity" />.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AccessRightType">
      <summary>Obtient <see cref="T:System.Type" /> de l'objet sécurisable associé à cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <returns>Type de l'objet sécurisable associé à cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.AccessRuleFactory(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.AccessRule" /> avec les valeurs spécifiées.</summary>
      <returns>Objet <see cref="T:System.Security.AccessControl.AccessRule" /> créé par cette méthode.</returns>
      <param name="identityReference">Identité à laquelle la règle d'accès s'applique.Il doit s'agir d'un objet pouvant être casté en tant que <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <param name="accessMask">Masque d'accès de cette règle.Le masque d'accès est une collection 32 bits de bits anonymes, dont la signification est définie par les intégrateurs individuels.</param>
      <param name="isInherited">True si cette règle est héritée d'un conteneur parent.</param>
      <param name="inheritanceFlags">Spécifie les propriétés d'héritage de la règle d'accès.</param>
      <param name="propagationFlags">Spécifie si les règles d'accès héritées sont automatiquement propagées.Les indicateurs de propagation sont ignorés si <paramref name="inheritanceFlags" /> a la valeur <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</param>
      <param name="type">Spécifie le type de contrôle d'accès valide.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AccessRulesModified">
      <summary>Obtient ou définit une valeur booléenne qui spécifie si les règles d'accès associées à cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" /> ont été modifiées.</summary>
      <returns>true si les règles d'accès associées à cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" /> ont été modifiées ; sinon, false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AccessRuleType">
      <summary>Obtient <see cref="T:System.Type" /> de l'objet associé aux règles d'accès de cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" />.L'objet <see cref="T:System.Type" /> doit pouvoir être casté en objet <see cref="T:System.Security.Principal.SecurityIdentifier" />.</summary>
      <returns>Type de l'objet associé aux règles d'accès de cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AreAccessRulesCanonical">
      <summary>Obtient une valeur booléenne qui spécifie si les règles d'accès associées à cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" /> suivent un ordre canonique.</summary>
      <returns>true si les règles d'accès suivent un ordre canonique ; sinon, false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AreAccessRulesProtected">
      <summary>Obtient une valeur booléenne qui spécifie si la liste de contrôle d'accès discrétionnaire associée à cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" /> est protégée.</summary>
      <returns>true si la liste est protégée ; sinon, false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AreAuditRulesCanonical">
      <summary>Obtient une valeur booléenne qui spécifie si les règles d'audit associées à cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" /> suivent un ordre canonique.</summary>
      <returns>true si les règles d'audit suivent un ordre canonique ; sinon, false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AreAuditRulesProtected">
      <summary>Obtient une valeur booléenne qui spécifie si la liste de contrôle d'accès système associée à cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" /> est protégée.</summary>
      <returns>true si la liste est protégée ; sinon, false.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.AuditRuleFactory(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.AuditRule" /> avec les valeurs spécifiées.</summary>
      <returns>Objet <see cref="T:System.Security.AccessControl.AuditRule" /> créé par cette méthode.</returns>
      <param name="identityReference">Identité à laquelle la règle d'audit s'applique.Il doit s'agir d'un objet pouvant être casté en tant que <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <param name="accessMask">Masque d'accès de cette règle.Le masque d'accès est une collection 32 bits de bits anonymes, dont la signification est définie par les intégrateurs individuels.</param>
      <param name="isInherited">true si cette règle est héritée d'un conteneur parent.</param>
      <param name="inheritanceFlags">Spécifie les propriétés d'héritage de la règle d'audit.</param>
      <param name="propagationFlags">Spécifie si les règles d'audit héritées sont automatiquement propagées.Les indicateurs de propagation sont ignorés si <paramref name="inheritanceFlags" /> a la valeur <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</param>
      <param name="flags">Spécifie les conditions pour lesquelles la règle est auditée.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AuditRulesModified">
      <summary>Obtient ou définit une valeur booléenne qui spécifie si les règles d'audit associées à cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" /> ont été modifiées.</summary>
      <returns>true si les règles d'audit associées à cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" /> ont été modifiées ; sinon, false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AuditRuleType">
      <summary>Obtient l'objet <see cref="T:System.Type" /> associé aux règles d'audit de cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" />.L'objet <see cref="T:System.Type" /> doit pouvoir être casté en objet <see cref="T:System.Security.Principal.SecurityIdentifier" />.</summary>
      <returns>Type de l'objet associé aux règles d'audit de cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.GetGroup(System.Type)">
      <summary>Obtient le groupe principal associé au propriétaire spécifié.</summary>
      <returns>Groupe principal associé au propriétaire spécifié.</returns>
      <param name="targetType">Propriétaire pour lequel obtenir le groupe principal. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.GetOwner(System.Type)">
      <summary>Obtient le propriétaire associé au groupe principal spécifié.</summary>
      <returns>Propriétaire associé au groupe spécifié.</returns>
      <param name="targetType">Groupe principal pour lequel obtenir le propriétaire.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.GetSecurityDescriptorBinaryForm">
      <summary>Retourne un tableau de valeurs d'octets, qui représente les informations de descripteur de sécurité de cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <returns>Tableau de valeurs, en octets, qui représente le descripteur de sécurité de cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" />.Cette méthode retourne null s'il n'y a pas d'informations sur la sécurité dans cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.GetSecurityDescriptorSddlForm(System.Security.AccessControl.AccessControlSections)">
      <summary>Retourne la représentation en SDDL, langage de définition des descripteurs de sécurité, des sections spécifiées du descripteur de sécurité associé à cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <returns>Représentation SDDL des sections spécifiées du descripteur de sécurité associé à cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</returns>
      <param name="includeSections">Spécifie quelles sections (règles d'accès, règles d'audit, groupe principal, propriétaire) du descripteur de sécurité obtenir.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.GroupModified">
      <summary>Obtient ou définit une valeur booléenne qui spécifie si le groupe associé à l'objet sécurisable a été modifié. </summary>
      <returns>true si le groupe associé à l'objet sécurisable a été modifié ; sinon, false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.IsContainer">
      <summary>Obtient une valeur booléenne qui spécifie si cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" /> est un objet conteneur.</summary>
      <returns>true si l'objet <see cref="T:System.Security.AccessControl.ObjectSecurity" /> est un objet conteneur ; sinon, false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.IsDS">
      <summary>Obtient une valeur booléenne qui spécifie si cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" /> est un objet annuaire.</summary>
      <returns>true si l'objet <see cref="T:System.Security.AccessControl.ObjectSecurity" /> est un objet annuaire ; sinon, false.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.IsSddlConversionSupported">
      <summary>Retourne une valeur booléenne qui spécifie si le descripteur de sécurité associé à cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" /> peut être converti au format SDDL.</summary>
      <returns>true si le descripteur de sécurité associé à cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" /> peut être converti au format SDDL ; sinon false.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.ModifyAccess(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AccessRule,System.Boolean@)">
      <summary>Applique la modification spécifiée à la liste de contrôle d'accès discrétionnaire associée à cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <returns>true si la modification de la liste de contrôle d'accès discrétionnaire réussit ; sinon false.</returns>
      <param name="modification">Modification à appliquer à la liste de contrôle d'accès discrétionnaire.</param>
      <param name="rule">Règle d'accès à modifier.</param>
      <param name="modified">true si la modification de la liste de contrôle d'accès discrétionnaire réussit ; sinon false.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.ModifyAccessRule(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AccessRule,System.Boolean@)">
      <summary>Applique la modification spécifiée à la liste de contrôle d'accès discrétionnaire associée à cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <returns>true si la modification de la liste de contrôle d'accès discrétionnaire réussit ; sinon false.</returns>
      <param name="modification">Modification à appliquer à la liste de contrôle d'accès discrétionnaire.</param>
      <param name="rule">Règle d'accès à modifier.</param>
      <param name="modified">true si la modification de la liste de contrôle d'accès discrétionnaire réussit ; sinon false.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.ModifyAudit(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AuditRule,System.Boolean@)">
      <summary>Applique la modification spécifiée à la liste de contrôle d'accès système associée à cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <returns>true si la modification de la liste de contrôle d'accès système réussit ; sinon false.</returns>
      <param name="modification">Modification à appliquer à la liste de contrôle d'accès système.</param>
      <param name="rule">Règle d'audit à modifier.</param>
      <param name="modified">true si la modification de la liste de contrôle d'accès système réussit ; sinon false.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.ModifyAuditRule(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AuditRule,System.Boolean@)">
      <summary>Applique la modification spécifiée à la liste de contrôle d'accès système associée à cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <returns>true si la modification de la liste de contrôle d'accès système réussit ; sinon false.</returns>
      <param name="modification">Modification à appliquer à la liste de contrôle d'accès système.</param>
      <param name="rule">Règle d'audit à modifier.</param>
      <param name="modified">true si la modification de la liste de contrôle d'accès système réussit ; sinon false.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.OwnerModified">
      <summary>Obtient ou définit une valeur booléenne qui spécifie si le propriétaire de l'objet sécurisable a été modifié.</summary>
      <returns>true si le propriétaire de l'objet sécurisable a été modifié ; sinon, false.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.Persist(System.Boolean,System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>Enregistre les sections spécifiées du descripteur de sécurité associées à cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" /> dans un stockage permanent.Nous recommandons d'attribuer des valeurs identiques aux paramètres <paramref name="includeSections" /> passés au constructeur et aux méthodes Persist.Pour plus d'informations, consultez la section Notes.</summary>
      <param name="enableOwnershipPrivilege">true pour activer le privilège qui permet à l'appelant de prendre possession de l'objet.</param>
      <param name="name">Nom utilisé pour récupérer les informations persistantes.</param>
      <param name="includeSections">Une des valeurs d'énumération <see cref="T:System.Security.AccessControl.AccessControlSections" /> qui spécifie les sections du descripteur de sécurité (règles d'accès, règles d'audit, propriétaire, groupe principal) de l'objet sécurisable à enregistrer.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.Persist(System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections)">
      <summary>Enregistre les sections spécifiées du descripteur de sécurité associées à cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" /> dans un stockage permanent.Nous recommandons d'attribuer des valeurs identiques aux paramètres <paramref name="includeSections" /> passés au constructeur et aux méthodes Persist.Pour plus d'informations, consultez la section Notes.</summary>
      <param name="handle">Handle utilisé pour récupérer les informations persistantes.</param>
      <param name="includeSections">Une des valeurs d'énumération <see cref="T:System.Security.AccessControl.AccessControlSections" /> qui spécifie les sections du descripteur de sécurité (règles d'accès, règles d'audit, propriétaire, groupe principal) de l'objet sécurisable à enregistrer.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.Persist(System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>Enregistre les sections spécifiées du descripteur de sécurité associées à cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" /> dans un stockage permanent.Nous recommandons d'attribuer des valeurs identiques aux paramètres <paramref name="includeSections" /> passés au constructeur et aux méthodes Persist.Pour plus d'informations, consultez la section Notes.</summary>
      <param name="name">Nom utilisé pour récupérer les informations persistantes.</param>
      <param name="includeSections">Une des valeurs d'énumération <see cref="T:System.Security.AccessControl.AccessControlSections" /> qui spécifie les sections du descripteur de sécurité (règles d'accès, règles d'audit, propriétaire, groupe principal) de l'objet sécurisable à enregistrer.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.PurgeAccessRules(System.Security.Principal.IdentityReference)">
      <summary>Supprime toutes les règles d'accès associées au <see cref="T:System.Security.Principal.IdentityReference" /> spécifié.</summary>
      <param name="identity">
        <see cref="T:System.Security.Principal.IdentityReference" /> pour lequel supprimer toutes les règles d'accès.</param>
      <exception cref="T:System.InvalidOperationException">Les règles d'accès ne suivent pas toutes un ordre canonique.</exception>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.PurgeAuditRules(System.Security.Principal.IdentityReference)">
      <summary>Supprime toutes les règles d'audit associées au <see cref="T:System.Security.Principal.IdentityReference" /> spécifié.</summary>
      <param name="identity">
        <see cref="T:System.Security.Principal.IdentityReference" /> pour lequel supprimer toutes les règles d'audit.</param>
      <exception cref="T:System.InvalidOperationException">Les règles d'audit ne suivent pas toutes un ordre canonique.</exception>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.ReadLock">
      <summary>Verrouille cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" /> pour un accès en lecture.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.ReadUnlock">
      <summary>Déverrouille cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" /> pour un accès en lecture.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetAccessRuleProtection(System.Boolean,System.Boolean)">
      <summary>Définit ou supprime la protection des règles d'accès associées à cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" />.Les règles d'accès protégées ne peuvent pas être modifiées par des objets parents via l'héritage.</summary>
      <param name="isProtected">true pour que les règles d'accès associées à cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" /> n'aient pas d'héritage ; false pour autoriser l'héritage.</param>
      <param name="preserveInheritance">true pour conserver les règles d'accès héritées ; false pour supprimer des règles d'accès héritées.Ce paramètre est ignoré si <paramref name="isProtected" /> est false.</param>
      <exception cref="T:System.InvalidOperationException">Cette méthode essaie de supprimer des règles héritées d'une liste de contrôle d'accès discrétionnaire non canonique.</exception>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetAuditRuleProtection(System.Boolean,System.Boolean)">
      <summary>Définit ou supprime la protection des règles d'audit associées à cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" />.Les règles d'audit protégées ne peuvent pas être modifiées par des objets parents via l'héritage.</summary>
      <param name="isProtected">true pour que les règles d'audit associées à cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" /> n'aient pas d'héritage ; false pour autoriser l'héritage.</param>
      <param name="preserveInheritance">true pour conserver les règles d'audit héritées ; false pour supprimer des règles d'audit héritées.Ce paramètre est ignoré si <paramref name="isProtected" /> est false.</param>
      <exception cref="T:System.InvalidOperationException">Cette méthode essaie de supprimer des règles héritées d'une liste de contrôle d'accès système non canonique.</exception>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetGroup(System.Security.Principal.IdentityReference)">
      <summary>Définit le groupe principal du descripteur de sécurité associé à cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <param name="identity">Groupe principal à définir.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetOwner(System.Security.Principal.IdentityReference)">
      <summary>Définit le propriétaire du descripteur de sécurité associé à cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" />.</summary>
      <param name="identity">Propriétaire à définir.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetSecurityDescriptorBinaryForm(System.Byte[])">
      <summary>Définit le descripteur de sécurité de cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" /> à partir du tableau de valeurs, spécifiées en octets.</summary>
      <param name="binaryForm">Tableau d'octets à partir duquel définir le descripteur de sécurité.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetSecurityDescriptorBinaryForm(System.Byte[],System.Security.AccessControl.AccessControlSections)">
      <summary>Définit les sections spécifiées du descripteur de sécurité de cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" /> à partir du tableau de valeurs spécifiées en octets.</summary>
      <param name="binaryForm">Tableau d'octets à partir duquel définir le descripteur de sécurité.</param>
      <param name="includeSections">Sections (règles d'accès, règles d'audit, propriétaire, groupe principal) du descripteur de sécurité à définir.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetSecurityDescriptorSddlForm(System.String)">
      <summary>Définit le descripteur de sécurité de cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" /> à partir de la chaîne spécifiée en langage SDDL.</summary>
      <param name="sddlForm">Chaîne SDDL à partir de laquelle définir le descripteur de sécurité.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetSecurityDescriptorSddlForm(System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>Définit les sections spécifiées du descripteur de sécurité de cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" /> à partir de la chaîne spécifiée en langage SDDL.</summary>
      <param name="sddlForm">Chaîne SDDL à partir de laquelle définir le descripteur de sécurité.</param>
      <param name="includeSections">Sections (règles d'accès, règles d'audit, propriétaire, groupe principal) du descripteur de sécurité à définir.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.WriteLock">
      <summary>Verrouille cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" /> pour un accès en écriture.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.WriteUnlock">
      <summary>Déverrouille cet objet <see cref="T:System.Security.AccessControl.ObjectSecurity" /> pour un accès en écriture.</summary>
    </member>
    <member name="T:System.Security.AccessControl.ObjectSecurity`1">
      <summary>Permet de contrôler l'accès aux objets sans manipulation directe des listes de contrôle d'accès (ACL). Permet aussi de disposer des droits d'accès de cast de type. </summary>
      <typeparam name="T">Droits d'accès pour l'objet.</typeparam>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.#ctor(System.Boolean,System.Security.AccessControl.ResourceType)">
      <summary>Initialise une nouvelle instance de la classe ObjectSecurity`1.</summary>
      <param name="isContainer">true si le nouvel objet <see cref="T:System.Security.AccessControl.ObjectSecurity`1" /> est un objet conteneur.</param>
      <param name="resourceType">Type de ressource.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections)">
      <summary>Initialise une nouvelle instance de la classe ObjectSecurity`1.</summary>
      <param name="isContainer">true si le nouvel objet <see cref="T:System.Security.AccessControl.ObjectSecurity`1" /> est un objet conteneur.</param>
      <param name="resourceType">Type de ressource.</param>
      <param name="safeHandle">Handle.</param>
      <param name="includeSections">Sections à inclure.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections,System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode,System.Object)">
      <summary>Initialise une nouvelle instance de la classe ObjectSecurity`1.</summary>
      <param name="isContainer">true si le nouvel objet <see cref="T:System.Security.AccessControl.ObjectSecurity`1" /> est un objet conteneur.</param>
      <param name="resourceType">Type de ressource.</param>
      <param name="safeHandle">Handle.</param>
      <param name="includeSections">Sections à inclure.</param>
      <param name="exceptionFromErrorCode">Délégué implémenté par des intégrateurs et fournissant des exceptions personnalisées.</param>
      <param name="exceptionContext">Objet qui contient des informations contextuelles sur la source ou la destination de l'exception.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>Initialise une nouvelle instance de la classe ObjectSecurity`1.</summary>
      <param name="isContainer">true si le nouvel objet <see cref="T:System.Security.AccessControl.ObjectSecurity`1" /> est un objet conteneur.</param>
      <param name="resourceType">Type de ressource.</param>
      <param name="name">Nom de l'objet sécurisable auquel le nouvel objet <see cref="T:System.Security.AccessControl.ObjectSecurity`1" /> est associé.</param>
      <param name="includeSections">Sections à inclure.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.String,System.Security.AccessControl.AccessControlSections,System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode,System.Object)">
      <summary>Initialise une nouvelle instance de la classe ObjectSecurity`1.</summary>
      <param name="isContainer">true si le nouvel objet <see cref="T:System.Security.AccessControl.ObjectSecurity`1" /> est un objet conteneur.</param>
      <param name="resourceType">Type de ressource.</param>
      <param name="name">Nom de l'objet sécurisable auquel le nouvel objet <see cref="T:System.Security.AccessControl.ObjectSecurity`1" /> est associé.</param>
      <param name="includeSections">Sections à inclure. </param>
      <param name="exceptionFromErrorCode">Délégué implémenté par des intégrateurs et fournissant des exceptions personnalisées.</param>
      <param name="exceptionContext">Objet qui contient des informations contextuelles sur la source ou la destination de l'exception.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity`1.AccessRightType">
      <summary>Obtient le Type de l'objet sécurisable associé à cet objet ObjectSecurity`1.</summary>
      <returns>Type de l'objet sécurisable associé à l'instance en cours.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.AccessRuleFactory(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
      <summary>Initialise une nouvelle instance de la classe ObjectAccessRule qui représente une nouvelle règle de contrôle d'accès pour l'objet de sécurité associé.</summary>
      <returns>Représente une nouvelle règle de contrôle d'accès pour l'utilisateur spécifié, avec les droits d'accès, le contrôle d'accès et les indicateurs spécifiés.</returns>
      <param name="identityReference">Représente un compte d'utilisateur.</param>
      <param name="accessMask">Type d'accès.</param>
      <param name="isInherited">true si la règle d'accès est héritée ; sinon, false.</param>
      <param name="inheritanceFlags">Spécifie comment propager les masques d'accès aux objets enfants.</param>
      <param name="propagationFlags">Spécifie la manière de propager les entrées du contrôle d'accès vers les objets enfants.</param>
      <param name="type">Spécifie si l'accès est autorisé ou refusé.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity`1.AccessRuleType">
      <summary>Obtient le Type de l'objet associé aux règles d'accès de cet objet ObjectSecurity`1. </summary>
      <returns>Type de l'objet associé aux règles d'accès de l'instance en cours.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.AddAccessRule(System.Security.AccessControl.AccessRule{`0})">
      <summary>Ajoute la règle d'accès spécifiée à la liste de contrôle d'accès discrétionnaire associée à cet objet ObjectSecurity`1.</summary>
      <param name="rule">Règle à ajouter.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.AddAuditRule(System.Security.AccessControl.AuditRule{`0})">
      <summary>Ajoute la règle d'audit spécifiée à la liste de contrôle d'accès système associée à cet objet ObjectSecurity`1.</summary>
      <param name="rule">Règle d'audit à ajouter.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.AuditRuleFactory(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.AuditRule" /> représentant la règle d'audit spécifiée pour l'utilisateur spécifié.</summary>
      <returns>Retourne la règle d'audit spécifiée pour l'utilisateur spécifié.</returns>
      <param name="identityReference">Représente un compte d'utilisateur. </param>
      <param name="accessMask">Entier qui spécifie un type d'accès.</param>
      <param name="isInherited">true si la règle d'accès est héritée ; sinon, false.</param>
      <param name="inheritanceFlags">Spécifie comment propager les masques d'accès aux objets enfants.</param>
      <param name="propagationFlags">Spécifie la manière de propager les entrées du contrôle d'accès vers les objets enfants.</param>
      <param name="flags">Décrit le type d'audit à exécuter.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity`1.AuditRuleType">
      <summary>Obtient l'objet Type associé aux règles d'audit de cet objet ObjectSecurity`1.</summary>
      <returns>Objet Type associé aux règles d'audit de l'instance en cours.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.Persist(System.Runtime.InteropServices.SafeHandle)">
      <summary>Enregistre le descripteur de sécurité associé à cet objet ObjectSecurity`1 dans un stockage permanent, à l'aide du handle spécifié.</summary>
      <param name="handle">Handle de l'objet sécurisable auquel cet objet ObjectSecurity`1 est associé.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.Persist(System.String)">
      <summary>Enregistre le descripteur de sécurité associé à cet objet ObjectSecurity`1 dans un stockage permanent, à l'aide du nom spécifié.</summary>
      <param name="name">Nom de l'objet sécurisable auquel cet objet ObjectSecurity`1 est associé.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAccessRule(System.Security.AccessControl.AccessRule{`0})">
      <summary>Supprime les règles d'accès qui contiennent les mêmes identificateur de sécurité et masque d'accès que la règle d'accès spécifiée dans la liste de contrôle d'accès discrétionnaire associée à cet objet ObjectSecurity`1.</summary>
      <returns>Retourne true si la suppression de la règle d'accès a réussi ; sinon, false.</returns>
      <param name="rule">Règle à supprimer.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAccessRuleAll(System.Security.AccessControl.AccessRule{`0})">
      <summary>Supprime toutes les règles d'accès qui ont le même identificateur de sécurité que la règle d'accès spécifiée dans la liste de contrôle d'accès discrétionnaire associée à cet objet ObjectSecurity`1.</summary>
      <param name="rule">Règle d'accès à supprimer.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAccessRuleSpecific(System.Security.AccessControl.AccessRule{`0})">
      <summary>Supprime toutes les règles d'accès qui correspondent exactement à la règle d'accès spécifiée dans la liste de contrôle d'accès discrétionnaire associée à cet objet ObjectSecurity`1.</summary>
      <param name="rule">Règle d'accès à supprimer.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAuditRule(System.Security.AccessControl.AuditRule{`0})">
      <summary>Supprime les règles d'audit qui contiennent les mêmes identificateur de sécurité et masque d'accès que la règle d'audit spécifiée dans la liste de contrôle d'accès système associée à cet objet ObjectSecurity`1.</summary>
      <returns>Retourne true si l'objet a été supprimé ; sinon, false.</returns>
      <param name="rule">Règle d'audit à supprimer</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAuditRuleAll(System.Security.AccessControl.AuditRule{`0})">
      <summary>Supprime toutes les règles d'audit qui ont le même identificateur de sécurité que la règle d'audit spécifiée dans la liste de contrôle d'accès système associée à cet objet ObjectSecurity`1.</summary>
      <param name="rule">Règle d'audit à supprimer.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAuditRuleSpecific(System.Security.AccessControl.AuditRule{`0})">
      <summary>Supprime toutes les règles d'audit qui correspondent exactement à la règle d'audit spécifiée dans la liste de contrôle d'accès système associée à cet objet ObjectSecurity`1.</summary>
      <param name="rule">Règle d'audit à supprimer.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.ResetAccessRule(System.Security.AccessControl.AccessRule{`0})">
      <summary>Supprime toutes les règles d'accès de la liste de contrôle d'accès discrétionnaire associée à cet objet ObjectSecurity`1, puis ajoute la règle d'accès spécifiée.</summary>
      <param name="rule">Règle d'accès à réinitialiser.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.SetAccessRule(System.Security.AccessControl.AccessRule{`0})">
      <summary>Supprime toutes les règles d'accès qui contiennent les mêmes identificateur de sécurité et qualificateur que la règle d'accès spécifiée dans la liste de contrôle d'accès discrétionnaire associée à cet objet ObjectSecurity`1, puis ajoute la règle d'accès spécifiée.</summary>
      <param name="rule">Règle d'accès à définir.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.SetAuditRule(System.Security.AccessControl.AuditRule{`0})">
      <summary>Supprime toutes les règles d'audit qui contiennent les mêmes identificateur de sécurité et qualificateur que la règle d'audit spécifiée dans la liste de contrôle d'accès système associée à cet objet ObjectSecurity`1, puis ajoute la règle d'audit spécifiée.</summary>
      <param name="rule">Règle d'audit à définir.</param>
    </member>
    <member name="T:System.Security.AccessControl.PrivilegeNotHeldException">
      <summary>Exception levée lorsqu'une méthode de l'espace de noms <see cref="N:System.Security.AccessControl" /> tente d'activer un privilège qu'elle n'a pas.</summary>
    </member>
    <member name="M:System.Security.AccessControl.PrivilegeNotHeldException.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.PrivilegeNotHeldException" />.</summary>
    </member>
    <member name="M:System.Security.AccessControl.PrivilegeNotHeldException.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.PrivilegeNotHeldException" /> avec le privilège spécifié.</summary>
      <param name="privilege">Privilège qui n'est pas activé.</param>
    </member>
    <member name="M:System.Security.AccessControl.PrivilegeNotHeldException.#ctor(System.String,System.Exception)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.PrivilegeNotHeldException" /> à l'aide de l'exception spécifiée.</summary>
      <param name="privilege">Privilège qui n'est pas activé.</param>
      <param name="inner">Exception qui constitue la cause de l'exception actuelle.Si le paramètre <paramref name="innerException" /> n'est pas une référence null (Nothing en Visual Basic), l'exception en cours est levée dans un bloc catch qui gère l'exception interne.</param>
    </member>
    <member name="P:System.Security.AccessControl.PrivilegeNotHeldException.PrivilegeName">
      <summary>Obtient le nom du privilège non activé.</summary>
      <returns>Nom du privilège que la méthode n'a pas activé.</returns>
    </member>
    <member name="T:System.Security.AccessControl.PropagationFlags">
      <summary>Spécifie la façon dont les entrées du contrôle d'accès sont propagées vers les objets enfants.  Ces indicateurs ne sont significatifs que si des indicateurs d'héritage sont présents. </summary>
    </member>
    <member name="F:System.Security.AccessControl.PropagationFlags.InheritOnly">
      <summary>Spécifie que l'entrée du contrôle d'accès n'est propagée qu'aux objets enfants.Les objets enfants sont des objets conteneurs et descendants.</summary>
    </member>
    <member name="F:System.Security.AccessControl.PropagationFlags.None">
      <summary>Spécifie qu'aucun indicateur d'héritage n'est défini.</summary>
    </member>
    <member name="F:System.Security.AccessControl.PropagationFlags.NoPropagateInherit">
      <summary>Spécifie que l'entrée du contrôle d'accès n'est pas propagée aux objets enfants.</summary>
    </member>
    <member name="T:System.Security.AccessControl.QualifiedAce">
      <summary>Représente une entrée du contrôle d'accès contenant un qualificateur.Le qualificateur, représenté par un objet <see cref="T:System.Security.AccessControl.AceQualifier" />, spécifie si l'entrée du contrôle d'accès accorde ou refuse l'accès et si elle provoque des audits ou des alertes de système.La classe <see cref="T:System.Security.AccessControl.QualifiedAce" /> est la classe de base abstraite des classes <see cref="T:System.Security.AccessControl.CommonAce" /> et <see cref="T:System.Security.AccessControl.ObjectAce" />.</summary>
    </member>
    <member name="P:System.Security.AccessControl.QualifiedAce.AceQualifier">
      <summary>Obtient une valeur qui spécifie si l'entrée du contrôle d'accès accorde ou refuse l'accès et si elle provoque des audits ou des alertes de système.</summary>
      <returns>Valeur qui spécifie si l'entrée du contrôle d'accès accorde ou refuse l'accès et si elle provoque des audits ou des alertes de système.</returns>
    </member>
    <member name="M:System.Security.AccessControl.QualifiedAce.GetOpaque">
      <summary>Retourne les données de rappel opaques associées à cet objet <see cref="T:System.Security.AccessControl.QualifiedAce" />. </summary>
      <returns>Tableau des valeurs d'octet qui représente les données de rappel opaques associées à cet objet <see cref="T:System.Security.AccessControl.QualifiedAce" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.QualifiedAce.IsCallback">
      <summary>Spécifie si cet objet <see cref="T:System.Security.AccessControl.QualifiedAce" /> contient des données de rappel.</summary>
      <returns>true si cet objet <see cref="T:System.Security.AccessControl.QualifiedAce" /> contient des données de rappel ; sinon, false.</returns>
    </member>
    <member name="P:System.Security.AccessControl.QualifiedAce.OpaqueLength">
      <summary>Obtient la longueur des données de rappel opaques associées à cet objet <see cref="T:System.Security.AccessControl.QualifiedAce" />.Cette propriété n'est valide que dans le cas d'entrées du contrôle d'accès.</summary>
      <returns>Longueur des données de rappel opaques.</returns>
    </member>
    <member name="M:System.Security.AccessControl.QualifiedAce.SetOpaque(System.Byte[])">
      <summary>Définit les données de rappel opaques associées à cet objet <see cref="T:System.Security.AccessControl.QualifiedAce" />.</summary>
      <param name="opaque">Tableau de valeurs d'octet qui représente les données de rappel opaques de cet objet <see cref="T:System.Security.AccessControl.QualifiedAce" />.</param>
    </member>
    <member name="T:System.Security.AccessControl.RawAcl">
      <summary>Représente une liste de contrôle d'accès.</summary>
    </member>
    <member name="M:System.Security.AccessControl.RawAcl.#ctor(System.Byte,System.Int32)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.RawAcl" /> avec le niveau de révision spécifié.</summary>
      <param name="revision">Niveau de révision de la nouvelle liste de contrôle d'accès.</param>
      <param name="capacity">Nombre d'entrées du contrôle d'accès que cet objet <see cref="T:System.Security.AccessControl.RawAcl" /> peut contenir.Ce nombre n'est à utiliser qu'à titre indicatif.</param>
    </member>
    <member name="M:System.Security.AccessControl.RawAcl.#ctor(System.Byte[],System.Int32)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.RawAcl" /> sous la forme binaire spécifiée.</summary>
      <param name="binaryForm">Tableau de valeurs, exprimées en octets, représentant une liste de contrôle d'accès.</param>
      <param name="offset">Offset dans le paramètre <paramref name="binaryForm" /> auquel commencer à démarshaler les données.</param>
    </member>
    <member name="P:System.Security.AccessControl.RawAcl.BinaryLength">
      <summary>Obtient la longueur, en octets, de la représentation binaire de l'objet <see cref="T:System.Security.AccessControl.RawAcl" /> en cours.Cette longueur doit être utilisée avant de marshaler la liste de contrôle d'accès dans un tableau binaire avec la méthode <see cref="M:System.Security.AccessControl.RawAcl.GetBinaryForm" />.</summary>
      <returns>Longueur, en octets, de la représentation binaire de l'objet <see cref="T:System.Security.AccessControl.RawAcl" /> en cours.</returns>
    </member>
    <member name="P:System.Security.AccessControl.RawAcl.Count">
      <summary>Obtient le nombre d'entrées du contrôle d'accès dans l'objet <see cref="T:System.Security.AccessControl.RawAcl" /> en cours.</summary>
      <returns>Nombre d'entrées du contrôle d'accès dans l'objet <see cref="T:System.Security.AccessControl.RawAcl" /> en cours.</returns>
    </member>
    <member name="M:System.Security.AccessControl.RawAcl.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Marshale le contenu de l'objet <see cref="T:System.Security.AccessControl.RawAcl" /> dans le tableau d'octets spécifié en commençant à l'offset spécifié.</summary>
      <param name="binaryForm">Tableau d'octets dans lequel le contenu de <see cref="T:System.Security.AccessControl.RawAcl" /> est marshalé.</param>
      <param name="offset">Offset au niveau duquel commencer le marshaling.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> est négatif ou trop élevé pour que <see cref="T:System.Security.AccessControl.RawAcl" /> puisse être entièrement copié dans <paramref name="array" />.</exception>
    </member>
    <member name="M:System.Security.AccessControl.RawAcl.InsertAce(System.Int32,System.Security.AccessControl.GenericAce)">
      <summary>Insère l'entrée du contrôle d'accès spécifiée à l'index spécifié.</summary>
      <param name="index">Position à laquelle ajouter la nouvelle entrée du contrôle d'accès.Spécifiez la valeur de la propriété <see cref="P:System.Security.AccessControl.RawAcl.Count" /> pour insérer une entrée du contrôle d'accès à la fin de l'objet <see cref="T:System.Security.AccessControl.RawAcl" />.</param>
      <param name="ace">entrée du contrôle d'accès à insérer.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> est négatif ou trop élevé pour que <see cref="T:System.Security.AccessControl.GenericAcl" /> puisse être entièrement copié dans <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Security.AccessControl.RawAcl.Item(System.Int32)">
      <summary>Obtient ou définit l'entrée du contrôle d'accès à l'index spécifié.</summary>
      <returns>entrée du contrôle d'accès à l'index spécifié.</returns>
      <param name="index">Index de base zéro de l'entrée du contrôle d'accès à obtenir ou définir.</param>
    </member>
    <member name="M:System.Security.AccessControl.RawAcl.RemoveAce(System.Int32)">
      <summary>Supprime l'entrée du contrôle d'accès à l'emplacement spécifié.</summary>
      <param name="index">Index de base zéro de l'entrée du contrôle d'accès à supprimer.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">La valeur du paramètre <paramref name="index" /> est plus élevée que la valeur de la propriété <see cref="P:System.Security.AccessControl.RawAcl.Count" /> moins un ou elle est négative.</exception>
    </member>
    <member name="P:System.Security.AccessControl.RawAcl.Revision">
      <summary>Obtient le numéro de révision de <see cref="T:System.Security.AccessControl.RawAcl" />.</summary>
      <returns>Valeur d'octet qui spécifie le niveau de révision de <see cref="T:System.Security.AccessControl.RawAcl" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.RawSecurityDescriptor">
      <summary>Représente un descripteur de sécurité.Un descripteur de sécurité inclut un propriétaire, un groupe principal, une liste de contrôle d'accès discrétionnaire et une liste de contrôle d'accès système.</summary>
    </member>
    <member name="M:System.Security.AccessControl.RawSecurityDescriptor.#ctor(System.Byte[],System.Int32)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> à partir du tableau spécifié de valeurs d'octets.</summary>
      <param name="binaryForm">Tableau de valeurs d'octets à partir duquel créer le nouvel objet <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</param>
      <param name="offset">Dans le tableau <paramref name="binaryForm" />, offset auquel commencer la copie.</param>
    </member>
    <member name="M:System.Security.AccessControl.RawSecurityDescriptor.#ctor(System.Security.AccessControl.ControlFlags,System.Security.Principal.SecurityIdentifier,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.RawAcl,System.Security.AccessControl.RawAcl)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> avec les valeurs spécifiées.</summary>
      <param name="flags">Indicateurs qui spécifient le comportement du nouvel objet <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</param>
      <param name="owner">Propriétaire du nouvel objet <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</param>
      <param name="group">Groupe principal du nouvel objet <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</param>
      <param name="systemAcl">Liste de contrôle d'accès système du nouvel objet <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</param>
      <param name="discretionaryAcl">Liste de contrôle d'accès discrétionnaire du nouvel objet <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</param>
    </member>
    <member name="M:System.Security.AccessControl.RawSecurityDescriptor.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> à partir de la chaîne SDDL spécifiée.</summary>
      <param name="sddlForm">Chaîne SDDL à partir de laquelle créer le nouvel objet <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</param>
    </member>
    <member name="P:System.Security.AccessControl.RawSecurityDescriptor.ControlFlags">
      <summary>Obtient des valeurs qui spécifient le comportement de l'objet <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</summary>
      <returns>Une ou plusieurs valeurs de l'énumération <see cref="T:System.Security.AccessControl.ControlFlags" /> associées par une opération OR logique.</returns>
    </member>
    <member name="P:System.Security.AccessControl.RawSecurityDescriptor.DiscretionaryAcl">
      <summary>Obtient ou définit la liste de contrôle d'accès discrétionnaire de cet objet <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.La liste de contrôle d'accès discrétionnaire contient des règles d'accès.</summary>
      <returns>Liste de contrôle d'accès discrétionnaire de cet objet <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.RawSecurityDescriptor.Group">
      <summary>Obtient ou définit le groupe principal de cet objet <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</summary>
      <returns>Groupe principal de cet objet <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.RawSecurityDescriptor.Owner">
      <summary>Obtient ou définit le propriétaire de l'objet associé à cet objet <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</summary>
      <returns>Propriétaire de l'objet associé à cet objet <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</returns>
    </member>
    <member name="P:System.Security.AccessControl.RawSecurityDescriptor.ResourceManagerControl">
      <summary>Obtient ou définit une valeur, en octets, qui représente les bits de contrôle de gestionnaire des ressources associés à cet objet <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</summary>
      <returns>Valeur en octets, qui représente les bits de contrôle du gestionnaire des ressources associés à cet objet <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.RawSecurityDescriptor.SetFlags(System.Security.AccessControl.ControlFlags)">
      <summary>Donne à la propriété <see cref="P:System.Security.AccessControl.RawSecurityDescriptor.ControlFlags" /> de cet objet <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> la valeur spécifiée.</summary>
      <param name="flags">Une ou plusieurs valeurs de l'énumération <see cref="T:System.Security.AccessControl.ControlFlags" /> associées par une opération OR logique.</param>
    </member>
    <member name="P:System.Security.AccessControl.RawSecurityDescriptor.SystemAcl">
      <summary>Obtient ou définit la liste de contrôle d'accès système de cet objet <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.La liste de contrôle d'accès système contient des règles d'audit.</summary>
      <returns>Liste de contrôle d'accès système de cet objet <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" />.</returns>
    </member>
    <member name="T:System.Security.AccessControl.ResourceType">
      <summary>Spécifie les types d'objet natifs définis.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.DSObject">
      <summary>Objet service d'annuaire, jeu de propriétés ou propriété d'un objet service d'annuaire.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.DSObjectAll">
      <summary>Objet service d'annuaire et tous ses jeux de propriétés et propriétés.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.FileObject">
      <summary>Fichier ou répertoire.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.KernelObject">
      <summary>Objet de noyau local.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.LMShare">
      <summary>Partage réseau.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.Printer">
      <summary>Imprimante.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.ProviderDefined">
      <summary>Objet défini par un fournisseur.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.RegistryKey">
      <summary>Clé de Registre.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.RegistryWow6432Key">
      <summary>Objet pour une entrée du Registre sous WOW64.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.Service">
      <summary>Service Windows.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.Unknown">
      <summary>Type d'objet inconnu.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.WindowObject">
      <summary>Station Windows ou objet de bureau sur l'ordinateur local.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.WmiGuidObject">
      <summary>Objet WMI (Windows Management Instrumentation).</summary>
    </member>
    <member name="T:System.Security.AccessControl.SecurityInfos">
      <summary>Spécifie la section d'un descripteur de sécurité à interroger ou définir.</summary>
    </member>
    <member name="F:System.Security.AccessControl.SecurityInfos.DiscretionaryAcl">
      <summary>Spécifie la liste de contrôle d'accès discrétionnaire.</summary>
    </member>
    <member name="F:System.Security.AccessControl.SecurityInfos.Group">
      <summary>Spécifie l'identificateur de groupe principal.</summary>
    </member>
    <member name="F:System.Security.AccessControl.SecurityInfos.Owner">
      <summary>Spécifie l'identificateur du propriétaire.</summary>
    </member>
    <member name="F:System.Security.AccessControl.SecurityInfos.SystemAcl">
      <summary>Spécifie la liste de contrôle d'accès système.</summary>
    </member>
    <member name="T:System.Security.AccessControl.SystemAcl">
      <summary>Représente une liste de contrôle d'accès système.</summary>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.#ctor(System.Boolean,System.Boolean,System.Byte,System.Int32)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.SystemAcl" /> avec les valeurs spécifiées.</summary>
      <param name="isContainer">true si le nouvel objet <see cref="T:System.Security.AccessControl.SystemAcl" /> est un conteneur.</param>
      <param name="isDS">true si le nouvel objet <see cref="T:System.Security.AccessControl.SystemAcl" /> est une liste de contrôle d'accès d'un objet annuaire.</param>
      <param name="revision">Niveau de révision du nouvel objet <see cref="T:System.Security.AccessControl.SystemAcl" />.</param>
      <param name="capacity">Nombre d'entrées du contrôle d'accès que cet objet <see cref="T:System.Security.AccessControl.SystemAcl" /> peut contenir.Ce nombre n'est à utiliser qu'à titre indicatif.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.#ctor(System.Boolean,System.Boolean,System.Int32)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.SystemAcl" /> avec les valeurs spécifiées.</summary>
      <param name="isContainer">true si le nouvel objet <see cref="T:System.Security.AccessControl.SystemAcl" /> est un conteneur.</param>
      <param name="isDS">true si le nouvel objet <see cref="T:System.Security.AccessControl.SystemAcl" /> est une liste de contrôle d'accès d'un objet annuaire.</param>
      <param name="capacity">Nombre d'entrées du contrôle d'accès que cet objet <see cref="T:System.Security.AccessControl.SystemAcl" /> peut contenir.Ce nombre n'est à utiliser qu'à titre indicatif.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.#ctor(System.Boolean,System.Boolean,System.Security.AccessControl.RawAcl)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Security.AccessControl.SystemAcl" /> avec les valeurs spécifiées à partir de l'objet <see cref="T:System.Security.AccessControl.RawAcl" /> spécifié.</summary>
      <param name="isContainer">true si le nouvel objet <see cref="T:System.Security.AccessControl.SystemAcl" /> est un conteneur.</param>
      <param name="isDS">true si le nouvel objet <see cref="T:System.Security.AccessControl.SystemAcl" /> est une liste de contrôle d'accès d'un objet annuaire.</param>
      <param name="rawAcl">Objet <see cref="T:System.Security.AccessControl.RawAcl" /> sous-jacent du nouvel objet <see cref="T:System.Security.AccessControl.SystemAcl" />.Spécifiez null pour créer une liste de contrôle d'accès vide.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.AddAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Ajoute une règle d'audit à l'objet <see cref="T:System.Security.AccessControl.SystemAcl" /> en cours.</summary>
      <param name="auditFlags">Type de règle d'audit à ajouter.</param>
      <param name="sid">
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> pour lequel ajouter une règle d'audit.</param>
      <param name="accessMask">Masque d'accès de la nouvelle règle d'audit.</param>
      <param name="inheritanceFlags">Indicateurs qui spécifient les propriétés d'héritage de la nouvelle règle d'audit.</param>
      <param name="propagationFlags">Indicateurs qui spécifient les propriétés de propagation d'héritage de la nouvelle règle d'audit.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.AddAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>Ajoute une règle d'audit avec les paramètres spécifiés à l'objet <see cref="T:System.Security.AccessControl.SystemAcl" /> en cours.Utilisez cette méthode pour les listes de contrôle d'accès des objets d'annuaire quand vous spécifiez le type d'objet ou le type d'objet hérité pour la nouvelle règle d'audit.</summary>
      <param name="auditFlags">Type de règle d'audit à ajouter.</param>
      <param name="sid">
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> pour lequel ajouter une règle d'audit.</param>
      <param name="accessMask">Masque d'accès de la nouvelle règle d'audit.</param>
      <param name="inheritanceFlags">Indicateurs qui spécifient les propriétés d'héritage de la nouvelle règle d'audit.</param>
      <param name="propagationFlags">Indicateurs qui spécifient les propriétés de propagation d'héritage de la nouvelle règle d'audit.</param>
      <param name="objectFlags">Indicateurs qui spécifient si les paramètres <paramref name="objectType" /> et <paramref name="inheritedObjectType" /> contiennent des valeurs non null.</param>
      <param name="objectType">Identité de la classe des objets auxquels la nouvelle règle d'audit s'applique.</param>
      <param name="inheritedObjectType">Identité de la classe des objets enfants qui peuvent hériter de la nouvelle règle d'audit.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.AddAudit(System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAuditRule)">
      <summary>Ajoute une règle d'audit à l'objet <see cref="T:System.Security.AccessControl.SystemAcl" /> en cours.</summary>
      <param name="sid">
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> pour lequel ajouter une règle d'audit.</param>
      <param name="rule">Le <see cref="T:System.Security.AccessControl.ObjectAuditRule" />pour la nouvelle règle d'audit.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.RemoveAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Supprime la règle d'audit spécifiée de l'objet <see cref="T:System.Security.AccessControl.SystemAcl" /> en cours.</summary>
      <returns>true si cette méthode réussit à supprimer la règle d'audit spécifiée ; sinon, false.</returns>
      <param name="auditFlags">Type de règle d'audit à supprimer.</param>
      <param name="sid">
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> pour lequel supprimer une règle d'audit.</param>
      <param name="accessMask">Masque d'accès de la règle à supprimer.</param>
      <param name="inheritanceFlags">Indicateurs qui spécifient les propriétés d'héritage de la règle à supprimer.</param>
      <param name="propagationFlags">Indicateurs qui spécifient les propriétés de propagation d'héritage de la règle à supprimer.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.RemoveAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>Supprime la règle d'audit spécifiée de l'objet <see cref="T:System.Security.AccessControl.SystemAcl" /> en cours.Utilisez cette méthode pour les listes de contrôle d'accès des objets d'annuaire quand vous spécifiez le type d'objet ou le type d'objet hérité.</summary>
      <returns>true si cette méthode réussit à supprimer la règle d'audit spécifiée ; sinon, false.</returns>
      <param name="auditFlags">Type de règle d'audit à supprimer.</param>
      <param name="sid">
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> pour lequel supprimer une règle d'audit.</param>
      <param name="accessMask">Masque d'accès de la règle à supprimer.</param>
      <param name="inheritanceFlags">Indicateurs qui spécifient les propriétés d'héritage de la règle à supprimer.</param>
      <param name="propagationFlags">Indicateurs qui spécifient les propriétés de propagation d'héritage de la règle à supprimer.</param>
      <param name="objectFlags">Indicateurs qui spécifient si les paramètres <paramref name="objectType" /> et <paramref name="inheritedObjectType" /> contiennent des valeurs non null.</param>
      <param name="objectType">Identité de la classe des objets auxquels la règle de contrôle d'audit supprimée s'applique.</param>
      <param name="inheritedObjectType">Identité de la classe des objets enfants qui peuvent hériter de la règle d'audit supprimée.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.RemoveAudit(System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAuditRule)">
      <summary>Supprime la règle d'audit spécifiée de l'objet <see cref="T:System.Security.AccessControl.SystemAcl" /> en cours.</summary>
      <returns>true si cette méthode réussit à supprimer la règle d'audit spécifiée ; sinon, false.</returns>
      <param name="sid">
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> pour lequel supprimer une règle d'audit.</param>
      <param name="rule">
        <see cref="T:System.Security.AccessControl.ObjectAuditRule" /> pour lequel supprimer une règle d'audit.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.RemoveAuditSpecific(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Supprime la règle d'audit spécifiée de l'objet <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> en cours.</summary>
      <param name="auditFlags">Type de règle d'audit à supprimer.</param>
      <param name="sid">
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> pour lequel supprimer une règle d'audit.</param>
      <param name="accessMask">Masque d'accès de la règle à supprimer.</param>
      <param name="inheritanceFlags">Indicateurs qui spécifient les propriétés d'héritage de la règle à supprimer.</param>
      <param name="propagationFlags">Indicateurs qui spécifient les propriétés de propagation d'héritage de la règle à supprimer.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.RemoveAuditSpecific(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>Supprime la règle d'audit spécifiée de l'objet <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> en cours.Utilisez cette méthode pour les listes de contrôle d'accès des objets d'annuaire quand vous spécifiez le type d'objet ou le type d'objet hérité.</summary>
      <param name="auditFlags">Type de règle d'audit à supprimer.</param>
      <param name="sid">
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> pour lequel supprimer une règle d'audit.</param>
      <param name="accessMask">Masque d'accès de la règle à supprimer.</param>
      <param name="inheritanceFlags">Indicateurs qui spécifient les propriétés d'héritage de la règle à supprimer.</param>
      <param name="propagationFlags">Indicateurs qui spécifient les propriétés de propagation d'héritage de la règle à supprimer.</param>
      <param name="objectFlags">Indicateurs qui spécifient si les paramètres <paramref name="objectType" /> et <paramref name="inheritedObjectType" /> contiennent des valeurs non null.</param>
      <param name="objectType">Identité de la classe des objets auxquels la règle de contrôle d'audit supprimée s'applique.</param>
      <param name="inheritedObjectType">Identité de la classe des objets enfants qui peuvent hériter de la règle d'audit supprimée.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.RemoveAuditSpecific(System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAuditRule)">
      <summary>Supprime la règle d'audit spécifiée de l'objet <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> en cours.</summary>
      <param name="sid">
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> pour lequel supprimer une règle d'audit.</param>
      <param name="rule">Le <see cref="T:System.Security.AccessControl.ObjectAuditRule" /> pour la règle à supprimer.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.SetAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>Définit la règle d'audit spécifiée de l'objet <see cref="T:System.Security.Principal.SecurityIdentifier" /> spécifié.</summary>
      <param name="auditFlags">Condition d'audit à définir.</param>
      <param name="sid">
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> pour lequel définir une règle d'audit.</param>
      <param name="accessMask">Masque d'accès de la nouvelle règle d'audit.</param>
      <param name="inheritanceFlags">Indicateurs qui spécifient les propriétés d'héritage de la nouvelle règle d'audit.</param>
      <param name="propagationFlags">Indicateurs qui spécifient les propriétés de propagation d'héritage de la nouvelle règle d'audit.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.SetAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>Définit la règle d'audit spécifiée de l'objet <see cref="T:System.Security.Principal.SecurityIdentifier" /> spécifié.Utilisez cette méthode pour les listes de contrôle d'accès des objets d'annuaire quand vous spécifiez le type d'objet ou le type d'objet hérité.</summary>
      <param name="auditFlags">Condition d'audit à définir.</param>
      <param name="sid">
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> pour lequel définir une règle d'audit.</param>
      <param name="accessMask">Masque d'accès de la nouvelle règle d'audit.</param>
      <param name="inheritanceFlags">Indicateurs qui spécifient les propriétés d'héritage de la nouvelle règle d'audit.</param>
      <param name="propagationFlags">Indicateurs qui spécifient les propriétés de propagation d'héritage de la nouvelle règle d'audit.</param>
      <param name="objectFlags">Indicateurs qui spécifient si les paramètres <paramref name="objectType" /> et <paramref name="inheritedObjectType" /> contiennent des valeurs non null.</param>
      <param name="objectType">Identité de la classe des objets auxquels la nouvelle règle d'audit s'applique.</param>
      <param name="inheritedObjectType">Identité de la classe des objets enfants qui peuvent hériter de la nouvelle règle d'audit.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.SetAudit(System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAuditRule)">
      <summary>Définit la règle d'audit spécifiée de l'objet <see cref="T:System.Security.Principal.SecurityIdentifier" /> spécifié.</summary>
      <param name="sid">
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> pour lequel définir une règle d'audit.</param>
      <param name="rule">
        <see cref="T:System.Security.AccessControl.ObjectAuditRule" /> pour lequel définir une règle d'audit.</param>
    </member>
  </members>
</doc>