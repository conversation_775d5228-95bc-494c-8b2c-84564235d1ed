﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.IO.Ports;
using WeifenLuo.WinFormsUI;
using AssistPro.vhf;
using System.IO;
using System.Diagnostics;
using AssistPro.Lib;
using AssistPro.navtex;
using AssistPro.navtex.msg;
using AssistPro.navtex.communication;
using AssistPro.ais;
using static AssistPro.navtex.communication.CommPort;

namespace AssistPro
{
    public partial class AssistPro : Form
    {
        private CommPort m_CommPort = CommPort.Instance;
        private CommProcess m_CommProc = CommProcess.Instance;

        private ioPortMonitorForm m_ioPortMonitor;
        private IV2500A_Form m_IV2500A_Simul;
        private ProtocolAnalyzerForm m_protocolAnalyzer;
        private NmeaGeneratorForm m_NmeaGenerator;
        private IN3000R_Form m_IN3000R_Form;
        private IS1250A_Form m_IS1250A_Form;
        private FirmwareUpdateForm m_FWUpdate;
        private RemoteControlForm m_IV2500A_RemoteControl;
        private BerTestForm m_BerTest_Form;

        public AssistPro()
        {
            InitializeComponent();

            Process.GetCurrentProcess().PriorityBoostEnabled = true;
            Process.GetCurrentProcess().PriorityClass = ProcessPriorityClass.RealTime;
        }


        private void engModeToolStripMenuItem_Click(object sender, EventArgs e)
        {

        }

        private void AssistPro_Load(object sender, EventArgs e)
        {
            m_ioPortMonitor = new ioPortMonitorForm();
            m_IV2500A_Simul = new IV2500A_Form();
            m_IV2500A_RemoteControl = new RemoteControlForm();
            m_protocolAnalyzer = new ProtocolAnalyzerForm();
            m_NmeaGenerator = new NmeaGeneratorForm();
            m_FWUpdate = new FirmwareUpdateForm();

            if (LoadLayout("layout.xml") == false)
            {
                m_ioPortMonitor.Show(dockPanel, DockState.DockBottom);
            }
        }

        private void SaveLayout(string filePath)
        {
            try
            {
                dockPanel.SaveAsXml(filePath, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                MessageBox.Show("Failed to save layout: " + ex.Message);
            }
        }

        private IDockContent DeserializeDockContent(string persistString)
        {
            // 여기에 로직을 추가하여 각 도킹 컨텐츠를 복원합니다.
            if (persistString == typeof(ioPortMonitorForm).ToString())
            {
                return new ioPortMonitorForm();
            }
            else if (persistString == typeof(IV2500A_Form).ToString())
            {
                return new IV2500A_Form();
            }
            else if (persistString == typeof(ProtocolAnalyzerForm).ToString())
            {
                return new ProtocolAnalyzerForm();
            }
            else if(persistString == typeof(IN3000R_Form).ToString())
            {
                return new IN3000R_Form();
            }
            else if (persistString == typeof(NmeaGeneratorForm).ToString())
            {
                return new NmeaGeneratorForm();
            }
            else if (persistString == typeof(FirmwareUpdateForm).ToString())
            {
                //return new FirmwareUpdateForm();
                return m_FWUpdate;
            }
            else if (persistString == typeof(RemoteControlForm).ToString())
            {
                return new RemoteControlForm();
            }
            else
                return null;

        }

        private bool LoadLayout(string filePath)
        {
            if (!File.Exists(filePath))
                return false;

            try
            {
                dockPanel.LoadFromXml(filePath, DeserializeDockContent);
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show("Failed to load layout: " + ex.Message);
                return false;
            }
        }

        private void saveDockingLayoutToolStripMenuItem_Click(object sender, EventArgs e)
        {
            SaveLayout("layout.xml");
        }

        private void restoreLayoutToolStripMenuItem_Click(object sender, EventArgs e)
        {
            LoadLayout("layout.xml");
        }

        private void comPortToolStripMenuItem_Click(object sender, EventArgs e)
        {
            m_ioPortMonitor.Show(dockPanel, DockState.DockBottom);
        }

        private void iV2500AToolStripMenuItem_Click(object sender, EventArgs e)
        {
            m_IV2500A_Simul.Show(dockPanel, DockState.Document);
        }

        private void dataProtocolAnalyzerToolStripMenuItem_Click(object sender, EventArgs e)
        {
            m_protocolAnalyzer.Show(dockPanel, DockState.DockBottom);
        }

        private void MainForm_Closing(object sender, FormClosingEventArgs e)
        {
            foreach (var process in Process.GetProcessesByName("AssistPro"))
            {
                try
                {
                    process.Kill();
                    process.WaitForExit();
                }
                catch (Exception ex)
                {
                    // 예외 처리 (로그 기록 등)
                    Console.WriteLine($"프로세스 종료 중 오류 발생: {ex.Message}");
                }
            }
            // 현재 애플리케이션 종료
            Application.Exit();

        }

        private void iN3000RToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if(m_IN3000R_Form == null || m_IN3000R_Form.IsDisposed)
            {
                m_IN3000R_Form = new IN3000R_Form();
                m_IN3000R_Form.Show(dockPanel, DockState.Document);
            }
            else
            {
                m_IN3000R_Form.Show(dockPanel, DockState.Document);
            }
        }

        private void nMEAGeneratorToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (m_NmeaGenerator == null || m_NmeaGenerator.IsDisposed)
            {
                m_NmeaGenerator = new NmeaGeneratorForm();
            }
            m_NmeaGenerator.Show(dockPanel, DockState.Document);
        }

        private void firmwareUpdateToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (m_FWUpdate == null || m_FWUpdate.IsDisposed)
            {
                m_FWUpdate = new FirmwareUpdateForm();
            }
            m_FWUpdate.Show(dockPanel, DockState.Document);
        }

        private void iS1250AToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (m_IS1250A_Form == null || m_IS1250A_Form.IsDisposed)
            {
                m_IS1250A_Form = new IS1250A_Form();
                m_IS1250A_Form.Show(dockPanel, DockState.Document);
            }
            else
            {
                m_IS1250A_Form.Show(dockPanel, DockState.Document);
            }
        }

        private void remoteControlToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (m_IV2500A_RemoteControl == null || m_IV2500A_RemoteControl.IsDisposed)
            {
                m_IV2500A_RemoteControl = new RemoteControlForm();
                m_IV2500A_RemoteControl.Show(dockPanel, DockState.Document);
            }
            else
            {
                m_IV2500A_RemoteControl.Show(dockPanel, DockState.Document);
            }
        }

        private void bERTESTToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if(m_BerTest_Form == null || m_BerTest_Form.IsDisposed)
            {
                m_BerTest_Form = new BerTestForm();
                m_BerTest_Form.Show(dockPanel, DockState.Document);
            }
            else
            {
                m_BerTest_Form.Show(dockPanel, DockState.Document);
            }
        }
    }
}
