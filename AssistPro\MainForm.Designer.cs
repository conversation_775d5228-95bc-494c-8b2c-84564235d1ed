﻿using System.Drawing;
using System.Windows.Forms;

namespace AssistPro
{
    partial class AssistPro
    {
        /// <summary>
        /// 필수 디자이너 변수입니다.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 사용 중인 모든 리소스를 정리합니다.
        /// </summary>
        /// <param name="disposing">관리되는 리소스를 삭제해야 하면 true이고, 그렇지 않으면 false입니다.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form 디자이너에서 생성한 코드

        /// <summary>
        /// 디자이너 지원에 필요한 메서드입니다. 
        /// 이 메서드의 내용을 코드 편집기로 수정하지 마세요.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.menuStrip1 = new System.Windows.Forms.MenuStrip();
            this.fileToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.saveDockingLayoutToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.editToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.viewToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.comPortToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.nMEAGeneratorToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.engModeToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.vHFSimulatorToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.iV2500AToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.baseBandControlToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.remoteControlToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.dataProtocolAnalyzerToolStripMenuItem1 = new System.Windows.Forms.ToolStripMenuItem();
            this.nAVTEXToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.iN3000RToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.aISToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.iS1250AToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.bERTESTToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.statusStrip1 = new System.Windows.Forms.StatusStrip();
            this.SerialPort = new System.IO.Ports.SerialPort(this.components);
            this.dockPanel = new WeifenLuo.WinFormsUI.DockPanel();
            this.firmwareUpdateToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.menuStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // menuStrip1
            // 
            this.menuStrip1.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.menuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.fileToolStripMenuItem,
            this.editToolStripMenuItem,
            this.viewToolStripMenuItem,
            this.engModeToolStripMenuItem});
            this.menuStrip1.Location = new System.Drawing.Point(0, 0);
            this.menuStrip1.Name = "menuStrip1";
            this.menuStrip1.Padding = new System.Windows.Forms.Padding(4, 1, 0, 1);
            this.menuStrip1.Size = new System.Drawing.Size(965, 24);
            this.menuStrip1.TabIndex = 0;
            this.menuStrip1.Text = "menuStrip1";
            // 
            // fileToolStripMenuItem
            // 
            this.fileToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.saveDockingLayoutToolStripMenuItem});
            this.fileToolStripMenuItem.Font = new System.Drawing.Font("휴먼모음T", 9F);
            this.fileToolStripMenuItem.Name = "fileToolStripMenuItem";
            this.fileToolStripMenuItem.Size = new System.Drawing.Size(37, 22);
            this.fileToolStripMenuItem.Text = "File";
            // 
            // saveDockingLayoutToolStripMenuItem
            // 
            this.saveDockingLayoutToolStripMenuItem.Name = "saveDockingLayoutToolStripMenuItem";
            this.saveDockingLayoutToolStripMenuItem.Size = new System.Drawing.Size(143, 22);
            this.saveDockingLayoutToolStripMenuItem.Text = "Save Layout";
            this.saveDockingLayoutToolStripMenuItem.Click += new System.EventHandler(this.saveDockingLayoutToolStripMenuItem_Click);
            // 
            // editToolStripMenuItem
            // 
            this.editToolStripMenuItem.Font = new System.Drawing.Font("휴먼모음T", 9F);
            this.editToolStripMenuItem.Name = "editToolStripMenuItem";
            this.editToolStripMenuItem.Size = new System.Drawing.Size(39, 22);
            this.editToolStripMenuItem.Text = "Edit";
            // 
            // viewToolStripMenuItem
            // 
            this.viewToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.comPortToolStripMenuItem,
            this.nMEAGeneratorToolStripMenuItem,
            this.firmwareUpdateToolStripMenuItem});
            this.viewToolStripMenuItem.Font = new System.Drawing.Font("휴먼모음T", 9F);
            this.viewToolStripMenuItem.Name = "viewToolStripMenuItem";
            this.viewToolStripMenuItem.Size = new System.Drawing.Size(42, 22);
            this.viewToolStripMenuItem.Text = "Tool";
            // 
            // comPortToolStripMenuItem
            // 
            this.comPortToolStripMenuItem.Name = "comPortToolStripMenuItem";
            this.comPortToolStripMenuItem.Size = new System.Drawing.Size(180, 22);
            this.comPortToolStripMenuItem.Text = "I/O Port Monitor";
            this.comPortToolStripMenuItem.Click += new System.EventHandler(this.comPortToolStripMenuItem_Click);
            // 
            // nMEAGeneratorToolStripMenuItem
            // 
            this.nMEAGeneratorToolStripMenuItem.Name = "nMEAGeneratorToolStripMenuItem";
            this.nMEAGeneratorToolStripMenuItem.Size = new System.Drawing.Size(180, 22);
            this.nMEAGeneratorToolStripMenuItem.Text = "NMEA Generator";
            this.nMEAGeneratorToolStripMenuItem.Click += new System.EventHandler(this.nMEAGeneratorToolStripMenuItem_Click);
            // 
            // engModeToolStripMenuItem
            // 
            this.engModeToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.vHFSimulatorToolStripMenuItem,
            this.nAVTEXToolStripMenuItem,
            this.aISToolStripMenuItem,
            this.bERTESTToolStripMenuItem});
            this.engModeToolStripMenuItem.Font = new System.Drawing.Font("휴먼모음T", 9F);
            this.engModeToolStripMenuItem.Name = "engModeToolStripMenuItem";
            this.engModeToolStripMenuItem.Size = new System.Drawing.Size(71, 22);
            this.engModeToolStripMenuItem.Text = "Simulator";
            this.engModeToolStripMenuItem.Click += new System.EventHandler(this.engModeToolStripMenuItem_Click);
            // 
            // vHFSimulatorToolStripMenuItem
            // 
            this.vHFSimulatorToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.iV2500AToolStripMenuItem});
            this.vHFSimulatorToolStripMenuItem.Name = "vHFSimulatorToolStripMenuItem";
            this.vHFSimulatorToolStripMenuItem.Size = new System.Drawing.Size(127, 22);
            this.vHFSimulatorToolStripMenuItem.Text = "VHF";
            // 
            // iV2500AToolStripMenuItem
            // 
            this.iV2500AToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.baseBandControlToolStripMenuItem,
            this.remoteControlToolStripMenuItem,
            this.dataProtocolAnalyzerToolStripMenuItem1});
            this.iV2500AToolStripMenuItem.Name = "iV2500AToolStripMenuItem";
            this.iV2500AToolStripMenuItem.Size = new System.Drawing.Size(121, 22);
            this.iV2500AToolStripMenuItem.Text = "IV2500A";
            // 
            // baseBandControlToolStripMenuItem
            // 
            this.baseBandControlToolStripMenuItem.Name = "baseBandControlToolStripMenuItem";
            this.baseBandControlToolStripMenuItem.Size = new System.Drawing.Size(204, 22);
            this.baseBandControlToolStripMenuItem.Text = "BaseBand Control";
            this.baseBandControlToolStripMenuItem.Click += new System.EventHandler(this.iV2500AToolStripMenuItem_Click);
            // 
            // remoteControlToolStripMenuItem
            // 
            this.remoteControlToolStripMenuItem.Name = "remoteControlToolStripMenuItem";
            this.remoteControlToolStripMenuItem.Size = new System.Drawing.Size(204, 22);
            this.remoteControlToolStripMenuItem.Text = "Remote Control";
            this.remoteControlToolStripMenuItem.Click += new System.EventHandler(this.remoteControlToolStripMenuItem_Click);
            // 
            // dataProtocolAnalyzerToolStripMenuItem1
            // 
            this.dataProtocolAnalyzerToolStripMenuItem1.Name = "dataProtocolAnalyzerToolStripMenuItem1";
            this.dataProtocolAnalyzerToolStripMenuItem1.Size = new System.Drawing.Size(204, 22);
            this.dataProtocolAnalyzerToolStripMenuItem1.Text = "Data Protocol Analyzer";
            this.dataProtocolAnalyzerToolStripMenuItem1.Click += new System.EventHandler(this.dataProtocolAnalyzerToolStripMenuItem_Click);
            // 
            // nAVTEXToolStripMenuItem
            // 
            this.nAVTEXToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.iN3000RToolStripMenuItem});
            this.nAVTEXToolStripMenuItem.Name = "nAVTEXToolStripMenuItem";
            this.nAVTEXToolStripMenuItem.Size = new System.Drawing.Size(127, 22);
            this.nAVTEXToolStripMenuItem.Text = "NAVTEX";
            // 
            // iN3000RToolStripMenuItem
            // 
            this.iN3000RToolStripMenuItem.Name = "iN3000RToolStripMenuItem";
            this.iN3000RToolStripMenuItem.Size = new System.Drawing.Size(122, 22);
            this.iN3000RToolStripMenuItem.Text = "IN3000R";
            this.iN3000RToolStripMenuItem.Click += new System.EventHandler(this.iN3000RToolStripMenuItem_Click);
            // 
            // aISToolStripMenuItem
            // 
            this.aISToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.iS1250AToolStripMenuItem});
            this.aISToolStripMenuItem.Name = "aISToolStripMenuItem";
            this.aISToolStripMenuItem.Size = new System.Drawing.Size(127, 22);
            this.aISToolStripMenuItem.Text = "AIS";
            // 
            // iS1250AToolStripMenuItem
            // 
            this.iS1250AToolStripMenuItem.Name = "iS1250AToolStripMenuItem";
            this.iS1250AToolStripMenuItem.Size = new System.Drawing.Size(120, 22);
            this.iS1250AToolStripMenuItem.Text = "IS1250A";
            this.iS1250AToolStripMenuItem.Click += new System.EventHandler(this.iS1250AToolStripMenuItem_Click);
            // 
            // bERTESTToolStripMenuItem
            // 
            this.bERTESTToolStripMenuItem.Name = "bERTESTToolStripMenuItem";
            this.bERTESTToolStripMenuItem.Size = new System.Drawing.Size(127, 22);
            this.bERTESTToolStripMenuItem.Text = "BER TEST";
            this.bERTESTToolStripMenuItem.Click += new System.EventHandler(this.bERTESTToolStripMenuItem_Click);
            // 
            // statusStrip1
            // 
            this.statusStrip1.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.statusStrip1.Location = new System.Drawing.Point(0, 672);
            this.statusStrip1.Name = "statusStrip1";
            this.statusStrip1.Size = new System.Drawing.Size(965, 22);
            this.statusStrip1.TabIndex = 1;
            this.statusStrip1.Text = "statusStrip1";
            // 
            // SerialPort
            // 
            this.SerialPort.BaudRate = 115200;
            // 
            // dockPanel
            // 
            this.dockPanel.ActiveAutoHideContent = null;
            this.dockPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dockPanel.Font = new System.Drawing.Font("맑은 고딕", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.World);
            this.dockPanel.Location = new System.Drawing.Point(0, 24);
            this.dockPanel.Name = "dockPanel";
            this.dockPanel.Size = new System.Drawing.Size(965, 648);
            this.dockPanel.TabIndex = 6;
            //
            // firmwareUpdateToolStripMenuItem
            // 
            this.firmwareUpdateToolStripMenuItem.Name = "firmwareUpdateToolStripMenuItem";
            this.firmwareUpdateToolStripMenuItem.Size = new System.Drawing.Size(180, 22);
            this.firmwareUpdateToolStripMenuItem.Text = "Firmware Update";
            this.firmwareUpdateToolStripMenuItem.Click += new System.EventHandler(this.firmwareUpdateToolStripMenuItem_Click);
            // 
            // AssistPro
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(965, 694);
            this.Controls.Add(this.dockPanel);
            this.Controls.Add(this.statusStrip1);
            this.Controls.Add(this.menuStrip1);
            this.IsMdiContainer = true;
            this.MainMenuStrip = this.menuStrip1;
            this.Name = "AssistPro";
            this.Text = "AssistPro";
            this.WindowState = System.Windows.Forms.FormWindowState.Maximized;
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.MainForm_Closing);
            this.Load += new System.EventHandler(this.AssistPro_Load);
            this.menuStrip1.ResumeLayout(false);
            this.menuStrip1.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.MenuStrip menuStrip1;
        private System.Windows.Forms.StatusStrip statusStrip1;
        private System.Windows.Forms.ToolStripMenuItem fileToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem editToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem viewToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem engModeToolStripMenuItem;
        private System.IO.Ports.SerialPort SerialPort;
        private ToolStripMenuItem comPortToolStripMenuItem;
        private WeifenLuo.WinFormsUI.DockPanel dockPanel;
        private ToolStripMenuItem saveDockingLayoutToolStripMenuItem;
        private ToolStripMenuItem vHFSimulatorToolStripMenuItem;
        private ToolStripMenuItem iV2500AToolStripMenuItem;
        private ToolStripMenuItem nAVTEXToolStripMenuItem;
        private ToolStripMenuItem iN3000RToolStripMenuItem;
        private ToolStripMenuItem nMEAGeneratorToolStripMenuItem;
        private ToolStripMenuItem firmwareUpdateToolStripMenuItem;
        private ToolStripMenuItem aISToolStripMenuItem;
        private ToolStripMenuItem iS1250AToolStripMenuItem;
        private ToolStripMenuItem baseBandControlToolStripMenuItem;
        private ToolStripMenuItem remoteControlToolStripMenuItem;
        private ToolStripMenuItem dataProtocolAnalyzerToolStripMenuItem1;
        private ToolStripMenuItem bERTESTToolStripMenuItem;
    }
}

