﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{85C26708-A989-4328-8170-C64753DCBB2B}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>AssistPro</RootNamespace>
    <AssemblyName>AssistPro</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <PublishUrl>게시\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject />
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.Win32.Registry, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Win32.Registry.4.7.0\lib\net461\Microsoft.Win32.Registry.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAPICodePack.Core, Version=8.0.4.0, Culture=neutral, PublicKeyToken=8afb38e9204fc0a9, processorArchitecture=MSIL">
      <HintPath>..\packages\WindowsAPICodePack.8.0.4\lib\net472\Microsoft.WindowsAPICodePack.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAPICodePack.ExtendedLinguisticServices, Version=8.0.4.0, Culture=neutral, PublicKeyToken=8afb38e9204fc0a9, processorArchitecture=MSIL">
      <HintPath>..\packages\WindowsAPICodePack.8.0.4\lib\net472\Microsoft.WindowsAPICodePack.ExtendedLinguisticServices.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAPICodePack.Sensors, Version=8.0.4.0, Culture=neutral, PublicKeyToken=8afb38e9204fc0a9, processorArchitecture=MSIL">
      <HintPath>..\packages\WindowsAPICodePack.8.0.4\lib\net472\Microsoft.WindowsAPICodePack.Sensors.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAPICodePack.Shell, Version=8.0.4.0, Culture=neutral, PublicKeyToken=8afb38e9204fc0a9, processorArchitecture=MSIL">
      <HintPath>..\packages\WindowsAPICodePack.8.0.4\lib\net472\Microsoft.WindowsAPICodePack.Shell.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAPICodePack.ShellExtensions, Version=8.0.4.0, Culture=neutral, PublicKeyToken=8afb38e9204fc0a9, processorArchitecture=MSIL">
      <HintPath>..\packages\WindowsAPICodePack.8.0.4\lib\net472\Microsoft.WindowsAPICodePack.ShellExtensions.dll</HintPath>
    </Reference>
    <Reference Include="NAudio, Version=2.2.1.0, Culture=neutral, PublicKeyToken=e279aa5131008a41, processorArchitecture=MSIL">
      <HintPath>..\packages\NAudio.2.2.1\lib\net472\NAudio.dll</HintPath>
    </Reference>
    <Reference Include="NAudio.Asio, Version=2.2.1.0, Culture=neutral, PublicKeyToken=e279aa5131008a41, processorArchitecture=MSIL">
      <HintPath>..\packages\NAudio.Asio.2.2.1\lib\netstandard2.0\NAudio.Asio.dll</HintPath>
    </Reference>
    <Reference Include="NAudio.Core, Version=2.2.1.0, Culture=neutral, PublicKeyToken=e279aa5131008a41, processorArchitecture=MSIL">
      <HintPath>..\packages\NAudio.Core.2.2.1\lib\netstandard2.0\NAudio.Core.dll</HintPath>
    </Reference>
    <Reference Include="NAudio.Midi, Version=2.2.1.0, Culture=neutral, PublicKeyToken=e279aa5131008a41, processorArchitecture=MSIL">
      <HintPath>..\packages\NAudio.Midi.2.2.1\lib\netstandard2.0\NAudio.Midi.dll</HintPath>
    </Reference>
    <Reference Include="NAudio.Wasapi, Version=2.2.1.0, Culture=neutral, PublicKeyToken=e279aa5131008a41, processorArchitecture=MSIL">
      <HintPath>..\packages\NAudio.Wasapi.2.2.1\lib\netstandard2.0\NAudio.Wasapi.dll</HintPath>
    </Reference>
    <Reference Include="NAudio.WinForms, Version=2.2.1.0, Culture=neutral, PublicKeyToken=e279aa5131008a41, processorArchitecture=MSIL">
      <HintPath>..\packages\NAudio.WinForms.2.2.1\lib\net472\NAudio.WinForms.dll</HintPath>
    </Reference>
    <Reference Include="NAudio.WinMM, Version=2.2.1.0, Culture=neutral, PublicKeyToken=e279aa5131008a41, processorArchitecture=MSIL">
      <HintPath>..\packages\NAudio.WinMM.2.2.1\lib\netstandard2.0\NAudio.WinMM.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.Core" />
    <Reference Include="System.Design" />
    <Reference Include="System.IO, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.4.3.0\lib\net462\System.IO.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Net.Http, Version=4.1.1.3, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Http.4.3.4\lib\net46\System.Net.Http.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.4.3.0\lib\net462\System.Runtime.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security" />
    <Reference Include="System.Security.AccessControl, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.AccessControl.4.7.0\lib\net461\System.Security.AccessControl.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Algorithms.4.3.0\lib\net463\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Encoding.4.3.0\lib\net46\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Primitives.4.3.0\lib\net46\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.X509Certificates.4.3.0\lib\net461\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Principal.Windows, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Principal.Windows.4.7.0\lib\net461\System.Security.Principal.Windows.dll</HintPath>
    </Reference>
    <Reference Include="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\WindowsAPICodePack.8.0.4\lib\net472\System.Windows.Forms.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Xml" />
    <Reference Include="WeifenLuo.WinFormsUI.Docking">
      <HintPath>..\WeifenLuo.WinFormsUI.Docking.dll</HintPath>
    </Reference>
    <Reference Include="WindowsBase" />
    <Reference Include="WindowsFormsIntegration, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\WindowsAPICodePack.8.0.4\lib\net472\WindowsFormsIntegration.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ais\AisMessageDatParser.cs" />
    <Compile Include="ais\AisOwnShip.cs" />
    <Compile Include="ais\AISSentence.cs" />
    <Compile Include="ais\AisVdmDetailForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ais\AisVdmDetailForm.Designer.cs">
      <DependentUpon>AisVdmDetailForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ais\IS1250A_Form.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ais\IS1250A_Form.Designer.cs">
      <DependentUpon>IS1250A_Form.cs</DependentUpon>
    </Compile>
    <Compile Include="BerTestForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="BerTestForm.Designer.cs">
      <DependentUpon>BerTestForm.cs</DependentUpon>
    </Compile>
    <Compile Include="FirmwareUpdateForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FirmwareUpdateForm.Designer.cs">
      <DependentUpon>FirmwareUpdateForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Lib\ATimer.cs" />
    <Compile Include="Lib\AudioWave.cs" />
    <Compile Include="Lib\Common.cs" />
    <Compile Include="Lib\IniFile.cs" />
    <Compile Include="Lib\MicroLibrary.cs" />
    <Compile Include="navtex\communication\CommPort.cs" />
    <Compile Include="navtex\communication\CommProcess.cs" />
    <Compile Include="navtex\communication\CommProcess_Map.cs" />
    <Compile Include="navtex\communication\Serial_Setting_Form.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="navtex\communication\Serial_Setting_Form.Designer.cs">
      <DependentUpon>Serial_Setting_Form.cs</DependentUpon>
    </Compile>
    <Compile Include="navtex\communication\Serial_Setting_Ini.cs" />
    <Compile Include="navtex\communication\UdpPort.cs" />
    <Compile Include="navtex\IN3000R_Form.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="navtex\IN3000R_Form.Designer.cs">
      <DependentUpon>IN3000R_Form.cs</DependentUpon>
    </Compile>
    <Compile Include="navtex\monitor\Interface_Monitor_Form.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="navtex\monitor\Interface_Monitor_Form.Designer.cs">
      <DependentUpon>Interface_Monitor_Form.cs</DependentUpon>
    </Compile>
    <Compile Include="navtex\monitor\Nmea_Monitor_Form.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="navtex\monitor\Nmea_Monitor_Form.Designer.cs">
      <DependentUpon>Nmea_Monitor_Form.cs</DependentUpon>
    </Compile>
    <Compile Include="navtex\msg\Audio_Wave_Control.cs" />
    <Compile Include="navtex\msg\Msg_Setting_Ini.cs" />
    <Compile Include="navtex\msg\Navtex_Message.cs" />
    <Compile Include="navtex\msg\Nav_Msg_Form.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="navtex\msg\Nav_Msg_Form.Designer.cs">
      <DependentUpon>Nav_Msg_Form.cs</DependentUpon>
    </Compile>
    <Compile Include="navtex\msg\Nav_Msg_Log.cs" />
    <Compile Include="navtex\msg\Serial_RTS_Send_Mgr.cs" />
    <Compile Include="navtex\Navtex_Instance.cs" />
    <Compile Include="navtex\sentence\Sentence_BAM_Form.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="navtex\sentence\Sentence_BAM_Form.Designer.cs">
      <DependentUpon>Sentence_BAM_Form.cs</DependentUpon>
    </Compile>
    <Compile Include="navtex\sentence\Sentence_Create_Form.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="navtex\sentence\Sentence_Create_Form.Designer.cs">
      <DependentUpon>Sentence_Create_Form.cs</DependentUpon>
    </Compile>
    <Compile Include="navtex\sentence\Sentence_CRQ_Form.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="navtex\sentence\Sentence_CRQ_Form.Designer.cs">
      <DependentUpon>Sentence_CRQ_Form.cs</DependentUpon>
    </Compile>
    <Compile Include="navtex\sentence\Sentence_NRM_Form.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="navtex\sentence\Sentence_NRM_Form.Designer.cs">
      <DependentUpon>Sentence_NRM_Form.cs</DependentUpon>
    </Compile>
    <Compile Include="NmeaGeneratorForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NmeaGeneratorForm.Designer.cs">
      <DependentUpon>NmeaGeneratorForm.cs</DependentUpon>
    </Compile>
    <Compile Include="vhf\ProtocolAnalyzerForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="vhf\ProtocolAnalyzerForm.Designer.cs">
      <DependentUpon>ProtocolAnalyzerForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Utill.cs" />
    <Compile Include="vhf\DataProtocol.cs" />
    <Compile Include="ioPortMonitorForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ioPortMonitorForm.Designer.cs">
      <DependentUpon>ioPortMonitorForm.cs</DependentUpon>
    </Compile>
    <Compile Include="SerialManager.cs" />
    <Compile Include="vhf\DscComposeForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="vhf\DscComposeForm.Designer.cs">
      <DependentUpon>DscComposeForm.cs</DependentUpon>
    </Compile>
    <Compile Include="vhf\DscMessage.cs" />
    <Compile Include="vhf\IV2500A_Form.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="vhf\IV2500A_Form.Designer.cs">
      <DependentUpon>IV2500A_Form.cs</DependentUpon>
    </Compile>
    <Compile Include="MainForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MainForm.Designer.cs">
      <DependentUpon>MainForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="vhf\RemoteControlForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="vhf\RemoteControlForm.Designer.cs">
      <DependentUpon>RemoteControlForm.cs</DependentUpon>
    </Compile>
    <Compile Include="vhf\SerialRmtProtocol.cs" />
    <EmbeddedResource Include="ais\AisVdmDetailForm.resx">
      <DependentUpon>AisVdmDetailForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ais\IS1250A_Form.resx">
      <DependentUpon>IS1250A_Form.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="BerTestForm.resx">
      <DependentUpon>BerTestForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FirmwareUpdateForm.resx">
      <DependentUpon>FirmwareUpdateForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ioPortMonitorForm.resx">
      <DependentUpon>ioPortMonitorForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="navtex\communication\Serial_Setting_Form.resx">
      <DependentUpon>Serial_Setting_Form.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="navtex\IN3000R_Form.resx">
      <DependentUpon>IN3000R_Form.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="navtex\monitor\Interface_Monitor_Form.resx">
      <DependentUpon>Interface_Monitor_Form.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="navtex\monitor\Nmea_Monitor_Form.resx">
      <DependentUpon>Nmea_Monitor_Form.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="navtex\msg\Nav_Msg_Form.resx" />
    <EmbeddedResource Include="navtex\sentence\Sentence_BAM_Form.resx">
      <DependentUpon>Sentence_BAM_Form.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="navtex\sentence\Sentence_Create_Form.resx">
      <DependentUpon>Sentence_Create_Form.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="navtex\sentence\Sentence_CRQ_Form.resx">
      <DependentUpon>Sentence_CRQ_Form.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="navtex\sentence\Sentence_NRM_Form.resx">
      <DependentUpon>Sentence_NRM_Form.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NmeaGeneratorForm.resx">
      <DependentUpon>NmeaGeneratorForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="vhf\ProtocolAnalyzerForm.resx">
      <DependentUpon>ProtocolAnalyzerForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="vhf\DscComposeForm.resx">
      <DependentUpon>DscComposeForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="vhf\IV2500A_Form.resx">
      <DependentUpon>IV2500A_Form.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="MainForm.resx">
      <DependentUpon>MainForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <EmbeddedResource Include="vhf\RemoteControlForm.resx">
      <DependentUpon>RemoteControlForm.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="navtex\setting\" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.7.2">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4.7.2%28x86 및 x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Connected Services\" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>