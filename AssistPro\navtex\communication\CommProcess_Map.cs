﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AssistPro.navtex;

namespace AssistPro.navtex.communication
{
    public partial class CommProcess_Map
    {
        static readonly CommProcess_Map instance = new CommProcess_Map();

        public static CommProcess_Map Instance
        {
            get
            {
                return instance;
            }
        }

        public enum DebugCmd_t
        {
            RX_TYPE_MSG_LOG = 0x00,
            RX_TYPE_CTRL_REQ,
            RX_TYPE_CTRL_RESP,

            RX_TYPE_MAX,
        }

        public class Packet_c
        {
            public int RxCnt = 0;
            public int TxCnt = 0;
            public byte[] TxBuffer = new byte[50000];
            public byte[] RxBuffer = new byte[50000];
            public int len = 0;
            public int sn = 0;
            public int cmd = 0;
            public int param = 0;
        }
        public Packet_c Debug = new Packet_c();
        public Packet_c INS = new Packet_c();
        public Packet_c BAM = new Packet_c();
        public Packet_c Mon_1 = new Packet_c();
        public Packet_c Mon_2 = new Packet_c();
        public Packet_c Mon_3 = new Packet_c();
    }
}
