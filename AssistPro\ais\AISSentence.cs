using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Runtime.Remoting.Channels;
using System.Runtime.Remoting.Messaging;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace AssistPro.ais
{

    internal enum TM_MODE
    {
        // Get Tx Params
        TM_GET_TX_PARAM = 0x001,
        TM_GET_TX_POWER,
        TM_GET_TX_FREQ_OFFSET,
        TM_GET_TX_DC_OFFSET,
        TM_GET_VSWRLIMIT,
        // Get Rx Params
        TM_GET_RX1,
        TM_GET_RX2,
        TM_GET_RX3,
        // Set Tx Params
        TM_SET_TX_PARAM = 0x011,
        TM_SET_TX_POWER,
        TM_SET_TX_FREQ_OFFSET,
        TM_SET_TX_DC_OFFSET,
        TM_SET_VSWRLIMIT,
        // Set RX Params
        TM_SET_RX1,
        TM_SET_RX2,
        TM_SET_RX3,

        TM_RES_DSC_RX_COUNT = 0x021,

        TM_TX_AISMSG_DATA = 0x0F1,

        TM_MODE_START = 0xC01,
        TM_MODE_STOP  = 0xC02,
    }

    internal enum SENTENCE_TYPE
    {
        ST_AIQ,
        ST_SSD,
        ST_VSD,
        ST_SPW,
        ST_EPV,

        ST_INL,     // Intellian Sentence
        ST_106,     // MMSI
        ST_107,     // IMO
    }

    internal enum PW_LEVEL
    {
        MASTER = 0,
        ENGINEER = 1,
        USER = 2,
        NONE = 3
    }


    internal class AISSentence
    {
        private const string MASTERP_PW = "DZjKTEL16wb?RXIN";
        private const string ENGINEER_PW = "c9yRgKdpuYJeIn13";

        public class TxParamEventArgs : EventArgs
        {
            public TM_MODE Cmd { get; set; }
            public int Param1 { get; set; }
            public int Param2 { get; set; }
        }

        public class SentenceParamEventArgs : EventArgs
        {
            public SENTENCE_TYPE Cmd { get; set; }
            public string Param1 { get; set; }
            public int Param2 { get; set; }
        }

        private readonly Func<int, bool> _msgTypeFilter;
        private readonly Func<string, bool> _mmsiFilter;
        private readonly Action<string> _sentenceSendAction;
        private readonly Action<TxParamEventArgs> _txParamCallback;
        private readonly Action<SentenceParamEventArgs> _sentenceParamCallback;

        public StringBuilder m_messageSizeBuffer = new StringBuilder();
        public AisOwnShip m_OwnShipInfo = new AisOwnShip();
        public int m_Msg_ID = 0;
        public int m_Msg_Cnt = 0;
        public string m_User_ID = null;
        public string m_sChnumber = "0";
        public string m_Msg6bitData = null;
        public string m_MsgchNum = null;
        public DataGridView m_dgv_vdmlist;

        //Msg1,2,3
        public int m_Repeat_Ind = 0;
        public int m_Nav_Status = 0;
        public int m_ROT_AIS = 0;
        public int m_SOG = 0;
        public int m_Pos_Accuracy = 0;
        public string m_Longitude = "0";
        public string m_Latitude = "0";
        public int m_COG = 0;
        public int m_True_Heading = 0;
        public int m_Time_Stamp = 0;
        public int m_Manoeuvre_Ind = 0;
        public int m_Spare3 = 0;
        public int m_Raim_Flag = 0;
        public int m_Sync_State = 0;
        public int m_Slot_Timeout = 0;
        public int m_SubMsg_Offset = 0;
        public int m_SubMsg_UTC = 0;
        public int m_SubMsg_Slot = 0;
        public int m_SubMsg_Receiv = 0;
        public int m_ITDMA_Increm = 0;
        public int m_ITDMA_Slots = 0;
        public int m_Keep_Flag = 0;

        //Msg4
        public int m_UTC_Year = 0;
        public int m_UTC_Month = 0;
        public int m_UTC_Day = 0;
        public int m_UTC_Hour = 0;
        public int m_UTC_Minute = 0;
        public int m_UTC_Second = 0;
        public int m_EPFS_Type = 0;
        public int m_TXCONTROL_BS_LR = 0;
        public int m_Spare9 = 0;

        //Msg5
        public int m_Ais_Version = 0;
        public int m_Imo_Number = 0;
        public string m_Call_Sign = "";
        public string m_Name = "";
        public int m_Ship_Type = 0;
        public int m_Dimemsion_A = 0;
        public int m_Dimemsion_B = 0;
        public int m_Dimemsion_C = 0;
        public int m_Dimemsion_D = 0;
        public int m_ETA_Month = 0;
        public int m_ETA_Day = 0;
        public int m_ETA_Hour = 0;
        public int m_ETA_Minute = 0;
        public int m_Draught = 0;
        public string m_Destination = "";
        public int m_DTE = 0;
        public int m_Spare1 = 0;

        //Msg6
        public int m_Sequence_NR = 0;
        public int m_Destination_ID = 0;
        public int m_Retrans_Flag = 0;
        public int m_Binary_Data = 0;

        //Msg7
        public int m_Spare2 = 0;
        public int m_Destination_ID2 = 0;
        public int m_Sequence_NR2 = 0;

        //Msg9
        public int m_Altitude = 0;
        public int m_SOG_Air = 0;
        public int m_Altitude_Sensor = 0;
        public int m_Spare7 = 0;
        public int m_Mode_Flag = 0;
        public int m_State_Select = 0;

        //Msg12
        public string m_Safety_Text = "";

        //Msg15
        public int m_Slot_Offset = 0;

        //Msg16
        public int m_Assig_Slot = 0;
        public int m_Assig_Increm = 0;
        public int m_Spare4 = 0;

        //Msg17
        public int m_Spare5 = 0;

        //Msg18
        public int m_Spare8 = 0;
        public int m_Reserved_Loc = 0;
        public int m_Reserved_Reg2 = 0;
        public int m_CLASS_B_Unit = 0;
        public int m_CLASS_B_Display = 0;
        public int m_CLASS_B_Dsp = 0;
        public int m_CLASS_B_Band = 0;
        public int m_CLASS_B_Msg22 = 0;

        //Msg19
        public int m_Reserved_Reg = 0;
        public int m_Reserved_Reg8 = 0;
        public int m_AS_ModeFlag = 0;

        //Msg20
        public int m_Offset_Number = 0;
        public int m_Number_Slots = 0;
        public int m_Base_TimeOut = 0;
        public int m_Base_Increm = 0;

        //Msg21
        public int m_ATN_Type = 0;
        public int m_ATN_Status = 0;
        public int m_Off_Position = 0;
        public int m_Virtual_ATN = 0;

        //Msg22
        public int m_Channel_A = 0;
        public int m_Channel_B = 0;
        public int m_Mode = 0;
        public int m_Power = 0;
        public string m_Longitude2 = "";
        public string m_Latitude2 = "";
        public int m_Addressed_H = 0;
        public int m_Address_Ind = 0;
        public int m_Bandwidth_A = 0;
        public int m_Bandwidth_B = 0;
        public int m_Zone_Size = 0;
        public int m_Spare23 = 0;

        //Msg23
        public int m_Station_Type = 0;
        public int m_Spare22 = 0;
        public int m_Mode_23 = 0;
        public int m_Report_Interval = 0;
        public int m_Quiet_Time = 0;
        public int m_Spare6 = 0;

        //Msg24
        public int m_Part_Msg24 = 0;
        public string m_Vendor_ID = "";

        //Msg27
        public int m_SOG_LR = 0;
        public int m_COG_LR = 0;
        public int m_GNSS_Status_LR = 0;

        public AISSentence(DataGridView dgv, 
            Func<int, bool> msgTypeFilter, 
            Func<string, bool> mmsiFilter, 
            Action<string> sentenceSendAction = null,
            Action<TxParamEventArgs> txParamCallback = null,
            Action<SentenceParamEventArgs> sentenceParamCallback = null
            )
        {
            m_dgv_vdmlist = dgv;
            _msgTypeFilter = msgTypeFilter;
            _mmsiFilter = mmsiFilter;
            _sentenceSendAction = sentenceSendAction;
            _txParamCallback = txParamCallback;
            _sentenceParamCallback = sentenceParamCallback;
        }

        public AISSentence(string binaryMessage)
        {
            MessageParser(binaryMessage);
        }

        public void ClearMsgCount()
        {
            m_Msg_Cnt = 0;
        }

        public static string AddSentenceTail(string sentence)
        {
            if (string.IsNullOrEmpty(sentence))
                return sentence;

            // '$' 또는 '!'로 시작하는 경우, 첫 글자 제외하고 체크섬 계산
            int startIdx = (sentence[0] == '$' || sentence[0] == '!' || sentence[0] == '#') ? 1 : 0;
            int len = sentence.Length - startIdx;

            byte checksum = 0;
            for (int i = 0; i < len; i++)
                checksum ^= (byte)sentence[startIdx + i];

            // 이미 '*'가 포함되어 있으면 중복 추가 방지
            int starIdx = sentence.IndexOf('*');
            string baseSentence = (starIdx >= 0) ? sentence.Substring(0, starIdx) : sentence;

            return $"{baseSentence}*{checksum:X2}\r\n";
        }

        public void SendRfTestEnable(int nMode)
        {
            string sentence = "";

            if (nMode == 0)         // RF Test Mode Off
            {
                sentence = "$AIINL,DIG,RFT,0";
                sentence = AddSentenceTail(sentence);
            }
            else if (nMode == 1)    // RF Tx Mode
            {
                sentence = "$AIINL,DIG,RFT,1";
                sentence = AddSentenceTail(sentence);
            }
            else if (nMode == 2)    // RF Rx Mode
            {
                sentence = "$AIINL,DIG,RFT,2";
                sentence = AddSentenceTail(sentence);
            }

            if (!string.IsNullOrEmpty(sentence) && _sentenceSendAction != null)
            {
                _sentenceSendAction(sentence);
            }
        }

        public void SendTxParam(TM_MODE cmd, int param1 = 0, int param2 = 0)
        {
            string sentence = "";

            if (cmd == TM_MODE.TM_SET_TX_PARAM || cmd == TM_MODE.TM_SET_TX_DC_OFFSET)
            {
                sentence = string.Format("#AIINL,RFT,{0:X3},{1:X4},{2:X4}", (int)cmd, param1, param2);
            }
            else if ((cmd >= TM_MODE.TM_GET_TX_PARAM && cmd <= TM_MODE.TM_GET_VSWRLIMIT) || cmd == TM_MODE.TM_MODE_STOP)
            {
                sentence = string.Format("#AIINL,RFT,{0:X3}", (int)cmd);
            }
            else
            {
                sentence = string.Format("#AIINL,RFT,{0:X3},{1:X4}", (int)cmd, param1);
            }

            sentence = AddSentenceTail(sentence);

            if (!string.IsNullOrEmpty(sentence) && _sentenceSendAction != null)
            {
                _sentenceSendAction(sentence);
            }
        }

        public void SendAISSentence(SENTENCE_TYPE Cmd, SENTENCE_TYPE subCmd=0, SENTENCE_TYPE subCmd2=0, int param1 = 0, int param2 = 0)
        {
            string sentence = "";

            if (Cmd == SENTENCE_TYPE.ST_AIQ)
            {
                if (subCmd == SENTENCE_TYPE.ST_SSD)
                {
                    sentence = "$AIAIQ,SSD";
                }
                else if (subCmd == SENTENCE_TYPE.ST_VSD)
                {
                    sentence = "$AIAIQ,VSD";
                }
            }
            else if (Cmd == SENTENCE_TYPE.ST_SPW)
            {
                if (subCmd == SENTENCE_TYPE.ST_EPV)
                {
                    sentence = string.Format("$AISPW,EPV,{0:D9},{1:D},{2}", param1, param2, MASTERP_PW);
                }
                else if (subCmd == SENTENCE_TYPE.ST_SSD)
                {
                    sentence = string.Format("$AISPW,SSD,{0:D9},{1:D},{2}", param1, param2, MASTERP_PW);
                }
            }
            else if (Cmd == SENTENCE_TYPE.ST_EPV)
            {
                if (subCmd == SENTENCE_TYPE.ST_106)
                {
                    sentence = string.Format("$AIEPV,C,AI,{0:D9},106,{1:D9}", m_OwnShipInfo.GetMMSI(), param1);
                }
                else if (subCmd == SENTENCE_TYPE.ST_107)
                {
                    sentence = string.Format("$AIEPV,C,AI,{0:D9},107,{1:D7}", m_OwnShipInfo.GetMMSI(), param1);
                }
            }
            else if (Cmd == SENTENCE_TYPE.ST_SSD)
            {
                if (param1 == 0)    // Internal Antenna Position
                {
                    sentence = string.Format("$AISSD,{0},{1},{2},{3},{4},{5},0,AI",
                        m_OwnShipInfo.GetCallSign(), m_OwnShipInfo.GetShipName(), 
                        m_OwnShipInfo.GetIntAntPosA(), m_OwnShipInfo.GetIntAntPosB(), 
                        m_OwnShipInfo.GetIntAntPosC(), m_OwnShipInfo.GetIntAntPosD());
                }
                else
                {
                    sentence = string.Format("$AISSD,{0},{1},{2},{3},{4},{5},0,--",
                        m_OwnShipInfo.GetCallSign(), m_OwnShipInfo.GetShipName(),
                        m_OwnShipInfo.GetExtAntPosA(), m_OwnShipInfo.GetExtAntPosB(),
                        m_OwnShipInfo.GetExtAntPosC(), m_OwnShipInfo.GetExtAntPosD());
                }
            }
            else if (Cmd == SENTENCE_TYPE.ST_VSD)
            {
                sentence = string.Format("$AIVSD,{0:D3},{1:D2}.{2:D1},{3:D4},{4},{5:D2}{6:D2}00,{7:D2},{8:D2},{9:D2},00",
                    m_OwnShipInfo.GetShipCargoType(), (int)(m_OwnShipInfo.GetDraught()), 
                    (int)((m_OwnShipInfo.GetDraught() * 10) %10), m_OwnShipInfo.GetPersonOnBoard(),
                    m_OwnShipInfo.GetDestination(), m_OwnShipInfo.GetEstHour(), m_OwnShipInfo.GetEstMinute(),
                    m_OwnShipInfo.GetEstDay(), m_OwnShipInfo.GetEstMonth(), m_OwnShipInfo.GetNavStatus());
            }
            else if (Cmd == SENTENCE_TYPE.ST_INL)
            {
                if (subCmd == SENTENCE_TYPE.ST_AIQ)
                {
                    if (subCmd2 == SENTENCE_TYPE.ST_106)
                    {
                        sentence = "$AIINL,AIQ,106";
                    }
                    else if (subCmd2 == SENTENCE_TYPE.ST_107)
                    {
                        sentence = "$AIINL,AIQ,107";
                    }
                }
            }

            sentence = AddSentenceTail(sentence);

            if (!string.IsNullOrEmpty(sentence) && _sentenceSendAction != null)
            {
                _sentenceSendAction(sentence);
            }
        }

        public string Get6bitAscii(string Smessage)
        {   // String -> 6bit Binary
            if (string.IsNullOrEmpty(Smessage))
                return string.Empty;

            string fullmsg = "";
            for (int i = 0; i < Smessage.Length; i++)
            {
                char temp = Smessage[i];
                int ibinary = temp;
                if (ibinary < 96)
                {
                    ibinary = ibinary - 48;
                }
                else
                {
                    ibinary = ibinary - 56;
                }

                string sbinary = Convert.ToString(ibinary, 2); // 2진수 문자열로 변환
                while (sbinary.Length < 6)
                {
                    sbinary = "0" + sbinary;
                }
                fullmsg += sbinary;
            }
            return fullmsg;
        }

        public bool MessageParser(string msg6bit)
        {
            try
            {
                // Message id
                m_Msg_ID = Convert.ToInt32(msg6bit.Substring(0, 6), 2);

                m_Repeat_Ind = Convert.ToInt32(msg6bit.Substring(6, 2), 2);

                // MMSI 추출 및 9자리로 포맷
                m_User_ID = Convert.ToInt64(msg6bit.Substring(8, 30), 2).ToString();
                while (m_User_ID.Length < 9)
                {
                    m_User_ID = "0" + m_User_ID;
                }

                switch (m_Msg_ID)
                {
                    case 1:
                    case 2:
                    case 3:
                        // Position Report
                        m_Nav_Status = Convert.ToInt32(msg6bit.Substring(38, 4), 2);
                        m_ROT_AIS = Convert.ToInt32(msg6bit.Substring(42, 8), 2);
                        m_SOG = Convert.ToInt32(msg6bit.Substring(50, 10), 2);
                        m_Pos_Accuracy = Convert.ToInt32(msg6bit.Substring(60, 1), 2);
                        m_Longitude = Convert.ToInt64(msg6bit.Substring(61, 28), 2).ToString();
                        m_Latitude = Convert.ToInt64(msg6bit.Substring(89, 27), 2).ToString();
                        m_COG = Convert.ToInt32(msg6bit.Substring(116, 12), 2);
                        m_True_Heading = Convert.ToInt32(msg6bit.Substring(128, 9), 2);
                        m_Time_Stamp = Convert.ToInt32(msg6bit.Substring(137, 6), 2);
                        m_Manoeuvre_Ind = Convert.ToInt32(msg6bit.Substring(143, 2), 2);
                        m_Spare3 = Convert.ToInt32(msg6bit.Substring(145, 3), 2);
                        m_Raim_Flag = Convert.ToInt32(msg6bit.Substring(148, 1), 2);
                        m_Sync_State = Convert.ToInt32(msg6bit.Substring(149, 2), 2);

                        if (m_Msg_ID != 3)
                        {
                            m_Slot_Timeout = Convert.ToInt32(msg6bit.Substring(151, 3), 2);
                            if (m_Slot_Timeout == 0)
                            {
                                m_SubMsg_Offset = Convert.ToInt32(msg6bit.Substring(154, 14), 2);
                            }
                            else if (m_Slot_Timeout == 1)
                            {
                                m_SubMsg_UTC = Convert.ToInt32(msg6bit.Substring(154, 14), 2);
                            }
                            else if (m_Slot_Timeout == 2 || m_Slot_Timeout == 4 || m_Slot_Timeout == 6)
                            {
                                m_SubMsg_Slot = Convert.ToInt32(msg6bit.Substring(154, 14), 2);
                            }
                            else
                            {
                                m_SubMsg_Receiv = Convert.ToInt32(msg6bit.Substring(154, 14), 2);
                            }
                        }
                        else
                        {
                            m_ITDMA_Increm = Convert.ToInt32(msg6bit.Substring(151, 13), 2);
                            m_ITDMA_Slots = Convert.ToInt32(msg6bit.Substring(164, 3), 2);
                            m_Keep_Flag = Convert.ToInt32(msg6bit.Substring(167, 1), 2);
                        }
                        break;
                    case 4:
                    case 11:
                        m_UTC_Year = Convert.ToInt32(msg6bit.Substring(38, 14), 2);
                        m_UTC_Month = Convert.ToInt32(msg6bit.Substring(52, 4), 2);
                        m_UTC_Day = Convert.ToInt32(msg6bit.Substring(56, 5), 2);
                        m_UTC_Hour = Convert.ToInt32(msg6bit.Substring(61, 5), 2);
                        m_UTC_Minute = Convert.ToInt32(msg6bit.Substring(66, 6), 2);
                        m_UTC_Second = Convert.ToInt32(msg6bit.Substring(72, 6), 2);
                        m_Pos_Accuracy = Convert.ToInt32(msg6bit.Substring(78, 1), 2);
                        m_Longitude = Convert.ToInt64(msg6bit.Substring(79, 28), 2).ToString();
                        m_Latitude = Convert.ToInt64(msg6bit.Substring(107, 27), 2).ToString();
                        m_EPFS_Type = Convert.ToInt32(msg6bit.Substring(134, 4), 2);
                        m_TXCONTROL_BS_LR = Convert.ToInt32(msg6bit.Substring(138, 1), 2);
                        m_Spare9 = Convert.ToInt32(msg6bit.Substring(139, 9), 2);
                        m_Raim_Flag = Convert.ToInt32(msg6bit.Substring(148, 1), 2);
                        m_Sync_State = Convert.ToInt32(msg6bit.Substring(149, 2), 2);
                        m_Slot_Timeout = Convert.ToInt32(msg6bit.Substring(151, 3), 2);

                        if (m_Slot_Timeout == 0)
                        {
                            m_SubMsg_Offset = Convert.ToInt32(msg6bit.Substring(154, 14), 2);
                        }
                        else if (m_Slot_Timeout == 1)
                        {
                            m_SubMsg_UTC = Convert.ToInt32(msg6bit.Substring(154, 14), 2);
                        }
                        else if (m_Slot_Timeout == 2 || m_Slot_Timeout == 4 || m_Slot_Timeout == 6)
                        {
                            m_SubMsg_Slot = Convert.ToInt32(msg6bit.Substring(154, 14), 2);
                        }
                        else
                        {
                            m_SubMsg_Receiv = Convert.ToInt32(msg6bit.Substring(154, 14), 2);
                        }
                        break;
                    case 5:
                        m_Ais_Version = Convert.ToInt32(msg6bit.Substring(38, 4), 2);
                        m_Imo_Number = Convert.ToInt32(msg6bit.Substring(42, 30), 2);
                        m_Call_Sign = Convert.ToString(Convert.ToInt64(msg6bit.Substring(72, 42), 2)).PadLeft(7, '0');
                        m_Name = Convert.ToString(Convert.ToInt64(msg6bit.Substring(114, 120), 2));
                        m_Ship_Type = Convert.ToInt32(msg6bit.Substring(234, 8), 2);
                        m_Dimemsion_A = Convert.ToInt32(msg6bit.Substring(242, 9), 2);
                        m_Dimemsion_B = Convert.ToInt32(msg6bit.Substring(251, 9), 2);
                        m_Dimemsion_C = Convert.ToInt32(msg6bit.Substring(260, 6), 2);
                        m_Dimemsion_D = Convert.ToInt32(msg6bit.Substring(266, 6), 2);
                        m_ETA_Month = Convert.ToInt32(msg6bit.Substring(272, 4), 2);
                        m_ETA_Day = Convert.ToInt32(msg6bit.Substring(276, 5), 2);
                        m_ETA_Hour = Convert.ToInt32(msg6bit.Substring(281, 5), 2);
                        m_ETA_Minute = Convert.ToInt32(msg6bit.Substring(286, 6), 2);
                        m_Draught = Convert.ToInt32(msg6bit.Substring(292, 8), 2);
                        m_Destination = Convert.ToString(Convert.ToInt64(msg6bit.Substring(300, 120), 2));
                        m_DTE = Convert.ToInt32(msg6bit.Substring(420, 1), 2);
                        m_Spare1 = Convert.ToInt32(msg6bit.Substring(421, 1), 2);
                        break;
                    case 6:
                        m_Sequence_NR = Convert.ToInt32(msg6bit.Substring(38, 2), 2);
                        m_Destination_ID = Convert.ToInt32(msg6bit.Substring(40, 30), 2);
                        m_Retrans_Flag = Convert.ToInt32(msg6bit.Substring(70, 1), 2);
                        m_Spare1 = Convert.ToInt32(msg6bit.Substring(71, 1), 2);
                        //m_Binary_Data = Convert.ToInt32(msg6bit.Substring(72, 64), 2);
                        break;
                    case 7:
                    case 13:
                        m_Spare2 = Convert.ToInt32(msg6bit.Substring(38, 2), 2);
                        m_Destination_ID = Convert.ToInt32(msg6bit.Substring(40, 30), 2);
                        m_Sequence_NR = Convert.ToInt32(msg6bit.Substring(70, 2), 2);
                        break;
                    case 8:
                        m_Spare2 = Convert.ToInt32(msg6bit.Substring(38, 2), 2);
                        //m_Binary_Data = Convert.ToInt32(msg6bit.Substring(40, 64), 2);
                        break;
                    case 9:
                        m_Altitude = Convert.ToInt32(msg6bit.Substring(38, 12), 2);
                        m_SOG_Air = Convert.ToInt32(msg6bit.Substring(50, 10), 2);
                        m_Pos_Accuracy = Convert.ToInt32(msg6bit.Substring(60, 1), 2);
                        m_Longitude = Convert.ToInt64(msg6bit.Substring(61, 28), 2).ToString();
                        m_Latitude = Convert.ToInt64(msg6bit.Substring(89, 27), 2).ToString();
                        m_COG = Convert.ToInt32(msg6bit.Substring(116, 12), 2);
                        m_Time_Stamp = Convert.ToInt32(msg6bit.Substring(128, 6), 2);
                        m_Altitude_Sensor = Convert.ToInt32(msg6bit.Substring(134, 1), 2);
                        m_Spare7 = Convert.ToInt32(msg6bit.Substring(135, 7), 2);
                        m_DTE = Convert.ToInt32(msg6bit.Substring(142, 1), 2);
                        m_Spare3 = Convert.ToInt32(msg6bit.Substring(143, 3), 2);
                        m_Mode_Flag = Convert.ToInt32(msg6bit.Substring(146, 1), 2);
                        m_Raim_Flag = Convert.ToInt32(msg6bit.Substring(147, 1), 2);
                        m_State_Select = Convert.ToInt32(msg6bit.Substring(148, 1), 2);
                        m_Sync_State = Convert.ToInt32(msg6bit.Substring(149, 2), 2);

                        if (m_State_Select == 0)
                        {
                            m_Slot_Timeout = Convert.ToInt32(msg6bit.Substring(151, 3), 2);
                            if (m_Slot_Timeout == 0)
                            {
                                m_SubMsg_Offset = Convert.ToInt32(msg6bit.Substring(154, 14), 2);
                            }
                            else if (m_Slot_Timeout == 1)
                            {
                                m_SubMsg_UTC = Convert.ToInt32(msg6bit.Substring(154, 14), 2);
                            }
                            else if (m_Slot_Timeout == 2 || m_Slot_Timeout == 4 || m_Slot_Timeout == 6)
                            {
                                m_SubMsg_Slot = Convert.ToInt32(msg6bit.Substring(154, 14), 2);
                            }
                            else
                            {
                                m_SubMsg_Receiv = Convert.ToInt32(msg6bit.Substring(154, 14), 2);
                            }
                        }
                        else
                        {
                            m_ITDMA_Increm = Convert.ToInt32(msg6bit.Substring(151, 13), 2);
                            m_ITDMA_Slots = Convert.ToInt32(msg6bit.Substring(164, 3), 2);
                            m_Keep_Flag = Convert.ToInt32(msg6bit.Substring(167, 1), 2);
                        }
                        break;
                    case 10:
                        m_Spare2 = Convert.ToInt32(msg6bit.Substring(38, 2), 2);
                        m_Destination_ID = Convert.ToInt32(msg6bit.Substring(40, 30), 2);
                        break;
                    case 12:
                        m_Sequence_NR = Convert.ToInt32(msg6bit.Substring(38, 2), 2);
                        m_Destination_ID = Convert.ToInt32(msg6bit.Substring(40, 30), 2);
                        m_Retrans_Flag = Convert.ToInt32(msg6bit.Substring(70, 1), 2);
                        m_Spare1 = Convert.ToInt32(msg6bit.Substring(71, 1), 2);
                        m_Safety_Text = Convert.ToString(Convert.ToInt64(msg6bit.Substring(72), 2));
                        break;
                    case 14:
                        m_Spare2 = Convert.ToInt32(msg6bit.Substring(38, 2), 2);
                        m_Safety_Text = Convert.ToString(Convert.ToInt64(msg6bit.Substring(40), 2));
                        break;
                    case 15:
                        m_Spare2 = Convert.ToInt32(msg6bit.Substring(38, 2), 2);
                        m_Destination_ID = Convert.ToInt32(msg6bit.Substring(40, 30), 2);
                        //m_Msg_ID = Convert.ToInt32(msg6bit.Substring(70, 6), 2);
                        m_Slot_Offset = Convert.ToInt32(msg6bit.Substring(76, 3), 2);
                        // To be implemented
                        break;
                    case 16:
                        m_Spare2 = Convert.ToInt32(msg6bit.Substring(38, 2), 2);
                        m_Destination_ID = Convert.ToInt32(msg6bit.Substring(40, 30), 2);
                        m_Assig_Slot = Convert.ToInt32(msg6bit.Substring(70, 12), 2);
                        m_Assig_Increm = Convert.ToInt32(msg6bit.Substring(82, 10), 2);
                        // To be implemented
                        break;
                    case 17:
                        m_Spare2 = Convert.ToInt32(msg6bit.Substring(38, 2), 2);
                        m_Longitude = Convert.ToInt64(msg6bit.Substring(40, 28), 2).ToString();
                        m_Latitude = Convert.ToInt64(msg6bit.Substring(68, 27), 2).ToString();
                        m_Spare5 = Convert.ToInt32(msg6bit.Substring(95, 5), 2);
                        // To be implemented
                        break;
                    case 18:
                        m_Reserved_Loc = Convert.ToInt32(msg6bit.Substring(38, 8), 2);
                        m_SOG = Convert.ToInt32(msg6bit.Substring(46, 10), 2);
                        m_Pos_Accuracy = Convert.ToInt32(msg6bit.Substring(56, 1), 2);
                        m_Longitude = Convert.ToInt64(msg6bit.Substring(57, 28), 2).ToString();
                        m_Latitude = Convert.ToInt64(msg6bit.Substring(85, 27), 2).ToString();
                        m_COG = Convert.ToInt32(msg6bit.Substring(112, 12), 2);
                        m_True_Heading = Convert.ToInt32(msg6bit.Substring(124, 9), 2);
                        m_Time_Stamp = Convert.ToInt32(msg6bit.Substring(133, 6), 2);
                        m_Spare2 = Convert.ToInt32(msg6bit.Substring(139, 2), 2);
                        m_CLASS_B_Unit = Convert.ToInt32(msg6bit.Substring(141, 1), 2);
                        m_CLASS_B_Display = Convert.ToInt32(msg6bit.Substring(142, 1), 2);
                        m_CLASS_B_Dsp = Convert.ToInt32(msg6bit.Substring(143, 1), 2);
                        m_CLASS_B_Band = Convert.ToInt32(msg6bit.Substring(144, 1), 2);
                        m_CLASS_B_Msg22 = Convert.ToInt32(msg6bit.Substring(145, 1), 2);
                        m_Mode_Flag = Convert.ToInt32(msg6bit.Substring(146, 1), 2);
                        m_Raim_Flag = Convert.ToInt32(msg6bit.Substring(147, 1), 2);
                        m_State_Select = Convert.ToInt32(msg6bit.Substring(148, 1), 2);
                        m_Sync_State = Convert.ToInt32(msg6bit.Substring(149, 2), 2);
                        if (m_State_Select == 0)
                        {
                            m_Slot_Timeout = Convert.ToInt32(msg6bit.Substring(151, 3), 2);
                            if (m_Slot_Timeout == 0)
                            {
                                m_SubMsg_Offset = Convert.ToInt32(msg6bit.Substring(154, 14), 2);
                            }
                            else if (m_Slot_Timeout == 1)
                            {
                                m_SubMsg_UTC = Convert.ToInt32(msg6bit.Substring(154, 14), 2);
                            }
                            else if (m_Slot_Timeout == 2 || m_Slot_Timeout == 4 || m_Slot_Timeout == 6)
                            {
                                m_SubMsg_Slot = Convert.ToInt32(msg6bit.Substring(154, 14), 2);
                            }
                            else
                            {
                                m_SubMsg_Receiv = Convert.ToInt32(msg6bit.Substring(154, 14), 2);
                            }
                        }
                        else
                        {
                            m_ITDMA_Increm = Convert.ToInt32(msg6bit.Substring(151, 13), 2);
                            m_ITDMA_Slots = Convert.ToInt32(msg6bit.Substring(164, 3), 2);
                            m_Keep_Flag = Convert.ToInt32(msg6bit.Substring(167, 1), 2);
                        }
                        break;
                    case 19:
                        m_Reserved_Reg8 = Convert.ToInt32(msg6bit.Substring(38, 8), 2);
                        m_SOG = Convert.ToInt32(msg6bit.Substring(46, 10), 2);
                        m_Pos_Accuracy = Convert.ToInt32(msg6bit.Substring(56, 1), 2);
                        m_Longitude = Convert.ToInt64(msg6bit.Substring(57, 28), 2).ToString();
                        m_Latitude = Convert.ToInt64(msg6bit.Substring(85, 27), 2).ToString();
                        m_COG = Convert.ToInt32(msg6bit.Substring(112, 12), 2);
                        m_True_Heading = Convert.ToInt32(msg6bit.Substring(124, 9), 2);
                        m_Time_Stamp = Convert.ToInt32(msg6bit.Substring(133, 6), 2);
                        m_Reserved_Reg = Convert.ToInt32(msg6bit.Substring(139, 4), 2);
                        m_Name = Convert.ToString(Convert.ToInt64(msg6bit.Substring(143, 120), 2));
                        m_Ship_Type = Convert.ToInt32(msg6bit.Substring(263, 8), 2);
                        m_Dimemsion_A = Convert.ToInt32(msg6bit.Substring(271, 9), 2);
                        m_Dimemsion_B = Convert.ToInt32(msg6bit.Substring(280, 9), 2);
                        m_Dimemsion_C = Convert.ToInt32(msg6bit.Substring(289, 6), 2);
                        m_Dimemsion_D = Convert.ToInt32(msg6bit.Substring(295, 6), 2);
                        m_EPFS_Type = Convert.ToInt32(msg6bit.Substring(301, 4), 2);
                        m_Raim_Flag = Convert.ToInt32(msg6bit.Substring(305, 1), 2);
                        m_DTE = Convert.ToInt32(msg6bit.Substring(306, 1), 2);
                        m_AS_ModeFlag = Convert.ToInt32(msg6bit.Substring(307, 1), 2);
                        m_Spare4 = Convert.ToInt32(msg6bit.Substring(308, 4), 2);
                        break;
                    case 20:    //Data link management message
                        m_Spare2 = Convert.ToInt32(msg6bit.Substring(38, 2), 2);
                        m_Offset_Number = Convert.ToInt32(msg6bit.Substring(40, 12), 2);
                        m_Number_Slots = Convert.ToInt32(msg6bit.Substring(52, 4), 2);
                        m_Base_TimeOut = Convert.ToInt32(msg6bit.Substring(56, 3), 2);
                        m_Base_Increm = Convert.ToInt32(msg6bit.Substring(59, 11), 2);
                        // To be implemented
                        break;
                    case 21:    //Aids-to-Navigation Report
                        m_ATN_Type = Convert.ToInt32(msg6bit.Substring(38, 5), 2);
                        m_Name = Convert.ToString(Convert.ToInt64(msg6bit.Substring(43, 120), 2));
                        m_Pos_Accuracy = Convert.ToInt32(msg6bit.Substring(163, 1), 2);
                        m_Longitude = Convert.ToInt64(msg6bit.Substring(164, 28), 2).ToString();
                        m_Latitude = Convert.ToInt64(msg6bit.Substring(192, 27), 2).ToString();
                        m_Dimemsion_A = Convert.ToInt32(msg6bit.Substring(219, 9), 2);
                        m_Dimemsion_B = Convert.ToInt32(msg6bit.Substring(228, 9), 2);
                        m_Dimemsion_C = Convert.ToInt32(msg6bit.Substring(237, 6), 2);
                        m_Dimemsion_D = Convert.ToInt32(msg6bit.Substring(243, 6), 2);
                        m_EPFS_Type = Convert.ToInt32(msg6bit.Substring(249, 4), 2);
                        m_Time_Stamp = Convert.ToInt32(msg6bit.Substring(253, 6), 2);
                        m_Off_Position = Convert.ToInt32(msg6bit.Substring(259, 1), 2);
                        m_ATN_Status = Convert.ToInt32(msg6bit.Substring(260, 8), 2);
                        m_Raim_Flag = Convert.ToInt32(msg6bit.Substring(268, 1), 2);
                        m_Virtual_ATN = Convert.ToInt32(msg6bit.Substring(269, 1), 2);
                        m_Mode_Flag = Convert.ToInt32(msg6bit.Substring(270, 1), 2);
                        m_Spare1 = Convert.ToInt32(msg6bit.Substring(271, 1), 2);
                        break;
                    case 22:
                        m_Spare2 = Convert.ToInt32(msg6bit.Substring(38, 2), 2);
                        m_Channel_A = Convert.ToInt32(msg6bit.Substring(40, 12), 2);
                        m_Channel_B = Convert.ToInt32(msg6bit.Substring(52, 12), 2);
                        m_Mode = Convert.ToInt32(msg6bit.Substring(64, 4), 2);
                        m_Power = Convert.ToInt32(msg6bit.Substring(68, 1), 2);
                        m_Address_Ind = Convert.ToInt32(msg6bit.Substring(139, 1), 2);

                        if (m_Address_Ind == 0)
                        {
                            m_Longitude = Convert.ToInt64(msg6bit.Substring(69, 18), 2).ToString();
                            m_Latitude = Convert.ToInt64(msg6bit.Substring(87, 17), 2).ToString();
                            m_Longitude2 = Convert.ToInt64(msg6bit.Substring(104, 18), 2).ToString();
                            m_Latitude2 = Convert.ToInt64(msg6bit.Substring(122, 17), 2).ToString();
                        }
                        else
                        {
                            m_Addressed_H = Convert.ToInt32(msg6bit.Substring(69, 35), 2);
                            m_Addressed_H = Convert.ToInt32(msg6bit.Substring(104, 35), 2);
                        }

                        m_Bandwidth_A = Convert.ToInt32(msg6bit.Substring(140, 1), 2);
                        m_Bandwidth_B = Convert.ToInt32(msg6bit.Substring(141, 1), 2);
                        m_Zone_Size = Convert.ToInt32(msg6bit.Substring(142, 3), 2);
                        m_Spare23 = Convert.ToInt32(msg6bit.Substring(145, 23), 2);
                        break;
                    case 23:
                        m_Spare2 = Convert.ToInt32(msg6bit.Substring(38, 2), 2);
                        m_Longitude = Convert.ToInt64(msg6bit.Substring(40, 18), 2).ToString();
                        m_Latitude = Convert.ToInt64(msg6bit.Substring(58, 17), 2).ToString();
                        m_Longitude2 = Convert.ToInt64(msg6bit.Substring(75, 18), 2).ToString();
                        m_Latitude2 = Convert.ToInt64(msg6bit.Substring(93, 17), 2).ToString();
                        m_Station_Type = Convert.ToInt32(msg6bit.Substring(110, 4), 2);
                        m_Ship_Type = Convert.ToInt32(msg6bit.Substring(114, 8), 2);
                        m_Spare22 = Convert.ToInt32(msg6bit.Substring(122, 22), 2);
                        m_Mode_23 = Convert.ToInt32(msg6bit.Substring(144, 2), 2);
                        m_Report_Interval = Convert.ToInt32(msg6bit.Substring(146, 4), 2);
                        m_Quiet_Time = Convert.ToInt32(msg6bit.Substring(150, 4), 2);
                        m_Spare6 = Convert.ToInt32(msg6bit.Substring(154, 6), 2);
                        break;
                    case 24:    //Static data report for Class B
                        m_Part_Msg24 = Convert.ToInt32(msg6bit.Substring(38, 2), 2);
                        if (m_Part_Msg24 == 0)
                        {
                            m_Name = Convert.ToString(Convert.ToInt64(msg6bit.Substring(40, 120), 2));
                        }
                        else
                        {
                            m_Ship_Type = Convert.ToInt32(msg6bit.Substring(40, 8), 2);
                            m_Vendor_ID = Convert.ToString(Convert.ToInt64(msg6bit.Substring(48, 42), 2));
                            m_Call_Sign = Convert.ToString(Convert.ToInt64(msg6bit.Substring(90, 42), 2)).PadLeft(7, '0');
                            m_Dimemsion_A = Convert.ToInt32(msg6bit.Substring(132, 9), 2);
                            m_Dimemsion_B = Convert.ToInt32(msg6bit.Substring(141, 9), 2);
                            m_Dimemsion_C = Convert.ToInt32(msg6bit.Substring(150, 6), 2);
                            m_Dimemsion_D = Convert.ToInt32(msg6bit.Substring(156, 6), 2);
                            m_EPFS_Type = Convert.ToInt32(msg6bit.Substring(162, 4), 2);
                            m_Spare2 = Convert.ToInt32(msg6bit.Substring(166, 2), 2);
                        }
                        break;
                    case 25:    //Single slot binary message
                    case 26:    //Multiple slot binary message
                        // To be implemented
                        break;
                    case 27:    //Long-range automatic identification system broadcast message
                        m_Pos_Accuracy = Convert.ToInt32(msg6bit.Substring(38, 1), 2);
                        m_Raim_Flag = Convert.ToInt32(msg6bit.Substring(39, 1), 2);
                        m_Nav_Status = Convert.ToInt32(msg6bit.Substring(40, 4), 2);
                        m_Longitude = Convert.ToInt64(msg6bit.Substring(44, 18), 2).ToString();
                        m_Latitude = Convert.ToInt64(msg6bit.Substring(62, 17), 2).ToString();
                        m_SOG_LR = Convert.ToInt32(msg6bit.Substring(79, 6), 2);
                        m_COG_LR = Convert.ToInt32(msg6bit.Substring(85, 9), 2);
                        m_GNSS_Status_LR = Convert.ToInt32(msg6bit.Substring(94, 1), 2);
                        break;
                    default:
                        // 기타 메시지
                        Console.WriteLine("Other Message: " + m_User_ID);
                        break;
                }

                Console.WriteLine("message id: " + m_Msg_ID + " mmsi: " + m_User_ID);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine("datatxtest");
                Console.WriteLine(ex);
            }

            return false;
        }

        public void SentenceProcess(string Sentence, bool vdmAddEnabled)
        {
            if (string.IsNullOrEmpty(Sentence))
                return;

            string orginalSentence = Sentence;

            // 센텐스 마지막에 있는 *와 checksum(2자리)만 제거
            int starIdx = Sentence.LastIndexOf('*');
            if (starIdx >= 0 && Sentence.Length >= starIdx + 3)
            {
                // * 이후 2자리(체크섬)와 그 뒤 개행문자까지 제거
                Sentence = Sentence.Substring(0, starIdx);
            }
            Sentence = Sentence.TrimEnd('\r', '\n');

            if ("$!#%".Contains(Sentence.Substring(0, 1)))
            {
                Console.WriteLine("Processed: " + Sentence);
                if (Sentence.StartsWith("%"))
                {
                    // 필요시 구현
                }
                else
                {
                    if (Sentence.Contains("VDM"))
                    {
                        var messagetemp = new List<string>();
                        string Smessage, sfillbit = null;

                        // ,,를 ,00,으로 치환
                        while (Sentence.Contains(",,"))
                        {
                            int idx = Sentence.IndexOf(",,");
                            Sentence = Sentence.Substring(0, idx) + ",00," + Sentence.Substring(idx + 2);
                        }

                        // 구분자로 분리
                        messagetemp.AddRange(Sentence.Split(','));

                        int msgSize = int.Parse(messagetemp[1].Trim());
                        int msgSizecount = int.Parse(messagetemp[2].Trim());

                        if (msgSize != msgSizecount)
                        {
                            Smessage = messagetemp[5];
                            m_messageSizeBuffer.Append(Get6bitAscii(Smessage));
                            m_sChnumber = messagetemp[4];
                        }
                        else
                        {
                            if (m_sChnumber != "0" && m_messageSizeBuffer.Length != 0)
                            {
                                Smessage = messagetemp[5];
                                m_Msg6bitData = m_messageSizeBuffer.ToString() + Get6bitAscii(Smessage);
                                m_MsgchNum = m_sChnumber;

                                m_sChnumber = "0";
                                m_messageSizeBuffer.Clear();

                                sfillbit = messagetemp[6].Substring(0, 1);
                            }
                            else
                            {
                                Smessage = messagetemp[5];
                                m_Msg6bitData = Get6bitAscii(Smessage);
                                m_MsgchNum = messagetemp[4];

                                sfillbit = messagetemp[6].Substring(0, 1);
                            }
                        }
                        if (m_Msg6bitData != null && m_MsgchNum != null)
                        {
                            if (MessageParser(m_Msg6bitData))
                            {
                                if (_msgTypeFilter != null && !_msgTypeFilter(m_Msg_ID))
                                    return;

                                if (_mmsiFilter != null && !_mmsiFilter(m_User_ID))
                                    return;

                                m_Msg_Cnt++;

                                if (vdmAddEnabled)
                                {
                                    // DataGridView에 데이터 추가
                                    if (m_dgv_vdmlist.InvokeRequired)
                                    {
                                        m_dgv_vdmlist.Invoke(new Action(() =>
                                            m_dgv_vdmlist.Rows.Add(m_Msg_Cnt, m_Msg_ID, m_User_ID, m_MsgchNum, orginalSentence, DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), m_Msg6bitData)
                                        ));
                                    }
                                    else
                                    {
                                        m_dgv_vdmlist.Rows.Add(m_Msg_Cnt, m_Msg_ID, m_User_ID, m_MsgchNum, orginalSentence, DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), m_Msg6bitData);
                                    }
                                }
                            }

                            m_Msg6bitData = null;
                            m_MsgchNum = null;
                        }
                    }
                    else if (Sentence.Contains("INL"))
                    {
                        if (Sentence.Contains("DIG,RFT"))
                        {
                            var parts = Sentence.Split(',');
                            if (parts.Length >= 4)
                            {
                                try
                                {
                                    int value1 = Convert.ToInt32(parts[3], 10);
                                    Console.WriteLine($"DIG,RFT 파싱 결과: {value1}");
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine("RFT 파싱 오류: " + ex.Message);
                                }
                            }
                        }
                        else if (Sentence.Contains("RFT"))
                        {
                            var parts = Sentence.Split(',');
                            if (parts.Length >= 1)
                            {
                                try
                                {
                                    TM_MODE cmd = (TM_MODE)Convert.ToInt32(parts[2], 16);
                                    int param1, param2 = 0;

                                    switch (cmd)
                                    {
                                        case TM_MODE.TM_GET_TX_PARAM:
                                            param1 = Convert.ToInt32(parts[3], 16);
                                            param2 = Convert.ToInt32(parts[4], 16);

                                            var freq = (((double)(param1) * 1000) / 80 * 1000);
                                            var args = new TxParamEventArgs { Cmd = cmd, Param1 = (int)freq, Param2 = param2 };
                                            _txParamCallback?.Invoke(args);
                                            break;
                                        case TM_MODE.TM_GET_TX_POWER:
                                        case TM_MODE.TM_GET_TX_FREQ_OFFSET:
                                        case TM_MODE.TM_GET_VSWRLIMIT:
                                        case TM_MODE.TM_GET_RX1:
                                        case TM_MODE.TM_GET_RX2:
                                        case TM_MODE.TM_GET_RX3:
                                            param1 = Convert.ToInt32(parts[3], 16);
                                            args = new TxParamEventArgs { Cmd = cmd, Param1 = param1 };
                                            _txParamCallback?.Invoke(args);
                                            break;
                                        case TM_MODE.TM_GET_TX_DC_OFFSET:
                                            param1 = Convert.ToInt32(parts[3], 16);
                                            param2 = Convert.ToInt32(parts[4], 16);
                                            args = new TxParamEventArgs { Cmd = cmd, Param1 = param1, Param2 = param2 };
                                            _txParamCallback?.Invoke(args);
                                            break;

                                        case TM_MODE.TM_RES_DSC_RX_COUNT:
                                            break;
                                        default:
                                            Console.WriteLine($"알 수 없는 RFT 명령: {cmd} ({(int)cmd})");
                                            break;
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine("RFT 파싱 오류: " + ex.Message);
                                }
                            }
                            return;
                        }
                        else if (Sentence.Contains("106"))
                        {
                            var parts = Sentence.Split(',');
                            if (parts.Length >= 2)
                            {
                                try
                                {
                                    m_OwnShipInfo.SetMMSI(Convert.ToUInt32(parts[2], 10));
                                    var args = new SentenceParamEventArgs { Cmd = SENTENCE_TYPE.ST_106, Param2 = (int)m_OwnShipInfo.GetMMSI() };
                                    _sentenceParamCallback?.Invoke(args);
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine("106 파싱 오류: " + ex.Message);
                                }
                            }
                        }
                        else if (Sentence.Contains("107"))
                        {
                            var parts = Sentence.Split(',');
                            if (parts.Length >= 2)
                            {
                                try
                                {
                                    m_OwnShipInfo.SetIMO(Convert.ToUInt32(parts[2], 10));
                                    var args = new SentenceParamEventArgs { Cmd = SENTENCE_TYPE.ST_107, Param2 = (int)m_OwnShipInfo.GetIMO() };
                                    _sentenceParamCallback?.Invoke(args);
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine("107 파싱 오류: " + ex.Message);
                                }
                            }
                        }
                    }
                    else if (Sentence.Contains("SSD"))
                    {
                        var parts = Sentence.Split(',');
                        if (parts.Length >= 9)
                        {
                            var args = new SentenceParamEventArgs { Cmd = SENTENCE_TYPE.ST_SSD, Param1 = Sentence };
                            _sentenceParamCallback?.Invoke(args);
                        }
                        return;
                    }
                    else if (Sentence.Contains("VSD"))
                    {
                        var parts = Sentence.Split(',');
                        if (parts.Length >= 10)
                        {
                            var args = new SentenceParamEventArgs { Cmd = SENTENCE_TYPE.ST_VSD, Param1 = Sentence };
                            _sentenceParamCallback?.Invoke(args);
                        }
                        return;
                    }
                }
            }
        }
    }
}
