﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AssistPro.navtex;
using System.Windows.Forms;

namespace AssistPro.navtex.msg
{
    public partial class Nav_Msg_Log
    {
        private string GetPath()
        {
            DateTime now = DateTime.Now;

            string s = Application.StartupPath + "\\Message_Log";
            DirectoryInfo di = new DirectoryInfo(s);
            if (di.Exists == false)
            {
                di.Create();
            }

            s += string.Format("\\{0}-{1}-{2}", now.Year, now.Month, now.Day);
            DirectoryInfo di_2nd = new DirectoryInfo(s);
            if(di_2nd.Exists == false)
            {
                di_2nd.Create();
            }

            return s;
        }

        private struct HeaderInfo
        {
            public int Start;
            public int End;
        }

        private static HeaderInfo SearchHeader(string str, byte[] data)
        {
            int cnt = 0;
            HeaderInfo result;

            result.Start = 0xFFFF;
            result.End = 0xFFFF;

            try
            {
                byte[] strByte = Encoding.UTF8.GetBytes(str);

                for (int i = 0; i < (data.Length - strByte.Length); i++)
                {
                    cnt = 0;
                    for (int n = 0; n < strByte.Length; n++)
                    {
                        if (data[i + n] == strByte[n])
                        {
                            cnt += 1;
                            if (cnt == strByte.Length)
                            {
                                result.Start = i;
                                result.End = i + n;
                                return result;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(System.Reflection.MethodBase.GetCurrentMethod().Name + ' ' + ex.Message);
            }
            return result;
        }

        private static int Navtex_Message_Id_Error_Check_Header_0(string str)
        {
            int ret = -1;
            if (str == string.Empty)
            {
                return ret;
            }

            char[] chars = str.ToCharArray();

            switch (chars[0])
            {
                case 'A': ret = 1; break;
                case 'B': ret = 1; break;
                case 'C': ret = 1; break;
                case 'D': ret = 1; break;
                case 'E': ret = 1; break;
                case 'F': ret = 1; break;
                case 'G': ret = 1; break;
                case 'H': ret = 1; break;
                case 'I': ret = 1; break;
                case 'J': ret = 1; break;
                case 'K': ret = 1; break;
                case 'L': ret = 1; break;
                case 'M': ret = 1; break;
                case 'O': ret = 1; break;
                case 'P': ret = 1; break;
                case 'Q': ret = 1; break;
                case 'R': ret = 1; break;
                case 'S': ret = 1; break;
                case 'T': ret = 1; break;
                case 'U': ret = 1; break;
                case 'V': ret = 1; break;
                case 'W': ret = 1; break;
                case 'X': ret = 1; break;
                case 'Y': ret = 1; break;
                case 'Z': ret = 1; break;
            }
            return ret;
        }

        private static int Navtex_Message_Id_Error_Check_Header_1(string str)
        {
            int ret = -1;
            if (str == string.Empty)
            {
                return ret;
            }

            char[] chars = str.ToCharArray();

            switch (chars[1])
            {
                case 'A': ret = 1; break;
                case 'B': ret = 1; break;
                case 'C': ret = 1; break;
                case 'D': ret = 1; break;
                case 'E': ret = 1; break;
                case 'F': ret = 1; break;
                case 'G': ret = 1; break;
                case 'H': ret = 1; break;
                case 'I': ret = 1; break;
                case 'J': ret = 1; break;
                case 'K': ret = 1; break;
                case 'L': ret = 1; break;
                case 'M': ret = 1; break;
                case 'O': ret = 1; break;
                case 'P': ret = 1; break;
                case 'Q': ret = 1; break;
                case 'R': ret = 1; break;
                case 'S': ret = 1; break;
                case 'T': ret = 1; break;
                case 'U': ret = 1; break;
                case 'V': ret = 1; break;
                case 'W': ret = 1; break;
                case 'X': ret = 1; break;
                case 'Y': ret = 1; break;
                case 'Z': ret = 1; break;
            }
            return ret;
        }

        private static int Navtex_Message_Id_Error_Check_Header_2(string str)
        {
            int ret = -1;
            if (str == string.Empty)
            {
                return ret;
            }

            char[] chars = str.ToCharArray();

            switch (chars[2])
            {
                case '0': ret = 1; break;
                case '1': ret = 1; break;
                case '2': ret = 1; break;
                case '3': ret = 1; break;
                case '4': ret = 1; break;
                case '5': ret = 1; break;
                case '6': ret = 1; break;
                case '7': ret = 1; break;
                case '8': ret = 1; break;
                case '9': ret = 1; break;
            }
            return ret;
        }

        private static int Navtex_Message_Id_Error_Check_Header_3(string str)
        {
            int ret = -1;
            if (str == string.Empty)
            {
                return ret;
            }

            char[] chars = str.ToCharArray();

            switch (chars[3])
            {
                case '0': ret = 1; break;
                case '1': ret = 1; break;
                case '2': ret = 1; break;
                case '3': ret = 1; break;
                case '4': ret = 1; break;
                case '5': ret = 1; break;
                case '6': ret = 1; break;
                case '7': ret = 1; break;
                case '8': ret = 1; break;
                case '9': ret = 1; break;
            }
            return ret;
        }

        public async void Navtex_Message_Data_Write(byte[] data)
        {
            string s = string.Empty;
            string s_2nd = string.Empty;
            string str = string.Empty;
            DateTime now = DateTime.Now;

            try
            {
                s = GetPath();
                s += string.Format("\\{0}.{1}.{2}_Message.txt", now.Hour, now.Minute, now.Second);

                str = Encoding.Default.GetString(data);
                using (StreamWriter sw = new StreamWriter(s))
                {
                    await sw.WriteLineAsync(str);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(System.Reflection.MethodBase.GetCurrentMethod().Name + ' ' + ex.Message);
            }
        }

        public async void Navtex_Message_ProcessData_Write(byte[] data)
        {
            int[] arrRet = new int[4];
            int Freq = 0;
            int TotalLen = 0;
            int ErrorLen = 0;
            float ErrorRate = 0;
            char tmp;
            string s = string.Empty;
            string str = string.Empty;
            string Hstr = string.Empty;
            string Fstr = string.Empty;
            DateTime now = DateTime.Now;
            HeaderInfo H_Info_rawdata;
            HeaderInfo H_Info_rawdx;
            HeaderInfo H_Info_rawrx;
            HeaderInfo H_Info_dx;
            HeaderInfo H_Info_rx;
            HeaderInfo H_Info_fec;
            HeaderInfo H_Info_id;
            HeaderInfo H_Info_msg;
            HeaderInfo H_Info_Freq;
            HeaderInfo H_Info_total;
            HeaderInfo H_Info_error;
            HeaderInfo H_Info_error_rate;
            HeaderInfo H_Info_end;

            H_Info_rawdata = SearchHeader("[RAWDATA]\r\n", data);
            H_Info_rawdx = SearchHeader("[RAWDX]\r\n", data);
            H_Info_rawrx = SearchHeader("[RAWRX]\r\n", data);
            H_Info_dx = SearchHeader("[DX]\r\n", data);
            H_Info_rx = SearchHeader("[RX]\r\n", data);
            H_Info_fec = SearchHeader("[FEC]\r\n", data);
            H_Info_id = SearchHeader("[ID]\r\n", data);
            H_Info_msg = SearchHeader("[DATA]\r\n", data);
            H_Info_end = SearchHeader("[END]", data);
            H_Info_Freq = SearchHeader("[FREQ]\r\n", data);
            H_Info_total = SearchHeader("[TOTAL]\r\n", data);
            H_Info_error = SearchHeader("[ERROR]\r\n", data);
            H_Info_error_rate = SearchHeader("[ERATE]\r\n", data);

            try
            {
                //////
                if (H_Info_rawdata.Start != 0xFFFF && H_Info_rawdata.End != 0xFFFF && H_Info_rawdx.Start != 0xFFFF && H_Info_rawdx.End != 0xFFFF)
                {
                    str += string.Format("[RAWDATA]\r\n");
                    for (int i = H_Info_rawdata.End + 1; i < H_Info_rawdx.Start - 2; i++)
                    {
                        str += string.Format("{0:X2},", data[i]);
                    }
                    str += string.Format("\r\n\r\n");
                }

                //////
                if (H_Info_rawdx.Start != 0xFFFF && H_Info_rawdx.End != 0xFFFF && H_Info_rawrx.Start != 0xFFFF && H_Info_rawrx.End != 0xFFFF)
                {
                    str += string.Format("[RAWDX]\r\n");
                    for (int i = H_Info_rawdx.End + 1; i < H_Info_rawrx.Start - 2; i++)
                    {
                        str += string.Format("{0:X2},", data[i]);
                    }
                    str += string.Format("\r\n\r\n");
                }

                //////
                if (H_Info_rawrx.Start != 0xFFFF && H_Info_rawrx.End != 0xFFFF && H_Info_dx.Start != 0xFFFF && H_Info_dx.End != 0xFFFF)
                {
                    str += string.Format("[RAWRX]\r\n");
                    for (int i = H_Info_rawrx.End + 1; i < H_Info_dx.Start - 2; i++)
                    {
                        str += string.Format("{0:X2},", data[i]);
                    }
                    str += string.Format("\r\n\r\n");
                }

                //////
                if (H_Info_dx.Start != 0xFFFF && H_Info_dx.End != 0xFFFF && H_Info_rx.Start != 0xFFFF && H_Info_rx.End != 0xFFFF)
                {
                    str += string.Format("[DX]\r\n");
                    for (int i = H_Info_dx.End + 1; i < H_Info_rx.Start - 2; i++)
                    {
                        //str += Convert.ToString(data[i]);
                        tmp = Convert.ToChar(data[i]);
                        str += string.Format("{0},", tmp);
                    }
                    str += string.Format("\r\n\r\n");
                }

                //////
                if (H_Info_rx.Start != 0xFFFF && H_Info_rx.End != 0xFFFF && H_Info_fec.Start != 0xFFFF && H_Info_fec.End != 0xFFFF)
                {
                    str += string.Format("[RX]\r\n");
                    for (int i = H_Info_rx.End + 1; i < H_Info_fec.Start - 2; i++)
                    {
                        //str += Convert.ToString(data[i]);
                        tmp = Convert.ToChar(data[i]);
                        str += string.Format("{0},", tmp);
                    }
                    str += string.Format("\r\n\r\n");
                }

                //////
                if (H_Info_fec.Start != 0xFFFF && H_Info_fec.End != 0xFFFF && H_Info_id.Start != 0xFFFF && H_Info_id.End != 0xFFFF)
                {
                    str += string.Format("[FEC]\r\n");
                    for (int i = H_Info_fec.End + 1; i < H_Info_id.Start - 2; i++)
                    {
                        tmp = Convert.ToChar(data[i]);
                        str += string.Format("{0},", tmp);
                    }
                    str += string.Format("\r\n\r\n");
                }

                //////
                if (H_Info_id.Start != 0xFFFF && H_Info_id.End != 0xFFFF && H_Info_msg.Start != 0xFFFF && H_Info_msg.End != 0xFFFF)
                {
                    str += string.Format("[ID]\r\n");
                    for (int i = H_Info_id.End + 1; i < H_Info_msg.Start - 2; i++)
                    {
                        tmp = Convert.ToChar(data[i]);
                        str += string.Format("{0},", tmp);

                        if (tmp == '*')
                        {
                            tmp = 'x';
                        }
                        Hstr += string.Format("{0}", tmp);
                    }
                    str += string.Format("\r\n\r\n");
                }

                //////
                if (H_Info_msg.Start != 0xFFFF && H_Info_msg.End != 0xFFFF && H_Info_end.Start != 0xFFFF && H_Info_end.End != 0xFFFF)
                {
                    str += string.Format("[MSG]\r\n");
                    for (int i = H_Info_msg.End + 1; i < H_Info_Freq.Start - 2; i++)
                    {
                        tmp = Convert.ToChar(data[i]);
                        str += string.Format("{0},", tmp);
                    }
                    str += string.Format("\r\n\r\n");
                }

                if (H_Info_Freq.Start != 0xFFFF && H_Info_Freq.End != 0xFFFF && H_Info_total.Start != 0xFFFF && H_Info_total.End != 0xFFFF)
                {
                    str += string.Format("[FREQ]\r\n");

                    Freq = data[H_Info_Freq.End + 1];
                    Freq += (data[H_Info_Freq.End + 2] << 8);
                    str += string.Format("{0}KHz", Freq);
                    Fstr = string.Format("{0}KHz", Freq);

                    str += string.Format("\r\n\r\n");
                }

                if (H_Info_total.Start != 0xFFFF && H_Info_total.End != 0xFFFF && H_Info_error.Start != 0xFFFF && H_Info_error.End != 0xFFFF)
                {
                    str += string.Format("[Total Character]\r\n");

                    TotalLen = data[H_Info_total.End + 1];
                    TotalLen += (data[H_Info_total.End + 2] << 8);
                    str += string.Format("{0}", TotalLen);

                    str += string.Format("\r\n\r\n");
                }

                if (H_Info_error.Start != 0xFFFF && H_Info_error.End != 0xFFFF && H_Info_error_rate.Start != 0xFFFF && H_Info_error_rate.End != 0xFFFF)
                {
                    str += string.Format("[Error Character]\r\n");

                    ErrorLen = data[H_Info_error.End + 1];
                    ErrorLen += (data[H_Info_error.End + 2] << 8);
                    str += string.Format("{0}", ErrorLen);

                    str += string.Format("\r\n\r\n");
                }

                if (H_Info_error_rate.Start != 0xFFFF && H_Info_error_rate.End != 0xFFFF && H_Info_end.Start != 0xFFFF && H_Info_end.End != 0xFFFF)
                {
                    str += string.Format("[Error Rate]\r\n");

                    ErrorRate = (float)data[H_Info_error_rate.End + 1];
                    ErrorRate += (float)(data[H_Info_error_rate.End + 2] << 8);
                    ErrorRate = ErrorRate / 10;
                    str += string.Format("{0:0.0}%", ErrorRate);

                    str += string.Format("\r\n\r\n");
                }

                arrRet[0] = Navtex_Message_Id_Error_Check_Header_0(Hstr);
                arrRet[1] = Navtex_Message_Id_Error_Check_Header_1(Hstr);
                arrRet[2] = Navtex_Message_Id_Error_Check_Header_2(Hstr);
                arrRet[3] = Navtex_Message_Id_Error_Check_Header_3(Hstr);
                if (arrRet[0] == 1 && arrRet[1] == 1 && arrRet[2] == 1 && arrRet[3] == 1)
                {
                    s = GetPath();
                    s += string.Format("\\{0}.{1}.{2}_{3}_{4}_ProcessMessage.txt", now.Hour, now.Minute, now.Second, Hstr, Fstr);
                }
                else
                {
                    s = GetPath();
                    s += string.Format("\\{0}.{1}.{2}__ProcessMessage.txt", now.Hour, now.Minute, now.Second);
                }

                using (StreamWriter sw = new StreamWriter(s))
                {
                    await sw.WriteLineAsync(str);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(System.Reflection.MethodBase.GetCurrentMethod().Name + ' ' + ex.Message);
            }
        }
    }
}
