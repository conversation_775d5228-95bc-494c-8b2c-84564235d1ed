﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AssistPro.Lib;
using NAudio.Wave;

namespace AssistPro.Lib
{
    public sealed class Wave
    {
        static readonly Wave instance = new Wave();

        BufferedWaveProvider buffer = new BufferedWaveProvider(new WaveFormat(48000, 1));
        WaveOutEvent waveOut = new WaveOutEvent();

        byte[] wave_byte_void = new byte[2];

        int frequency = 0;
        double phase = 0.0;
        double vpp = 0.0;
        int channelCount = 0;
        int blockAlign = 0;
        int sampleRate = 0;
        double waveLevel = new double();

        static Wave()
        {
        }

        Wave()
        {
            wave_byte_void[0] = 0;
            wave_byte_void[1] = 0;

            buffer.BufferDuration = TimeSpan.FromSeconds(3600);
            waveOut.DeviceNumber = 0;
            waveLevel = 0;
        }

        public static Wave Instance
        {
            get
            {
                return instance;
            }
        }

        public void DOT_Out(int MarkFreq, int SpaceFreq, int BPS)
        {
            WaveStop();

            sampleRate = buffer.WaveFormat.SampleRate;
            channelCount = buffer.WaveFormat.Channels;
            blockAlign = buffer.WaveFormat.BlockAlign;

            int i = 0;
            byte[] wave_mark = new byte[(sampleRate / BPS) * 2];
            byte[] wave_space = new byte[(sampleRate / BPS) * 2];
            short wave_data = 0;

            buffer.AddSamples(wave_byte_void, 0, 2);

            for (long n = 0; n < (21600000 / ((sampleRate / BPS) * 2)); n++) // 7min
            {
                frequency = MarkFreq;
                for (i = 0; i < (sampleRate / BPS); i++)
                {
                    phase = (Math.PI * 2 * frequency) / sampleRate;
                    vpp = (double)short.MaxValue;
                    waveLevel += phase;
                    if (waveLevel >= (Math.PI * 2))
                    {
                        waveLevel -= (Math.PI * 2);
                    }
                    wave_data = (short)(vpp * Math.Sin(waveLevel));

                    wave_mark[(i * 2) + 1] = (byte)((wave_data >> 8) & 0xff);
                    wave_mark[(i * 2)] = (byte)((wave_data) & 0xff);
                }
                buffer.AddSamples(wave_mark, 0, ((sampleRate / BPS) * 2));

                frequency = SpaceFreq;
                for (i = 0; i < (sampleRate / BPS); i++)
                {
                    phase = (Math.PI * 2 * frequency) / sampleRate;
                    vpp = (double)short.MaxValue;
                    waveLevel += phase;
                    if (waveLevel >= (Math.PI * 2))
                    {
                        waveLevel -= (Math.PI * 2);
                    }
                    wave_data = (short)(vpp * Math.Sin(waveLevel));

                    wave_space[(i * 2) + 1] = (byte)((wave_data >> 8) & 0xff);
                    wave_space[(i * 2)] = (byte)((wave_data) & 0xff);
                }
                buffer.AddSamples(wave_space, 0, ((sampleRate / BPS) * 2));
            }

            waveOut.Volume = 1.0f;
            waveOut.Init(buffer);
            waveOut.Play();
        }

        public void Frequency_Out(int Freq)
        {
            WaveStop();

            sampleRate = buffer.WaveFormat.SampleRate;
            channelCount = buffer.WaveFormat.Channels;
            blockAlign = buffer.WaveFormat.BlockAlign;

            int i = 0;
            byte[] wave_byte = new byte[sampleRate * 2];
            short wave_data = 0;

            frequency = Freq;
            buffer.AddSamples(wave_byte_void, 0, 2);

            // 480 sample = 10ms = 1BPS
            // 48khz samplerate = 1sec
            // 48khz * 60sec = 1min
            // 48khz * 60sec * 60min = 1hour
            // 172800000 sample / (480*2) = 180000num ... 30min
            // 86400000 sample = 30min
            for (long n = 0; n < (21600000 / (sampleRate / frequency)); n++) // 7min
            {
                for (i = 0; i < (sampleRate / frequency); i++)
                {
                    phase = (Math.PI * 2 * frequency) / sampleRate;
                    vpp = (double)short.MaxValue;
                    waveLevel += phase;
                    if (waveLevel >= (Math.PI * 2))
                    {
                        waveLevel -= (Math.PI * 2);
                    }
                    wave_data = (short)(vpp * Math.Sin(waveLevel));

                    wave_byte[(i * 2) + 1] = (byte)((wave_data >> 8) & 0xff);
                    wave_byte[(i * 2)] = (byte)((wave_data) & 0xff);
                }
                buffer.AddSamples(wave_byte, 0, (sampleRate / frequency) * 2);
            }

            waveOut.Volume = 1.0f;
            waveOut.Init(buffer);
            waveOut.Play();
        }
        public void Frequency_Add(int Freq, int BPS)
        {
            sampleRate = buffer.WaveFormat.SampleRate;
            channelCount = buffer.WaveFormat.Channels;
            blockAlign = buffer.WaveFormat.BlockAlign;

            int i = 0;
            byte[] wave_byte = new byte[sampleRate * 2];
            short wave_data = 0;

            frequency = Freq;
            for (i = 0; i < (sampleRate / BPS); i++)
            {
                phase = (Math.PI * 2 * frequency) / sampleRate;
                vpp = (double)short.MaxValue;
                waveLevel += phase;
                if (waveLevel >= (Math.PI * 2))
                {
                    waveLevel -= (Math.PI * 2);
                }
                wave_data = (short)(vpp * Math.Sin(waveLevel));

                wave_byte[(i * 2) + 1] = (byte)((wave_data >> 8) & 0xff);
                wave_byte[(i * 2)] = (byte)((wave_data) & 0xff);
            }
            buffer.AddSamples(wave_byte, 0, ((sampleRate / BPS) * 2));
        }

        public void TestOut(int MarkFreq, int SpaceFreq, int BPS)
        {
            waveOut.Stop();
            waveOut.Dispose();
            buffer.ClearBuffer();

            sampleRate = buffer.WaveFormat.SampleRate;
            channelCount = buffer.WaveFormat.Channels;
            blockAlign = buffer.WaveFormat.BlockAlign;

            int i = 0;
            byte[] wave_mark = new byte[(sampleRate / BPS) * 2];
            byte[] wave_space = new byte[(sampleRate / BPS) * 2];
            short wave_data = 0;

            buffer.AddSamples(wave_byte_void, 0, 2);

            frequency = MarkFreq;
            for (i = 0; i < (sampleRate / BPS); i++)
            {
                phase = (Math.PI * 2 * frequency) / sampleRate;
                vpp = (double)short.MaxValue;
                waveLevel += phase;
                if (waveLevel >= (Math.PI * 2))
                {
                    waveLevel -= (Math.PI * 2);
                }
                wave_data = (short)(vpp * Math.Sin(waveLevel));

                wave_mark[(i * 2) + 1] = (byte)((wave_data >> 8) & 0xff);
                wave_mark[(i * 2)] = (byte)((wave_data) & 0xff);
            }
            buffer.AddSamples(wave_mark, 0, ((sampleRate / BPS) * 2));

            frequency = SpaceFreq;
            for (i = 0; i < (sampleRate / BPS); i++)
            {
                phase = (Math.PI * 2 * frequency) / sampleRate;
                vpp = (double)short.MaxValue;
                waveLevel += phase;
                if (waveLevel >= (Math.PI * 2))
                {
                    waveLevel -= (Math.PI * 2);
                }
                wave_data = (short)(vpp * Math.Sin(waveLevel));

                wave_space[(i * 2) + 1] = (byte)((wave_data >> 8) & 0xff);
                wave_space[(i * 2)] = (byte)((wave_data) & 0xff);
            }
            buffer.AddSamples(wave_space, 0, ((sampleRate / BPS) * 2));

            waveOut.Volume = 1.0f;
            waveOut.Init(buffer);
            waveOut.Play();
        }

        public void WavePlay()
        {
            waveOut.Volume = 1;
            waveOut.Init(buffer);
            waveOut.Play();
        }

        public void WaveStop()
        {
            buffer.ClearBuffer();
            waveOut.Stop();
            waveOut.Dispose();
            waveLevel = 0;
        }

        public void VoidSignal()
        {
            buffer.AddSamples(wave_byte_void, 0, 2);
        }
    }
}
