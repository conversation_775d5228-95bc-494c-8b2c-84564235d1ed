﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Microsoft.NETCore.Platforms" version="7.0.0" targetFramework="net472" />
  <package id="Microsoft.Win32.Registry" version="4.7.0" targetFramework="net472" />
  <package id="NAudio" version="2.2.1" targetFramework="net472" />
  <package id="NAudio.Asio" version="2.2.1" targetFramework="net472" />
  <package id="NAudio.Core" version="2.2.1" targetFramework="net472" />
  <package id="NAudio.Midi" version="2.2.1" targetFramework="net472" />
  <package id="NAudio.Wasapi" version="2.2.1" targetFramework="net472" />
  <package id="NAudio.WinForms" version="2.2.1" targetFramework="net472" />
  <package id="NAudio.WinMM" version="2.2.1" targetFramework="net472" />
  <package id="System.IO" version="4.3.0" targetFramework="net472" />
  <package id="System.Net.Http" version="4.3.4" targetFramework="net472" />
  <package id="System.Runtime" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.AccessControl" version="4.7.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.Algorithms" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.Encoding" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.Primitives" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.X509Certificates" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Principal.Windows" version="4.7.0" targetFramework="net472" />
  <package id="WindowsAPICodePack" version="8.0.4" targetFramework="net472" />
</packages>