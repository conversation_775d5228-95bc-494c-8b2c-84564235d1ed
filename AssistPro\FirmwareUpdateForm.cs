﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.NetworkInformation;
using System.Net.Sockets;
using System.Runtime.InteropServices;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using WeifenLuo.WinFormsUI;
using System.Security.Cryptography;

namespace AssistPro
{
    [StructLayout(LayoutKind.Sequential, Pack = 1)]
    public struct DeviceInfo
    {
        public byte DeviceParam;
        public byte MajorVersion;
        public byte MinorVersion;
        public byte PatchVersion;
        public uint IpAddress;
        public uint SubnetMask;
        // IP 주소 문자열 반환 헬퍼 메서드
        public string GetIpAddressString() => new IPAddress(IpAddress).ToString();
        // 서브넷 마스크 문자열 반환 헬퍼 메서드
        public string GetSubnetMaskString() => new IPAddress(SubnetMask).ToString();
        // 장치 타입 관련 정보 반환 헬퍼 메서드
        public byte GetDeviceType() => (byte)(DeviceParam & 0x03);
        public byte GetSubDeviceType() => (byte)((DeviceParam >> 2) & 0x07);
        public byte GetDeviceNumber() => (byte)((DeviceParam >> 5) & 0x07);
    }

    public partial class FirmwareUpdateForm : DockContent
    {

        bool logHexDetails = false; // 헥사 전송 상세 로그 출력 여부

        // --- 상수 정의 ---
        private const int UdpPort = 18930; // UDP 송수신에 사용할 단일 포트
        private const string BroadcastAddress = "***************";
        private const int DeviceUdpListenPort = 18931; // 장비가 UDP 요청을 수신할 포트
        private const int TcpServerPort = 18932; // PC의 TCP 서버 포트
        private const int DefaultTimeoutMs = 10000; // TCP 읽기 기본 타임아웃 (10초)
        private const int BINARY_CHUNK_DATA_SIZE = 1024; // 바이너리 모드에서 한 프레임에 보낼 최대 데이터 바이트 수 (1KB)
        private const int MAX_SEND_RETRIES = 3; // TCP 명령 재시도 횟수

        // --- 프레임 필드 인덱스 (2바이트 Length 기준) ---
        private const int PROTO_IDX_START = 0;  // 시작 바이트 ($)
        private const int PROTO_IDX_LEN_L = 1;  // 길이 Low Byte
        private const int PROTO_IDX_LEN_H = 2;  // 길이 High Byte
        private const int PROTO_IDX_SEQ_L = 3;  // 시퀀스 번호 Low Byte
        private const int PROTO_IDX_SEQ_H = 4;  // 시퀀스 번호 High Byte
        private const int PROTO_IDX_CMD_L = 5;  // 명령어 Low Byte
        private const int PROTO_IDX_CMD_H = 6;  // 명령어 High Byte
        private const int PROTO_IDX_SUB_L = 7;  // 서브 명령어 Low Byte
        private const int PROTO_IDX_SUB_H = 8;  // 서브 명령어 High Byte
        private const int PROTO_IDX_PAYLOAD = 9;  // 페이로드 시작

        // --- 프로토콜 명령/서브명령 코드 ---
        private const int PROTOCOL_START_BYTE = 0x24; // 프레임 시작 바이트
        private const int CMD_UPDATE_READY_REQ = 0xF0;
        private const int SUB_UPDATE_READY_REQ = 0x01;
        private const int CMD_BINARY_CHUNK = 0xF0;
        private const int SUB_BINARY_CHUNK = 0x02;
        private const int CMD_BINARY_COMPLETE = 0xF0;
        private const int SUB_BINARY_COMPLETE = 0x03;


        private const int CMD_DEVICE_SEARCH = 0xFF;
        private const int SUB_DEVICE_SEARCH = 0x00;
        private const int CMD_DEVICE_SEARCH_ACK = 0xFA;
        private const int SUB_DEVICE_SEARCH_ACK = 0x00;

        private const int CMD_UPDATE_SEND_REQ = 0xF1;
        private const int SUB_UPDATE_SEND_REQ = 0x04;
        private const int CMD_BINARY_CHUNK_ACK = 0xF1;
        private const int SUB_BINARY_CHUNK_ACK = 0x05;
        private const int CMD_BINARY_COMPLETE_ACK = 0xF1;
        private const int SUB_BINARY_COMPLETE_ACK = 0x06;

        private const int CMD_UPDATE_STATUS = 0xFC;
        private const int SUB_UPDATE_STATUS_RESET = 0xFF;
        private const int SUB_UPDATE_STATUS_START = 0x01; // byte1: dev_prop, byte 2: major ver, byte 3: minor ver, byte 4: patch ver
        private const int SUB_UPDATE_STATUS_PROGRESS = 0x02; // byte1~4: uint32_t progress
        private const int SUB_UPDATE_STATUS_FLASHING = 0x03;
        private const int SUB_UPDATE_STATUS_FINISH = 0x04;

        private const int CMD_ERROR = 0xF1;
        private const int SUB_ERROR = 0xFF;

        // --- 최소/최대 크기 정의 ---
        // 최소 프레임 길이 (페이로드 없음): Start(1)+Len(2)+Seq(2)+Cmd(2)+Sub(2)+CRC(2) = 11
        private const int PROTOCOL_MIN_FRAME_SIZE = 11;
        // 최소 Frame Length 값 (페이로드 없음): Len(2)+Seq(2)+Cmd(2)+Sub(2) = 8
        private const int PROTOCOL_MIN_LENGTH_VALUE = 8;
        // 최대 페이로드 크기 (예: 주소 4 + 데이터 1024 = 1028)
        private const int PROTOCOL_MAX_PAYLOAD_SIZE = 4 + 1024;
        // 최대 프레임 길이 값 (Len+Seq+Cmd+Sub+Payload)
        private const int PROTOCOL_MAX_LENGTH_VALUE = PROTOCOL_MIN_LENGTH_VALUE + PROTOCOL_MAX_PAYLOAD_SIZE;
        // 최대 프레임 크기 (넉넉하게 설정)
        // Start(1) + Len(2) + Seq(2) + Cmd(2) + Sub(2) + Payload(Max) + Padding(Max 3) + CRC(2)
        private const int PROTOCOL_MAX_FRAME_SIZE = 1 + 2 + 2 + 2 + PROTOCOL_MAX_PAYLOAD_SIZE + 3 + 2;

        // --- UDP 클라이언트 ---
        private UdpClient _udpClient; // 송신 및 수신에 사용될 UDP 클라이언트

        // --- TCP 서버 ---
        private TcpListener _tcpListener; // 장비의 TCP 연결 수신용 리스너
        private CancellationTokenSource _tcpServerCts; // TCP 서버 및 관련 작업 취소용 토큰 소스
        private List<Task> _clientHandlerTasks = new List<Task>(); // 연결된 클라이언트 처리 태스크 목록
        private ushort tcp_cmd = 0;

        // --- 장치 관련 ---
        private List<DeviceInfo> _devices = new List<DeviceInfo>(); // 발견된 장치 목록
        private string _selectedFilePath = null; // 선택된 파일 경로

        // --- 통합 시퀀스 번호 ---
        private ushort _sequenceNumber = 0; // 모든 송신 메시지에 사용될 단일 시퀀스 번호

        // --- 현재 PC에 연결된 인터페이스의 IP 정보 ---
        List<UnicastIPAddressInformation> ipInfoList = new List<UnicastIPAddressInformation>();


        byte[] metainfoByte = new byte[16];

        private readonly Dictionary<byte, string> _deviceTypeDescriptions = new Dictionary<byte, string>
        {
            { 0b01, "MF/HF" },
            { 0b10, "VHF" },
            { 0b11, "NAVTEX" }
        };

        private readonly Dictionary<byte, string> _subDeviceTypeDescriptions = new Dictionary<byte, string>
        {
            { 0b001, "Controller" },
            { 0b010, "Transceiver" },
            { 0b011, "ATU" }, // (MF/HF Only, handled in GetDeviceInfoString)
            { 0b100, "Remote Handset" },  // (VHF Only, handled in GetDeviceInfoString)
            { 0b101, "Alarm Box" },
            { 0b110, "Alarm Unit" }
        };

        private readonly Dictionary<byte, string> _deviceNumberAccDescriptions = new Dictionary<byte, string>
        {
            { 0b000, "No.1" },
            { 0b001, "No.2" },
            { 0b010, "No.3" },
            { 0b011, "No.4" },
            { 0b111, "All Device" }
        };

        private readonly Dictionary<byte, string> _deviceNumberDescriptions = new Dictionary<byte, string>
        {
            { 0b000, "Main Flash" },
            { 0b001, "External Image" },
            { 0b010, "External Font" },
            { 0b011, "External Language" },
            { 0b111, "All Device" }
        };

        // --- 오류 코드 설명 ---
        private readonly Dictionary<uint, string> _firmwareErrorDescriptions = new Dictionary<uint, string>
        {
            //{ 0xffffff01, "타임아웃" },
            //{ 0xffffff02, "CRC 오류" },
            //{ 0xffffff03, "시작 구분자 오류" },
            //{ 0xffffff04, "프레임 길이 오류" },
            //{ 0xffffff05, "시퀀스 번호 오류" },
            //{ 0xffffff06, "명령어 오류" },
            //{ 0xffffff07, "서브 명령어 오류" },
            //{ 0xffffff10, "헥사 라인 번호 오류" },
            //{ 0xffffff11, "헥사 체크섬 오류" },
            //{ 0xffffff12, "헥사 주소 오류" },
            //{ 0xffffff13, "헥사 시작 문자 오류" },
            //{ 0xffffff14, "헥사 데이터 카운트 오류" },
            //{ 0xffffff15, "헥사 레코드 타입 오류" }

            { 0xffffff01, "Timeout" },
            { 0xffffff02, "CRC Error" },
            { 0xffffff03, "Start Delimiter Error" },
            { 0xffffff04, "Frame Length Error" },
            { 0xffffff05, "Sequence Number Error" },
            { 0xffffff06, "Command Error" },
            { 0xffffff07, "Sub Command Error" },
            { 0xffffff10, "Hex Line Number Error" },
            { 0xffffff11, "Hex Checksum Error" },
            { 0xffffff12, "Hex Address Error" },
            { 0xffffff13, "Hex Start Char Error" },
            { 0xffffff14, "Hex Data Count Error" },
            { 0xffffff15, "Hex Record Type Error" },
            { 0xffffff20, "Invalid Binary Address" },
            { 0xffffff21, "Binary Write Failed (or Erase Failed)" },
            { 0xFFFFFFFF, "General/Unknown Error" }
        };

        public FirmwareUpdateForm()
        {
            InitializeComponent();
        }

        public static List<UnicastIPAddressInformation> GetLocalIPv4AddressesLinq()
        {
            try
            {
                return NetworkInterface.GetAllNetworkInterfaces()
                    // 활성화된, 루프백/터널이 아닌 인터페이스 필터링
                    .Where(ni => ni.OperationalStatus == OperationalStatus.Up &&
                                 ni.NetworkInterfaceType != NetworkInterfaceType.Loopback &&
                                 ni.NetworkInterfaceType != NetworkInterfaceType.Tunnel)
                    // 각 인터페이스의 유니캐스트 주소들을 하나의 시퀀스로 펼침
                    .SelectMany(ni => ni.GetIPProperties().UnicastAddresses)
                    // IPv4 주소만 필터링
                    .Where(ipInfo => ipInfo.Address.AddressFamily == AddressFamily.InterNetwork)
                    // IP 주소를 문자열로 변환
                    //.Select(ipInfo => ipInfo.Address.ToString())
                    // 리스트로 변환
                    .ToList();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"IP 주소를 가져오는 중 오류 발생 (LINQ): {ex.Message}");
                return new List<UnicastIPAddressInformation>(); // 오류 시 빈 리스트 반환
            }
        }

        private void RefreshPCIP()
        {
            ipInfoList = GetLocalIPv4AddressesLinq();
            toolStripComboBoxIP.Items.Clear();
            toolStripComboBoxIP.Items.AddRange(ipInfoList.Select(ipInfo => ipInfo.Address.ToString()).ToArray());
        }

        private void FirmwareUpdateForm_Load(object sender, EventArgs e)
        {
            RefreshPCIP();
        }

        private void toolStripButtonSelectIP_Click(object sender, EventArgs e)
        {
            if (toolStripButtonSelectIP.Text == "Open")
            {
                if (InitializeUdp(toolStripComboBoxIP.Text))
                {
                    groupBoxSearchDevice.Enabled = true;
                    toolStripComboBoxIP.Enabled = false;
                    toolStripButtonSelectIP.Text = "Close";
                    toolStripButtonSelectIP.BackColor = Color.PaleGreen;
                }
            }
            else
            {
                StopTcpServer();
                buttonStartUpdate.Enabled = true;
                buttonStartUpdate.BackColor = Color.SpringGreen;
                buttonStopUpdate.Enabled = false;
                buttonStopUpdate.BackColor = SystemColors.Control;
                progressBarUpdate.Value = 0;
                Task.Delay(200).ContinueWith(t =>
                {
                    try
                    {
                        _udpClient?.Close();
                        _udpClient = null;
                        Log("UDP client closed.");
                    }
                    catch { }
                });
                toolStripButtonSelectIP.Text = "Open";
                toolStripButtonSelectIP.BackColor = Color.Silver;
                toolStripComboBoxIP_SelectedIndexChanged(null, null);
                ClearSearchList();
            }
        }

        private void toolStripComboBoxIP_SelectedIndexChanged(object sender, EventArgs e)
        {
            toolStripComboBoxIP.Enabled = true;
            groupBoxSearchDevice.Enabled = false;
            groupBoxDevInfo.Enabled = false;
            groupBoxFileInfo.Enabled = false;
            groupBoxSelectFile.Enabled = false;
            buttonStartUpdate.Enabled = false;
            buttonStartUpdate.BackColor = SystemColors.Control;
            buttonStopUpdate.Enabled = false;
            buttonStopUpdate.BackColor = SystemColors.Control;
        }

        // --- UDP 초기화 ---
        private bool InitializeUdp(string ipStr)
        {
            try
            {
                IPAddress localIp = IPAddress.Parse(ipStr); // 사용할 인터페이스의 IP 주소
                IPEndPoint localEndPoint = new IPEndPoint(localIp, UdpPort);

                // 단일 UdpClient를 수신 포트에 바인딩
                _udpClient = new UdpClient(localEndPoint);
                //_udpClient = new UdpClient(UdpPort);
                _udpClient.EnableBroadcast = true; // 브로드캐스트 송신 허용
                _ = ReceiveUdpDataAsync(); // 단일 클라이언트로 수신 시작
                return true;
            }
            catch (Exception ex)
            {
                string errorMsg = $"UDP Initialization Error: {ex.Message}";
                Log(errorMsg);
                MessageBox.Show(errorMsg, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        // --- 로그 기록 ---
        private void Log(string message)
        {
            if (listBoxUpdateLog.InvokeRequired)
            {
                listBoxUpdateLog.BeginInvoke(new Action(() => Log(message)));
            }
            else
            {
                // 폼이 이미 닫혔으면 로그 기록 중단
                if (listBoxUpdateLog.IsDisposed)
                {
                    return;
                }
                listBoxUpdateLog.Items.Add($"[{DateTime.Now:HH:mm:ss}] {message}");
                // 자동으로 마지막 항목으로 스크롤
                if (listBoxUpdateLog.Items.Count > 0)
                {
                    listBoxUpdateLog.SelectedIndex = listBoxUpdateLog.Items.Count - 1;
                    listBoxUpdateLog.ClearSelected(); // 선택 강조 제거 (선택 사항)
                }
                // 메모리 문제를 방지하기 위해 최대 로그 라인 수 제한 (예: 1000줄)
                const int maxLogLines = 1000;
                while (listBoxUpdateLog.Items.Count > maxLogLines)
                {
                    listBoxUpdateLog.Items.RemoveAt(0);
                }
            }
        }

        // CRC16-CCITT 계산 함수
        static ushort CalculateCrc(byte[] data)
        {
            ushort crc = 0xFFFF;
            foreach (byte b in data)
            {
                crc ^= (ushort)(b << 8);
                for (int j = 0; j < 8; j++)
                {
                    if ((crc & 0x8000) != 0)
                    {
                        crc = (ushort)((crc << 1) ^ 0x1021);
                    }
                    else
                    {
                        crc <<= 1;
                    }
                }
            }
            return crc;
        }
        // --- 메시지 프레임 구성 ---
        private byte[] ConstructMessage(ushort command, ushort subCommand, byte[] payload = null)
        {
            payload = payload ?? new byte[0];

            // 1. 페이로드 패딩 계산
            int paddingLength = (4 - (payload.Length % 4)) % 4;
            byte[] paddedPayload = new byte[payload.Length + paddingLength];
            Array.Copy(payload, paddedPayload, payload.Length);

            // 2. 프레임 길이(값) 계산: Len(2) + Seq(2) + Cmd(2) + Sub(2) + PaddedPayload
            ushort frameLength = (ushort)(2 + 2 + 2 + 2 + paddedPayload.Length);

            List<byte> frame = new List<byte>();
            frame.Add(0x24); // 시작 바이트
            frame.Add((byte)(frameLength & 0xFF));     // Length Low
            frame.Add((byte)(frameLength >> 8));       // Length High
            frame.Add((byte)(_sequenceNumber & 0xFF)); // Sequence High Byte (Index 3)
            frame.Add((byte)(_sequenceNumber >> 8));   // Sequence Low Byte (Index 4)
            frame.Add((byte)(command & 0xFF));         // Command High Byte (Index 5)
            frame.Add((byte)(command >> 8));           // Command Low Byte (Index 6)
            frame.Add((byte)(subCommand & 0xFF));      // Sub Command High Byte (Index 7)
            frame.Add((byte)(subCommand >> 8));        // Sub Command Low Byte (Index 8)
            frame.AddRange(paddedPayload);             // Payload (Index 9부터)
            _sequenceNumber++;

            ushort crc = CalculateCrc(frame.GetRange(PROTO_IDX_LEN_L, frameLength).ToArray());
            frame.Add((byte)(crc & 0xFF)); // CRC Low Byte
            frame.Add((byte)(crc >> 8));   // CRC High Byte

            return frame.ToArray();
        }


        // --- UDP 데이터 전송 ---
        private async Task SendUdpDataAsync(byte[] data, string targetIp, int targetPort)
        {
            try
            {
                // UDP 클라이언트 초기화 여부 확인
                if (_udpClient == null)
                {
                    throw new InvalidOperationException("UDP Client not initialized.");
                }
                // 비동기 데이터 전송
                await _udpClient.SendAsync(data, data.Length, targetIp, targetPort);
                // 전송 로그 출력 (영문)
                Log($"UDP Sent to {targetIp}:{targetPort}: {BitConverter.ToString(data)}");
            }
            catch (Exception ex)
            {
                Log($"UDP Send Error: {ex.Message}");
            }
        }

        // --- UDP 데이터 수신 ---
        private async Task ReceiveUdpDataAsync()
        {
            Log("Starting UDP receive loop...");
            while (true)
            {
                UdpReceiveResult result;
                try
                {
                    // UDP 클라이언트 유효성 검사
                    if (_udpClient == null || _udpClient.Client == null || !_udpClient.Client.IsBound)
                    {
                        // 클라이언트가 준비되지 않았으면 잠시 대기 후 다시 시도
                        await Task.Delay(500);
                        continue;
                    }
                    result = await _udpClient.ReceiveAsync();
                }
                catch (ObjectDisposedException)
                {
                    // 클라이언트가 닫혔을 때 루프 종료
                    Log("UDP reception stopped (client disposed).");
                    break;
                }
                catch (SocketException sockEx) when (sockEx.SocketErrorCode == SocketError.Interrupted) // 10004
                {
                    // 소켓 작업이 중단되었을 때 루프 종료
                    Log("UDP receive operation interrupted (likely closing).");
                    break;
                }
                catch (Exception ex)
                {
                    // 기타 수신 오류 처리
                    Log($"UDP Receive Error: {ex.Message}");
                    await Task.Delay(100); // 오류 발생 시 잠시 대기 후 계속 시도
                    continue;
                }

                // 수신된 데이터 처리
                byte[] receivedData = result.Buffer;
                Log($"UDP Received from {result.RemoteEndPoint}: {BitConverter.ToString(receivedData)}");
                ParseAndProcessUdpMessage(receivedData, result.RemoteEndPoint); // 파싱 및 처리 함수 호출
            }
            Log("UDP receive loop finished.");

        }

        // --- 메시지 파싱 및 처리 ---
        private void ParseAndProcessUdpMessage(byte[] data, IPEndPoint remoteEndPoint)
        {
            // 최소 프레임 길이 (페이로드 없음): Start(1)+Len(2)+Seq(2)+Cmd(2)+Sub(2)+CRC(2) = 11
            if (data == null || data.Length < PROTOCOL_MIN_FRAME_SIZE || data[0] != 0x24)
            {
                Log("Received invalid UDP data format (basic).");
                return;
            }

            ushort frameLength = (ushort)(data[PROTO_IDX_LEN_L] | (data[PROTO_IDX_LEN_H] << 8)); // 값 = Len+Seq+Cmd+Sub+Payload 길이

            // 전체 길이 검증: Start(1) + Value(len) + CRC(2)
            if (data.Length != 1 + frameLength + 2)
            {
                Log($"UDP data length error: Received={data.Length}, Expected={1 + frameLength + 2}");
                // F0/01 같은 짧은 메시지는 길이가 다를 수 있으므로 오류 로그만 남기고 계속 진행할 수 있음
                // return;
            }

            // CRC 검증
            byte[] dataForCrc = null;
            ushort receivedCrc = 0;
            ushort calculatedCrc = 0;
            try
            {
                int crcDataLen = Math.Min(frameLength, data.Length - 1 - 2); // Len부터 Payload 끝까지의 길이
                if (crcDataLen < frameLength || frameLength < PROTOCOL_MIN_LENGTH_VALUE) // Len+Seq+Cmd+Sub 최소 8바이트 필요
                {
                    Log($"UDP data too short for declared frame length or invalid length. Cannot verify CRC. Declared Len Value={frameLength}, Actual Data Len={data.Length}");
                    return;
                }
                dataForCrc = new byte[frameLength]; // CRC 계산 범위 크기 = frameLength 값
                Array.Copy(data, 1, dataForCrc, 0, frameLength);
                receivedCrc = (ushort)(data[data.Length - 1] << 8 | data[data.Length - 2]);
                calculatedCrc = CalculateCrc(dataForCrc);
                if (receivedCrc != calculatedCrc)
                {
                    Log($"UDP CRC error: Received={receivedCrc:X4}, Calculated={calculatedCrc:X4}");
                    return;
                }
            }
            catch (Exception ex)
            {
                Log($"Error during UDP CRC validation: {ex.Message}");
                return;
            }

            // 필드 추출
            ushort sequence = (ushort)(data[PROTO_IDX_SEQ_L] | (data[PROTO_IDX_SEQ_H] << 8));
            ushort command = (ushort)(data[PROTO_IDX_CMD_L] | (data[PROTO_IDX_CMD_H] << 8));
            ushort subCommand = (ushort)(data[PROTO_IDX_SUB_L] | (data[PROTO_IDX_SUB_H] << 8));
            // 페이로드 추출: frameLength 값 - 헤더 8바이트(Len, Seq, Cmd, Sub)
            byte[] payload = new byte[Math.Max(0, frameLength - PROTOCOL_MIN_LENGTH_VALUE)];
            // 실제 페이로드 복사 가능한 길이 계산
            int availablePayloadLength = data.Length - PROTO_IDX_PAYLOAD - 2;
            int actualPayloadLength = Math.Min(payload.Length, availablePayloadLength);
            if (actualPayloadLength > 0)
            {
                Array.Copy(data, PROTO_IDX_PAYLOAD, payload, 0, actualPayloadLength);
            }
            else if (payload.Length > 0)
            {
                payload = new byte[0];
            }

            // 명령별 처리 (FA/00 응답만 처리)
            if (command == 0xFA && subCommand == 0x00)
            {
                DeviceInfo? deviceInfo = ParseDeviceSearchResponsePayload(payload); // 실제 복사된 payload 전달
                if (deviceInfo.HasValue)
                {
                    //if (!_devices.Any(d => d.IpAddress == deviceInfo.Value.IpAddress))
                    {
                        _devices.Add(deviceInfo.Value);
                        string deviceInfoString = GetDeviceInfoString(deviceInfo.Value);
                        Log($"Device Found: {deviceInfoString}");

                        listBoxSearch.Items.Add(deviceInfoString);
                    }
                }
            }
            else
            {
                Log($"Unknown or Unhandled UDP command received: Cmd={command:X2}, Sub={subCommand:X2}");
            }
        }

        // 장치 검색 응답 페이로드 파싱
        private DeviceInfo? ParseDeviceSearchResponsePayload(byte[] payload)
        {
            // 페이로드 길이 확인 (복사된 실제 길이 기준)
            if (payload.Length < PROTOCOL_MIN_FRAME_SIZE)
            {
                Log("Device search response payload length error.");
                return null;
            }
            try
            {
                // 바이트 배열에서 직접 필드 값 읽기
                DeviceInfo info = new DeviceInfo
                {
                    DeviceParam = payload[0],
                    MajorVersion = payload[1],
                    MinorVersion = payload[2],
                    PatchVersion = payload[3],
                    IpAddress = BitConverter.ToUInt32(payload, 4),
                    SubnetMask = BitConverter.ToUInt32(payload, 8)
                };
                return info;
            }
            catch (Exception ex)
            {
                Log($"Device info parsing error: {ex.Message}");
                return null;
            }
        }


        // --- 장치 정보 문자열 포맷 ---
        private string GetDeviceInfoString(DeviceInfo deviceInfo)
        {
            byte deviceTypeVal = deviceInfo.GetDeviceType();
            byte subDeviceTypeVal = deviceInfo.GetSubDeviceType();
            byte deviceNumberVal = deviceInfo.GetDeviceNumber();

            string deviceTypeDesc = _deviceTypeDescriptions.ContainsKey(deviceTypeVal)
                ? _deviceTypeDescriptions[deviceTypeVal] : $"Unknown({deviceTypeVal:X})";
            string subDeviceTypeDesc = _subDeviceTypeDescriptions.ContainsKey(subDeviceTypeVal)
                ? _subDeviceTypeDescriptions[subDeviceTypeVal] : $"Unknown({subDeviceTypeVal:X})";
            string deviceNumberDesc = "";
            if (deviceInfo.GetSubDeviceType() != 0b101 && deviceInfo.GetSubDeviceType() != 0b110) // not alarm box or unit
            {
                deviceNumberDesc = _deviceNumberDescriptions.ContainsKey(deviceNumberVal)
                    ? _deviceNumberDescriptions[deviceNumberVal] : $"Unknown({deviceNumberVal:X})";
            }   
            else
            {
                deviceNumberDesc = _deviceNumberAccDescriptions.ContainsKey(deviceNumberVal)
                    ? _deviceNumberAccDescriptions[deviceNumberVal] : $"Unknown({deviceNumberVal:X})";
            }

            // 특별 케이스 처리 (MF/HF, VHF)
            //if (deviceTypeVal == 0b01 && subDeviceTypeVal != 0b010) subDeviceTypeDesc += " (Invalid for MF/HF)";
            //if (deviceTypeVal == 0b10 && subDeviceTypeVal != 0b011) subDeviceTypeDesc += " (Invalid for VHF)";

            return $"{deviceTypeDesc} | {subDeviceTypeDesc} | {deviceNumberDesc}";
            //return $"Type: {deviceTypeDesc}, SubType: {subDeviceTypeDesc}, Num: {deviceNumberDesc}, Ver: {deviceInfo.MajorVersion}.{deviceInfo.MinorVersion}.{deviceInfo.PatchVersion}, IP: {deviceInfo.GetIpAddressString()}, Subnet: {deviceInfo.GetSubnetMaskString()}";
        }

        private void ClearSearchList()
        {
            groupBoxDevInfo.Enabled = false;
            groupBoxSelectFile.Enabled = false;
            groupBoxFileInfo.Enabled = false;
            listBoxSearch.Items.Clear();
            _devices.Clear();
            textBoxSearchDevice.Text = "";
            textBoxSearchSubDevice.Text = "";
            textBoxSearchDeviceNum.Text = "";
            textBoxSearchDeviceVer.Text = "";
            textBoxSearchIPAddr.Text = "";
            textBoxSearchSubnet.Text = "";
        }

        // --- 버튼 이벤트 핸들러 ---

        // 장치 검색 버튼
        private async void searchDeviceButton_Click(object sender, EventArgs e)
        {
            // 새 검색 시작 시 목록 초기화
            ClearSearchList();
            Log("Starting device search...");
            byte[] message = ConstructMessage(CMD_DEVICE_SEARCH, SUB_DEVICE_SEARCH); // 장치 검색 명령
            await SendUdpDataAsync(message, BroadcastAddress, DeviceUdpListenPort); // 장비 수신 포트로 브로드캐스트
        }

        // 헥사 파일 선택 버튼
        private void buttonSelectHexFile_Click(object sender, EventArgs e)
        {
            using (OpenFileDialog ofd = new OpenFileDialog())
            {
                ofd.Filter = "Firmware Files (*.hex, *.bin)|*.hex;*.bin|Hex Files (*.hex)|*.hex|Binary Files (*.bin)|*.bin|All Files (*.*)|*.*";
                ofd.Title = "Select Firmware Hex File";
                if (ofd.ShowDialog() == DialogResult.OK)
                {
                    buttonStartUpdate.Enabled = true;
                    buttonStartUpdate.BackColor = Color.SpringGreen;
                    _selectedFilePath = ofd.FileName;
                    textBoxHexFilePath.Text = _selectedFilePath;
                    FileInfo fileInfo = new FileInfo(_selectedFilePath);
                    Log($"{fileInfo.Extension} file selected: {_selectedFilePath}");

                    groupBoxFileInfo.Enabled = true;
                    textBoxFileSize.Text = string.Format($"{fileInfo.Length / 1024.0:F2} KB");
                    textBoxFileDate.Text = string.Format($"{fileInfo.CreationTime:yyyy-MM-dd HH:mm:ss}");
                    metainfoByte = new byte[16];
                    if (fileInfo.Extension == ".bin")
                    {
                        try
                        {
                            using (FileStream fs = new FileStream(_selectedFilePath, FileMode.Open, FileAccess.Read))
                            {
                                // FileStream.Read를 사용하여 첫 4바이트 읽기
                                // Read(buffer, offset, count)
                                //   buffer: 읽은 데이터를 저장할 바이트 배열
                                //   offset: buffer 배열 내에서 쓰기를 시작할 오프셋
                                //   count: 읽을 최대 바이트 수
                                int bytesRead = fs.Read(metainfoByte, 0, 16);

                                if (bytesRead < 16)
                                {
                                    Log($"경고: 파일에서 예상보다 적은 바이트({bytesRead}개)를 읽었습니다.");
                                }
                            }
                        }
                        catch (IOException ex)
                        {
                            Log($"파일 읽기 오류: {ex.Message}");
                        }
                        catch (Exception ex)
                        {
                            Log($"알 수 없는 오류: {ex.Message}");
                        }
                        // alarm box, unit의 경우 metainfo 확인 후 제외
                        if (BitConverter.ToUInt32(metainfoByte, 0) == 0xA0 || BitConverter.ToUInt32(metainfoByte, 0) == 0xAB)
                        {
                            textBoxFileDevInfo.Text = metainfoByte[0] == 0xA0 ? "Alarm Unit" : "Alarm Box";
                            textBoxFileVer.Text = $"{BitConverter.ToUInt32(metainfoByte, 4)}.{BitConverter.ToUInt32(metainfoByte, 8)}.{BitConverter.ToUInt32(metainfoByte, 12)}";
                        }
                        else
                        {
                            textBoxFileDevInfo.Text = "";
                            textBoxFileVer.Text = "";
                        }
                    }


                }
                else
                    groupBoxFileInfo.Enabled = false;
            }
        }

        // 업데이트 시작 버튼
        private async void buttonStartUpdate_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(_selectedFilePath) || !File.Exists(_selectedFilePath))
            {
                MessageBox.Show("Please select a valid hex file first.", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            // 확장자 확인 (소문자로 변환하여 비교)
            string extension = Path.GetExtension(_selectedFilePath)?.ToLowerInvariant();
            if (extension != ".hex" && extension != ".bin")
            {
                MessageBox.Show("Invalid file type. Only .hex or .bin files are supported.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error); // 오류 메시지 추가
                return;
            }

            // TCP 서버 실행 중인지 확인
            if (_tcpListener != null && _tcpListener.Server.IsBound)
            {
                Log("TCP server is already running.");
                // Optionally: Stop the server first?
                return;
            }

            // 1. TCP 서버 시작
            if (!StartTcpServer())
            {
                return; // 서버 시작 실패
            }

            // 2. PC의 IP 및 서브넷 정보 가져오기
            //(IPAddress localIp, IPAddress subnetMask) = GetLocalIpAddressAndSubnet();
            //if (localIp == null)
            //{
            //    Log("Could not find local IP address or subnet mask. Check network connection.");
            //    StopTcpServer();
            //    buttonStartUpdate.Enabled = true;
            //    buttonStartUpdate.BackColor = Color.SpringGreen;
            //    buttonStopUpdate.Enabled = false;
            //    buttonStopUpdate.BackColor = SystemColors.Control;
            //    progressBarUpdate.Value = 0;
            //    return;
            //}

            //localIp = IPAddress.Parse("************"); // 사용할 인터페이스의 IP 주소
            IPAddress localIp = ipInfoList[toolStripComboBoxIP.SelectedIndex].Address;
            IPAddress subnetMask = ipInfoList[toolStripComboBoxIP.SelectedIndex].IPv4Mask;

            Log($"Local IP: {localIp}, Subnet: {subnetMask}");

            // 3. "업데이트 준비 요청" UDP 메시지 전송
            byte[] target = { _devices[listBoxSearch.SelectedIndex].DeviceParam, metainfoByte[4], metainfoByte[8], metainfoByte[12] };
            byte[] ipBytes = localIp.GetAddressBytes();
            byte[] subnetBytes = subnetMask.GetAddressBytes();
            byte[] payload = new byte[12];
            Array.Copy(target, 0, payload, 0, 4);
            Array.Copy(ipBytes, 0, payload, 4, 4);
            Array.Copy(subnetBytes, 0, payload, 8, 4);

            // "업데이트 준비 요청" UDP 메시지 생성 및 전송
            tcp_cmd = (ushort)((_devices[listBoxSearch.SelectedIndex].DeviceParam << 8) | CMD_UPDATE_READY_REQ);
            byte[] message = ConstructMessage(tcp_cmd, SUB_UPDATE_READY_REQ, payload);
            //byte[] message = ConstructMessage(CMD_UPDATE_READY_REQ, SUB_UPDATE_READY_REQ, payload);
            await SendUdpDataAsync(message, BroadcastAddress, DeviceUdpListenPort);

            // 상태 업데이트
            Log("Update ready request sent. Waiting for device connection...");
            buttonStartUpdate.Enabled = false; // 중복 시작 방지
            buttonStartUpdate.BackColor = SystemColors.Control;
            buttonStopUpdate.Enabled = true;
            buttonStopUpdate.BackColor = Color.Tomato;
            progressBarUpdate.Value = 0; // 진행률 초기화
        }

        // 업데이트 중지 버튼
        private void buttonStopUpdate_Click(object sender, EventArgs e)
        {
            Log("Update stop requested...");
            StopTcpServer();
            buttonStartUpdate.Enabled = true;
            buttonStartUpdate.BackColor = Color.SpringGreen;
            buttonStopUpdate.Enabled = false;
            buttonStopUpdate.BackColor = SystemColors.Control;
            progressBarUpdate.Value = 0;
        }


        // --- TCP 서버 관련 ---

        // TCP 서버 시작
        private bool StartTcpServer()
        {
            try
            {
                _tcpServerCts = new CancellationTokenSource();
                IPAddress localAddr = IPAddress.Any; // 모든 로컬 인터페이스에서 수신
                _tcpListener = new TcpListener(localAddr, TcpServerPort);
                _tcpListener.Start();
                Log($"TCP server started. Port: {TcpServerPort}");

                // 비동기적으로 클라이언트 연결 수락 시작
                Task.Run(() => AcceptClientsAsync(_tcpServerCts.Token));
                return true;
            }
            catch (Exception ex)
            {
                Log($"TCP server start error: {ex.Message}");
                return false;
            }
        }

        // TCP 서버 중지
        private void StopTcpServer()
        {
            try
            {
                // 취소 토큰을 통해 진행 중인 비동기 작업 취소 요청
                if (_tcpServerCts != null && !_tcpServerCts.IsCancellationRequested)
                {
                    _tcpServerCts.Cancel();
                    Log("Cancellation requested for TCP operations.");
                }
                // 리스너 중지 (잠시 후 실행하여 취소 전파 시간 확보)
                Task.Delay(100).ContinueWith(t =>
                {
                    _tcpListener?.Stop();
                    _tcpListener = null;
                    Log("TCP server stopped.");
                });

                // 클라이언트 핸들러 태스크 목록 정리 (핸들러는 취소 시 스스로 종료해야 함)
                _clientHandlerTasks.Clear();

                //_tcpServerCts?.Cancel(); // 모든 비동기 작업 취소 요청
                //_tcpListener?.Stop();
                //_tcpListener = null;
                //Log("TCP 서버 중지됨.");

                //// 진행 중이던 클라이언트 핸들러 태스크들이 완료되기를 기다릴 수 있음 (선택 사항)
                //// Task.WhenAll(_clientHandlerTasks).Wait(); // UI 스레드에서 Wait()는 피해야 함
                //_clientHandlerTasks.Clear();

            }
            catch (Exception ex)
            {
                Log($"TCP server stop error: {ex.Message}");
            }
            finally
            {
                // 리소스 해제 및 UI 상태 업데이트
                _tcpServerCts?.Dispose();
                _tcpServerCts = null;
                //if (!this.IsDisposed && this.IsHandleCreated)
                //{
                //    this.BeginInvoke(new Action(() =>
                //    {
                //        buttonStartUpdate.Enabled = true;
                //        buttonStartUpdate.BackColor = Color.SpringGreen;
                //        buttonStopUpdate.Enabled = false;
                //        buttonStopUpdate.BackColor = SystemColors.Control;
                //        progressBarUpdate.Value = 0;
                //    }));
                //}
            }
        }

        // 클라이언트 연결 수락 루프
        private async Task AcceptClientsAsync(CancellationToken cancellationToken)
        {
            Log("Starting to accept client connections...");
            while (!cancellationToken.IsCancellationRequested && _tcpListener != null)
            {
                try
                {
                    // .NET Framework에서 CancellationToken으로 AcceptTcpClientAsync 취소하는 패턴
                    var acceptTask = _tcpListener.AcceptTcpClientAsync();
                    // 취소될 때까지 무한정 대기하는 Delay 작업 생성
                    using (var delayCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken))
                    {
                        var delayTask = Task.Delay(Timeout.Infinite, delayCts.Token);
                        // Accept 작업 또는 Delay 작업 중 먼저 완료되는 것 기다림
                        var completedTask = await Task.WhenAny(acceptTask, delayTask);

                        // Delay 작업이 완료되었거나 외부에서 취소 요청이 온 경우 루프 종료
                        if (completedTask == delayTask || cancellationToken.IsCancellationRequested)
                        {
                            // 만약 Accept 작업이 취소 경쟁 후 완료되었다면 해당 클라이언트 닫기
                            acceptTask.ContinueWith(t => t.Result?.Close(), TaskContinuationOptions.OnlyOnRanToCompletion);
                            break;
                        }
                        // Accept 작업이 성공적으로 완료된 경우
                        delayCts.Cancel(); // Delay 작업 취소
                        TcpClient client = await acceptTask; // 연결된 클라이언트 가져오기

                        Log($"Client connected: {client.Client.RemoteEndPoint}");
                        // client.NoDelay = true; // 선택 사항: Nagle 알고리즘 비활성화

                        // 클라이언트 처리를 위한 별도 태스크 시작 및 목록에 추가
                        var handlerTask = HandleTcpClientAsync(client, cancellationToken);
                        _clientHandlerTasks.Add(handlerTask);
                        // 완료된 태스크는 목록에서 제거
                        _clientHandlerTasks.RemoveAll(t => t.IsCompleted);
                    }
                }
                // 예상되는 예외 처리 (루프 종료 조건)
                catch (ObjectDisposedException)
                {
                    Log("TCP Listener disposed, stopping accept loop."); break;
                }
                catch (InvalidOperationException)
                {
                    Log("TCP Listener stopped, stopping accept loop."); break;
                }
                catch (SocketException sockEx) when (sockEx.SocketErrorCode == SocketError.Interrupted)
                {
                    Log("TCP accept operation interrupted (likely closing)."); break;
                }
                catch (OperationCanceledException)
                {
                    Log("TCP connection acceptance cancelled."); break;
                }
                // 기타 예외 처리
                catch (Exception ex)
                {
                    if (!cancellationToken.IsCancellationRequested)
                    {
                        Log($"Client connection acceptance error: {ex.Message}");
                    }
                    break; // 오류 발생 시 일단 루프 종료
                }
            }
            Log("Finished accepting client connections.");
        }

        // *** TCP 클라이언트 처리 (파일 처리 분기 수정) ***
        private async Task HandleTcpClientAsync(TcpClient client, CancellationToken cancellationToken)
        {
            // ... (로깅 변수 선언 및 초기화) ...
            IPEndPoint remoteEndPoint = null;
            string clientLogPrefix = "[Unknown Client]";
            Stopwatch transferStopwatch = new Stopwatch();
            byte[] fullFirmwareDataForHash = null; // SHA 계산용 전체 데이터
            byte[] expectedFirmwareHash = null; // 기대 해시값
            ushort command = 0;
            ushort subCommand = 0;

            try
            {
                /* 클라이언트 정보 가져오기 */
                remoteEndPoint = client.Client.RemoteEndPoint as IPEndPoint;
                if (remoteEndPoint != null)
                    clientLogPrefix = $"[{remoteEndPoint}]";
            }
            catch { }
            Log($"{clientLogPrefix} Starting client handling (Binary Mode Forced).");

            using (var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken))
            using (client)
            using (NetworkStream stream = client.GetStream())
            {
                try
                {
                    // 1. 펌웨어 전송 요청 (F1/04) 대기
                    Log($"{clientLogPrefix} Waiting for Update Send Request (F1/04)...");
                    byte[] requestFrame = await ReadTcpMessageAsync(stream, linkedCts.Token);
                    if (requestFrame == null)
                        return;
                    command = (ushort)(requestFrame[PROTO_IDX_CMD_L]);// | (requestFrame[PROTO_IDX_CMD_H] << 8));
                    subCommand = (ushort)(requestFrame[PROTO_IDX_SUB_L] | (requestFrame[PROTO_IDX_SUB_H] << 8));
                    if (requestFrame.Length < PROTOCOL_MIN_FRAME_SIZE || command != CMD_UPDATE_SEND_REQ || subCommand != SUB_UPDATE_SEND_REQ)
                    {
                        Log($"{clientLogPrefix} Received invalid start command...");
                        return;
                    }
                    Log($"{clientLogPrefix} Received update send request. Preparing binary data...");
                    UpdateProgress(0, 1);
                    transferStopwatch.Start(); // 시간 측정 시작

                    List<BinaryChunk> binaryChunks = null; // 파싱/읽기 결과 저장

                    // --- 파일 확장자에 따른 처리 ---
                    string fileExtension = Path.GetExtension(_selectedFilePath)?.ToLowerInvariant();

                    if (fileExtension == ".hex")
                    {
                        Log($"{clientLogPrefix} Parsing .hex file and creating contiguous image with 0xFF fill...");
                        try
                        {
                            binaryChunks = ParseHexFileToBinary_FillGaps(_selectedFilePath, out fullFirmwareDataForHash);
                            if (binaryChunks == null || binaryChunks.Count == 0 || fullFirmwareDataForHash == null)
                            {
                                Log($"{clientLogPrefix} ERROR: Failed to parse hex file or no data found.");
                                UpdateProgress(0, 1); return;
                            }
                            Log($"{clientLogPrefix} Created contiguous binary image from hex file ({fullFirmwareDataForHash.Length} bytes).");
                        }
                        catch (Exception ex)
                        {
                            Log($"{clientLogPrefix} ERROR parsing hex file: {ex.Message}");
                            UpdateProgress(0, 1);
                            return;
                        }
                    }
                    else if (fileExtension == ".bin")
                    {
                        Log($"{clientLogPrefix} Reading .bin file...");
                        try
                        {
                            binaryChunks = ReadBinFileToBinary(_selectedFilePath); // Address=0, Data=파일 내용
                            if (binaryChunks == null || binaryChunks.Count == 0 || binaryChunks[0].Data == null || binaryChunks[0].Data.Length == 0)
                            {
                                Log($"{clientLogPrefix} ERROR: Binary file empty or could not be read.");
                                UpdateProgress(0, 1); return;
                            }
                            fullFirmwareDataForHash = binaryChunks[0].Data; // 전체 데이터를 해시 계산용으로 저장
                            Log($"{clientLogPrefix} Read {fullFirmwareDataForHash.Length} bytes from binary file.");
                        }
                        catch (Exception ex)
                        {
                            Log($"{clientLogPrefix} ERROR reading bin file: {ex.Message}");
                            UpdateProgress(0, 1);
                            return;
                        }
                    }
                    else
                    {
                        Log($"{clientLogPrefix} ERROR: Unsupported file type '{fileExtension}'.");
                        UpdateProgress(0, 1);
                        return;
                    }

                    // --- SHA-256 계산 ---
                    Log($"{clientLogPrefix} Calculating SHA-256 hash...");
                    try
                    {
                        using (SHA256 sha256 = SHA256.Create())
                        {
                            expectedFirmwareHash = sha256.ComputeHash(fullFirmwareDataForHash);
                        }
                        Log($"{clientLogPrefix} Expected SHA-256: {BitConverter.ToString(expectedFirmwareHash)}");
                    }
                    catch (Exception ex)
                    {
                        Log($"{clientLogPrefix} ERROR calculating SHA-256: {ex.Message}");
                        return;
                    }

                    // --- 바이너리 데이터 전송 로직 ---
                    long totalBytesToSend = fullFirmwareDataForHash.Length; // 전체 크기
                    long bytesSentSoFar = 0;
                    //int totalSegments = binaryChunks.Count;
                    int totalSegments = binaryChunks[0].Data.Count() / 1024;
                    int segmentIndex = 0;
                    uint currentRelativeAddress = binaryChunks[0].Address; // 시작 상대 주소 (항상 0)

                    Log($"{clientLogPrefix} Starting binary data transfer ({totalBytesToSend} bytes)...");

                    int fullDataOffset = 0;
                    while (fullDataOffset < totalBytesToSend) // 전체 데이터를 1KB 단위로 나누어 전송
                    {
                        if (linkedCts.Token.IsCancellationRequested)
                            break;
                        segmentIndex++;

                        int currentChunkSize = (int)Math.Min(BINARY_CHUNK_DATA_SIZE, totalBytesToSend - fullDataOffset);
                        byte[] currentChunkData = new byte[currentChunkSize];
                        // --- *** fullFirmwareDataForHash 에서 직접 복사 *** ---
                        Buffer.BlockCopy(fullFirmwareDataForHash, fullDataOffset, currentChunkData, 0, currentChunkSize);

                        // 페이로드: 상대 주소(4) + 데이터(N)
                        List<byte> chunkPayloadList = new List<byte>();
                        chunkPayloadList.AddRange(BitConverter.GetBytes(currentRelativeAddress));
                        chunkPayloadList.AddRange(currentChunkData);

                        Log($"{clientLogPrefix} Sending Binary Chunk: RelAddr=0x{currentRelativeAddress:X8}, Len={currentChunkSize}");
                        byte[] sendChunkFrame = ConstructMessage(tcp_cmd, SUB_BINARY_CHUNK, chunkPayloadList.ToArray());

                        // --- 전송 및 ACK 대기 (재시도 로직 포함) ---
                        bool ackReceived = false;
                        for (int retryCount = 0; retryCount <= MAX_SEND_RETRIES; retryCount++)
                        {
                            if (linkedCts.Token.IsCancellationRequested)
                                break;

                            Log($"{clientLogPrefix} Sending Binary Chunk (Seg {segmentIndex}/{totalSegments}, Try {retryCount + 1}): RelAddr=0x{currentRelativeAddress:X8}, Len={currentChunkSize}, Seq={_sequenceNumber}");
                            await WriteTcpMessageAsync(stream, sendChunkFrame, linkedCts.Token);

                            // ACK (F1/05) 또는 오류(F1/FF) 대기
                            byte[] ackFrame = await ReadTcpMessageAsync(stream, linkedCts.Token); // 타임아웃 포함

                            if (ackFrame == null) // 타임아웃 발생
                            {
                                Log($"{clientLogPrefix} Timeout waiting for ACK (F1/05) for chunk at RelAddr=0x{currentRelativeAddress:X8}. Retrying ({retryCount + 1}/{MAX_SEND_RETRIES})...");
                                // 재시도 전 잠시 대기 (선택 사항)
                                await Task.Delay(100, linkedCts.Token);
                                continue; // 재시도
                            }
                            else
                            {
                                command = (ushort)(ackFrame[PROTO_IDX_CMD_L]);// | (ackFrame[PROTO_IDX_CMD_H] << 8));
                                subCommand = (ushort)(ackFrame[PROTO_IDX_SUB_L] | (ackFrame[PROTO_IDX_SUB_H] << 8));
                                if (ackFrame.Length >= PROTOCOL_MIN_FRAME_SIZE && command == CMD_UPDATE_SEND_REQ && subCommand == SUB_ERROR) // 오류 프레임 수신
                                {
                                    uint ec = (ackFrame.Length >= PROTOCOL_MIN_FRAME_SIZE) ? BitConverter.ToUInt32(ackFrame, PROTO_IDX_PAYLOAD) : 0xFFFFFFFF;
                                    string ed = _firmwareErrorDescriptions.ContainsKey(ec) ? _firmwareErrorDescriptions[ec] : "Unknown";
                                    Log($"{clientLogPrefix} ABORTING: Device reported error: Code=0x{ec:X8} ({ed})");
                                    UpdateProgress(0, 1);
                                    return; // 오류 시 전체 중단
                                }
                                else if (ackFrame.Length >= PROTOCOL_MIN_FRAME_SIZE && command == CMD_UPDATE_SEND_REQ && subCommand == SUB_BINARY_CHUNK_ACK) // 정상 ACK 수신
                                {
                                    Log($"{clientLogPrefix} Chunk acknowledged (RelAddr=0x{currentRelativeAddress:X8}).");
                                    ackReceived = true;
                                    break; // ACK 받았으므로 재시도 루프 탈출
                                }
                                else // 예상치 못한 응답
                                {
                                    Log($"{clientLogPrefix} Received invalid/unexpected response waiting for F1/05 ACK. Aborting.");
                                    UpdateProgress(0, 1);
                                    return; // 알 수 없는 응답 시 중단
                                }
                            }
                        } // end for retryCount

                        // 최대 재시도 후에도 ACK 못 받으면 실패 처리
                        if (!ackReceived)
                        {
                            Log($"{clientLogPrefix} Failed to get ACK for chunk at RelAddr=0x{currentRelativeAddress:X8} after {MAX_SEND_RETRIES} retries. Aborting.");
                            UpdateProgress(0, 1);
                            return;
                        }

                        bytesSentSoFar += currentChunkSize;
                        currentRelativeAddress += (uint)currentChunkSize; // 다음 상대 주소
                        fullDataOffset += currentChunkSize; // 전체 데이터 오프셋 증가
                        if (totalBytesToSend > 0)
                        {
                            UpdateProgress((int)((double)bytesSentSoFar * 100 / totalBytesToSend), 100);
                        }
                        else
                        {
                            UpdateProgress(100, 100);
                        }
                    } // end while (sending chunks)

                    if (linkedCts.Token.IsCancellationRequested)
                    {
                        Log($"{clientLogPrefix} Binary transfer cancelled.");
                        UpdateProgress(0, 1);
                        return;
                    }

                    // --- 전송 완료 (F0/03) + SHA-256 해시 전송 ---
                    Log($"{clientLogPrefix} All binary chunks sent. Sending completion (F0/03) with SHA-256...");
                    if (expectedFirmwareHash == null || expectedFirmwareHash.Length != 32)
                    {
                        /* 오류 */
                        Log($"{clientLogPrefix} ERROR: Invalid SHA hash.");
                        UpdateProgress(0, 1);
                        return;
                    }
                    byte[] completeFrame = ConstructMessage(tcp_cmd, SUB_BINARY_COMPLETE, expectedFirmwareHash);
                    await WriteTcpMessageAsync(stream, completeFrame, linkedCts.Token);

                    // 최종 ACK (F1/06) 대기
                    Log($"{clientLogPrefix} Waiting for final ACK and verification (F1/06)...");
                    byte[] finalAckFrame = await ReadTcpMessageAsync(stream, linkedCts.Token, DefaultTimeoutMs * 5);
                    if (finalAckFrame == null)
                    {
                        /* 타임아웃 처리 */
                        Log($"{clientLogPrefix} Timeout final ACK (F1/06). Aborting.");
                        UpdateProgress(0, 1);
                        return;
                    }

                    command = (ushort)(finalAckFrame[PROTO_IDX_CMD_L]);// | (finalAckFrame[PROTO_IDX_CMD_H] << 8));
                    subCommand = (ushort)(finalAckFrame[PROTO_IDX_SUB_L] | (finalAckFrame[PROTO_IDX_SUB_H] << 8));
                    if (finalAckFrame.Length >= PROTO_IDX_PAYLOAD && command == CMD_UPDATE_SEND_REQ && subCommand == SUB_ERROR)
                    {
                        /* 오류 처리 */
                        uint ec = (finalAckFrame.Length >= PROTOCOL_MIN_FRAME_SIZE) ? BitConverter.ToUInt32(finalAckFrame, PROTO_IDX_PAYLOAD) : 0;
                        string ed = _firmwareErrorDescriptions.ContainsKey(ec) ? _firmwareErrorDescriptions[ec] : "Unk"; Log($"{clientLogPrefix} ABORT: Dev final error: {ed}");
                        UpdateProgress(0, 1);
                        return;
                    }
                    if (finalAckFrame.Length < PROTOCOL_MIN_FRAME_SIZE || command != CMD_UPDATE_SEND_REQ || subCommand != SUB_BINARY_COMPLETE_ACK)
                    {
                        /* 오류 처리 */
                        Log($"{clientLogPrefix} Received invalid final ACK. Aborting.");
                        UpdateProgress(0, 1);
                        return;
                    }

                    Log($"{clientLogPrefix} Binary firmware update and verification completed successfully!");
                    UpdateProgress(100, 100);
                }
                // --- 예외 처리 및 finally ---
                catch (OperationCanceledException)
                {
                    Log($"{clientLogPrefix} Client handling stopped (cancelled).");
                    UpdateProgress(0, 1);
                }
                catch (IOException ioEx)
                {
                    Log($"{clientLogPrefix} TCP communication error: {ioEx.Message}");
                    UpdateProgress(0, 1);
                }
                catch (Exception ex)
                {
                    Log($"{clientLogPrefix} Unexpected error during client handling: {ex.Message}\n{ex.StackTrace}");
                    UpdateProgress(0, 1);
                }
                finally
                {
                    transferStopwatch.Stop();
                    Log($"{clientLogPrefix} Transfer duration: {transferStopwatch.ElapsedMilliseconds} ms");
                    Log($"{clientLogPrefix} Client handling finished.");
                    if (_tcpServerCts == null || _tcpServerCts.IsCancellationRequested)
                    {
                        if (!this.IsDisposed && this.IsHandleCreated)
                        {
                            this.BeginInvoke(new Action(() =>
                            {
                                buttonStartUpdate.Enabled = true;
                                buttonStartUpdate.BackColor = Color.SpringGreen;
                                buttonStopUpdate.Enabled = false;
                                buttonStopUpdate.BackColor = SystemColors.Control;
                            }));
                        }
                    }
                    this.BeginInvoke(new Action(() => buttonStopUpdate_Click(null, null)));
                }
            }
        }

        // Intel HEX 파싱 함수 (0xFF 채우기 및 단일 청크 반환, out 파라미터를 사용하여 생성된 전체 바이너리 데이터도 반환 (SHA 계산용))            
        private List<BinaryChunk> ParseHexFileToBinary_FillGaps(string filePath, out byte[] fullContiguousData)
        {
            fullContiguousData = null; // 출력 파라미터 초기화
            List<BinaryChunk> resultChunkList = new List<BinaryChunk>();
            // 주소와 바이트 데이터를 임시 저장
            SortedDictionary<uint, byte> tempData = new SortedDictionary<uint, byte>();
            uint currentBaseAddress = 0; // 확장 주소 저장용
            uint minAddress = uint.MaxValue; // 데이터의 시작 주소 (최소값)
            uint maxAddress = uint.MinValue; // 데이터의 마지막 주소
            bool dataFound = false;          // 유효 데이터 발견 여부

            const uint VALID_FLASH_START_ADDR = 0x08000000;
            const uint VALID_FLASH_END_ADDR = 0x08100000;

            Log($"Parsing Hex file. Valid address range: 0x{VALID_FLASH_START_ADDR:X8} - 0x{VALID_FLASH_END_ADDR - 1:X8}");

            // 1단계: 유효 범위 데이터 수집 및 최소/최대 주소 찾기
            try
            {
                foreach (string line in File.ReadLines(filePath))
                {
                    string tl = line.Trim(); // 앞뒤 공백 제거
                    // 기본 유효성 검사 (시작 문자, 최소 길이)
                    if (string.IsNullOrEmpty(tl) || tl[0] != ':' || tl.Length < 11)
                    {
                        continue;
                    }

                    // 레코드 필드 파싱
                    byte byteCount = Convert.ToByte(tl.Substring(1, 2), 16);
                    uint addressOffset = Convert.ToUInt32(tl.Substring(3, 4), 16);
                    byte recordType = Convert.ToByte(tl.Substring(7, 2), 16);
                    uint absoluteAddress = currentBaseAddress + addressOffset; // 실제 절대 주소 계산

                    // 라인 길이 및 데이터 길이 확인
                    if (tl.Length < 11 + byteCount * 2)
                    {
                        Log($"Warning: Skipping hex line due to data length mismatch: {tl}");
                        continue; // 데이터 길이 부족 시 해당 라인 건너뛰기
                    }
                    // TODO: 체크섬 검증 로직 추가

                    // --- 레코드 타입별 처리 ---
                    if (recordType == 0x00) // 데이터 레코드
                    {
                        // 주소 범위 확인
                        if (absoluteAddress >= VALID_FLASH_START_ADDR && absoluteAddress < VALID_FLASH_END_ADDR)
                        {
                            dataFound = true; // 유효 데이터 발견 플래그 설정
                            minAddress = Math.Min(minAddress, absoluteAddress); // 최소 주소 갱신

                            // 데이터 바이트를 SortedDictionary에 추가
                            for (int i = 0; i < byteCount; i++)
                            {
                                uint byteAddress = absoluteAddress + (uint)i;
                                if (byteAddress < VALID_FLASH_END_ADDR) // 최종 주소 범위 확인
                                {
                                    tempData[byteAddress] = Convert.ToByte(tl.Substring(9 + i * 2, 2), 16);
                                    maxAddress = Math.Max(maxAddress, byteAddress); // 실제 데이터가 있는 마지막 주소 갱신
                                }
                                else
                                {
                                    Log($"Warning: Data byte at 0x{byteAddress:X8} ignored (out of range).");
                                }
                            }
                        }
                        // else { Log($"DEBUG: Ignoring data record outside valid range. Addr=0x{absoluteAddress:X8}"); }
                    }
                    else if (recordType == 0x01) // 파일 끝 레코드
                    {
                        Log("DEBUG: End Of File record reached.");
                        break;
                    }
                    else if (recordType == 0x04) // 확장 선형 주소
                    {
                        currentBaseAddress = (uint)(Convert.ToUInt16(tl.Substring(9, 4), 16) << 16);
                        Log($"DEBUG: Set Base Address to 0x{currentBaseAddress:X8}");
                    }
                    else if (recordType == 0x02) // 확장 세그먼트 주소
                    {
                        currentBaseAddress = (uint)(Convert.ToUInt16(tl.Substring(9, 4), 16) << 4);
                        Log($"DEBUG: Set Segment Address to 0x{currentBaseAddress:X8}");
                    }
                    else if (recordType == 0x05) // 시작 선형 주소 (무시)
                    {
                        Log($"DEBUG: Ignoring Start Linear Address record (Type 05).");
                    }
                    else
                    {
                        Log($"DEBUG: Ignoring unsupported record type {recordType:X2}.");
                    }
                } // end foreach line
            }
            catch (Exception ex)
            {
                Log($"Hex Parse Error during read: {ex.Message}");
                throw; // 파싱 오류 시 예외 발생
            }

            // 2단계: 연속적인 바이너리 이미지 생성 (0xFF 채우기)
            if (!dataFound)
            {
                Log("No valid data found within the specified flash range in the hex file.");
                fullContiguousData = new byte[0]; // 빈 배열 할당
                return resultChunkList; // 빈 리스트 반환
            }

            // 기준 주소 = 찾은 최소 주소
            uint baseOffsetAddress = minAddress;
            // 전체 크기 계산 (maxAddress는 마지막 바이트의 주소임)
            long totalSizeLong = (long)maxAddress - baseOffsetAddress + 1;
            if (totalSizeLong <= 0 || totalSizeLong > int.MaxValue) // 크기 유효성 검사 (int 배열 최대 크기 고려)
            {
                Log($"ERROR: Invalid calculated binary image size ({totalSizeLong}). Min=0x{minAddress:X8}, Max=0x{maxAddress:X8}");
                fullContiguousData = new byte[0];
                return resultChunkList;
            }
            int totalSize = (int)totalSizeLong;

            Log($"Creating contiguous binary image. BaseAddr=0x{baseOffsetAddress:X8}, MaxAddr=0x{maxAddress:X8}, Size={totalSize} bytes.");

            try
            {
                fullContiguousData = new byte[totalSize]; // 전체 크기 배열 할당

                // 0xFF으로 초기화 (플래시 지워진 상태 시뮬레이션)
                // Array.Fill 사용 가능 시: Array.Fill<byte>(fullContiguousData, 0xFF);
                for (int i = 0; i < totalSize; i++)
                {
                    fullContiguousData[i] = 0xFF;
                }

                // SortedDictionary의 데이터로 배열 채우기
                foreach (var kvp in tempData)
                {
                    // 절대 주소를 배열 인덱스(상대 오프셋)로 변환
                    long index = (long)kvp.Key - baseOffsetAddress;
                    if (index >= 0 && index < totalSize) // 범위 확인 (필수)
                    {
                        fullContiguousData[index] = kvp.Value; // 데이터 덮어쓰기
                    }
                }

                // --- 상대 주소 0을 가진 단일 청크 생성 ---
                resultChunkList.Add(new BinaryChunk { Address = 0, Data = fullContiguousData });
                Log($"Generated single binary chunk. Relative Address: 0, Size: {fullContiguousData.Length}");
            }
            catch (OutOfMemoryException)
            {
                Log($"ERROR: Out of memory allocating {totalSize} bytes for binary image. Hex file might be too sparse.");
                MessageBox.Show($"Out of memory trying to create a {totalSize / 1024.0 / 1024.0:F2} MB image from the hex file. The hex file might be too sparse.", "Memory Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                fullContiguousData = null; // 실패 시 null
                return new List<BinaryChunk>(); // 빈 리스트 반환
            }
            catch (Exception ex)
            {
                Log($"ERROR creating binary image: {ex.Message}");
                fullContiguousData = null; // 실패 시 null
                throw; // 예외 다시 던지기
            }

            return resultChunkList;
        }


        // .bin 파일 읽기 함수 (Address=0 반환)
        private List<BinaryChunk> ReadBinFileToBinary(string filePath)
        {
            try
            {
                byte[] binData = File.ReadAllBytes(filePath);
                if (binData == null || binData.Length == 0)
                {
                    Log($"ERROR: Binary file is empty or could not be read: {filePath}");
                    return new List<BinaryChunk>();
                }
                //// alarm box, unit의 경우 metainfo 확인 후 제외
                //if (BitConverter.ToUInt32(binData, 0) == 0xA0 || BitConverter.ToUInt32(binData, 0) == 0xAB)
                //{
                //    binData = new byte[binData.Length - 16];
                //    Array.Copy(File.ReadAllBytes(filePath), 16, binData, 0, binData.Length);
                //}
                // .bin 파일은 주소 0부터 시작하는 단일 청크로 처리
                Log($"Read {binData.Length} bytes from binary file. Assuming start address 0.");
                return new List<BinaryChunk> { new BinaryChunk { Address = 0, Data = binData } }; // 상대 주소 0
            }
            catch (Exception ex)
            {
                Log($"ERROR reading bin file {filePath}: {ex.Message}");
                throw;
            }
        }


        // TCP 스트림 읽기 함수 (2바이트 Length 적용) 
        private async Task<byte[]> ReadTcpMessageAsync(NetworkStream stream, CancellationToken cancellationToken, int timeoutMilliseconds = DefaultTimeoutMs)
        {
            byte[] fullFrame = null;
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            try
            {
                // 1. 시작 바이트 읽기 ($)
                byte[] startByte = new byte[1];
                int bytesRead = await ReadWithTimeoutAsync(stream, startByte, 0, 1, cancellationToken, timeoutMilliseconds);
                if (bytesRead <= 0 || startByte[0] != PROTOCOL_START_BYTE)
                {
                    if (bytesRead > 0)
                        Log($"TCP receive error: Start byte mismatch (Got {startByte[0]:X2}).");
                    return null;
                }

                // 2. 프레임 길이 읽기 (2 바이트, Little Endian: Low + High)
                byte[] lengthBytes = new byte[2];
                int totalLenRead = 0;
                while (totalLenRead < 2 && !cancellationToken.IsCancellationRequested)
                {
                    bytesRead = await ReadWithTimeoutAsync(stream, lengthBytes, totalLenRead, 2 - totalLenRead, cancellationToken, timeoutMilliseconds);
                    if (bytesRead <= 0)
                    {
                        Log("TCP receive error: Failed to read full length field.");
                        return null;
                    }
                    totalLenRead += bytesRead;
                }
                if (cancellationToken.IsCancellationRequested)
                    return null;
                ushort frameLength = (ushort)(lengthBytes[0] | (lengthBytes[1] << 8)); // 값 = Len+Seq+Cmd+Sub+Payload 길이

                // 3. 길이 유효성 검사 (최소: Len(2)+Seq(2)+Cmd(2)+Sub(2) = 8)
                // 최대 페이로드(1KB + 주소4) + 헤더(8) = 1024+4+8 = 1036. ushort는 충분.
                if (frameLength < PROTOCOL_MIN_LENGTH_VALUE || frameLength > PROTOCOL_MAX_LENGTH_VALUE + 100) // 최소 8, 최대값 적절히 설정
                {
                    Log($"TCP receive error: Invalid frame length value {frameLength}.");
                    return null;
                }

                // 4. 나머지 데이터 + CRC 읽기
                // 읽어야 할 바이트 수 = frameLength 값 - Len (2) + CRC (2)
                int bytesToReadAfterLength = frameLength;
                byte[] restOfData = new byte[bytesToReadAfterLength];
                int totalBytesRead = 0;
                while (totalBytesRead < bytesToReadAfterLength && !cancellationToken.IsCancellationRequested)
                {
                    bytesRead = await ReadWithTimeoutAsync(stream, restOfData, totalBytesRead, bytesToReadAfterLength - totalBytesRead, cancellationToken, timeoutMilliseconds);
                    if (bytesRead <= 0)
                    {
                        Log($"TCP read failed or timed out while reading remaining frame data (read {totalBytesRead}/{bytesToReadAfterLength} bytes before fail).");
                        return null;
                    }
                    totalBytesRead += bytesRead;
                }
                stopwatch.Stop();
                if (cancellationToken.IsCancellationRequested)
                    return null;
                if (totalBytesRead != bytesToReadAfterLength)
                {
                    Log($"TCP read error: Incomplete frame read ({totalBytesRead}/{bytesToReadAfterLength}).");
                    return null;
                }

                // 5. 전체 프레임 재구성
                // 크기 = Start(1) + Len(2) + Rest(frameLength + 2)
                fullFrame = new byte[1 + bytesToReadAfterLength + 2];
                fullFrame[0] = PROTOCOL_START_BYTE;
                fullFrame[1] = lengthBytes[0]; // Len Low
                fullFrame[2] = lengthBytes[1]; // Len High
                Array.Copy(restOfData, 0, fullFrame, PROTO_IDX_SEQ_L, restOfData.Length); // Seq부터 CRC까지 복사

                // 6. CRC 검증
                // 범위: Len부터 Payload 끝까지 (총 frameLength 값 개수)
                byte[] dataForCrc = new byte[frameLength];
                Array.Copy(fullFrame, PROTO_IDX_LEN_L, dataForCrc, 0, frameLength); // 인덱스 1부터 복사
                ushort receivedCrc = (ushort)(fullFrame[fullFrame.Length - 1] << 8 | fullFrame[fullFrame.Length - 2]); // H, L 순서
                ushort calculatedCrc = CalculateCrc(dataForCrc);
                if (receivedCrc != calculatedCrc)
                {
                    Log($"TCP CRC Check Failed! Received={receivedCrc:X4}, Calculated={calculatedCrc:X4}. Frame: {BitConverter.ToString(fullFrame)}");
                    return null;
                }

                //Log($"TCP Received: {BitConverter.ToString(fullFrame)}");
                return fullFrame;
            }
            catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested) { return null; }
            catch (IOException ioEx) { Log($"TCP read IO error: {ioEx.Message}"); return null; }
            catch (Exception ex) { Log($"Error during TCP message read: {ex.Message}\n{ex.StackTrace}"); return null; }
            finally { stopwatch?.Stop(); }
        }

        // 타임아웃 포함 읽기
        private async Task<int> ReadWithTimeoutAsync(NetworkStream stream, byte[] buffer, int offset, int count, CancellationToken cancellationToken, int timeoutMilliseconds)
        {
            using (var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken))
            {
                Task<int> readTask = null;
                Task timeoutTask = null;
                try
                {
                    readTask = stream.ReadAsync(buffer, offset, count, timeoutCts.Token);
                    timeoutTask = Task.Delay(timeoutMilliseconds, timeoutCts.Token);
                    Task completedTask = await Task.WhenAny(readTask, timeoutTask);
                    if (completedTask == timeoutTask)
                    {
                        Log("TCP read timeout.");
                        timeoutCts.Cancel();
                        return 0;
                    }
                    else
                    {
                        timeoutCts.Cancel();
                        return await readTask;
                    }
                }
                catch (OperationCanceledException)
                {
                    if (cancellationToken.IsCancellationRequested)
                        Log("TCP read cancelled by external token.");
                    return 0;
                }
                catch (IOException ex)
                {
                    Log($"TCP read IO error (in ReadWithTimeoutAsync): {ex.Message}");
                    return 0;
                }
                catch (ObjectDisposedException)
                {
                    Log("TCP stream disposed during read.");
                    return 0;
                }
            }
        }

        // TCP 스트림에 메시지 쓰기
        private async Task WriteTcpMessageAsync(NetworkStream stream, byte[] data, CancellationToken cancellationToken)
        {
            try
            {
                await stream.WriteAsync(data, 0, data.Length, cancellationToken);
                if (logHexDetails)
                {
                    Log($"TCP Sent: {BitConverter.ToString(data)}");
                }
            }
            catch (IOException ioEx)
            {
                Log($"TCP write error: {ioEx.Message}");
                throw;
            }
            catch (OperationCanceledException)
            {
                Log("TCP write cancelled.");
                throw;
            }
            catch (ObjectDisposedException)
            {
                Log("TCP stream disposed during write.");
                throw;
            }
        }

        // 로컬 IP 주소 및 서브넷 마스크 가져오기
        private (IPAddress, IPAddress) GetLocalIpAddressAndSubnet()
        {
            try
            {
                var ni = NetworkInterface.GetAllNetworkInterfaces().Where(n => n.OperationalStatus == OperationalStatus.Up
                && n.NetworkInterfaceType != NetworkInterfaceType.Loopback
                && n.Supports(NetworkInterfaceComponent.IPv4)).OrderByDescending(n => n.GetIPProperties().GatewayAddresses.Any()).FirstOrDefault();
                if (ni != null)
                {
                    var ipProps = ni.GetIPProperties();
                    var ipV4 = ipProps.UnicastAddresses.FirstOrDefault(addr => addr.Address.AddressFamily == AddressFamily.InterNetwork && addr.IPv4Mask != null);
                    if (ipV4 != null)
                        return (ipV4.Address, ipV4.IPv4Mask);
                    else
                        Log($"Found active interface '{ni.Name}', but no suitable IPv4 address/mask.");
                }
                else
                    Log("No suitable active network interface found.");
            }
            catch (Exception ex)
            {
                Log($"Error getting local IP information: {ex.Message}");
            }
            return (null, null);
        }

        // 진행률 업데이트
        private void UpdateProgress(int current, int total)
        {
            if (progressBarUpdate.InvokeRequired)
            {
                progressBarUpdate.BeginInvoke(new Action(() => UpdateProgress(current, total)));
            }
            else
            {
                if (progressBarUpdate.IsDisposed)
                    return;
                int percentage = (total > 0) ? Math.Min(100, Math.Max(0, (int)Math.Round((double)current * 100 / total))) : 0;
                progressBarUpdate.Value = percentage;
            }
        }

        // 폼 종료 시 리소스 정리
        private void FirmwareUpdateForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            Log("Application shutting down...");
            StopTcpServer();
            Task.Delay(200).ContinueWith(t =>
            {
                try
                {
                    _udpClient?.Close();
                    _udpClient = null;
                    Log("UDP client closed.");
                }
                catch { }
            });
            Log("Cleanup initiated.");
        }

        private void searchListBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (listBoxSearch.SelectedIndex < 0)
                return;
            DeviceInfo deviceInfo = _devices[listBoxSearch.SelectedIndex];
            textBoxSearchDevice.Text = _deviceTypeDescriptions.ContainsKey(deviceInfo.GetDeviceType())
                ? _deviceTypeDescriptions[deviceInfo.GetDeviceType()]
                : "Unknown";

            textBoxSearchSubDevice.Text = _subDeviceTypeDescriptions.ContainsKey(deviceInfo.GetSubDeviceType())
                ? _subDeviceTypeDescriptions[deviceInfo.GetSubDeviceType()]
                : "Unknown";

            if (deviceInfo.GetSubDeviceType() != 0b101 && deviceInfo.GetSubDeviceType() != 0b110) // not alarm box or unit
            {
                textBoxSearchDeviceNum.Text = _deviceNumberDescriptions.ContainsKey(deviceInfo.GetDeviceNumber())
                    ? _deviceNumberDescriptions[deviceInfo.GetDeviceNumber()]
                    : "Unknown";
            }
            else
            {
                textBoxSearchDeviceNum.Text = _deviceNumberAccDescriptions.ContainsKey(deviceInfo.GetDeviceNumber())
                    ? _deviceNumberAccDescriptions[deviceInfo.GetDeviceNumber()]
                    : "Unknown";
            }

            textBoxSearchDeviceVer.Text = $"{deviceInfo.MajorVersion}.{deviceInfo.MinorVersion}.{deviceInfo.PatchVersion}";
            textBoxSearchIPAddr.Text = deviceInfo.GetIpAddressString();
            textBoxSearchSubnet.Text = deviceInfo.GetSubnetMaskString();

            groupBoxDevInfo.Enabled = true;
            groupBoxSelectFile.Enabled = true;
        }

        private void buttonClearLog_Click(object sender, EventArgs e)
        {
            listBoxUpdateLog.Items.Clear();
        }

    }
    // 바이너리 데이터 덩어리를 나타내는 간단한 클래스
    public class BinaryChunk
    {
        public uint Address { get; set; }
        public byte[] Data { get; set; }
    }
}
