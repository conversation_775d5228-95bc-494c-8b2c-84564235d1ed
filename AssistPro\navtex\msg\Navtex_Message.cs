﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AssistPro.navtex;
using AssistPro.navtex.communication;
using static AssistPro.navtex.msg.Serial_RTS_Send_Mgr;

namespace AssistPro.navtex.msg
{
    public partial class Navtex_Message
    {
        private Audio_Wave_Control m_Audio_Wave_Control = Audio_Wave_Control.Instance;
        private Serial_RTS_Send_Mgr m_Serial_RTS_Send_Mgr = Serial_RTS_Send_Mgr.Instance;

        int CCIR_MODE_DataIn = 0xFF;
        int CCIR_MODE_Current = 0xFF;

        private class Constants
        {
            public const char Letters_Shift = 'a';
            public const char Figures_Shift = 'b';
            public const char Idle_Signal_B = 'c';
            public const char Idle_Signal_A = 'd';
            public const char Phasing_Signal_1 = 'e';
            public const char Phasing_Signal_2 = 'f';
            public const int Start_Phasing_Tick = 72;   // 140ms x 72 = 10s
            public const int End_Phasing_Tick = 20;     // 140ms x 20 = 2.8s
            public const int ReStart_Phasing_Tick = 36; // 140ms x 36 = 5.0s
        }

        private byte CCIR476_Encode(char str, int ccir_mode)
        {
            byte ret = 0xFF;

            switch (str)
            {
                case '\r': ret = 0x78; break;
                case '\n': ret = 0x6C; break;
                case ' ': ret = 0x5C; break;
                case 'a': ret = 0x5A; CCIR_MODE_Current = 0; break;  // Autoset Mode = Letters
                case 'b': ret = 0x36; CCIR_MODE_Current = 1; break;  // Autoset Mode = Figures
                //case "CS1": ret = 0x65; break;
                //case "CS2": ret = 0x6A; break;
                //case "CS3": ret = 0x59; break;
                //case "CS4": ret = 0x35; break;
                //case "CS5": ret = 0x69; break;
                case 'c': ret = 0x33; break;  // idle signal B
                case 'd': ret = 0x0F; break;  // idle signal A
                case 'e': ret = 0x0F; break;  // Phasing Signal 1
                case 'f': ret = 0x66; break;  // Phasing Signal 2
            }

            switch (ccir_mode)
            {
                case 0: // Autoset Mode = Letters
                    switch (str)
                    {
                        case 'A': ret = 0x47; break;
                        case 'B': ret = 0x72; break;
                        case 'C': ret = 0x1D; break;
                        case 'D': ret = 0x53; break;
                        case 'E': ret = 0x56; break;
                        case 'F': ret = 0x1B; break;
                        case 'G': ret = 0x35; break;
                        case 'H': ret = 0x69; break;
                        case 'I': ret = 0x4D; break;
                        case 'J': ret = 0x17; break;
                        case 'K': ret = 0x1E; break;
                        case 'L': ret = 0x65; break;
                        case 'M': ret = 0x39; break;
                        case 'N': ret = 0x59; break;
                        case 'O': ret = 0x71; break;
                        case 'P': ret = 0x2D; break;
                        case 'Q': ret = 0x2E; break;
                        case 'R': ret = 0x55; break;
                        case 'S': ret = 0x4B; break;
                        case 'T': ret = 0x74; break;
                        case 'U': ret = 0x4E; break;
                        case 'V': ret = 0x3C; break;
                        case 'W': ret = 0x27; break;
                        case 'X': ret = 0x3A; break;
                        case 'Y': ret = 0x2B; break;
                        case 'Z': ret = 0x63; break;
                    }
                    break;

                case 1: // Autoset Mode = Figures
                    switch (str)
                    {
                        case '0': ret = 0x2D; break;
                        case '1': ret = 0x2E; break;
                        case '2': ret = 0x27; break;
                        case '3': ret = 0x56; break;
                        case '4': ret = 0x55; break;
                        case '5': ret = 0x74; break;
                        case '6': ret = 0x2B; break;
                        case '7': ret = 0x4E; break;
                        case '8': ret = 0x4D; break;
                        case '9': ret = 0x71; break;
                        case '\'': ret = 0x17; break;
                        case '!': ret = 0x1B; break;
                        case ':': ret = 0x1D; break;
                        case '(': ret = 0x1E; break;
                        case '&': ret = 0x35; break;
                        case '.': ret = 0x39; break;
                        case '/': ret = 0x3A; break;
                        case '=': ret = 0x3C; break;
                        case '-': ret = 0x47; break;
                        case '$': ret = 0x53; break;
                        case ',': ret = 0x59; break;
                        case '+': ret = 0x63; break;
                        case ')': ret = 0x65; break;
                        case '#': ret = 0x69; break;
                        case '?': ret = 0x72; break;
                    }
                    break;
            }
            return ret;
        }

        private byte CCIR476_Character_Check(char str)
        {
            byte ret = (byte)CCIR_MODE_Current;

            switch (str)
            {
                case 'A': ret = 0x00; break;
                case 'B': ret = 0x00; break;
                case 'C': ret = 0x00; break;
                case 'D': ret = 0x00; break;
                case 'E': ret = 0x00; break;
                case 'F': ret = 0x00; break;
                case 'G': ret = 0x00; break;
                case 'H': ret = 0x00; break;
                case 'I': ret = 0x00; break;
                case 'J': ret = 0x00; break;
                case 'K': ret = 0x00; break;
                case 'L': ret = 0x00; break;
                case 'M': ret = 0x00; break;
                case 'N': ret = 0x00; break;
                case 'O': ret = 0x00; break;
                case 'P': ret = 0x00; break;
                case 'Q': ret = 0x00; break;
                case 'R': ret = 0x00; break;
                case 'S': ret = 0x00; break;
                case 'T': ret = 0x00; break;
                case 'U': ret = 0x00; break;
                case 'V': ret = 0x00; break;
                case 'W': ret = 0x00; break;
                case 'X': ret = 0x00; break;
                case 'Y': ret = 0x00; break;
                case 'Z': ret = 0x00; break;

                case '0': ret = 0x01; break;
                case '1': ret = 0x01; break;
                case '2': ret = 0x01; break;
                case '3': ret = 0x01; break;
                case '4': ret = 0x01; break;
                case '5': ret = 0x01; break;
                case '6': ret = 0x01; break;
                case '7': ret = 0x01; break;
                case '8': ret = 0x01; break;
                case '9': ret = 0x01; break;
                case '\'': ret = 0x01; break;
                case '!': ret = 0x01; break;
                case ':': ret = 0x01; break;
                case '(': ret = 0x01; break;
                case '&': ret = 0x01; break;
                case '.': ret = 0x01; break;
                case '/': ret = 0x01; break;
                case '=': ret = 0x01; break;
                case '-': ret = 0x01; break;
                case '$': ret = 0x01; break;
                case ',': ret = 0x01; break;
                case '+': ret = 0x01; break;
                case ')': ret = 0x01; break;
                case '#': ret = 0x01; break;
                case '?': ret = 0x01; break;
            }
            return ret;
        }

        private void Byte_to_7Bit_Add_Frequency(byte data, int MarkFreq, int SpaceFreq, int BPS)
        {
            for (int n = 0; n < 7; n++)
            {
                if ((data & 0x01) == 0x01) // Space
                {
                    m_Audio_Wave_Control.Frequency_Add(SpaceFreq, BPS);
                }
                else // Mark
                {
                    m_Audio_Wave_Control.Frequency_Add(MarkFreq, BPS);
                }
                data >>= 1;
            }
        }

        private void Byte_to_7Bit_Add_Rts(byte data)
        {
            for (int n = 0; n < 7; n++)
            {
                if ((data & 0x01) == 0x01) // Space
                {
                    m_Serial_RTS_Send_Mgr.Add_Buffer((int)Serial_FSK_t.Space);
                }
                else // Mark
                {
                    m_Serial_RTS_Send_Mgr.Add_Buffer((int)Serial_FSK_t.Mark);
                }
                data >>= 1;
            }
        }

        //\r = CR = Carriage Return
        //\n = LF = Line Feed
        //ZCZC HB88
        //211522 UTC SEP 23
        //WWJP72 RJTD 211200
        //IMPORTANT WARNING FOR MOJI
        //NAVTEX AREA
        //211200UTC ISSUED AT 211500UTC
        //DEVELOPING LOW 1000HPA AT 40N
        //143E MOV ENE 35 KT
        //C-FRONT FM 40N 143E TO 39N 140E
        //35N 137E 33N 132E 30N 129E
        //WARNING(NEAR GALE) SEA OFF NOTO
        //NXT WARNING WILL BE ISSUED
        //BEFORE 212100UTC

        //NNNN
        public void Audio_MessageSend(int MarkFreq, int SpaceFreq, int BPS, string strMsg)
        {
            int Dx_cnt = 0;
            int Rx_cnt = 0;
            int i = 0;
            byte Encode_Data = 0;
            byte Check_Data = 0;
            int Buffer_cnt = 0;
            String str = String.Empty;

            string strRxStation = string.Empty;
            string strDxStation = string.Empty;
            string strStation = string.Empty;

            CCIR_MODE_DataIn = 0xFF;
            CCIR_MODE_Current = 0xFF;

            char[] Dx_Station = new char[Constants.Start_Phasing_Tick + strMsg.Length + Constants.End_Phasing_Tick + 3 + 200];
            char[] Rx_Station = new char[Constants.Start_Phasing_Tick + strMsg.Length + Constants.End_Phasing_Tick + 2 + 3 + 200];
            char[] Buffer_Station = new char[Rx_Station.Length * 2];

            m_Audio_Wave_Control.WaveStop();
            m_Audio_Wave_Control.VoidSignal();
            for (i = 0; i < Constants.Start_Phasing_Tick; i++)
            {
                Dx_Station[Dx_cnt++] = Constants.Phasing_Signal_2;
                Rx_Station[Rx_cnt++] = Constants.Phasing_Signal_1;
            }
            Rx_Station[Rx_cnt++] = Constants.Phasing_Signal_1;
            Rx_Station[Rx_cnt++] = Constants.Phasing_Signal_1;

            char[] chars = strMsg.ToCharArray();
            for (i = 0; i < strMsg.Length; i++)
            {
                Check_Data = CCIR476_Character_Check(chars[i]);
                if (Check_Data != CCIR_MODE_DataIn)
                {
                    if (Check_Data == 0)
                    {
                        Dx_Station[Dx_cnt++] = Constants.Letters_Shift;
                        Rx_Station[Rx_cnt++] = Constants.Letters_Shift;
                        CCIR_MODE_DataIn = 0;
                    }
                    else if (Check_Data == 1)
                    {
                        Dx_Station[Dx_cnt++] = Constants.Figures_Shift;
                        Rx_Station[Rx_cnt++] = Constants.Figures_Shift;
                        CCIR_MODE_DataIn = 1;
                    }
                }

                Dx_Station[Dx_cnt++] = chars[i];
                Rx_Station[Rx_cnt++] = chars[i];
            }

            for (i = 0; i < Constants.End_Phasing_Tick; i++)
            {
                Dx_Station[Dx_cnt++] = Constants.Idle_Signal_A;
                Rx_Station[Rx_cnt++] = Constants.Idle_Signal_B;
            }

            Dx_Station[Dx_cnt++] = '\r';
            Rx_Station[Rx_cnt++] = '\r';
            Dx_Station[Dx_cnt++] = '\n';
            Rx_Station[Rx_cnt++] = '\n';
            Dx_Station[Dx_cnt++] = '\n';
            Rx_Station[Rx_cnt++] = '\n';

            for (i = 0; i < Rx_cnt; i++)
            {
                if (i <= (Rx_cnt - 3))
                {
                    // real transmit data
                    Encode_Data = CCIR476_Encode(Dx_Station[i], CCIR_MODE_Current);
                    Byte_to_7Bit_Add_Frequency(Encode_Data, MarkFreq, SpaceFreq, BPS);

                    Buffer_Station[Buffer_cnt] = Dx_Station[i];
                    Buffer_cnt += 1;

                    if (Dx_Station[i] == '\r')
                    {
                        Dx_Station[i] = 'r';
                    }
                    else if (Dx_Station[i] == '\n')
                    {
                        Dx_Station[i] = 'n';
                    }
                    strDxStation += string.Format("Dx_Station[{0}], {1} \r\n", i, Dx_Station[i]);
                    strStation += string.Format("Dx_Station[{0}], {1} \r\n", i, Dx_Station[i]);
                }

                // real transmit data
                Encode_Data = CCIR476_Encode(Rx_Station[i], CCIR_MODE_Current);
                Byte_to_7Bit_Add_Frequency(Encode_Data, MarkFreq, SpaceFreq, BPS);

                // log data
                Buffer_Station[Buffer_cnt] = Rx_Station[i];
                Buffer_cnt += 1;

                if (Rx_Station[i] == '\r')
                {
                    Rx_Station[i] = 'r';
                }
                else if (Rx_Station[i] == '\n')
                {
                    Rx_Station[i] = 'n';
                }
                strRxStation += string.Format("Rx_Station[{0}], {1} \r\n", i, Rx_Station[i]);
                strStation += string.Format("Rx_Station[{0}], {1} \r\n", i, Rx_Station[i]);
            }

            //Fsk_Setting.Setting.strBuffer = strDxStation;
            //Fsk_Setting.DX_Station_Buffer_Write();

            //Fsk_Setting.Setting.strBuffer = strRxStation;
            //Fsk_Setting.RX_Station_Buffer_Write();

            //Fsk_Setting.Setting.strBuffer = strStation;
            //Fsk_Setting.Station_Buffer_Write();

            m_Audio_Wave_Control.WavePlay();
        }
        private void Audio_MessageSend_repeat_start(int num, int MarkFreq, int SpaceFreq, int BPS, string strMsg)
        {
            int Dx_cnt = 0;
            int Rx_cnt = 0;
            int i = 0;
            byte Encode_Data = 0;
            byte Check_Data = 0;
            String str = String.Empty;

            CCIR_MODE_DataIn = 0xFF;
            CCIR_MODE_Current = 0xFF;

            char[] Dx_Station = new char[Constants.ReStart_Phasing_Tick + strMsg.Length + 3 + 200];
            char[] Rx_Station = new char[Constants.ReStart_Phasing_Tick + strMsg.Length + 2 + 3 + 200];

            for (i = 0; i < Constants.ReStart_Phasing_Tick; i++)
            {
                Dx_Station[Dx_cnt++] = Constants.Phasing_Signal_2;
                Rx_Station[Rx_cnt++] = Constants.Phasing_Signal_1;
            }
            Rx_Station[Rx_cnt++] = Constants.Phasing_Signal_1;
            Rx_Station[Rx_cnt++] = Constants.Phasing_Signal_1;

            char[] chars = strMsg.ToCharArray();
            chars[7] = (char)((num / 10) + '0');
            chars[8] = (char)((num % 10) + '0');
            for (i = 0; i < strMsg.Length; i++)
            {
                Check_Data = CCIR476_Character_Check(chars[i]);
                if (Check_Data != CCIR_MODE_DataIn)
                {
                    if (Check_Data == 0)
                    {
                        Dx_Station[Dx_cnt++] = Constants.Letters_Shift;
                        Rx_Station[Rx_cnt++] = Constants.Letters_Shift;
                        CCIR_MODE_DataIn = 0;
                    }
                    else if (Check_Data == 1)
                    {
                        Dx_Station[Dx_cnt++] = Constants.Figures_Shift;
                        Rx_Station[Rx_cnt++] = Constants.Figures_Shift;
                        CCIR_MODE_DataIn = 1;
                    }
                }

                Dx_Station[Dx_cnt++] = chars[i];
                Rx_Station[Rx_cnt++] = chars[i];
            }

            Dx_Station[Dx_cnt++] = '\r';
            Rx_Station[Rx_cnt++] = '\r';
            Dx_Station[Dx_cnt++] = '\n';
            Rx_Station[Rx_cnt++] = '\n';
            Dx_Station[Dx_cnt++] = '\n';
            Rx_Station[Rx_cnt++] = '\n';

            for (i = 0; i < Rx_cnt; i++)
            {
                if (i <= (Rx_cnt - 3))
                {
                    // real transmit data
                    Encode_Data = CCIR476_Encode(Dx_Station[i], CCIR_MODE_Current);
                    Byte_to_7Bit_Add_Frequency(Encode_Data, MarkFreq, SpaceFreq, BPS);
                }

                // real transmit data
                Encode_Data = CCIR476_Encode(Rx_Station[i], CCIR_MODE_Current);
                Byte_to_7Bit_Add_Frequency(Encode_Data, MarkFreq, SpaceFreq, BPS);
            }
        }

        private void Audio_MessageSend_repeat_end(int num, int MarkFreq, int SpaceFreq, int BPS, string strMsg)
        {
            int Dx_cnt = 0;
            int Rx_cnt = 0;
            int i = 0;
            byte Encode_Data = 0;
            byte Check_Data = 0;
            String str = String.Empty;

            CCIR_MODE_DataIn = 0xFF;
            CCIR_MODE_Current = 0xFF;

            char[] Dx_Station = new char[Constants.ReStart_Phasing_Tick + Constants.End_Phasing_Tick + strMsg.Length + 3 + 200];
            char[] Rx_Station = new char[Constants.ReStart_Phasing_Tick + Constants.End_Phasing_Tick + strMsg.Length + 2 + 3 + 200];

            for (i = 0; i < Constants.ReStart_Phasing_Tick; i++)
            {
                Dx_Station[Dx_cnt++] = Constants.Phasing_Signal_2;
                Rx_Station[Rx_cnt++] = Constants.Phasing_Signal_1;
            }
            Rx_Station[Rx_cnt++] = Constants.Phasing_Signal_1;
            Rx_Station[Rx_cnt++] = Constants.Phasing_Signal_1;

            char[] chars = strMsg.ToCharArray();
            chars[7] = (char)((num / 10) + '0');
            chars[8] = (char)((num % 10) + '0');
            for (i = 0; i < strMsg.Length; i++)
            {
                Check_Data = CCIR476_Character_Check(chars[i]);
                if (Check_Data != CCIR_MODE_DataIn)
                {
                    if (Check_Data == 0)
                    {
                        Dx_Station[Dx_cnt++] = Constants.Letters_Shift;
                        Rx_Station[Rx_cnt++] = Constants.Letters_Shift;
                        CCIR_MODE_DataIn = 0;
                    }
                    else if (Check_Data == 1)
                    {
                        Dx_Station[Dx_cnt++] = Constants.Figures_Shift;
                        Rx_Station[Rx_cnt++] = Constants.Figures_Shift;
                        CCIR_MODE_DataIn = 1;
                    }
                }

                Dx_Station[Dx_cnt++] = chars[i];
                Rx_Station[Rx_cnt++] = chars[i];
            }

            Dx_Station[Dx_cnt++] = '\r';
            Rx_Station[Rx_cnt++] = '\r';
            Dx_Station[Dx_cnt++] = '\n';
            Rx_Station[Rx_cnt++] = '\n';
            Dx_Station[Dx_cnt++] = '\n';
            Rx_Station[Rx_cnt++] = '\n';

            for (i = 0; i < Constants.End_Phasing_Tick; i++)
            {
                Dx_Station[Dx_cnt++] = Constants.Idle_Signal_A;
                Rx_Station[Rx_cnt++] = Constants.Idle_Signal_B;
            }

            for (i = 0; i < Rx_cnt; i++)
            {
                if (i <= (Rx_cnt - 3))
                {
                    // real transmit data
                    Encode_Data = CCIR476_Encode(Dx_Station[i], CCIR_MODE_Current);
                    Byte_to_7Bit_Add_Frequency(Encode_Data, MarkFreq, SpaceFreq, BPS);
                }

                // real transmit data
                Encode_Data = CCIR476_Encode(Rx_Station[i], CCIR_MODE_Current);
                Byte_to_7Bit_Add_Frequency(Encode_Data, MarkFreq, SpaceFreq, BPS);
            }
        }
        public void Audio_MessageSend_repeat(int MarkFreq, int SpaceFreq, int BPS, int count, string strMsg)
        {
            int i = 0;

            m_Audio_Wave_Control.WaveStop();
            m_Audio_Wave_Control.VoidSignal();

            for (i = 0; i < count-1; i++)
            {
                Audio_MessageSend_repeat_start(i, MarkFreq, SpaceFreq, BPS, strMsg);
            }
            Audio_MessageSend_repeat_end(i, MarkFreq, SpaceFreq, BPS, strMsg);

            m_Audio_Wave_Control.WavePlay();
        }

        public void Serial_MessageSend(int BPS, string strMsg)
        {
            int Dx_cnt = 0;
            int Rx_cnt = 0;
            int i = 0;
            byte Encode_Data = 0;
            byte Check_Data = 0;
            int Buffer_cnt = 0;
            String str = String.Empty;

            string strRxStation = string.Empty;
            string strDxStation = string.Empty;
            string strStation = string.Empty;

            CCIR_MODE_DataIn = 0xFF;
            CCIR_MODE_Current = 0xFF;

            char[] Dx_Station = new char[Constants.Start_Phasing_Tick + strMsg.Length + Constants.End_Phasing_Tick + 3 + 200];
            char[] Rx_Station = new char[Constants.Start_Phasing_Tick + strMsg.Length + Constants.End_Phasing_Tick + 2 + 3 + 200];
            char[] Buffer_Station = new char[Rx_Station.Length * 2];

            m_Serial_RTS_Send_Mgr.Clear_Buffer();
            for (i = 0; i < Constants.Start_Phasing_Tick; i++)
            {
                Dx_Station[Dx_cnt++] = Constants.Phasing_Signal_2;
                Rx_Station[Rx_cnt++] = Constants.Phasing_Signal_1;
            }
            Rx_Station[Rx_cnt++] = Constants.Phasing_Signal_1;
            Rx_Station[Rx_cnt++] = Constants.Phasing_Signal_1;

            char[] chars = strMsg.ToCharArray();
            for (i = 0; i < strMsg.Length; i++)
            {
                Check_Data = CCIR476_Character_Check(chars[i]);
                if (Check_Data != CCIR_MODE_DataIn)
                {
                    if (Check_Data == 0)
                    {
                        Dx_Station[Dx_cnt++] = Constants.Letters_Shift;
                        Rx_Station[Rx_cnt++] = Constants.Letters_Shift;
                        CCIR_MODE_DataIn = 0;
                    }
                    else if (Check_Data == 1)
                    {
                        Dx_Station[Dx_cnt++] = Constants.Figures_Shift;
                        Rx_Station[Rx_cnt++] = Constants.Figures_Shift;
                        CCIR_MODE_DataIn = 1;
                    }
                }

                Dx_Station[Dx_cnt++] = chars[i];
                Rx_Station[Rx_cnt++] = chars[i];
            }

            for (i = 0; i < Constants.End_Phasing_Tick; i++)
            {
                Dx_Station[Dx_cnt++] = Constants.Idle_Signal_A;
                Rx_Station[Rx_cnt++] = Constants.Idle_Signal_B;
            }

            Dx_Station[Dx_cnt++] = '\r';
            Rx_Station[Rx_cnt++] = '\r';
            Dx_Station[Dx_cnt++] = '\n';
            Rx_Station[Rx_cnt++] = '\n';
            Dx_Station[Dx_cnt++] = '\n';
            Rx_Station[Rx_cnt++] = '\n';

            for (i = 0; i < Rx_cnt; i++)
            {
                if (i <= (Rx_cnt - 3))
                {
                    // real transmit data
                    Encode_Data = CCIR476_Encode(Dx_Station[i], CCIR_MODE_Current);
                    Byte_to_7Bit_Add_Rts(Encode_Data);

                    Buffer_Station[Buffer_cnt] = Dx_Station[i];
                    Buffer_cnt += 1;

                    if (Dx_Station[i] == '\r')
                    {
                        Dx_Station[i] = 'r';
                    }
                    else if (Dx_Station[i] == '\n')
                    {
                        Dx_Station[i] = 'n';
                    }
                    strDxStation += string.Format("Dx_Station[{0}], {1} \r\n", i, Dx_Station[i]);
                    strStation += string.Format("Dx_Station[{0}], {1} \r\n", i, Dx_Station[i]);
                }

                // real transmit data
                Encode_Data = CCIR476_Encode(Rx_Station[i], CCIR_MODE_Current);
                Byte_to_7Bit_Add_Rts(Encode_Data);

                // log data
                Buffer_Station[Buffer_cnt] = Rx_Station[i];
                Buffer_cnt += 1;

                if (Rx_Station[i] == '\r')
                {
                    Rx_Station[i] = 'r';
                }
                else if (Rx_Station[i] == '\n')
                {
                    Rx_Station[i] = 'n';
                }
                strRxStation += string.Format("Rx_Station[{0}], {1} \r\n", i, Rx_Station[i]);
                strStation += string.Format("Rx_Station[{0}], {1} \r\n", i, Rx_Station[i]);
            }

            //Fsk_Setting.Setting.strBuffer = strDxStation;
            //Fsk_Setting.DX_Station_Buffer_Write();

            //Fsk_Setting.Setting.strBuffer = strRxStation;
            //Fsk_Setting.RX_Station_Buffer_Write();

            //Fsk_Setting.Setting.strBuffer = strStation;
            //Fsk_Setting.Station_Buffer_Write();

            m_Serial_RTS_Send_Mgr.Send_Message();
        }

        private void Serial_MessageSend_repeat_start(int num, int BPS, string strMsg)
        {
            int Dx_cnt = 0;
            int Rx_cnt = 0;
            int i = 0;
            byte Encode_Data = 0;
            byte Check_Data = 0;
            String str = String.Empty;

            CCIR_MODE_DataIn = 0xFF;
            CCIR_MODE_Current = 0xFF;

            char[] Dx_Station = new char[Constants.ReStart_Phasing_Tick + strMsg.Length + 3 + 200];
            char[] Rx_Station = new char[Constants.ReStart_Phasing_Tick + strMsg.Length + 2 + 3 + 200];

            for (i = 0; i < Constants.ReStart_Phasing_Tick; i++)
            {
                Dx_Station[Dx_cnt++] = Constants.Phasing_Signal_2;
                Rx_Station[Rx_cnt++] = Constants.Phasing_Signal_1;
            }
            Rx_Station[Rx_cnt++] = Constants.Phasing_Signal_1;
            Rx_Station[Rx_cnt++] = Constants.Phasing_Signal_1;

            char[] chars = strMsg.ToCharArray();
            chars[7] = (char)((num / 10) + '0');
            chars[8] = (char)((num % 10) + '0');
            for (i = 0; i < strMsg.Length; i++)
            {
                Check_Data = CCIR476_Character_Check(chars[i]);
                if (Check_Data != CCIR_MODE_DataIn)
                {
                    if (Check_Data == 0)
                    {
                        Dx_Station[Dx_cnt++] = Constants.Letters_Shift;
                        Rx_Station[Rx_cnt++] = Constants.Letters_Shift;
                        CCIR_MODE_DataIn = 0;
                    }
                    else if (Check_Data == 1)
                    {
                        Dx_Station[Dx_cnt++] = Constants.Figures_Shift;
                        Rx_Station[Rx_cnt++] = Constants.Figures_Shift;
                        CCIR_MODE_DataIn = 1;
                    }
                }

                Dx_Station[Dx_cnt++] = chars[i];
                Rx_Station[Rx_cnt++] = chars[i];
            }

            Dx_Station[Dx_cnt++] = '\r';
            Rx_Station[Rx_cnt++] = '\r';
            Dx_Station[Dx_cnt++] = '\n';
            Rx_Station[Rx_cnt++] = '\n';
            Dx_Station[Dx_cnt++] = '\n';
            Rx_Station[Rx_cnt++] = '\n';

            for (i = 0; i < Rx_cnt; i++)
            {
                if (i <= (Rx_cnt - 3))
                {
                    // real transmit data
                    Encode_Data = CCIR476_Encode(Dx_Station[i], CCIR_MODE_Current);
                    Byte_to_7Bit_Add_Rts(Encode_Data);
                }

                // real transmit data
                Encode_Data = CCIR476_Encode(Rx_Station[i], CCIR_MODE_Current);
                Byte_to_7Bit_Add_Rts(Encode_Data);
            }
        }

        private void Serial_MessageSend_repeat_end(int num, int BPS, string strMsg)
        {
            int Dx_cnt = 0;
            int Rx_cnt = 0;
            int i = 0;
            byte Encode_Data = 0;
            byte Check_Data = 0;
            String str = String.Empty;

            CCIR_MODE_DataIn = 0xFF;
            CCIR_MODE_Current = 0xFF;

            char[] Dx_Station = new char[Constants.ReStart_Phasing_Tick + Constants.End_Phasing_Tick + strMsg.Length + 3 + 200];
            char[] Rx_Station = new char[Constants.ReStart_Phasing_Tick + Constants.End_Phasing_Tick + strMsg.Length + 2 + 3 + 200];

            for (i = 0; i < Constants.ReStart_Phasing_Tick; i++)
            {
                Dx_Station[Dx_cnt++] = Constants.Phasing_Signal_2;
                Rx_Station[Rx_cnt++] = Constants.Phasing_Signal_1;
            }
            Rx_Station[Rx_cnt++] = Constants.Phasing_Signal_1;
            Rx_Station[Rx_cnt++] = Constants.Phasing_Signal_1;

            char[] chars = strMsg.ToCharArray();
            chars[7] = (char)((num / 10) + '0');
            chars[8] = (char)((num % 10) + '0');
            for (i = 0; i < strMsg.Length; i++)
            {
                Check_Data = CCIR476_Character_Check(chars[i]);
                if (Check_Data != CCIR_MODE_DataIn)
                {
                    if (Check_Data == 0)
                    {
                        Dx_Station[Dx_cnt++] = Constants.Letters_Shift;
                        Rx_Station[Rx_cnt++] = Constants.Letters_Shift;
                        CCIR_MODE_DataIn = 0;
                    }
                    else if (Check_Data == 1)
                    {
                        Dx_Station[Dx_cnt++] = Constants.Figures_Shift;
                        Rx_Station[Rx_cnt++] = Constants.Figures_Shift;
                        CCIR_MODE_DataIn = 1;
                    }
                }

                Dx_Station[Dx_cnt++] = chars[i];
                Rx_Station[Rx_cnt++] = chars[i];
            }

            Dx_Station[Dx_cnt++] = '\r';
            Rx_Station[Rx_cnt++] = '\r';
            Dx_Station[Dx_cnt++] = '\n';
            Rx_Station[Rx_cnt++] = '\n';
            Dx_Station[Dx_cnt++] = '\n';
            Rx_Station[Rx_cnt++] = '\n';

            for (i = 0; i < Constants.End_Phasing_Tick; i++)
            {
                Dx_Station[Dx_cnt++] = Constants.Idle_Signal_A;
                Rx_Station[Rx_cnt++] = Constants.Idle_Signal_B;
            }

            for (i = 0; i < Rx_cnt; i++)
            {
                if (i <= (Rx_cnt - 3))
                {
                    // real transmit data
                    Encode_Data = CCIR476_Encode(Dx_Station[i], CCIR_MODE_Current);
                    Byte_to_7Bit_Add_Rts(Encode_Data);
                }

                // real transmit data
                Encode_Data = CCIR476_Encode(Rx_Station[i], CCIR_MODE_Current);
                Byte_to_7Bit_Add_Rts(Encode_Data);
            }
        }

        public void Serial_MessageSend_repeat(int BPS, int count, string strMsg)
        {
            int i = 0;

            m_Serial_RTS_Send_Mgr.Clear_Buffer();
            for (i = 0; i < count-1; i++)
            {
                Serial_MessageSend_repeat_start(i, BPS, strMsg);
            }
            Serial_MessageSend_repeat_end(i, BPS, strMsg);

            m_Serial_RTS_Send_Mgr.Send_Message();
        }
    }
}
