﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.IO.Ports;

namespace AssistPro.vhf
{
    internal class SerialRmtProtocol
    {
        private const int FRAME_LEN_SIZE = 2;
        private const int FRAME_SEQ_NO_SIZE = 2;
        private const int FRAME_CMD1_SIZE = 2;
        private const int FRAME_CMD2_SIZE = 2;

        public const int PAYLOAD_OFFSET = 9;
        public const int FRAME_CRC_SIZE = 2;

        public const int FRAME_HEADER_SIZE = FRAME_LEN_SIZE + FRAME_SEQ_NO_SIZE + FRAME_CMD1_SIZE + FRAME_CMD2_SIZE;
        public const int MAX_FRAME_LEN = 300;


        public enum ERROR
        {
            ERR_OK = 0,
            ERR_SFD = -1,
            ERR_LEN = -2,
            ERR_CRC = -3,
        }


        public enum PROTO_STAT
        {
            IDLE_STAT,
            DATA_BAD,
            DATA_OK,
            WAIT_FOR_ACK,
            WAIT_FOR_RESPONSE,
        }

        // CMD TYPE
        public const byte CMD_REQ = 0x00;
        public const byte CMD_RSP = 0x01;
        public const byte CMD_SET_NOTI = 0x02;
        public const byte CMD_ACK = 0x03;

        // TARGET TYPE
        public const byte TRANSCEIVER = 0x00;
        public const byte CONTROLLER = 0x01;
        public const byte REMOTE = 0x02; // REMOTE HANDSET
        public const byte ALARM_BOX = 0x03;
        public const byte REMOTE_SERIAL = 0x04;
        public const byte BLOADCAST = 0x04;


        // COMMAND PARAM
        public const byte FRAMEINFO = 3;
        public const byte FRM_SFD = 0x24;

        // COMMAND
        public const byte RMT_CTRL_CH_SET = 0x01;
        public const byte RMT_CTRL_VOL_SET = 0x02;
        public const byte RMT_CTRL_SQL_SET = 0x03;

        public const byte RMT_CTRL_PTT_SET = 0x04;
        public const byte RMT_CTRL_PWR_SET = 0x05;

        // TEST CONTROL
        public const byte RMT_CTRL_SERVICE_SET = 0xE0;
        
        public struct DataFrame
        {
            public byte RxFrameIdx;
            public ushort SFD;
            public ushort Length;
            public ushort SeqNum;
            public ushort CMD;
            public ushort SubCmd;
            public byte[] data_buf;
            public byte CRC_L;
            public byte CRC_H;
            public ERROR ErrorStat;
        }

        public struct RcvFrame
        {
            public bool bReceiving;
            public uint rcv_cnt;
            public ushort frame_len;
            public ushort seq_no;
            public byte[] rx_data;
        }
        private byte[] m_ComTxBuf;
        private ushort m_SeqNum;
        private int m_DataIdx;
        private SerialPort m_SerialPort;
        public PROTO_STAT m_protocol_status;
        public DataFrame m_DataFrame;
        public RcvFrame m_RcvFrame; // serial raw data;

        private SerialManager m_SerialManager;
        private void AllocateData()
        {
        }

        public SerialRmtProtocol(SerialPort port)
        {
            m_ComTxBuf = new byte[1024];
            m_DataFrame.data_buf = new byte[256];
            m_DataFrame.SeqNum = 0;

            m_RcvFrame.rx_data = new byte[MAX_FRAME_LEN + FRAME_CRC_SIZE];
            m_SerialPort = port;

            AllocateData();
            Init();
        }

        public void SendFullFrameData(byte[] frame, byte length)
        {
            int idx = 5;
            ushort crc16;

            int len = length / 2 + 1;

            m_ComTxBuf[0] = frame[0];
            m_ComTxBuf[1] = frame[1];
            m_ComTxBuf[2] = frame[2];
            m_ComTxBuf[3] = frame[3];
            m_ComTxBuf[4] = frame[4];

            for (byte i = 5; i < len; i++)
            {
                m_ComTxBuf[i + PAYLOAD_OFFSET] = frame[i];
                idx++;
            }

            crc16 = crc16_calc(0xFFFF, m_ComTxBuf, (uint)(idx - 1));

            m_ComTxBuf[idx++] = (byte)(crc16);
            m_ComTxBuf[idx++] = (byte)(crc16 >> 8);

            m_SerialPort.Write(m_ComTxBuf, 0, idx);
        }

        //Target type
        // 0 : Transceiver
        // 1 : Controller
        // 2 : Alarm unit
        // 3 : Alarm box
        // 4 : All

        //Command type
        // 0 : Request
        // 1 : Response
        // 2 : Set or Noti
        // 3 : Ack

        public ushort GenerateCMD(byte cmd_type, byte sender, byte receiver)
        {
            ushort cmd;

            cmd = (ushort)((cmd_type << 6) | (sender << 3) | (receiver));

            return cmd;
        }

        public void ResetReceiveFrame()
        {
            m_RcvFrame.bReceiving = false;
            m_RcvFrame.frame_len = 0;
            m_RcvFrame.rcv_cnt = 0;
            m_RcvFrame.seq_no = 0;
            Array.Clear(m_RcvFrame.rx_data, 0, m_RcvFrame.rx_data.Length);
        }

        public void Init()
        {
            m_SeqNum = 0;
            m_protocol_status = PROTO_STAT.IDLE_STAT;

            ResetReceiveFrame();
        }


        public PROTO_STAT ProcCommandHandler()
        {
            byte cmd_type = (byte)((m_DataFrame.CMD & 0xC0) >> 6);
            ushort cmd_param = m_DataFrame.SubCmd;

            switch (cmd_type)
            {
                case DataProtocol.CMD_ACK:
                     break;
                case DataProtocol.CMD_RSP:
                    break;
                case DataProtocol.CMD_SET_NOTI:
                    break;

                default:
                    break;

            }
            m_protocol_status = PROTO_STAT.IDLE_STAT;
            return m_protocol_status;
        }
        
        public void SetProtocoStatus(PROTO_STAT status)
        {
            m_protocol_status = status;
        }

        public void SendDataFrame(ushort cmd, ushort param, byte[] payload, int length)
        {
            ushort crc16;
            ushort len;

            m_DataIdx = PAYLOAD_OFFSET;

            len = (ushort)(length + (4 * 2));

            m_ComTxBuf[0] = DataProtocol.FRM_SFD;

            //frame length 4*2byte (length(2), Seq Num(2), Cmd(2), Sub Cmd(2))
            m_ComTxBuf[1] = (byte)(len & 0xFF);        
            m_ComTxBuf[2] = (byte)((len >> 8) & 0xFF); 


            m_ComTxBuf[3] = (byte)(m_SeqNum & 0xFF);       
            m_ComTxBuf[4] = (byte)((m_SeqNum >> 8) & 0xFF);

            m_ComTxBuf[5] = (byte)(cmd & 0xFF);
            m_ComTxBuf[6] = (byte)((cmd >> 8) & 0xFF);

            //m_ComTxBuf[3] = cmd;

            if (param != 0xFFFF)
            {
                m_ComTxBuf[7] = (byte)(param & 0xFF);       // 하위 8비트
                m_ComTxBuf[8] = (byte)((param >> 8) & 0xFF); // 상위 8비트

                //m_ComTxBuf[4] = param;
            }
            else
            {
                m_ComTxBuf[7] = 0; // 하위 8비트
                m_ComTxBuf[8] = 0; // 상위 8비트
                //m_ComTxBuf[4] = 0;
            }

            for (ushort i = 0; i < length; i++)
            {
                m_ComTxBuf[i + PAYLOAD_OFFSET] = payload[i];
                m_DataIdx++;
            }

            crc16 = crc16_calc(0xFFFF, m_ComTxBuf, (uint)(m_DataIdx - 1));

            m_ComTxBuf[m_DataIdx++] = (byte)(crc16);
            m_ComTxBuf[m_DataIdx++] = (byte)(crc16 >> 8);

            m_SerialPort.Write(m_ComTxBuf, 0, m_DataIdx);

            m_SeqNum++;
        }

        public ERROR ParseCommand(byte[] buffer, ushort frame_len, uint rcv_cnt)
        {
            ushort calc_CRC16;
            ushort rcv_CRC16;

            m_DataFrame.SFD = buffer[0];
            if (m_DataFrame.SFD != FRM_SFD)
            {
                return ERROR.ERR_SFD;
            }

            m_DataFrame.Length = (ushort)((buffer[2] << 8) | buffer[1]);
            if (m_DataFrame.Length > frame_len + 3) // SFD(1), CRC(2)
            {
                return ERROR.ERR_LEN;
            }

            calc_CRC16 = crc16_calc(0xFFFF, buffer, frame_len);
            rcv_CRC16 = (ushort)((buffer[rcv_cnt - 1] << 8) | buffer[rcv_cnt - 2]);

            if (calc_CRC16 != rcv_CRC16)
            {
                return ERROR.ERR_CRC;
            }

            m_DataFrame.SeqNum = (ushort)((buffer[4] << 8) | buffer[3]);
            m_DataFrame.CMD = (ushort)((buffer[6] << 8) | buffer[5]);
            m_DataFrame.SubCmd = (ushort)((buffer[8] << 8) | buffer[7]);
            m_DataFrame.CRC_L = buffer[frame_len - 2];
            m_DataFrame.CRC_H = buffer[frame_len - 1];

            if (m_DataFrame.Length > 8)
            {
                Array.Copy(buffer, PAYLOAD_OFFSET, m_DataFrame.data_buf, 0, m_DataFrame.Length - 8);
            }
            m_DataFrame.RxFrameIdx++;

            return ERROR.ERR_OK;
        }

        private  ushort crc16_calc(ushort init_crc, byte[] p, uint len)
        {
            byte[] data = p;
            ushort crc = init_crc;

            for (int i = 1; i <= len; i++) // except for SFD
            {
                crc = (ushort)((crc >> 8) | (crc << 8));
                crc ^= data[i];
                crc ^= (ushort)((crc & 0xFF) >> 4);
                crc ^= (ushort)((crc << 8) << 4);
                crc ^= (ushort)(((crc & 0xFF) << 4) << 1);
            }

            return crc;
        }
    }
}
