﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>Microsoft.Win32.Registry</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Win32.Registry">
      <summary>Provides <see cref="T:Microsoft.Win32.RegistryKey" /> objects that represent the root keys in the Windows registry, and static methods to access key/value pairs.</summary>
    </member>
    <member name="F:Microsoft.Win32.Registry.ClassesRoot">
      <summary>Defines the types (or classes) of documents and the properties associated with those types. This field reads the Windows registry base key HKEY_CLASSES_ROOT.</summary>
    </member>
    <member name="F:Microsoft.Win32.Registry.CurrentConfig">
      <summary>Contains configuration information pertaining to the hardware that is not specific to the user. This field reads the Windows registry base key HKEY_CURRENT_CONFIG.</summary>
    </member>
    <member name="F:Microsoft.Win32.Registry.CurrentUser">
      <summary>Contains information about the current user preferences. This field reads the Windows registry base key HKEY_CURRENT_USER </summary>
    </member>
    <member name="M:Microsoft.Win32.Registry.GetValue(System.String,System.String,System.Object)">
      <summary>Retrieves the value associated with the specified name, in the specified registry key. If the name is not found in the specified key, returns a default value that you provide, or null if the specified key does not exist. </summary>
      <returns>null if the subkey specified by <paramref name="keyName" /> does not exist; otherwise, the value associated with <paramref name="valueName" />, or <paramref name="defaultValue" /> if <paramref name="valueName" /> is not found.</returns>
      <param name="keyName">The full registry path of the key, beginning with a valid registry root, such as "HKEY_CURRENT_USER".</param>
      <param name="valueName">The name of the name/value pair.</param>
      <param name="defaultValue">The value to return if <paramref name="valueName" /> does not exist.</param>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to read from the registry key. </exception>
      <exception cref="T:System.IO.IOException">The <see cref="T:Microsoft.Win32.RegistryKey" /> that contains the specified value has been marked for deletion. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="keyName" /> does not begin with a valid registry root. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.RegistryPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Read="\" />
      </PermissionSet>
    </member>
    <member name="F:Microsoft.Win32.Registry.LocalMachine">
      <summary>Contains the configuration data for the local machine. This field reads the Windows registry base key HKEY_LOCAL_MACHINE.</summary>
    </member>
    <member name="F:Microsoft.Win32.Registry.PerformanceData">
      <summary>Contains performance information for software components. This field reads the Windows registry base key HKEY_PERFORMANCE_DATA.</summary>
    </member>
    <member name="M:Microsoft.Win32.Registry.SetValue(System.String,System.String,System.Object)">
      <summary>Sets the specified name/value pair on the specified registry key. If the specified key does not exist, it is created.</summary>
      <param name="keyName">The full registry path of the key, beginning with a valid registry root, such as "HKEY_CURRENT_USER".</param>
      <param name="valueName">The name of the name/value pair.</param>
      <param name="value">The value to be stored.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="keyName" /> does not begin with a valid registry root. -or-<paramref name="keyName" /> is longer than the maximum length allowed (255 characters).</exception>
      <exception cref="T:System.UnauthorizedAccessException">The <see cref="T:Microsoft.Win32.RegistryKey" /> is read-only, and thus cannot be written to; for example, it is a root-level node. </exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to create or modify registry keys. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.RegistryPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.Win32.Registry.SetValue(System.String,System.String,System.Object,Microsoft.Win32.RegistryValueKind)">
      <summary>Sets the name/value pair on the specified registry key, using the specified registry data type. If the specified key does not exist, it is created.</summary>
      <param name="keyName">The full registry path of the key, beginning with a valid registry root, such as "HKEY_CURRENT_USER".</param>
      <param name="valueName">The name of the name/value pair.</param>
      <param name="value">The value to be stored.</param>
      <param name="valueKind">The registry data type to use when storing the data.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="keyName" /> does not begin with a valid registry root.-or-<paramref name="keyName" /> is longer than the maximum length allowed (255 characters).-or- The type of <paramref name="value" /> did not match the registry data type specified by <paramref name="valueKind" />, therefore the data could not be converted properly. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The <see cref="T:Microsoft.Win32.RegistryKey" /> is read-only, and thus cannot be written to; for example, it is a root-level node, or the key has not been opened with write access. </exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to create or modify registry keys. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.RegistryPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="F:Microsoft.Win32.Registry.Users">
      <summary>Contains information about the default user configuration. This field reads the Windows registry base key HKEY_USERS.</summary>
    </member>
    <member name="T:Microsoft.Win32.RegistryHive">
      <summary>Represents the possible values for a top-level node on a foreign machine.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryHive.ClassesRoot">
      <summary>Represents the HKEY_CLASSES_ROOT base key on another computer. This value can be passed to the <see cref="M:Microsoft.Win32.RegistryKey.OpenRemoteBaseKey(Microsoft.Win32.RegistryHive,System.String)" /> method, to open this node remotely.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryHive.CurrentConfig">
      <summary>Represents the HKEY_CURRENT_CONFIG base key on another computer. This value can be passed to the <see cref="M:Microsoft.Win32.RegistryKey.OpenRemoteBaseKey(Microsoft.Win32.RegistryHive,System.String)" /> method, to open this node remotely.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryHive.CurrentUser">
      <summary>Represents the HKEY_CURRENT_USER base key on another computer. This value can be passed to the <see cref="M:Microsoft.Win32.RegistryKey.OpenRemoteBaseKey(Microsoft.Win32.RegistryHive,System.String)" /> method, to open this node remotely.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryHive.LocalMachine">
      <summary>Represents the HKEY_LOCAL_MACHINE base key on another computer. This value can be passed to the <see cref="M:Microsoft.Win32.RegistryKey.OpenRemoteBaseKey(Microsoft.Win32.RegistryHive,System.String)" /> method, to open this node remotely.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryHive.PerformanceData">
      <summary>Represents the HKEY_PERFORMANCE_DATA base key on another computer. This value can be passed to the <see cref="M:Microsoft.Win32.RegistryKey.OpenRemoteBaseKey(Microsoft.Win32.RegistryHive,System.String)" /> method, to open this node remotely.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryHive.Users">
      <summary>Represents the HKEY_USERS base key on another computer. This value can be passed to the <see cref="M:Microsoft.Win32.RegistryKey.OpenRemoteBaseKey(Microsoft.Win32.RegistryHive,System.String)" /> method, to open this node remotely.</summary>
    </member>
    <member name="T:Microsoft.Win32.RegistryKey">
      <summary>Represents a key-level node in the Windows registry. This class is a registry encapsulation.</summary>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.CreateSubKey(System.String)">
      <summary>Creates a new subkey or opens an existing subkey for write access.  </summary>
      <returns>The newly created subkey, or null if the operation failed. If a zero-length string is specified for <paramref name="subkey" />, the current <see cref="T:Microsoft.Win32.RegistryKey" /> object is returned.</returns>
      <param name="subkey">The name or path of the subkey to create or open. This string is not case-sensitive.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="subkey" /> is null. </exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to create or open the registry key. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> on which this method is being invoked is closed (closed keys cannot be accessed). </exception>
      <exception cref="T:System.UnauthorizedAccessException">The <see cref="T:Microsoft.Win32.RegistryKey" /> cannot be written to; for example, it was not opened as a writable key , or the user does not have the necessary access rights. </exception>
      <exception cref="T:System.IO.IOException">The nesting level exceeds 510.-or-A system error occurred, such as deletion of the key, or an attempt to create a key in the <see cref="F:Microsoft.Win32.Registry.LocalMachine" /> root.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.RegistryPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.CreateSubKey(System.String,System.Boolean)">
      <summary>Creates a new subkey or opens an existing subkey with the specified access. Available starting in .NET Framework 2015</summary>
      <returns>The newly created subkey, or null if the operation failed. If a zero-length string is specified for <paramref name="subkey" />, the current <see cref="T:Microsoft.Win32.RegistryKey" /> object is returned.</returns>
      <param name="subkey">The name or path of the subkey to create or open. This string is not case-sensitive.</param>
      <param name="writable">true to indicate the new subkey is writable; otherwise, false.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="subkey" /> is null. </exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to create or open the registry key. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The current <see cref="T:Microsoft.Win32.RegistryKey" /> cannot be written to; for example, it was not opened as a writable key, or the user does not have the necessary access rights.</exception>
      <exception cref="T:System.IO.IOException">The nesting level exceeds 510.-or-A system error occurred, such as deletion of the key, or an attempt to create a key in the <see cref="F:Microsoft.Win32.Registry.LocalMachine" /> root.</exception>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.CreateSubKey(System.String,System.Boolean,Microsoft.Win32.RegistryOptions)">
      <summary>Creates a new subkey or opens an existing subkey with the specified access. Available starting in .NET Framework 2015</summary>
      <returns>The newly created subkey, or null if the operation failed. If a zero-length string is specified for <paramref name="subkey" />, the current <see cref="T:Microsoft.Win32.RegistryKey" /> object is returned.</returns>
      <param name="subkey">The name or path of the subkey to create or open. This string is not case-sensitive.</param>
      <param name="writable">true to indicate the new subkey is writable; otherwise, false.</param>
      <param name="options">The registry option to use.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="subkey" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="options" /> does not specify a valid Option</exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to create or open the registry key. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The current <see cref="T:Microsoft.Win32.RegistryKey" /> cannot be written to; for example, it was not opened as a writable key, or the user does not have the necessary access rights.</exception>
      <exception cref="T:System.IO.IOException">The nesting level exceeds 510.-or-A system error occurred, such as deletion of the key, or an attempt to create a key in the <see cref="F:Microsoft.Win32.Registry.LocalMachine" /> root.</exception>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.DeleteSubKey(System.String)">
      <summary>Deletes the specified subkey. </summary>
      <param name="subkey">The name of the subkey to delete. This string is not case-sensitive.</param>
      <exception cref="T:System.InvalidOperationException">The <paramref name="subkey" /> has child subkeys </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="subkey" /> parameter does not specify a valid registry key </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="subkey" /> is null</exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to delete the key. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> being manipulated is closed (closed keys cannot be accessed). </exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have the necessary registry rights.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.RegistryPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.DeleteSubKey(System.String,System.Boolean)">
      <summary>Deletes the specified subkey, and specifies whether an exception is raised if the subkey is not found. </summary>
      <param name="subkey">The name of the subkey to delete. This string is not case-sensitive.</param>
      <param name="throwOnMissingSubKey">Indicates whether an exception should be raised if the specified subkey cannot be found. If this argument is true and the specified subkey does not exist, an exception is raised. If this argument is false and the specified subkey does not exist, no action is taken. </param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="subkey" /> has child subkeys. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="subkey" /> does not specify a valid registry key, and <paramref name="throwOnMissingSubKey" /> is true. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="subkey" /> is null.</exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to delete the key. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> being manipulated is closed (closed keys cannot be accessed). </exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have the necessary registry rights.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.RegistryPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.DeleteSubKeyTree(System.String)">
      <summary>Deletes a subkey and any child subkeys recursively. </summary>
      <param name="subkey">The subkey to delete. This string is not case-sensitive.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="subkey" /> is null. </exception>
      <exception cref="T:System.ArgumentException">Deletion of a root hive is attempted.-or-<paramref name="subkey" /> does not specify a valid registry subkey. </exception>
      <exception cref="T:System.IO.IOException">An I/O error has occurred.</exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to delete the key. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> being manipulated is closed (closed keys cannot be accessed). </exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have the necessary registry rights.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.RegistryPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.DeleteSubKeyTree(System.String,System.Boolean)">
      <summary>Deletes the specified subkey and any child subkeys recursively, and specifies whether an exception is raised if the subkey is not found. </summary>
      <param name="subkey">The name of the subkey to delete. This string is not case-sensitive.</param>
      <param name="throwOnMissingSubKey">Indicates whether an exception should be raised if the specified subkey cannot be found. If this argument is true and the specified subkey does not exist, an exception is raised. If this argument is false and the specified subkey does not exist, no action is taken.</param>
      <exception cref="T:System.ArgumentException">An attempt was made to delete the root hive of the tree.-or-<paramref name="subkey" /> does not specify a valid registry subkey, and <paramref name="throwOnMissingSubKey" /> is true.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="subkey" /> is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> is closed (closed keys cannot be accessed).</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have the necessary registry rights.</exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to delete the key.</exception>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.DeleteValue(System.String)">
      <summary>Deletes the specified value from this key.</summary>
      <param name="name">The name of the value to delete. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is not a valid reference to a value. </exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to delete the value. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> being manipulated is closed (closed keys cannot be accessed). </exception>
      <exception cref="T:System.UnauthorizedAccessException">The <see cref="T:Microsoft.Win32.RegistryKey" /> being manipulated is read-only. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.RegistryPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.DeleteValue(System.String,System.Boolean)">
      <summary>Deletes the specified value from this key, and specifies whether an exception is raised if the value is not found.</summary>
      <param name="name">The name of the value to delete. </param>
      <param name="throwOnMissingValue">Indicates whether an exception should be raised if the specified value cannot be found. If this argument is true and the specified value does not exist, an exception is raised. If this argument is false and the specified value does not exist, no action is taken. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is not a valid reference to a value and <paramref name="throwOnMissingValue" /> is true. -or- <paramref name="name" /> is null.</exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to delete the value. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> being manipulated is closed (closed keys cannot be accessed). </exception>
      <exception cref="T:System.UnauthorizedAccessException">The <see cref="T:Microsoft.Win32.RegistryKey" /> being manipulated is read-only. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.RegistryPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.Dispose">
      <summary>Releases all resources used by the current instance of the <see cref="T:Microsoft.Win32.RegistryKey" /> class.</summary>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.Flush">
      <summary>Writes all the attributes of the specified open registry key into the registry.</summary>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.FromHandle(Microsoft.Win32.SafeHandles.SafeRegistryHandle)">
      <summary>[SECURITY CRITICAL] Creates a registry key from a specified handle.</summary>
      <returns>A registry key.</returns>
      <param name="handle">The handle to the registry key.</param>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.FromHandle(Microsoft.Win32.SafeHandles.SafeRegistryHandle,Microsoft.Win32.RegistryView)">
      <summary>[SECURITY CRITICAL] Creates a registry key from a specified handle and registry view setting. </summary>
      <returns>A registry key.</returns>
      <param name="handle">The handle to the registry key.</param>
      <param name="view">The registry view to use.</param>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.GetSubKeyNames">
      <summary>Retrieves an array of strings that contains all the subkey names.</summary>
      <returns>An array of strings that contains the names of the subkeys for the current key.</returns>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to read from the key. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> being manipulated is closed (closed keys cannot be accessed). </exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have the necessary registry rights.</exception>
      <exception cref="T:System.IO.IOException">A system error occurred, for example the current key has been deleted.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.RegistryPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.GetValue(System.String)">
      <summary>Retrieves the value associated with the specified name. Returns null if the name/value pair does not exist in the registry.</summary>
      <returns>The value associated with <paramref name="name" />, or null if <paramref name="name" /> is not found.</returns>
      <param name="name">The name of the value to retrieve. This string is not case-sensitive.</param>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to read from the registry key. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> that contains the specified value is closed (closed keys cannot be accessed). </exception>
      <exception cref="T:System.IO.IOException">The <see cref="T:Microsoft.Win32.RegistryKey" /> that contains the specified value has been marked for deletion. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have the necessary registry rights.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.RegistryPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Read="\" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.GetValue(System.String,System.Object)">
      <summary>Retrieves the value associated with the specified name. If the name is not found, returns the default value that you provide.</summary>
      <returns>The value associated with <paramref name="name" />, with any embedded environment variables left unexpanded, or <paramref name="defaultValue" /> if <paramref name="name" /> is not found.</returns>
      <param name="name">The name of the value to retrieve. This string is not case-sensitive.</param>
      <param name="defaultValue">The value to return if <paramref name="name" /> does not exist. </param>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to read from the registry key. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> that contains the specified value is closed (closed keys cannot be accessed). </exception>
      <exception cref="T:System.IO.IOException">The <see cref="T:Microsoft.Win32.RegistryKey" /> that contains the specified value has been marked for deletion. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have the necessary registry rights.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.RegistryPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Read="\" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.GetValue(System.String,System.Object,Microsoft.Win32.RegistryValueOptions)">
      <summary>Retrieves the value associated with the specified name and retrieval options. If the name is not found, returns the default value that you provide.</summary>
      <returns>The value associated with <paramref name="name" />, processed according to the specified <paramref name="options" />, or <paramref name="defaultValue" /> if <paramref name="name" /> is not found.</returns>
      <param name="name">The name of the value to retrieve. This string is not case-sensitive.</param>
      <param name="defaultValue">The value to return if <paramref name="name" /> does not exist. </param>
      <param name="options">One of the enumeration values that specifies optional processing of the retrieved value.</param>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to read from the registry key. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> that contains the specified value is closed (closed keys cannot be accessed). </exception>
      <exception cref="T:System.IO.IOException">The <see cref="T:Microsoft.Win32.RegistryKey" /> that contains the specified value has been marked for deletion. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="options" /> is not a valid <see cref="T:Microsoft.Win32.RegistryValueOptions" /> value; for example, an invalid value is cast to <see cref="T:Microsoft.Win32.RegistryValueOptions" />.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have the necessary registry rights.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.RegistryPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Read="\" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.GetValueKind(System.String)">
      <summary>Retrieves the registry data type of the value associated with the specified name.</summary>
      <returns>The registry data type of the value associated with <paramref name="name" />.</returns>
      <param name="name">The name of the value whose registry data type is to be retrieved. This string is not case-sensitive.</param>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to read from the registry key. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> that contains the specified value is closed (closed keys cannot be accessed). </exception>
      <exception cref="T:System.IO.IOException">The subkey that contains the specified value does not exist.-or-The name/value pair specified by <paramref name="name" /> does not exist.This exception is not thrown on Windows 95, Windows 98, or Windows Millennium Edition.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have the necessary registry rights.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.RegistryPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Read="\" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.GetValueNames">
      <summary>Retrieves an array of strings that contains all the value names associated with this key.</summary>
      <returns>An array of strings that contains the value names for the current key.</returns>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to read from the registry key. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" />  being manipulated is closed (closed keys cannot be accessed). </exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have the necessary registry rights.</exception>
      <exception cref="T:System.IO.IOException">A system error occurred; for example, the current key has been deleted.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.RegistryPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:Microsoft.Win32.RegistryKey.Handle">
      <summary>[SECURITY CRITICAL] Gets a <see cref="T:Microsoft.Win32.SafeHandles.SafeRegistryHandle" /> object that represents the registry key that the current <see cref="T:Microsoft.Win32.RegistryKey" /> object encapsulates.</summary>
      <returns>The handle to the registry key.</returns>
    </member>
    <member name="P:Microsoft.Win32.RegistryKey.Name">
      <summary>Retrieves the name of the key.</summary>
      <returns>The absolute (qualified) name of the key.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> is closed (closed keys cannot be accessed). </exception>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.OpenBaseKey(Microsoft.Win32.RegistryHive,Microsoft.Win32.RegistryView)">
      <summary>Opens a new <see cref="T:Microsoft.Win32.RegistryKey" /> that represents the requested key on the local machine with the specified view.</summary>
      <returns>The requested registry key.</returns>
      <param name="hKey">The HKEY to open.</param>
      <param name="view">The registry view to use.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="hKey" /> or <paramref name="view" /> is invalid.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have the necessary registry rights.</exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to perform this action.</exception>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.OpenSubKey(System.String)">
      <summary>Retrieves a subkey as read-only.</summary>
      <returns>The subkey requested, or null if the operation failed.</returns>
      <param name="name">The name or path of the subkey to open as read-only. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> is closed (closed keys cannot be accessed). </exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to read the registry key. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.RegistryPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Read="\" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.OpenSubKey(System.String,System.Boolean)">
      <summary>Retrieves a specified subkey, and specifies whether write access is to be applied to the key. </summary>
      <returns>The subkey requested, or null if the operation failed.</returns>
      <param name="name">Name or path of the subkey to open. </param>
      <param name="writable">Set to true if you need write access to the key. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> is closed (closed keys cannot be accessed). </exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to access the registry key in the specified mode. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.RegistryPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.OpenSubKey(System.String,System.Security.AccessControl.RegistryRights)">
      <summary>Retrieves a subkey with the specified name and .Available starting in .NET Framework 2015</summary>
      <returns>The subkey requested, or null if the operation failed.</returns>
      <param name="name">The name or path of the subkey to create or open.</param>
      <param name="rights">The rights for the registry key.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> is closed (closed keys cannot be accessed). </exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to access the registry key in the specified mode. </exception>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.SetValue(System.String,System.Object)">
      <summary>Sets the specified name/value pair.</summary>
      <param name="name">The name of the value to store. </param>
      <param name="value">The data to be stored. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> is an unsupported data type. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> that contains the specified value is closed (closed keys cannot be accessed). </exception>
      <exception cref="T:System.UnauthorizedAccessException">The <see cref="T:Microsoft.Win32.RegistryKey" /> is read-only, and cannot be written to; for example, the key has not been opened with write access. -or-The <see cref="T:Microsoft.Win32.RegistryKey" /> object represents a root-level node, and the operating system is Windows Millennium Edition or Windows 98.</exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to create or modify registry keys. </exception>
      <exception cref="T:System.IO.IOException">The <see cref="T:Microsoft.Win32.RegistryKey" /> object represents a root-level node, and the operating system is Windows 2000, Windows XP, or Windows Server 2003.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.RegistryPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.SetValue(System.String,System.Object,Microsoft.Win32.RegistryValueKind)">
      <summary>Sets the value of a name/value pair in the registry key, using the specified registry data type.</summary>
      <param name="name">The name of the value to be stored. </param>
      <param name="value">The data to be stored. </param>
      <param name="valueKind">The registry data type to use when storing the data. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentException">The type of <paramref name="value" /> did not match the registry data type specified by <paramref name="valueKind" />, therefore the data could not be converted properly. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> that contains the specified value is closed (closed keys cannot be accessed). </exception>
      <exception cref="T:System.UnauthorizedAccessException">The <see cref="T:Microsoft.Win32.RegistryKey" /> is read-only, and cannot be written to; for example, the key has not been opened with write access.-or-The <see cref="T:Microsoft.Win32.RegistryKey" /> object represents a root-level node, and the operating system is Windows Millennium Edition or Windows 98. </exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to create or modify registry keys. </exception>
      <exception cref="T:System.IO.IOException">The <see cref="T:Microsoft.Win32.RegistryKey" /> object represents a root-level node, and the operating system is Windows 2000, Windows XP, or Windows Server 2003.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.RegistryPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="P:Microsoft.Win32.RegistryKey.SubKeyCount">
      <summary>Retrieves the count of subkeys of the current key.</summary>
      <returns>The number of subkeys of the current key.</returns>
      <exception cref="T:System.Security.SecurityException">The user does not have read permission for the key. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> being manipulated is closed (closed keys cannot be accessed). </exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have the necessary registry rights.</exception>
      <exception cref="T:System.IO.IOException">A system error occurred, for example the current key has been deleted.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.RegistryPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.ToString">
      <summary>Retrieves a string representation of this key.</summary>
      <returns>A string representing the key. If the specified key is invalid (cannot be found) then null is returned.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> being accessed is closed (closed keys cannot be accessed). </exception>
    </member>
    <member name="P:Microsoft.Win32.RegistryKey.ValueCount">
      <summary>Retrieves the count of values in the key.</summary>
      <returns>The number of name/value pairs in the key.</returns>
      <exception cref="T:System.Security.SecurityException">The user does not have read permission for the key. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> being manipulated is closed (closed keys cannot be accessed). </exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have the necessary registry rights.</exception>
      <exception cref="T:System.IO.IOException">A system error occurred, for example the current key has been deleted.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.RegistryPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:Microsoft.Win32.RegistryKey.View">
      <summary>Gets the view that was used to create the registry key. </summary>
      <returns>The view that was used to create the registry key.-or-<see cref="F:Microsoft.Win32.RegistryView.Default" />, if no view was used.</returns>
    </member>
    <member name="T:Microsoft.Win32.RegistryOptions">
      <summary>Specifies options to use when creating a registry key.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryOptions.None">
      <summary>A non-volatile key. This is the default.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryOptions.Volatile">
      <summary>A volatile key. The information is stored in memory and is not preserved when the corresponding registry hive is unloaded.</summary>
    </member>
    <member name="T:Microsoft.Win32.RegistryValueKind">
      <summary>Specifies the data types to use when storing values in the registry, or identifies the data type of a value in the registry.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryValueKind.Binary">
      <summary>Binary data in any form. This value is equivalent to the Win32 API registry data type REG_BINARY. </summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryValueKind.DWord">
      <summary>A 32-bit binary number. This value is equivalent to the Win32 API registry data type REG_DWORD.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryValueKind.ExpandString">
      <summary>A null-terminated string that contains unexpanded references to environment variables, such as %PATH%, that are expanded when the value is retrieved. This value is equivalent to the Win32 API registry data type REG_EXPAND_SZ.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryValueKind.MultiString">
      <summary>An array of null-terminated strings, terminated by two null characters. This value is equivalent to the Win32 API registry data type REG_MULTI_SZ.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryValueKind.None">
      <summary>No data type.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryValueKind.QWord">
      <summary>A 64-bit binary number. This value is equivalent to the Win32 API registry data type REG_QWORD.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryValueKind.String">
      <summary>A null-terminated string. This value is equivalent to the Win32 API registry data type REG_SZ.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryValueKind.Unknown">
      <summary>An unsupported registry data type. For example, the Microsoft Win32 API registry data type REG_RESOURCE_LIST is unsupported. Use this value to specify that the <see cref="M:Microsoft.Win32.RegistryKey.SetValue(System.String,System.Object)" /> method should determine the appropriate registry data type when storing a name/value pair.</summary>
    </member>
    <member name="T:Microsoft.Win32.RegistryValueOptions">
      <summary>Specifies optional behavior when retrieving name/value pairs from a registry key.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryValueOptions.DoNotExpandEnvironmentNames">
      <summary>A value of type <see cref="F:Microsoft.Win32.RegistryValueKind.ExpandString" /> is retrieved without expanding its embedded environment variables. </summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryValueOptions.None">
      <summary>No optional behavior is specified.</summary>
    </member>
    <member name="T:Microsoft.Win32.RegistryView">
      <summary>Specifies which registry view to target on a 64-bit operating system.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryView.Default">
      <summary>The default view.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryView.Registry32">
      <summary>The 32-bit view.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryView.Registry64">
      <summary>The 64-bit view.</summary>
    </member>
    <member name="T:Microsoft.Win32.SafeHandles.SafeRegistryHandle">
      <summary>[SECURITY CRITICAL] Represents a safe handle to the Windows registry.</summary>
    </member>
    <member name="M:Microsoft.Win32.SafeHandles.SafeRegistryHandle.#ctor(System.IntPtr,System.Boolean)">
      <summary>[SECURITY CRITICAL] Initializes a new instance of the <see cref="T:Microsoft.Win32.SafeHandles.SafeRegistryHandle" /> class. </summary>
      <param name="preexistingHandle">An object that represents the pre-existing handle to use.</param>
      <param name="ownsHandle">true to reliably release the handle during the finalization phase; false to prevent reliable release.</param>
    </member>
    <member name="P:Microsoft.Win32.SafeHandles.SafeRegistryHandle.IsInvalid"></member>
    <member name="T:System.Security.AccessControl.RegistryRights">
      <summary>Specifies the access control rights that can be applied to registry objects.</summary>
    </member>
    <member name="F:System.Security.AccessControl.RegistryRights.ChangePermissions">
      <summary>The right to change the access rules and audit rules associated with a registry key.</summary>
    </member>
    <member name="F:System.Security.AccessControl.RegistryRights.CreateLink">
      <summary>Reserved for system use.</summary>
    </member>
    <member name="F:System.Security.AccessControl.RegistryRights.CreateSubKey">
      <summary>The right to create subkeys of a registry key.</summary>
    </member>
    <member name="F:System.Security.AccessControl.RegistryRights.Delete">
      <summary>The right to delete a registry key.</summary>
    </member>
    <member name="F:System.Security.AccessControl.RegistryRights.EnumerateSubKeys">
      <summary>The right to list the subkeys of a registry key.</summary>
    </member>
    <member name="F:System.Security.AccessControl.RegistryRights.ExecuteKey">
      <summary>Same as <see cref="F:System.Security.AccessControl.RegistryRights.ReadKey" />.</summary>
    </member>
    <member name="F:System.Security.AccessControl.RegistryRights.FullControl">
      <summary>The right to exert full control over a registry key, and to modify its access rules and audit rules.</summary>
    </member>
    <member name="F:System.Security.AccessControl.RegistryRights.Notify">
      <summary>The right to request notification of changes on a registry key.</summary>
    </member>
    <member name="F:System.Security.AccessControl.RegistryRights.QueryValues">
      <summary>The right to query the name/value pairs in a registry key.</summary>
    </member>
    <member name="F:System.Security.AccessControl.RegistryRights.ReadKey">
      <summary>The right to query the name/value pairs in a registry key, to request notification of changes, to enumerate its subkeys, and to read its access rules and audit rules.</summary>
    </member>
    <member name="F:System.Security.AccessControl.RegistryRights.ReadPermissions">
      <summary>The right to open and copy the access rules and audit rules for a registry key.</summary>
    </member>
    <member name="F:System.Security.AccessControl.RegistryRights.SetValue">
      <summary>The right to create, delete, or set name/value pairs in a registry key.</summary>
    </member>
    <member name="F:System.Security.AccessControl.RegistryRights.TakeOwnership">
      <summary>The right to change the owner of a registry key.</summary>
    </member>
    <member name="F:System.Security.AccessControl.RegistryRights.WriteKey">
      <summary>The right to create, delete, and set the name/value pairs in a registry key, to create or delete subkeys, to request notification of changes, to enumerate its subkeys, and to read its access rules and audit rules.</summary>
    </member>
  </members>
</doc>