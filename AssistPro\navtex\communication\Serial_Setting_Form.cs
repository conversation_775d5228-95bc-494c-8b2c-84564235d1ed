﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using WeifenLuo.WinFormsUI;
using AssistPro.navtex;
using static System.Windows.Forms.VisualStyles.VisualStyleElement;
using System.IO.Ports;
using static AssistPro.vhf.DataProtocol;
using static AssistPro.navtex.communication.CommPort;

namespace AssistPro.navtex.communication
{
    public partial class Serial_Setting_Form : DockContent
    {
        public static Serial_Setting_Form m_serial_setting_form;

        CommPort com = CommPort.Instance;
        IN3000R_Form _IN3000R_Form = IN3000R_Form.Instance;
        int m_num;
        Serial_Port_Info_c info = new Serial_Port_Info_c();

        // @brief : Port component
        string[] portList;

        // @brief : Baudrate component
        Int32[] baudRates =
        {
            4800,9600,38400,115200,0
        };

        public Serial_Setting_Form(int num)
        {
            InitializeComponent();

            m_serial_setting_form = this;
            m_num = num;

            Serial_Setting_Ini ini = new Serial_Setting_Ini();
            info = ini.StoreRead(num);

            Color c = Color.Blue;
            label_SettingName.ForeColor = c;
            label_SettingName.Text = com.SettingName[num];

            int found = 0;
            // @brief : Port
            portList = com.GetAvailablePorts();
            for (int i = 0; i < portList.Length; ++i)
            {
                string name = portList[i];
                comboBox_Port.Items.Add(name);
                if(name == info.PortName)
                {
                    found = i;
                }
            }
            if(portList.Length > 0)
            {
                comboBox_Port.SelectedIndex = found;
            }

            // @brief : Baudrate
            for (int i=0; baudRates[i] != 0; i++)
            {
                comboBox_Baudrate.Items.Add(baudRates[i].ToString());
                if (baudRates[i] == info.BaudRate)
                {
                    found = i;
                }
            }
            comboBox_Baudrate.SelectedIndex = found;

            // @brief : Databits
            comboBox_Databits.Items.Add("5");
            comboBox_Databits.Items.Add("6");
            comboBox_Databits.Items.Add("7");
            comboBox_Databits.Items.Add("8");
            comboBox_Databits.SelectedIndex = info.DataBits - 5;

            // @brief : Parity
            foreach (string s in Enum.GetNames(typeof(Parity)))
            {
                comboBox_Parity.Items.Add(s);
            }
            comboBox_Parity.SelectedIndex = (int)info.Parity;

            // @brief : Stopbits
            foreach (string s in Enum.GetNames(typeof(StopBits)))
            {
                comboBox_Stopbits.Items.Add(s);
            }
            comboBox_Stopbits.SelectedIndex = (int)info.StopBits;
        }

        private void Serial_Setting_Form_FormClosing(object sender, FormClosingEventArgs e)
        {
            this.Dispose();
        }

        private void button_Serial_OK_Click(object sender, EventArgs e)
        {
            string str = string.Empty;

            if ( comboBox_Port.SelectedIndex >= 0         &&
                comboBox_Baudrate.SelectedIndex >= 0     &&
                comboBox_Databits.SelectedIndex >= 0     &&
                comboBox_Parity.SelectedIndex >= 0       &&
                comboBox_Stopbits.SelectedIndex >= 0     )
            {
                Serial_Port_Info_c info = new Serial_Port_Info_c();
                info.PortName = comboBox_Port.Text;
                info.BaudRate = Int32.Parse(comboBox_Baudrate.Text);
                info.DataBits = comboBox_Databits.SelectedIndex + 5;
                info.Parity = (Parity)comboBox_Parity.SelectedIndex;
                info.StopBits = (StopBits)comboBox_Stopbits.SelectedIndex;

                Serial_Setting_Ini ini = new Serial_Setting_Ini();
                ini.StoreWrite(m_num, info);

                com.Open(m_num, info);
                if (com.SettingName[m_num] == "Serial Main")
                {
                    str = com.IsStatus((int)Serial_t.SERIAL_TYPE_MAIN);
                    if (str == "Close")
                    {
                        _IN3000R_Form.toolStripTextBox_Main.Text = str;
                        _IN3000R_Form.toolStripTextBox_Main.BackColor = Color.FromArgb(255, 0, 0);
                    }
                    else
                    {
                        _IN3000R_Form.toolStripTextBox_Main.Text = str;
                        _IN3000R_Form.toolStripTextBox_Main.BackColor = Color.FromArgb(0, 255, 0);
                    }
                }
                else if (com.SettingName[m_num] == "Serial INS")
                {
                    str = com.IsStatus((int)Serial_t.SERIAL_TYPE_INS);
                    if (str == "Close")
                    {
                        _IN3000R_Form.toolStripTextBox_INS.Text = str;
                        _IN3000R_Form.toolStripTextBox_INS.BackColor = Color.FromArgb(255, 0, 0);
                    }
                    else
                    {
                        _IN3000R_Form.toolStripTextBox_INS.Text = str;
                        _IN3000R_Form.toolStripTextBox_INS.BackColor = Color.FromArgb(0, 255, 0);
                    }
                }
                else if (com.SettingName[m_num] == "Serial BAM")
                {
                    str = com.IsStatus((int)Serial_t.SERIAL_TYPE_BAM);
                    if (str == "Close")
                    {
                        _IN3000R_Form.toolStripTextBox_BAM.Text = str;
                        _IN3000R_Form.toolStripTextBox_BAM.BackColor = Color.FromArgb(255, 0, 0);
                    }
                    else
                    {
                        _IN3000R_Form.toolStripTextBox_BAM.Text = str;
                        _IN3000R_Form.toolStripTextBox_BAM.BackColor = Color.FromArgb(0, 255, 0);
                    }
                }
                else if (com.SettingName[m_num] == "NMEA Monitor 1")
                {
                    str = com.IsStatus((int)Serial_t.SERIAL_TYPE_MON_1);
                    if (str == "Close")
                    {
                        _IN3000R_Form.toolStripTextBox_Mon_1.Text = str;
                        _IN3000R_Form.toolStripTextBox_Mon_1.BackColor = Color.FromArgb(255, 0, 0);
                    }
                    else
                    {
                        _IN3000R_Form.toolStripTextBox_Mon_1.Text = str;
                        _IN3000R_Form.toolStripTextBox_Mon_1.BackColor = Color.FromArgb(0, 255, 0);
                    }
                }
                else if (com.SettingName[m_num] == "NMEA Monitor 2")
                {
                    str = com.IsStatus((int)Serial_t.SERIAL_TYPE_MON_2);
                    if (str == "Close")
                    {
                        _IN3000R_Form.toolStripTextBox_Mon_2.Text = str;
                        _IN3000R_Form.toolStripTextBox_Mon_2.BackColor = Color.FromArgb(255, 0, 0);
                    }
                    else
                    {
                        _IN3000R_Form.toolStripTextBox_Mon_2.Text = str;
                        _IN3000R_Form.toolStripTextBox_Mon_2.BackColor = Color.FromArgb(0, 255, 0);
                    }
                }
                else if (com.SettingName[m_num] == "NMEA Monitor 3")
                {
                    str = com.IsStatus((int)Serial_t.SERIAL_TYPE_MON_3);
                    if (str == "Close")
                    {
                        _IN3000R_Form.toolStripTextBox_Mon_3.Text = str;
                        _IN3000R_Form.toolStripTextBox_Mon_3.BackColor = Color.FromArgb(255, 0, 0);
                    }
                    else
                    {
                        _IN3000R_Form.toolStripTextBox_Mon_3.Text = str;
                        _IN3000R_Form.toolStripTextBox_Mon_3.BackColor = Color.FromArgb(0, 255, 0);
                    }
                }

                this.Dispose();
            }
        }

        private void button_Serial_Cancel_Click(object sender, EventArgs e)
        {
            this.Dispose();
        }
    }
}
