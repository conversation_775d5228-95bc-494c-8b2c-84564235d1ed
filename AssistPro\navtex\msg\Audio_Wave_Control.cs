﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NAudio.Wave;

namespace AssistPro.navtex.msg
{
    public partial class Audio_Wave_Control
    {
        static readonly Audio_Wave_Control instance = new Audio_Wave_Control();

        BufferedWaveProvider buffer = new BufferedWaveProvider(new WaveFormat(48000, 1));
        WaveOutEvent waveOut = new WaveOutEvent();

        byte[] wave_byte_void = new byte[2];

        int frequency = 0;
        double phase = 0.0;
        double vpp = 0.0;
        int channelCount = 0;
        int blockAlign = 0;
        int sampleRate = 0;
        double waveLevel = new double();

        public bool isProcessing = false;

        public static Audio_Wave_Control Instance
        {
            get
            {
                return instance;
            }
        }

        Audio_Wave_Control()
        {
            wave_byte_void[0] = 0;
            wave_byte_void[1] = 0;

            buffer.BufferDuration = TimeSpan.FromSeconds(3600);
            waveOut.DeviceNumber = 0;
            waveLevel = 0;
        }

        public void WavePlay()
        {
            waveOut.Volume = 1;
            waveOut.Init(buffer);
            waveOut.Play();
        }

        public void WaveStop()
        {
            buffer.ClearBuffer();
            waveOut.Stop();
            waveOut.Dispose();
            waveLevel = 0;
            isProcessing = false;
        }

        private long GetMinCount(int min)
        {
            long ret = 0;
            int BPS = 480;

            // 480 sample = 10ms
            // 1sec = 480 * 100
            // 1min = 480 * 100 * 60
            ret = BPS * 100 * 60 * min;

            return ret;
        }

        public void VoidSignal()
        {
            buffer.AddSamples(wave_byte_void, 0, 2);
        }

        public void DOT_Out(int MarkFreq, int SpaceFreq, int BPS)
        {
            if(isProcessing == true)
            {
                return;
            }
            isProcessing = true;

            WaveStop();

            sampleRate = buffer.WaveFormat.SampleRate;
            channelCount = buffer.WaveFormat.Channels;
            blockAlign = buffer.WaveFormat.BlockAlign;

            int i = 0;
            byte[] wave_mark = new byte[(sampleRate / BPS) * 2];
            byte[] wave_space = new byte[(sampleRate / BPS) * 2];
            short wave_data = 0;

            buffer.AddSamples(wave_byte_void, 0, 2);

            for (long n = 0; n < (GetMinCount(5) / ((sampleRate / BPS) * 2)); n++) // 7min
            {
                frequency = MarkFreq;
                for (i = 0; i < (sampleRate / BPS); i++)
                {
                    phase = (Math.PI * 2 * frequency) / sampleRate;
                    vpp = (double)short.MaxValue;
                    waveLevel += phase;
                    if (waveLevel >= (Math.PI * 2))
                    {
                        waveLevel -= (Math.PI * 2);
                    }
                    wave_data = (short)(vpp * Math.Sin(waveLevel));

                    wave_mark[(i * 2) + 1] = (byte)((wave_data >> 8) & 0xff);
                    wave_mark[(i * 2)] = (byte)((wave_data) & 0xff);
                }
                buffer.AddSamples(wave_mark, 0, ((sampleRate / BPS) * 2));

                frequency = SpaceFreq;
                for (i = 0; i < (sampleRate / BPS); i++)
                {
                    phase = (Math.PI * 2 * frequency) / sampleRate;
                    vpp = (double)short.MaxValue;
                    waveLevel += phase;
                    if (waveLevel >= (Math.PI * 2))
                    {
                        waveLevel -= (Math.PI * 2);
                    }
                    wave_data = (short)(vpp * Math.Sin(waveLevel));

                    wave_space[(i * 2) + 1] = (byte)((wave_data >> 8) & 0xff);
                    wave_space[(i * 2)] = (byte)((wave_data) & 0xff);
                }
                buffer.AddSamples(wave_space, 0, ((sampleRate / BPS) * 2));
            }

            waveOut.Volume = 1.0f;
            waveOut.Init(buffer);
            waveOut.Play();

            isProcessing = false;
        }

        public void Frequency_Out(int Freq)
        {
            if (isProcessing == true)
            {
                return;
            }
            isProcessing = true;

            WaveStop();

            sampleRate = buffer.WaveFormat.SampleRate;
            channelCount = buffer.WaveFormat.Channels;
            blockAlign = buffer.WaveFormat.BlockAlign;

            int i = 0;
            byte[] wave_byte = new byte[sampleRate * 2];
            short wave_data = 0;

            frequency = Freq;
            buffer.AddSamples(wave_byte_void, 0, 2);

            for (long n = 0; n < (GetMinCount(5) / (sampleRate / frequency)); n++) // 7min
            {
                for (i = 0; i < (sampleRate / frequency); i++)
                {
                    phase = (Math.PI * 2 * frequency) / sampleRate;
                    vpp = (double)short.MaxValue;
                    waveLevel += phase;
                    if (waveLevel >= (Math.PI * 2))
                    {
                        waveLevel -= (Math.PI * 2);
                    }
                    wave_data = (short)(vpp * Math.Sin(waveLevel));

                    wave_byte[(i * 2) + 1] = (byte)((wave_data >> 8) & 0xff);
                    wave_byte[(i * 2)] = (byte)((wave_data) & 0xff);
                }
                buffer.AddSamples(wave_byte, 0, (sampleRate / frequency) * 2);
            }

            waveOut.Volume = 1.0f;
            waveOut.Init(buffer);
            waveOut.Play();

            isProcessing = false;
        }

        public void Frequency_Add(int Freq, int BPS)
        {
            if (isProcessing == true)
            {
                return;
            }
            isProcessing = true;

            sampleRate = buffer.WaveFormat.SampleRate;
            channelCount = buffer.WaveFormat.Channels;
            blockAlign = buffer.WaveFormat.BlockAlign;

            int i = 0;
            byte[] wave_byte = new byte[sampleRate * 2];
            short wave_data = 0;

            frequency = Freq;
            for (i = 0; i < (sampleRate / BPS); i++)
            {
                phase = (Math.PI * 2 * frequency) / sampleRate;
                vpp = (double)short.MaxValue;
                waveLevel += phase;
                if (waveLevel >= (Math.PI * 2))
                {
                    waveLevel -= (Math.PI * 2);
                }
                wave_data = (short)(vpp * Math.Sin(waveLevel));

                wave_byte[(i * 2) + 1] = (byte)((wave_data >> 8) & 0xff);
                wave_byte[(i * 2)] = (byte)((wave_data) & 0xff);
            }
            buffer.AddSamples(wave_byte, 0, ((sampleRate / BPS) * 2));

            isProcessing = false;
        }
    }
}
