﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Security.AccessControl</name>
  </assembly>
  <members>
    <member name="T:System.Security.AccessControl.AccessControlActions">
      <summary>보안 가능한 개체에 사용할 수 있는 동작을 지정합니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlActions.Change">
      <summary>쓰기 전용 액세스를 지정합니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlActions.None">
      <summary>액세스를 지정하지 않습니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlActions.View">
      <summary>읽기 전용 액세스를 지정합니다.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AccessControlModification">
      <summary>수행할 액세스 제어 수정의 형식을 지정합니다.이 열거형은 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 클래스 및 해당 하위 항목의 메서드에서 사용됩니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlModification.Add">
      <summary>지정한 권한 부여 규칙을 ACL(액세스 제어 목록)에 추가합니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlModification.Remove">
      <summary>ACL에서 지정한 권한 부여 규칙과 동일한 SID(보안 식별자) 및 액세스 마스크가 들어 있는 권한 부여 규칙을 제거합니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlModification.RemoveAll">
      <summary>ACL에서 지정한 권한 부여 규칙과 동일한 SID가 들어 있는 권한 부여 규칙을 제거합니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlModification.RemoveSpecific">
      <summary>ACL에서 지정한 권한 부여 규칙과 정확히 일치하는 권한 부여 규칙을 제거합니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlModification.Reset">
      <summary>ACL에서 지정한 권한 부여 규칙과 동일한 SID가 들어 있는 권한 부여 규칙을 제거한 다음 지정한 권한 부여 규칙을 ACL에 추가합니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlModification.Set">
      <summary>ACL에서 모든 권한 부여 규칙을 제거한 다음 지정한 권한 부여 규칙을 ACL에 추가합니다.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AccessControlSections">
      <summary>저장하거나 로드할 보안 설명자 섹션을 지정합니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlSections.Access">
      <summary>DACL(임의 액세스 제어 목록)입니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlSections.All">
      <summary>전체 보안 설명자입니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlSections.Audit">
      <summary>SACL(시스템 액세스 제어 목록)입니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlSections.Group">
      <summary>기본 그룹입니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlSections.None">
      <summary>섹션이 없습니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlSections.Owner">
      <summary>소유자입니다.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AccessControlType">
      <summary>
        <see cref="T:System.Security.AccessControl.AccessRule" /> 개체를 액세스 허용에 사용할지 아니면 액세스 거부에 사용할지 지정합니다.이러한 값은 플래그가 아니며 조합할 수 없습니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlType.Allow">
      <summary>
        <see cref="T:System.Security.AccessControl.AccessRule" /> 개체는 보안 개체에 대한 액세스를 허용하는 데 사용됩니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AccessControlType.Deny">
      <summary>
        <see cref="T:System.Security.AccessControl.AccessRule" /> 개체는 보안 개체에 대한 액세스를 거부하는 데 사용됩니다.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AccessRule">
      <summary>사용자의 ID, 액세스 마스크 및 액세스 제어 형식(허용 또는 거부)의 조합을 나타냅니다.또한 <see cref="T:System.Security.AccessControl.AccessRule" /> 개체는 자식 개체가 규칙을 상속하는 방식과 해당 상속이 전파되는 방식에 대한 정보를 포함합니다.</summary>
    </member>
    <member name="M:System.Security.AccessControl.AccessRule.#ctor(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
      <summary>지정된 값을 사용하여 <see cref="T:System.Security.AccessControl.AccessRule" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="identity">액세스 규칙이 적용되는 ID입니다.이 매개 변수는 <see cref="T:System.Security.Principal.SecurityIdentifier" />로 캐스팅할 수 있는 개체여야 합니다.</param>
      <param name="accessMask">이 규칙의 액세스 마스크입니다.액세스 마스크는 익명 비트의 32비트 컬렉션으로, 비트의 의미는 개별 통합자가 정의합니다.</param>
      <param name="isInherited">이 규칙이 부모 컨테이너에서 상속되면 true입니다.</param>
      <param name="inheritanceFlags">액세스 규칙의 상속 속성입니다.</param>
      <param name="propagationFlags">상속된 액세스 규칙이 자동으로 전파되는지 여부입니다.<paramref name="inheritanceFlags" />가 <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />으로 설정되면 전파 플래그는 무시됩니다.</param>
      <param name="type">유효한 액세스 제어 형식입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="identity" /> 매개 변수 값이 <see cref="T:System.Security.Principal.SecurityIdentifier" />로 캐스팅될 수 없거나 <paramref name="type" /> 매개 변수에 잘못된 값이 포함된 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="accessMask" /> 매개 변수 값이 0이거나 <paramref name="inheritanceFlags" /> 또는 <paramref name="propagationFlags" /> 매개 변수에 인식할 수 없는 플래그 값이 포함된 경우</exception>
    </member>
    <member name="P:System.Security.AccessControl.AccessRule.AccessControlType">
      <summary>이 <see cref="T:System.Security.AccessControl.AccessRule" /> 개체와 관련된 <see cref="T:System.Security.AccessControl.AccessControlType" /> 값을 가져옵니다.</summary>
      <returns>이 <see cref="T:System.Security.AccessControl.AccessRule" /> 개체와 관련된 <see cref="T:System.Security.AccessControl.AccessControlType" /> 값입니다.</returns>
    </member>
    <member name="T:System.Security.AccessControl.AccessRule`1">
      <summary>사용자의 ID, 액세스 마스크 및 액세스 제어 형식(허용 또는 거부)의 조합을 나타냅니다.또한 AccessRule`1 개체는 자식 개체가 규칙을 상속하는 방식과 해당 상속이 전파되는 방식에 대한 정보를 포함합니다.</summary>
      <typeparam name="T">액세스 규칙에 대한 액세스 권한 형식입니다.</typeparam>
    </member>
    <member name="M:System.Security.AccessControl.AccessRule`1.#ctor(System.Security.Principal.IdentityReference,`0,System.Security.AccessControl.AccessControlType)">
      <summary>지정된 값을 사용하여 AccessRule’1 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="identity">액세스 규칙이 적용되는 ID입니다.</param>
      <param name="rights">액세스 규칙의 권한입니다.</param>
      <param name="type">유효한 액세스 제어 형식입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.AccessRule`1.#ctor(System.Security.Principal.IdentityReference,`0,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
      <summary>지정된 값을 사용하여 AccessRule’1 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="identity">액세스 규칙이 적용되는 ID입니다.</param>
      <param name="rights">액세스 규칙의 권한입니다.</param>
      <param name="inheritanceFlags">액세스 규칙의 상속 속성입니다.</param>
      <param name="propagationFlags">상속된 액세스 규칙이 자동으로 전파되는지 여부입니다.<paramref name="inheritanceFlags" />가 <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />으로 설정되면 전파 플래그는 무시됩니다.</param>
      <param name="type">유효한 액세스 제어 형식입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.AccessRule`1.#ctor(System.String,`0,System.Security.AccessControl.AccessControlType)">
      <summary>지정된 값을 사용하여 AccessRule’1 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="identity">액세스 규칙이 적용되는 ID입니다.</param>
      <param name="rights">액세스 규칙의 권한입니다.</param>
      <param name="type">유효한 액세스 제어 형식입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.AccessRule`1.#ctor(System.String,`0,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
      <summary>지정된 값을 사용하여 AccessRule’1 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="identity">액세스 규칙이 적용되는 ID입니다.</param>
      <param name="rights">액세스 규칙의 권한입니다.</param>
      <param name="inheritanceFlags">액세스 규칙의 상속 속성입니다.</param>
      <param name="propagationFlags">상속된 액세스 규칙이 자동으로 전파되는지 여부입니다.<paramref name="inheritanceFlags" />가 <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />으로 설정되면 전파 플래그는 무시됩니다.</param>
      <param name="type">유효한 액세스 제어 형식입니다.</param>
    </member>
    <member name="P:System.Security.AccessControl.AccessRule`1.Rights">
      <summary>현재 인스턴스의 권한을 가져옵니다.</summary>
      <returns>현재 인스턴스의 권한으로, 형식 &lt;T&gt;로 캐스팅됩니다.</returns>
    </member>
    <member name="T:System.Security.AccessControl.AceEnumerator">
      <summary>ACL(액세스 제어 목록)의 ACE(액세스 제어 항목)를 반복하는 기능을 제공합니다. </summary>
    </member>
    <member name="P:System.Security.AccessControl.AceEnumerator.Current">
      <summary>
        <see cref="T:System.Security.AccessControl.GenericAce" /> 컬렉션의 현재 요소를 가져옵니다.이 속성은 익숙한 형식의 개체 버전을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Security.AccessControl.GenericAce" /> 컬렉션의 현재 요소입니다.</returns>
    </member>
    <member name="M:System.Security.AccessControl.AceEnumerator.MoveNext">
      <summary>열거자를 <see cref="T:System.Security.AccessControl.GenericAce" /> 컬렉션의 다음 요소로 이동합니다.</summary>
      <returns>열거자가 다음 요소로 이동한 경우 true가 반환되고, 컬렉션의 끝을 지난 경우 false가 반환됩니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 만들어진 후 컬렉션이 수정된 경우</exception>
    </member>
    <member name="M:System.Security.AccessControl.AceEnumerator.Reset">
      <summary>
        <see cref="T:System.Security.AccessControl.GenericAce" /> 컬렉션의 첫 번째 요소 앞의 초기 위치에 열거자를 설정합니다.</summary>
      <exception cref="T:System.InvalidOperationException">열거자가 만들어진 후 컬렉션이 수정된 경우</exception>
    </member>
    <member name="P:System.Security.AccessControl.AceEnumerator.System#Collections#IEnumerator#Current"></member>
    <member name="T:System.Security.AccessControl.AceFlags">
      <summary>ACE(액세스 제어 항목)의 상속 및 감사 동작을 지정합니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.AuditFlags">
      <summary>모든 액세스 시도를 감사합니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.ContainerInherit">
      <summary>액세스 마스크는 자식 컨테이너 개체로 전파됩니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.FailedAccess">
      <summary>실패한 액세스 시도를 감사합니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.InheritanceFlags">
      <summary>
        <see cref="F:System.Security.AccessControl.AceFlags.ObjectInherit" />, <see cref="F:System.Security.AccessControl.AceFlags.ContainerInherit" />, <see cref="F:System.Security.AccessControl.AceFlags.NoPropagateInherit" /> 및 <see cref="F:System.Security.AccessControl.AceFlags.InheritOnly" />의 논리 OR입니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.Inherited">
      <summary>ACE는 개체에 대해 명시적으로 설정되지 않고 부모 컨테이너에서 상속됩니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.InheritOnly">
      <summary>액세스 마스크는 자식 개체로만 전파됩니다.여기에는 컨테이너와 리프 자식 개체도 모두 포함됩니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.None">
      <summary>ACE 플래그가 설정되지 않습니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.NoPropagateInherit">
      <summary>액세스 검사는 개체에 적용되는 것이 아니라 개체의 자식에만 적용됩니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.ObjectInherit">
      <summary>액세스 마스크는 자식 리프 개체로 전파됩니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceFlags.SuccessfulAccess">
      <summary>성공한 액세스 시도를 감사합니다.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AceQualifier">
      <summary>ACE(액세스 제어 항목)의 함수를 지정합니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceQualifier.AccessAllowed">
      <summary>액세스를 허용합니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceQualifier.AccessDenied">
      <summary>액세스를 거부합니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceQualifier.SystemAlarm">
      <summary>시스템 경고를 발생시킵니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceQualifier.SystemAudit">
      <summary>시스템 감사를 발생시킵니다.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AceType">
      <summary>사용 가능한 ACE(액세스 제어 항목) 형식을 정의합니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessAllowed">
      <summary>
        <see cref="T:System.Security.Principal.IdentityReference" /> 개체로 식별되는 특정 트러스티의 개체에 대한 액세스를 허용합니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessAllowedCallback">
      <summary>
        <see cref="T:System.Security.Principal.IdentityReference" /> 개체로 식별되는 특정 트러스티의 개체에 대한 액세스를 허용합니다.이 ACE 형식은 선택적인 콜백 데이터를 포함할 수 있습니다.콜백 데이터는 해석되지 않은 리소스 관리자별 BLOB입니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessAllowedCallbackObject">
      <summary>개체, 속성 집합 또는 속성에 대한 액세스를 허용합니다.이 ACE는 액세스 권한 집합, 개체의 형식을 식별하는 GUID 및 시스템에서 액세스 권한을 부여할 트러스티를 식별하는 <see cref="T:System.Security.Principal.IdentityReference" /> 개체를 포함합니다.ACE에는 또한 GUID와 자식 개체의 ACE 상속을 제어하는 플래그 집합이 포함되어 있습니다.이 ACE 형식은 선택적인 콜백 데이터를 포함할 수 있습니다.콜백 데이터는 해석되지 않은 리소스 관리자별 BLOB입니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessAllowedCompound">
      <summary>정의되어 있지만 사용되지는 않습니다.편의를 위해 제공되었습니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessAllowedObject">
      <summary>개체, 속성 집합 또는 속성에 대한 액세스를 허용합니다.이 ACE는 액세스 권한 집합, 개체의 형식을 식별하는 GUID 및 시스템에서 액세스 권한을 부여할 트러스티를 식별하는 <see cref="T:System.Security.Principal.IdentityReference" /> 개체를 포함합니다.ACE에는 또한 GUID와 자식 개체의 ACE 상속을 제어하는 플래그 집합이 포함되어 있습니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessDenied">
      <summary>
        <see cref="T:System.Security.Principal.IdentityReference" /> 개체로 식별되는 특정 트러스티의 개체에 대한 액세스를 거부합니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessDeniedCallback">
      <summary>
        <see cref="T:System.Security.Principal.IdentityReference" /> 개체로 식별되는 특정 트러스티의 개체에 대한 액세스를 거부합니다.이 ACE 형식은 선택적인 콜백 데이터를 포함할 수 있습니다.콜백 데이터는 해석되지 않은 리소스 관리자별 BLOB입니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessDeniedCallbackObject">
      <summary>개체, 속성 집합 또는 속성에 대한 액세스를 거부합니다.이 ACE는 액세스 권한 집합, 개체의 형식을 식별하는 GUID 및 시스템에서 액세스 권한을 부여할 트러스티를 식별하는 <see cref="T:System.Security.Principal.IdentityReference" /> 개체를 포함합니다.ACE에는 또한 GUID와 자식 개체의 ACE 상속을 제어하는 플래그 집합이 포함되어 있습니다.이 ACE 형식은 선택적인 콜백 데이터를 포함할 수 있습니다.콜백 데이터는 해석되지 않은 리소스 관리자별 BLOB입니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.AccessDeniedObject">
      <summary>개체, 속성 집합 또는 속성에 대한 액세스를 거부합니다.이 ACE는 액세스 권한 집합, 개체의 형식을 식별하는 GUID 및 시스템에서 액세스 권한을 부여할 트러스티를 식별하는 <see cref="T:System.Security.Principal.IdentityReference" /> 개체를 포함합니다.ACE에는 또한 GUID와 자식 개체의 ACE 상속을 제어하는 플래그 집합이 포함되어 있습니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.MaxDefinedAceType">
      <summary>열거형에 정의되어 있는 최대 ACE 형식을 추적합니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAlarm">
      <summary>다음에 사용하도록 예약됩니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAlarmCallback">
      <summary>다음에 사용하도록 예약됩니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAlarmCallbackObject">
      <summary>다음에 사용하도록 예약됩니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAlarmObject">
      <summary>다음에 사용하도록 예약됩니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAudit">
      <summary>지정된 트러스티가 개체에 대한 액세스 권한을 얻으려고 시도할 때 감사 메시지가 기록되도록 합니다.트러스티는 <see cref="T:System.Security.Principal.IdentityReference" /> 개체로 식별됩니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAuditCallback">
      <summary>지정된 트러스티가 개체에 대한 액세스 권한을 얻으려고 시도할 때 감사 메시지가 기록되도록 합니다.트러스티는 <see cref="T:System.Security.Principal.IdentityReference" /> 개체로 식별됩니다.이 ACE 형식은 선택적인 콜백 데이터를 포함할 수 있습니다.콜백 데이터는 해석되지 않은 리소스 관리자별 BLOB입니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAuditCallbackObject">
      <summary>지정된 트러스티가 속성 집합이나 속성 같은 개체나 하위 개체에 대한 액세스 권한을 얻으려고 시도할 때 감사 메시지가 기록되도록 합니다.이 ACE는 액세스 권한 집합, 개체 또는 하위 개체의 형식을 식별하는 GUID 및 시스템에서 액세스 권한을 감사할 트러스티를 식별하는 <see cref="T:System.Security.Principal.IdentityReference" /> 개체를 포함합니다.ACE에는 또한 GUID와 자식 개체의 ACE 상속을 제어하는 플래그 집합이 포함되어 있습니다.이 ACE 형식은 선택적인 콜백 데이터를 포함할 수 있습니다.콜백 데이터는 해석되지 않은 리소스 관리자별 BLOB입니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AceType.SystemAuditObject">
      <summary>지정된 트러스티가 속성 집합이나 속성 같은 개체나 하위 개체에 대한 액세스 권한을 얻으려고 시도할 때 감사 메시지가 기록되도록 합니다.이 ACE는 액세스 권한 집합, 개체 또는 하위 개체의 형식을 식별하는 GUID 및 시스템에서 액세스 권한을 감사할 트러스티를 식별하는 <see cref="T:System.Security.Principal.IdentityReference" /> 개체를 포함합니다.ACE에는 또한 GUID와 자식 개체의 ACE 상속을 제어하는 플래그 집합이 포함되어 있습니다.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AuditFlags">
      <summary>보안 개체 액세스 시도를 감사하기 위한 조건을 지정합니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AuditFlags.Failure">
      <summary>실패한 액세스 시도가 감사됩니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AuditFlags.None">
      <summary>어떠한 액세스 시도도 감사되지 않습니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.AuditFlags.Success">
      <summary>성공한 액세스 시도가 감사됩니다.</summary>
    </member>
    <member name="T:System.Security.AccessControl.AuditRule">
      <summary>사용자의 ID와 액세스 마스크의 조합을 나타냅니다.또한 <see cref="T:System.Security.AccessControl.AuditRule" /> 개체에는 자식 개체에서 규칙을 상속하는 방법, 상속을 전파하는 방법 및 감사 조건에 대한 정보가 들어 있습니다.</summary>
    </member>
    <member name="M:System.Security.AccessControl.AuditRule.#ctor(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
      <summary>지정된 값을 사용하여 <see cref="T:System.Security.AccessControl.AuditRule" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="identity">감사 규칙이 적용되는 ID입니다.이 값은 <see cref="T:System.Security.Principal.SecurityIdentifier" />로 캐스팅할 수 있는 개체여야 합니다.</param>
      <param name="accessMask">이 규칙의 액세스 마스크입니다.액세스 마스크는 익명 비트의 32비트 컬렉션으로, 비트의 의미는 개별 통합자가 정의합니다.</param>
      <param name="isInherited">이 규칙이 부모 컨테이너에서 상속되면 true입니다.</param>
      <param name="inheritanceFlags">감사 규칙의 상속 속성입니다.</param>
      <param name="propagationFlags">상속된 감사 규칙을 자동으로 전파할 것인지 여부를 나타냅니다.<paramref name="inheritanceFlags" />가 <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />으로 설정되면 전파 플래그는 무시됩니다.</param>
      <param name="auditFlags">규칙의 감사 조건입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="identity" /> 매개 변수 값을 <see cref="T:System.Security.Principal.SecurityIdentifier" />로 캐스팅할 수 없거나 <paramref name="auditFlags" /> 매개 변수에 잘못된 값이 포함되어 있는 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="accessMask" /> 매개 변수 값이 0이거나 <paramref name="inheritanceFlags" /> 또는 <paramref name="propagationFlags" /> 매개 변수에 인식할 수 없는 플래그 값이 포함된 경우</exception>
    </member>
    <member name="P:System.Security.AccessControl.AuditRule.AuditFlags">
      <summary>이 감사 규칙의 감사 플래그를 가져옵니다.</summary>
      <returns>열거형 값의 비트 조합입니다.이 조합은 이 감사 규칙의 감사 조건을 지정합니다.</returns>
    </member>
    <member name="T:System.Security.AccessControl.AuditRule`1">
      <summary>사용자의 ID와 액세스 마스크의 조합을 나타냅니다.</summary>
      <typeparam name="T"></typeparam>
    </member>
    <member name="M:System.Security.AccessControl.AuditRule`1.#ctor(System.Security.Principal.IdentityReference,`0,System.Security.AccessControl.AuditFlags)">
      <summary>지정된 값을 사용하여 AuditRule’1 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="identity">이 감사 규칙이 적용되는 ID입니다.</param>
      <param name="rights">감사 규칙의 권한입니다.</param>
      <param name="flags">규칙의 감사 조건입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.AuditRule`1.#ctor(System.Security.Principal.IdentityReference,`0,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
      <summary>지정된 값을 사용하여 AuditRule’1 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="identity">감사 규칙이 적용되는 ID입니다. </param>
      <param name="rights">감사 규칙의 권한입니다.</param>
      <param name="inheritanceFlags">감사 규칙의 상속 속성입니다.</param>
      <param name="propagationFlags">상속된 감사 규칙을 자동으로 전파할 것인지 여부를 나타냅니다.</param>
      <param name="flags">규칙의 감사 조건입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.AuditRule`1.#ctor(System.String,`0,System.Security.AccessControl.AuditFlags)">
      <summary>지정된 값을 사용하여 AuditRule’1 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="identity">감사 규칙이 적용되는 ID입니다.</param>
      <param name="rights">감사 규칙의 권한입니다.</param>
      <param name="flags">감사 규칙의 속성입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.AuditRule`1.#ctor(System.String,`0,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
      <summary>지정된 값을 사용하여 AuditRule’1 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="identity">감사 규칙이 적용되는 ID입니다.</param>
      <param name="rights">감사 규칙의 권한입니다.</param>
      <param name="inheritanceFlags">감사 규칙의 상속 속성입니다.</param>
      <param name="propagationFlags">상속된 감사 규칙을 자동으로 전파할 것인지 여부를 나타냅니다.</param>
      <param name="flags">규칙의 감사 조건입니다.</param>
    </member>
    <member name="P:System.Security.AccessControl.AuditRule`1.Rights">
      <summary>감사 규칙의 권한입니다.</summary>
      <returns>
        <see cref="{0}" />를 반환합니다.</returns>
    </member>
    <member name="T:System.Security.AccessControl.AuthorizationRule">
      <summary>보안 가능한 개체에 대한 액세스를 결정합니다.파생 클래스 <see cref="T:System.Security.AccessControl.AccessRule" /> 및 <see cref="T:System.Security.AccessControl.AuditRule" />은 특수 액세스 및 감사 기능을 제공합니다.</summary>
    </member>
    <member name="M:System.Security.AccessControl.AuthorizationRule.#ctor(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>지정된 값을 사용하여 <see cref="T:System.Security.AuthorizationControl.AccessRule" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="identity">액세스 규칙이 적용되는 ID입니다.  이 매개 변수는 <see cref="T:System.Security.Principal.SecurityIdentifier" />로 캐스팅할 수 있는 개체여야 합니다.</param>
      <param name="accessMask">이 규칙의 액세스 마스크입니다.액세스 마스크는 익명 비트의 32비트 컬렉션으로, 비트의 의미는 개별 통합자가 정의합니다.</param>
      <param name="isInherited">이 규칙이 부모 컨테이너에서 상속되면 true입니다.</param>
      <param name="inheritanceFlags">액세스 규칙의 상속 속성입니다.</param>
      <param name="propagationFlags">상속된 액세스 규칙이 자동으로 전파되는지 여부입니다.<paramref name="inheritanceFlags" />가 <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />으로 설정되면 전파 플래그는 무시됩니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="identity" /> 매개 변수 값을 <see cref="T:System.Security.Principal.SecurityIdentifier" />로 캐스팅할 수 없는 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="accessMask" /> 매개 변수 값이 0이거나 <paramref name="inheritanceFlags" /> 또는 <paramref name="propagationFlags" /> 매개 변수에 인식할 수 없는 플래그 값이 포함된 경우</exception>
    </member>
    <member name="P:System.Security.AccessControl.AuthorizationRule.AccessMask">
      <summary>이 규칙의 액세스 마스크를 가져옵니다.</summary>
      <returns>이 규칙의 액세스 마스크입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.AuthorizationRule.IdentityReference">
      <summary>이 규칙을 적용할 <see cref="T:System.Security.Principal.IdentityReference" />를 가져옵니다.</summary>
      <returns>이 규칙을 적용할 <see cref="T:System.Security.Principal.IdentityReference" />입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.AuthorizationRule.InheritanceFlags">
      <summary>이 규칙이 자식 개체에서 상속되는 방법을 결정하는 플래그 값을 가져옵니다.</summary>
      <returns>열거형 값의 비트 조합입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.AuthorizationRule.IsInherited">
      <summary>이 규칙을 명시적으로 설정할 것인지, 아니면 부모 컨테이너 개체에서 상속할 것인지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>이 규칙을 명시적으로 설정하지 않고 부모 컨테이너에서 상속하면 true입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.AuthorizationRule.PropagationFlags">
      <summary>이 규칙의 상속을 자식 개체로 전파하는 방법을 결정하는 전파 플래그 값을 가져옵니다.이 속성은 <see cref="T:System.Security.AccessControl.InheritanceFlags" /> 열거형 값이 <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />이 아닌 경우에만 의미를 가집니다.</summary>
      <returns>열거형 값의 비트 조합입니다.</returns>
    </member>
    <member name="T:System.Security.AccessControl.AuthorizationRuleCollection">
      <summary>
        <see cref="T:System.Security.AccessControl.AuthorizationRule" /> 개체의 컬렉션을 나타냅니다.</summary>
    </member>
    <member name="M:System.Security.AccessControl.AuthorizationRuleCollection.#ctor">
      <summary>
        <see cref="T:System.Security.AccessControl.AuthorizationRuleCollection" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Security.AccessControl.AuthorizationRuleCollection.AddRule(System.Security.AccessControl.AuthorizationRule)">
      <summary>컬렉션에 <see cref="T:System.Web.Configuration.AuthorizationRule" /> 개체를 추가합니다.</summary>
      <param name="rule">컬렉션에 추가할 <see cref="T:System.Web.Configuration.AuthorizationRule" /> 개체입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.AuthorizationRuleCollection.CopyTo(System.Security.AccessControl.AuthorizationRule[],System.Int32)">
      <summary>컬렉션의 내용을 배열에 복사합니다.</summary>
      <param name="rules">컬렉션의 내용을 복사할 배열입니다.</param>
      <param name="index">복사가 시작되는 인덱스(0부터 시작)입니다.</param>
    </member>
    <member name="P:System.Security.AccessControl.AuthorizationRuleCollection.Count"></member>
    <member name="P:System.Security.AccessControl.AuthorizationRuleCollection.Item(System.Int32)">
      <summary>컬렉션의 지정된 인덱스에서 <see cref="T:System.Security.AccessControl.AuthorizationRule" /> 개체를 가져옵니다.</summary>
      <returns>지정된 인덱스의 <see cref="T:System.Security.AccessControl.AuthorizationRule" /> 개체입니다.</returns>
      <param name="index">가져올 <see cref="T:System.Security.AccessControl.AuthorizationRule" /> 개체의 인덱스(0부터 시작)입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.AuthorizationRuleCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)"></member>
    <member name="P:System.Security.AccessControl.AuthorizationRuleCollection.System#Collections#ICollection#IsSynchronized"></member>
    <member name="P:System.Security.AccessControl.AuthorizationRuleCollection.System#Collections#ICollection#SyncRoot"></member>
    <member name="M:System.Security.AccessControl.AuthorizationRuleCollection.System#Collections#IEnumerable#GetEnumerator"></member>
    <member name="T:System.Security.AccessControl.CommonAce">
      <summary>ACE(액세스 제어 항목)를 나타냅니다.</summary>
    </member>
    <member name="M:System.Security.AccessControl.CommonAce.#ctor(System.Security.AccessControl.AceFlags,System.Security.AccessControl.AceQualifier,System.Int32,System.Security.Principal.SecurityIdentifier,System.Boolean,System.Byte[])">
      <summary>
        <see cref="T:System.Security.AccessControl.CommonAce" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="flags">새 ACE(액세스 제어 항목)의 상속, 상속 전파 및 감사 조건에 대한 정보를 지정하는 플래그입니다.</param>
      <param name="qualifier">새 ACE의 사용 방법입니다.</param>
      <param name="accessMask">ACE의 액세스 마스크입니다.</param>
      <param name="sid">새 ACE와 관련된 <see cref="T:System.Security.Principal.SecurityIdentifier" />입니다.</param>
      <param name="isCallback">새 ACE가 콜백 형식 ACE임을 지정하려면 true입니다.</param>
      <param name="opaque">새 ACE와 관련된 불투명 데이터입니다.불투명 데이터는 콜백 ACE 형식에만 허용됩니다.이 배열의 길이는 <see cref="M:System.Security.AccessControl.CommonAce.MaxOpaqueLength(System.Boolean)" /> 메서드의 반환 값보다 크지 않아야 합니다.</param>
    </member>
    <member name="P:System.Security.AccessControl.CommonAce.BinaryLength">
      <summary>현재 <see cref="T:System.Security.AccessControl.CommonAce" /> 개체에 대한 이진 표현의 길이(바이트)를 가져옵니다.이 길이는 ACL을 이진 배열로 마샬링하기 전에 <see cref="M:System.Security.AccessControl.CommonAce.GetBinaryForm(System.Byte[],System.Int32)" /> 메서드와 함께 사용합니다.</summary>
      <returns>현재 <see cref="T:System.Security.AccessControl.CommonAce" /> 개체의 이진 표현 길이(바이트)입니다.</returns>
    </member>
    <member name="M:System.Security.AccessControl.CommonAce.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>
        <see cref="T:System.Security.AccessControl.CommonAce" /> 개체의 내용을 특정 오프셋 위치에서 시작하여 지정된 바이트 배열에 마샬링합니다.</summary>
      <param name="binaryForm">
        <see cref="T:System.Security.AccessControl.CommonAce" /> 개체의 내용이 마샬링되는 바이트 배열입니다.</param>
      <param name="offset">마샬링을 시작할 오프셋입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" />이 음수이거나, 너무 커서 전체 <see cref="T:System.Security.AccessControl.CommonAce" />를 <paramref name="binaryForm" /> 배열에 복사할 수 없는 경우</exception>
    </member>
    <member name="M:System.Security.AccessControl.CommonAce.MaxOpaqueLength(System.Boolean)">
      <summary>콜백 ACE(액세스 제어 항목)에 대해 불투명 데이터 BLOB의 최대 허용 길이를 가져옵니다.</summary>
      <returns>불투명 데이터 BLOB의 허용 길이입니다.</returns>
      <param name="isCallback">
        <see cref="T:System.Security.AccessControl.CommonAce" /> 개체가 콜백 ACE 형식임을 지정하려면 true입니다.</param>
    </member>
    <member name="T:System.Security.AccessControl.CommonAcl">
      <summary>ACL(액세스 제어 목록)을 나타내며 <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> 및 <see cref="T:System.Security.AccessControl.SystemAcl" /> 클래스의 기본 클래스입니다.</summary>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.BinaryLength">
      <summary>현재 <see cref="T:System.Security.AccessControl.CommonAcl" /> 개체에 대한 이진 표현의 길이(바이트)를 가져옵니다.이 길이는 <see cref="M:System.Security.AccessControl.CommonAcl.GetBinaryForm(System.Byte[],System.Int32)" /> 메서드를 사용하여 ACL(액세스 제어 목록)을 이진 배열로 마샬링하기 전에 사용해야 합니다.</summary>
      <returns>현재 <see cref="T:System.Security.AccessControl.CommonAcl" /> 개체의 이진 표현 길이(바이트)입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.Count">
      <summary>현재 <see cref="T:System.Security.AccessControl.CommonAcl" /> 개체의 ACE(액세스 제어 항목) 수를 가져옵니다.</summary>
      <returns>현재 <see cref="T:System.Security.AccessControl.CommonAcl" /> 개체의 ACE 수입니다.</returns>
    </member>
    <member name="M:System.Security.AccessControl.CommonAcl.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>
        <see cref="T:System.Security.AccessControl.CommonAcl" /> 개체의 내용을 특정 오프셋에서 시작하여 지정된 바이트 배열에 마샬링합니다.</summary>
      <param name="binaryForm">
        <see cref="T:System.Security.AccessControl.CommonAcl" />의 내용이 마샬링되는 바이트 배열입니다.</param>
      <param name="offset">마샬링을 시작할 오프셋입니다.</param>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.IsCanonical">
      <summary>현재 <see cref="T:System.Security.AccessControl.CommonAcl" /> 개체의 ACE(액세스 제어 항목)가 정식 순서대로 되어 있는지 여부를 지정하는 부울 값을 가져옵니다.</summary>
      <returns>현재 <see cref="T:System.Security.AccessControl.CommonAcl" /> 개체의 ACE가 정식 순서대로 되어 있으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.IsContainer">
      <summary>
        <see cref="T:System.Security.AccessControl.CommonAcl" /> 개체가 컨테이너인지 여부를 설정합니다. </summary>
      <returns>현재 <see cref="T:System.Security.AccessControl.CommonAcl" /> 개체가 컨테이너이면 true입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.IsDS">
      <summary>현재 <see cref="T:System.Security.AccessControl.CommonAcl" /> 개체가 디렉터리 개체 ACL(액세스 제어 목록)인지 여부를 설정합니다.</summary>
      <returns>현재 <see cref="T:System.Security.AccessControl.CommonAcl" /> 개체가 디렉터리 개체 ACL이면 true입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.Item(System.Int32)">
      <summary>지정된 인덱스에 있는 <see cref="T:System.Security.AccessControl.CommonAce" />를 가져오거나 설정합니다.</summary>
      <returns>지정된 인덱스의 <see cref="T:System.Security.AccessControl.CommonAce" />입니다.</returns>
      <param name="index">가져오거나 설정할 <see cref="T:System.Security.AccessControl.CommonAce" />의 인덱스(0부터 시작)입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonAcl.Purge(System.Security.Principal.SecurityIdentifier)">
      <summary>지정한 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체와 관련되어 있으며 이 <see cref="T:System.Security.AccessControl.CommonAcl" /> 개체에 포함된 모든 ACE(액세스 제어 항목)를 제거합니다.</summary>
      <param name="sid">확인할 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonAcl.RemoveInheritedAces">
      <summary>이 <see cref="T:System.Security.AccessControl.CommonAcl" /> 개체에서 상속된 모든 ACE(액세스 제어 항목)를 제거합니다.</summary>
    </member>
    <member name="P:System.Security.AccessControl.CommonAcl.Revision">
      <summary>
        <see cref="T:System.Security.AccessControl.CommonAcl" />의 수정 수준을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Security.AccessControl.CommonAcl" />의 수정 수준을 지정하는 바이트 값입니다.</returns>
    </member>
    <member name="T:System.Security.AccessControl.CommonObjectSecurity">
      <summary>ACL(액세스 제어 목록)을 직접 조작하지 않고 개체에 대한 액세스를 제어합니다.이 클래스는 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 클래스에 대한 추상 기본 클래스입니다.</summary>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.#ctor(System.Boolean)">
      <summary>
        <see cref="T:System.Security.AccessControl.CommonObjectSecurity" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="isContainer">새 개체가 컨테이너 개체이면 true입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.AddAccessRule(System.Security.AccessControl.AccessRule)">
      <summary>지정한 액세스 규칙을 이 <see cref="T:System.Security.AccessControl.CommonObjectSecurity" /> 개체와 관련된 DACL(임의 액세스 제어 목록)에 추가합니다.</summary>
      <param name="rule">추가할 액세스 규칙입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.AddAuditRule(System.Security.AccessControl.AuditRule)">
      <summary>이 <see cref="T:System.Security.AccessControl.CommonObjectSecurity" /> 개체와 관련된 SACL(시스템 액세스 제어 목록)에 지정된 감사 규칙을 추가합니다.</summary>
      <param name="rule">추가할 감사 규칙입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.GetAccessRules(System.Boolean,System.Boolean,System.Type)">
      <summary>지정된 보안 식별자와 관련된 액세스 규칙 컬렉션을 가져옵니다.</summary>
      <returns>지정된 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체와 관련된 액세스 규칙 컬렉션입니다.</returns>
      <param name="includeExplicit">개체에 대해 명시적으로 설정된 액세스 규칙을 포함시키려면 true입니다.</param>
      <param name="includeInherited">상속된 액세스 규칙을 포함시키려면 true입니다.</param>
      <param name="targetType">액세스 규칙을 검색할 보안 식별자가 T:System.Security.Principal.SecurityIdentifier 형식인지 T:System.Security.Principal.NTAccount 형식인지 여부를 지정합니다.이 매개 변수의 값은 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 형식으로 변환 가능한 형식이어야 합니다.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.GetAuditRules(System.Boolean,System.Boolean,System.Type)">
      <summary>지정된 보안 식별자와 관련된 감사 규칙 컬렉션을 가져옵니다.</summary>
      <returns>지정된 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체와 관련된 감사 규칙 컬렉션입니다.</returns>
      <param name="includeExplicit">개체에 대해 명시적으로 설정된 감사 규칙을 포함시키려면 true입니다.</param>
      <param name="includeInherited">상속된 감사 규칙을 포함시키려면 true입니다.</param>
      <param name="targetType">감사 규칙을 검색할 보안 식별자입니다.이 값은 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체로 캐스팅할 수 있는 개체여야 합니다.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.ModifyAccess(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AccessRule,System.Boolean@)">
      <summary>지정된 수정 사항을 이 <see cref="T:System.Security.AccessControl.CommonObjectSecurity" /> 개체와 관련된 DACL(임의 액세스 제어 목록)에 적용합니다.</summary>
      <returns>DACL이 성공적으로 수정되면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="modification">DACL에 적용할 수정 내용입니다.</param>
      <param name="rule">수정할 액세스 규칙입니다.</param>
      <param name="modified">DACL이 성공적으로 수정되면 true이고, 그렇지 않으면 false입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.ModifyAudit(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AuditRule,System.Boolean@)">
      <summary>이 <see cref="T:System.Security.AccessControl.CommonObjectSecurity" /> 개체와 관련된 SACL(시스템 액세스 제어 목록)에 지정된 수정 내용을 적용합니다.</summary>
      <returns>SACL이 성공적으로 수정되면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="modification">SACL에 적용할 수정 내용입니다.</param>
      <param name="rule">수정할 감사 규칙입니다.</param>
      <param name="modified">SACL이 성공적으로 수정되면 true이고, 그렇지 않으면 false입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAccessRule(System.Security.AccessControl.AccessRule)">
      <summary>이 <see cref="T:System.Security.AccessControl.CommonObjectSecurity" /> 개체와 관련된 DACL(임의 액세스 제어 목록)에서 지정한 액세스 규칙과 동일한 보안 식별자 및 액세스 마스크가 들어 있는 액세스 규칙을 제거합니다.</summary>
      <returns>액세스 규칙이 성공적으로 제거되었으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="rule">제거할 액세스 규칙입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAccessRuleAll(System.Security.AccessControl.AccessRule)">
      <summary>이 <see cref="T:System.Security.AccessControl.CommonObjectSecurity" /> 개체와 관련된 DACL(임의 액세스 제어 목록)에서 지정한 액세스 규칙과 동일한 보안 식별자가 있는 모든 액세스 규칙을 제거합니다.</summary>
      <param name="rule">제거할 액세스 규칙입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAccessRuleSpecific(System.Security.AccessControl.AccessRule)">
      <summary>이 <see cref="T:System.Security.AccessControl.CommonObjectSecurity" /> 개체와 관련된 DACL(임의 액세스 제어 목록)에서 지정한 액세스 규칙과 정확히 일치하는 모든 액세스 규칙을 제거합니다.</summary>
      <param name="rule">제거할 액세스 규칙입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAuditRule(System.Security.AccessControl.AuditRule)">
      <summary>이 <see cref="T:System.Security.AccessControl.CommonObjectSecurity" /> 개체와 관련된 SACL(시스템 액세스 제어 목록)에서 지정된 감사 규칙과 동일한 보안 식별자 및 액세스 마스크를 포함하는 액세스 규칙을 제거합니다.</summary>
      <returns>감사 규칙이 성공적으로 제거되었으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="rule">제거할 감사 규칙입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAuditRuleAll(System.Security.AccessControl.AuditRule)">
      <summary>이 <see cref="T:System.Security.AccessControl.CommonObjectSecurity" /> 개체와 관련된 SACL(시스템 액세스 제어 목록)에서 지정된 감사 규칙과 동일한 보안 식별자를 갖는 모든 감사 규칙을 제거합니다.</summary>
      <param name="rule">제거할 감사 규칙입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAuditRuleSpecific(System.Security.AccessControl.AuditRule)">
      <summary>이 <see cref="T:System.Security.AccessControl.CommonObjectSecurity" /> 개체와 관련된 SACL(시스템 액세스 제어 목록)에서 지정된 감사 규칙과 정확히 일치하는 모든 감사 규칙을 제거합니다.</summary>
      <param name="rule">제거할 감사 규칙입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.ResetAccessRule(System.Security.AccessControl.AccessRule)">
      <summary>
        <see cref="T:System.Security.AccessControl.CommonObjectSecurity" /> 개체와 관련된 DACL(임의 액세스 제어 목록)의 모든 액세스 규칙을 제거한 다음 지정한 액세스 규칙을 추가합니다.</summary>
      <param name="rule">재설정할 액세스 규칙입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.SetAccessRule(System.Security.AccessControl.AccessRule)">
      <summary>이 <see cref="T:System.Security.AccessControl.CommonObjectSecurity" /> 개체와 관련된 DACL(임의 액세스 제어 목록)에서 지정한 액세스 규칙과 동일한 보안 식별자 및 한정자가 들어 있는 모든 액세스 규칙을 제거한 다음 지정한 액세스 규칙을 추가합니다.</summary>
      <param name="rule">설정할 액세스 규칙입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonObjectSecurity.SetAuditRule(System.Security.AccessControl.AuditRule)">
      <summary>이 <see cref="T:System.Security.AccessControl.CommonObjectSecurity" /> 개체와 관련된 SACL(시스템 액세스 제어 목록)에서 지정된 감사 규칙과 동일한 보안 식별자 및 한정자를 포함하는 모든 감사 규칙을 제거한 후 지정한 감사 규칙을 추가합니다.</summary>
      <param name="rule">설정할 감사 규칙입니다.</param>
    </member>
    <member name="T:System.Security.AccessControl.CommonSecurityDescriptor">
      <summary>보안 설명자를 나타냅니다.보안 설명자에는 소유자, 주 그룹, DACL(임의 액세스 제어 목록) 및 SACL(시스템 액세스 제어 목록)이 포함됩니다.</summary>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.#ctor(System.Boolean,System.Boolean,System.Byte[],System.Int32)">
      <summary>지정된 바이트 값 배열을 사용하여 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="isContainer">새 보안 설명자가 컨테이너 개체와 관련되어 있는 경우 true입니다.</param>
      <param name="isDS">새 보안 설명자가 디렉터리 개체와 관련되어 있는 경우 true입니다.</param>
      <param name="binaryForm">새 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 개체를 만들 바이트 값 배열입니다.</param>
      <param name="offset">
        <paramref name="binaryForm" /> 배열에서 복사를 시작할 오프셋입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.#ctor(System.Boolean,System.Boolean,System.Security.AccessControl.ControlFlags,System.Security.Principal.SecurityIdentifier,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.SystemAcl,System.Security.AccessControl.DiscretionaryAcl)">
      <summary>지정된 정보에서 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="isContainer">새 보안 설명자가 컨테이너 개체와 관련되어 있는 경우 true입니다.</param>
      <param name="isDS">새 보안 설명자가 디렉터리 개체와 관련되어 있는 경우 true입니다.</param>
      <param name="flags">새 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 개체의 동작을 지정하는 플래그입니다.</param>
      <param name="owner">새 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 개체의 소유자입니다.</param>
      <param name="group">새 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 개체의 주 그룹입니다.</param>
      <param name="systemAcl">새 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 개체의 SACL(시스템 액세스 제어 목록)입니다.</param>
      <param name="discretionaryAcl">새 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 개체의 DACL(임의 액세스 제어 목록)입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.#ctor(System.Boolean,System.Boolean,System.Security.AccessControl.RawSecurityDescriptor)">
      <summary>지정된 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 개체에서 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="isContainer">새 보안 설명자가 컨테이너 개체와 관련되어 있는 경우 true입니다.</param>
      <param name="isDS">새 보안 설명자가 디렉터리 개체와 관련되어 있는 경우 true입니다.</param>
      <param name="rawSecurityDescriptor">새 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 개체를 만들 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 개체입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.#ctor(System.Boolean,System.Boolean,System.String)">
      <summary>지정한 SDDL(Security Descriptor Definition Language) 문자열에서 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="isContainer">새 보안 설명자가 컨테이너 개체와 관련되어 있는 경우 true입니다.</param>
      <param name="isDS">새 보안 설명자가 디렉터리 개체와 관련되어 있는 경우 true입니다.</param>
      <param name="sddlForm">새 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 개체를 만들 SDDL 문자열입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.AddDiscretionaryAcl(System.Byte,System.Int32)">
      <summary>설정의 <see cref="P:System.Security.AccessControl.CommonSecurityDescriptor.DiscretionaryAcl" /> 이 대 한 속성 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 인스턴스 및 설정의 <see cref="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclPresent" /> 플래그입니다.</summary>
      <param name="revision">새 <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> 개체의 수정 수준입니다.</param>
      <param name="trusted">이 <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> 개체에 포함될 수 있는 ACE(액세스 제어 항목)의 수입니다.이 번호는 힌트로만 사용됩니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.AddSystemAcl(System.Byte,System.Int32)">
      <summary>설정의 <see cref="P:System.Security.AccessControl.CommonSecurityDescriptor.SystemAcl" /> 이 대 한 속성 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 인스턴스 및 설정의 <see cref="F:System.Security.AccessControl.ControlFlags.SystemAclPresent" /> 플래그입니다.</summary>
      <param name="revision">새 <see cref="T:System.Security.AccessControl.SystemAcl" /> 개체의 수정 수준입니다.</param>
      <param name="trusted">이 <see cref="T:System.Security.AccessControl.SystemAcl" /> 개체에 포함될 수 있는 ACE(액세스 제어 항목)의 수입니다.이 번호는 힌트로만 사용됩니다.</param>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.ControlFlags">
      <summary>
        <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 개체의 동작을 지정하는 값을 가져옵니다.</summary>
      <returns>논리 OR 연산으로 조합된 <see cref="T:System.Security.AccessControl.ControlFlags" /> 열거형의 하나 이상의 값입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.DiscretionaryAcl">
      <summary>이 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 개체의 DACL(임의 액세스 제어 목록)을 가져오거나 설정합니다.DACL에는 액세스 규칙이 포함되어 있습니다.</summary>
      <returns>이 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 개체의 DACL입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.Group">
      <summary>이 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 개체의 주 그룹을 가져오거나 설정합니다.</summary>
      <returns>이 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 개체의 주 그룹입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.IsContainer">
      <summary>이 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 개체와 관련된 개체가 컨테이너 개체인지 여부를 지정하는 부울 값을 가져옵니다.</summary>
      <returns>이 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 개체와 관련된 개체가 컨테이너 개체이면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.IsDiscretionaryAclCanonical">
      <summary>이 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 개체와 관련된 DACL(임의 액세스 제어 목록)이 정식 순서대로 되어 있는지 여부를 지정하는 부울 값을 가져옵니다.</summary>
      <returns>이 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 개체와 관련된 DACL이 정식 순서대로 되어 있으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.IsDS">
      <summary>이 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 개체와 관련된 개체가 디렉터리 개체인지 여부를 지정하는 부울 값을 가져옵니다.</summary>
      <returns>이 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 개체와 관련된 개체가 디렉터리 개체이면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.IsSystemAclCanonical">
      <summary>이 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 개체와 관련된 SACL(시스템 액세스 제어 목록)이 정식 순서대로 되어 있는지 여부를 지정하는 부울 값을 가져옵니다.</summary>
      <returns>이 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 개체와 관련된 SACL이 정식 순서대로 되어 있으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.Owner">
      <summary>이 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 개체와 관련된 개체의 소유자를 가져오거나 설정합니다.</summary>
      <returns>이 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 개체와 관련된 개체의 소유자입니다.</returns>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.PurgeAccessControl(System.Security.Principal.SecurityIdentifier)">
      <summary>이 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 개체와 관련된 DACL(임의 액세스 제어 목록)에서 지정된 보안 식별자에 대한 모든 액세스 규칙을 제거합니다.</summary>
      <param name="sid">액세스 규칙을 제거할 보안 식별자입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.PurgeAudit(System.Security.Principal.SecurityIdentifier)">
      <summary>이 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 개체와 관련된 SACL(시스템 액세스 제어 목록)에서 지정된 보안 식별자에 대한 모든 감사 규칙을 제거합니다.</summary>
      <param name="sid">감사 규칙을 제거할 보안 식별자입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.SetDiscretionaryAclProtection(System.Boolean,System.Boolean)">
      <summary>이 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 개체와 관련된 DACL(임의 액세스 제어 목록)에 대해 상속 보호를 설정합니다.보호된 DACL은 부모 컨테이너에서 액세스 규칙을 상속하지 않습니다.</summary>
      <param name="isProtected">상속으로부터 DACL을 보호하려면 true입니다.</param>
      <param name="preserveInheritance">DACL에 상속된 액세스 규칙을 유지하려면 true이고, DACL에서 상속된 액세스 규칙을 제거하려면 false입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.SetSystemAclProtection(System.Boolean,System.Boolean)">
      <summary>이 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 개체와 관련된 SACL(시스템 액세스 제어 목록)에 대해 상속 보호를 설정합니다.보호된 SACL은 부모 컨테이너에서 감사 규칙을 상속하지 않습니다.</summary>
      <param name="isProtected">상속으로부터 SACL을 보호하려면 true입니다.</param>
      <param name="preserveInheritance">SACL에 상속된 감사 규칙을 유지하려면 true이고, SACL에서 상속된 감사 규칙을 제거하려면 false입니다.</param>
    </member>
    <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.SystemAcl">
      <summary>이 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 개체의 SACL(시스템 액세스 제어 목록)을 가져오거나 설정합니다.SACL에는 감사 규칙이 포함되어 있습니다.</summary>
      <returns>이 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" /> 개체의 SACL입니다.</returns>
    </member>
    <member name="T:System.Security.AccessControl.CompoundAce">
      <summary>복합 ACE(액세스 제어 항목)를 나타냅니다.</summary>
    </member>
    <member name="M:System.Security.AccessControl.CompoundAce.#ctor(System.Security.AccessControl.AceFlags,System.Int32,System.Security.AccessControl.CompoundAceType,System.Security.Principal.SecurityIdentifier)">
      <summary>
        <see cref="T:System.Security.AccessControl.CompoundAce" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="flags">새 ACE(액세스 제어 항목)의 상속, 상속 전파 및 감사 조건에 대한 정보를 지정하는 플래그가 포함되어 있습니다.</param>
      <param name="accessMask">ACE의 액세스 마스크입니다.</param>
      <param name="compoundAceType">
        <see cref="T:System.Security.AccessControl.CompoundAceType" /> 열거형의 값입니다.</param>
      <param name="sid">새 ACE와 관련된 <see cref="T:System.Security.Principal.SecurityIdentifier" />입니다.</param>
    </member>
    <member name="P:System.Security.AccessControl.CompoundAce.BinaryLength">
      <summary>현재 <see cref="T:System.Security.AccessControl.CompoundAce" /> 개체에 대한 이진 표현의 길이(바이트)를 가져옵니다.이 길이는 <see cref="M:System.Security.AccessControl.CompoundAce.GetBinaryForm" /> 메서드를 사용하여 ACL을 이진 배열로 마샬링하기 전에 사용해야 합니다.</summary>
      <returns>현재 <see cref="T:System.Security.AccessControl.CompoundAce" /> 개체의 이진 표현 길이(바이트)입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.CompoundAce.CompoundAceType">
      <summary>이 <see cref="T:System.Security.AccessControl.CompoundAce" /> 개체의 형식을 가져오거나 설정합니다.</summary>
      <returns>이 <see cref="T:System.Security.AccessControl.CompoundAce" /> 개체의 형식입니다.</returns>
    </member>
    <member name="M:System.Security.AccessControl.CompoundAce.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>
        <see cref="T:System.Security.AccessControl.CompoundAce" /> 개체의 내용을 특정 오프셋 위치에서 시작하여 지정된 바이트 배열에 마샬링합니다.</summary>
      <param name="binaryForm">
        <see cref="T:System.Security.AccessControl.CompoundAce" />의 내용이 마샬링되는 바이트 배열입니다.</param>
      <param name="offset">마샬링을 시작할 오프셋입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 값이 음수이거나, 너무 커서 전체 <see cref="T:System.Security.AccessControl.CompoundAce" />를 <paramref name="array" />에 복사할 수 없는 경우</exception>
    </member>
    <member name="T:System.Security.AccessControl.CompoundAceType">
      <summary>
        <see cref="T:System.Security.AccessControl.CompoundAce" /> 개체의 형식을 지정합니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.CompoundAceType.Impersonation">
      <summary>
        <see cref="T:System.Security.AccessControl.CompoundAce" /> 개체는 가장에 사용됩니다.</summary>
    </member>
    <member name="T:System.Security.AccessControl.ControlFlags">
      <summary>이 플래그는 보안 설명자의 동작에 영향을 줍니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclAutoInherited">
      <summary>DACL(임의 액세스 제어 목록)이 부모에서 자동으로 상속되었음을 지정합니다.리소스 관리자에 의해서만 설정됩니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclAutoInheritRequired">
      <summary>무시합니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclDefaulted">
      <summary>DACL을 기본 메커니즘을 통해 가져왔음을 지정합니다.리소스 관리자에 의해서만 설정됩니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclPresent">
      <summary>DACL이 null이 아님을 지정합니다.리소스 관리자나 사용자가 설정합니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclProtected">
      <summary>리소스 관리자에 의해 자동 상속을 사용할 수 없음을 지정합니다.리소스 관리자나 사용자가 설정합니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclUntrusted">
      <summary>무시합니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.GroupDefaulted">
      <summary>
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> 그룹을 기본 메커니즘을 통해 가져왔음을 지정합니다.리소스 관리자에 의해서만 설정되며, 호출자는 설정하면 안 됩니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.None">
      <summary>제어 플래그가 없습니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.OwnerDefaulted">
      <summary>소유자 <see cref="T:System.Security.Principal.SecurityIdentifier" />를 기본 메커니즘을 통해 가져왔음을 지정합니다.리소스 관리자에 의해서만 설정되며, 호출자는 설정하면 안 됩니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.RMControlValid">
      <summary>예약 필드의 내용이 올바르다는 것을 지정합니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.SelfRelative">
      <summary>보안 설명자 이진 표시가 자체 상대 형식임을 지정합니다.  이 플래그는 항상 설정되어 있습니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.ServerSecurity">
      <summary>무시합니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.SystemAclAutoInherited">
      <summary>SACL(시스템 액세스 제어 목록)이 부모에서 자동으로 상속되었음을 지정합니다.리소스 관리자에 의해서만 설정됩니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.SystemAclAutoInheritRequired">
      <summary>무시합니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.SystemAclDefaulted">
      <summary>SACL을 기본 메커니즘을 통해 가져왔음을 지정합니다.리소스 관리자에 의해서만 설정됩니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.SystemAclPresent">
      <summary>DACL이 null이 아님을 지정합니다.리소스 관리자나 사용자가 설정합니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ControlFlags.SystemAclProtected">
      <summary>리소스 관리자에 의해 자동 상속을 사용할 수 없음을 지정합니다.리소스 관리자나 사용자가 설정합니다.</summary>
    </member>
    <member name="T:System.Security.AccessControl.CustomAce">
      <summary>
        <see cref="T:System.Security.AccessControl.AceType" /> 열거형 멤버 중 하나에 의해 정의되지 않은 ACE(액세스 제어 항목)를 나타냅니다.</summary>
    </member>
    <member name="M:System.Security.AccessControl.CustomAce.#ctor(System.Security.AccessControl.AceType,System.Security.AccessControl.AceFlags,System.Byte[])">
      <summary>
        <see cref="T:System.Security.AccessControl.CustomAce" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="type">새 ACE(액세스 제어 항목) 형식입니다.이 값은 <see cref="F:System.Security.AccessControl.AceType.MaxDefinedAceType" />보다 커야 합니다.</param>
      <param name="flags">새 ACE의 상속, 상속 전파 및 감사 조건에 대한 정보를 지정하는 플래그입니다.</param>
      <param name="opaque">새 ACE의 데이터가 포함된 바이트 값의 배열입니다.이 값은 null일 수 있습니다.이 배열의 길이는 <see cref="F:System.Security.AccessControl.CustomAce.MaxOpaqueLength" /> 필드 값보다 크지 않아야 하며 4의 배수여야 합니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="type" /> 매개 변수 값이 <see cref="F:System.Security.AccessControl.AceType.MaxDefinedAceType" />보다 크지 않거나 <paramref name="opaque" /> 배열의 길이가 <see cref="F:System.Security.AccessControl.CustomAce.MaxOpaqueLength" /> 필드 값보다 크거나 4의 배수가 아닌 경우</exception>
    </member>
    <member name="P:System.Security.AccessControl.CustomAce.BinaryLength">
      <summary>현재 <see cref="T:System.Security.AccessControl.CustomAce" /> 개체에 대한 이진 표현의 길이(바이트)를 가져옵니다.이 길이는 <see cref="M:System.Security.AccessControl.CustomAce.GetBinaryForm" /> 메서드를 사용하여 ACL을 이진 배열로 마샬링하기 전에 사용해야 합니다.</summary>
      <returns>현재 <see cref="T:System.Security.AccessControl.CustomAce" /> 개체의 이진 표현 길이(바이트)입니다.</returns>
    </member>
    <member name="M:System.Security.AccessControl.CustomAce.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>
        <see cref="T:System.Security.AccessControl.CustomAce" /> 개체의 내용을 특정 오프셋 위치에서 시작하여 지정된 바이트 배열에 마샬링합니다.</summary>
      <param name="binaryForm">
        <see cref="T:System.Security.AccessControl.CustomAce" />의 내용이 마샬링되는 바이트 배열입니다.</param>
      <param name="offset">마샬링을 시작할 오프셋입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 값이 음수이거나, 너무 커서 전체 <see cref="T:System.Security.AccessControl.CustomAce" />를 <paramref name="array" />에 복사할 수 없는 경우</exception>
    </member>
    <member name="M:System.Security.AccessControl.CustomAce.GetOpaque">
      <summary>이 <see cref="T:System.Security.AccessControl.CustomAce" /> 개체와 관련된 불투명 데이터를 반환합니다. </summary>
      <returns>이 <see cref="T:System.Security.AccessControl.CustomAce" /> 개체와 관련된 불투명 데이터를 나타내는 바이트 값의 배열입니다.</returns>
    </member>
    <member name="F:System.Security.AccessControl.CustomAce.MaxOpaqueLength">
      <summary>이 <see cref="T:System.Security.AccessControl.CustomAce" /> 개체에 대한 불투명 데이터 BLOB의 최대 허용 길이를 반환합니다.</summary>
    </member>
    <member name="P:System.Security.AccessControl.CustomAce.OpaqueLength">
      <summary>이 <see cref="T:System.Security.AccessControl.CustomAce" /> 개체와 관련된 불투명 데이터의 길이를 가져옵니다.</summary>
      <returns>불투명 콜백 데이터의 길이입니다.</returns>
    </member>
    <member name="M:System.Security.AccessControl.CustomAce.SetOpaque(System.Byte[])">
      <summary>이 <see cref="T:System.Security.AccessControl.CustomAce" /> 개체와 관련된 불투명 콜백 데이터를 설정합니다.</summary>
      <param name="opaque">이 <see cref="T:System.Security.AccessControl.CustomAce" /> 개체의 불투명 콜백 데이터를 나타내는 바이트 값의 배열입니다.</param>
    </member>
    <member name="T:System.Security.AccessControl.DiscretionaryAcl">
      <summary>DACL(임의 액세스 제어 목록)을 나타냅니다.</summary>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.#ctor(System.Boolean,System.Boolean,System.Byte,System.Int32)">
      <summary>지정된 값을 사용하여 <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="isContainer">true if the new <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> object is a container.</param>
      <param name="isDS">true if the new <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> object is a directory object Access Control List (ACL).</param>
      <param name="revision">새 <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> 개체의 수정 수준입니다.</param>
      <param name="capacity">이 <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> 개체에 포함될 수 있는 ACE(액세스 제어 항목)의 수입니다.이 번호는 힌트로만 사용됩니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.#ctor(System.Boolean,System.Boolean,System.Int32)">
      <summary>지정된 값을 사용하여 <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="isContainer">true if the new <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> object is a container.</param>
      <param name="isDS">true if the new <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> object is a directory object Access Control List (ACL).</param>
      <param name="capacity">이 <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> 개체에 포함될 수 있는 ACE(액세스 제어 항목)의 수입니다.이 번호는 힌트로만 사용됩니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.#ctor(System.Boolean,System.Boolean,System.Security.AccessControl.RawAcl)">
      <summary>지정된 <see cref="T:System.Security.AccessControl.RawAcl" /> 개체의 지정된 값을 사용하여 <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="isContainer">true if the new <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> object is a container.</param>
      <param name="isDS">true if the new <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> object is a directory object Access Control List (ACL).</param>
      <param name="rawAcl">The underlying <see cref="T:System.Security.AccessControl.RawAcl" /> object for the new <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> object.null을 지정하여 빈 ACL을 만듭니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.AddAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>지정된 설정의 ACE(액세스 제어 항목)를 현재 <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> 개체에 추가합니다.</summary>
      <param name="accessType">추가할 액세스 제어 형식(허용 또는 거부)입니다.</param>
      <param name="sid">ACE를 추가할 <see cref="T:System.Security.Principal.SecurityIdentifier" />입니다.</param>
      <param name="accessMask">새 ACE에 대한 액세스 규칙입니다.</param>
      <param name="inheritanceFlags">새 ACE의 상속 속성을 지정하는 플래그입니다.</param>
      <param name="propagationFlags">새 ACE의 상속 전파 속성을 지정하는 플래그입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.AddAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>지정된 설정의 ACE(액세스 제어 항목)를 현재 <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> 개체에 추가합니다.이 메서드는 새 ACE의 개체 형식 또는 상속된 개체 형식을 지정할 때 디렉터리 개체 ACL(액세스 제어 목록)에 사용합니다.</summary>
      <param name="accessType">추가할 액세스 제어 형식(허용 또는 거부)입니다.</param>
      <param name="sid">ACE를 추가할 <see cref="T:System.Security.Principal.SecurityIdentifier" />입니다.</param>
      <param name="accessMask">새 ACE에 대한 액세스 규칙입니다.</param>
      <param name="inheritanceFlags">새 ACE의 상속 속성을 지정하는 플래그입니다.</param>
      <param name="propagationFlags">새 ACE의 상속 전파 속성을 지정하는 플래그입니다.</param>
      <param name="objectFlags">
        <paramref name="objectType" /> 및 <paramref name="inheritedObjectType" /> 매개 변수에 null이 아닌 값이 포함되어 있는지 여부를 지정하는 플래그입니다.</param>
      <param name="objectType">새 ACE가 적용되는 개체 클래스의 ID입니다.</param>
      <param name="inheritedObjectType">새 ACE를 상속할 수 있는 자식 개체 클래스의 ID입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.AddAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAccessRule)">
      <summary>지정된 설정의 ACE(액세스 제어 항목)를 현재 <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> 개체에 추가합니다.</summary>
      <param name="accessType">추가할 액세스 제어 형식(허용 또는 거부)입니다.</param>
      <param name="sid">ACE를 추가할 <see cref="T:System.Security.Principal.SecurityIdentifier" />입니다.</param>
      <param name="rule">
        <see cref="T:System.Security.AccessControl.ObjectAccessRule" /> 에 대 한 새로운 액세스 합니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>지정한 액세스 제어 규칙을 현재 <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> 개체에서 제거합니다.</summary>
      <returns>지정한 액세스 권한이 이 메서드로 제거되면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="accessType">제거할 액세스 제어 형식(허용 또는 거부)입니다.</param>
      <param name="sid">액세스 제어 규칙을 제거할 <see cref="T:System.Security.Principal.SecurityIdentifier" />입니다.</param>
      <param name="accessMask">제거할 규칙에 대한 액세스 마스크입니다.</param>
      <param name="inheritanceFlags">제거할 규칙의 상속 속성을 지정하는 플래그입니다.</param>
      <param name="propagationFlags">제거할 규칙의 상속 전파 속성을 지정하는 플래그입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>지정한 액세스 제어 규칙을 현재 <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> 개체에서 제거합니다.이 메서드는 개체 형식 또는 상속된 개체 형식을 지정할 때 디렉터리 개체 ACL(액세스 제어 목록)에 사용합니다.</summary>
      <returns>지정한 액세스 권한이 이 메서드로 제거되면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="accessType">제거할 액세스 제어 형식(허용 또는 거부)입니다.</param>
      <param name="sid">액세스 제어 규칙을 제거할 <see cref="T:System.Security.Principal.SecurityIdentifier" />입니다.</param>
      <param name="accessMask">제거할 액세스 제어 규칙에 대한 액세스 마스크입니다.</param>
      <param name="inheritanceFlags">제거할 액세스 제어 규칙의 상속 속성을 지정하는 플래그입니다.</param>
      <param name="propagationFlags">제거할 액세스 제어 규칙의 상속 전파 속성을 지정하는 플래그입니다.</param>
      <param name="objectFlags">
        <paramref name="objectType" /> 및 <paramref name="inheritedObjectType" /> 매개 변수에 null이 아닌 값이 포함되어 있는지 여부를 지정하는 플래그입니다.</param>
      <param name="objectType">제거한 액세스 제어 규칙이 적용되는 개체 클래스의 ID입니다.</param>
      <param name="inheritedObjectType">제거한 액세스 제어 규칙을 상속할 수 있는 자식 개체 클래스의 ID입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAccessRule)">
      <summary>지정한 액세스 제어 규칙을 현재 <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> 개체에서 제거합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.</returns>
      <param name="accessType">제거할 액세스 제어 형식(허용 또는 거부)입니다.</param>
      <param name="sid">액세스 제어 규칙을 제거할 <see cref="T:System.Security.Principal.SecurityIdentifier" />입니다.</param>
      <param name="rule">
        <see cref="T:System.Security.AccessControl.ObjectAccessRule" /> 액세스 권한을 제거할입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccessSpecific(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>지정한 ACE(액세스 제어 항목)를 현재 <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> 개체에서 제거합니다.</summary>
      <param name="accessType">제거할 액세스 제어 형식(허용 또는 거부)입니다.</param>
      <param name="sid">ACE를 제거할 <see cref="T:System.Security.Principal.SecurityIdentifier" />입니다.</param>
      <param name="accessMask">제거할 ACE에 대한 액세스 마스크입니다.</param>
      <param name="inheritanceFlags">제거할 ACE의 상속 속성을 지정하는 플래그입니다.</param>
      <param name="propagationFlags">제거할 ACE의 상속 전파 속성을 지정하는 플래그입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccessSpecific(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>지정한 ACE(액세스 제어 항목)를 현재 <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> 개체에서 제거합니다.이 메서드는 제거할 ACE의 개체 형식 또는 상속된 개체 형식을 지정할 때 디렉터리 개체 ACL(액세스 제어 목록)에 사용합니다.</summary>
      <param name="accessType">제거할 액세스 제어 형식(허용 또는 거부)입니다.</param>
      <param name="sid">ACE를 제거할 <see cref="T:System.Security.Principal.SecurityIdentifier" />입니다.</param>
      <param name="accessMask">제거할 ACE에 대한 액세스 마스크입니다.</param>
      <param name="inheritanceFlags">제거할 ACE의 상속 속성을 지정하는 플래그입니다.</param>
      <param name="propagationFlags">제거할 ACE의 상속 전파 속성을 지정하는 플래그입니다.</param>
      <param name="objectFlags">
        <paramref name="objectType" /> 및 <paramref name="inheritedObjectType" /> 매개 변수에 null이 아닌 값이 포함되어 있는지 여부를 지정하는 플래그입니다.</param>
      <param name="objectType">제거한 ACE가 적용되는 개체 클래스의 ID입니다.</param>
      <param name="inheritedObjectType">제거한 ACE를 상속할 수 있는 자식 개체 클래스의 ID입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccessSpecific(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAccessRule)">
      <summary>지정한 ACE(액세스 제어 항목)를 현재 <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> 개체에서 제거합니다.</summary>
      <param name="accessType">제거할 액세스 제어 형식(허용 또는 거부)입니다.</param>
      <param name="sid">ACE를 제거할 <see cref="T:System.Security.Principal.SecurityIdentifier" />입니다.</param>
      <param name="rule">
        <see cref="T:System.Security.AccessControl.ObjectAccessRule" /> 액세스 권한을 제거할입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.SetAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>지정된 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체에 대해 지정된 액세스 제어를 설정합니다.</summary>
      <param name="accessType">설정할 액세스 제어 형식(허용 또는 거부)입니다.</param>
      <param name="sid">ACE를 설정할 <see cref="T:System.Security.Principal.SecurityIdentifier" />입니다.</param>
      <param name="accessMask">새 ACE에 대한 액세스 규칙입니다.</param>
      <param name="inheritanceFlags">새 ACE의 상속 속성을 지정하는 플래그입니다.</param>
      <param name="propagationFlags">새 ACE의 상속 전파 속성을 지정하는 플래그입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.SetAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>지정된 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체에 대해 지정된 액세스 제어를 설정합니다.</summary>
      <param name="accessType">설정할 액세스 제어 형식(허용 또는 거부)입니다.</param>
      <param name="sid">ACE를 설정할 <see cref="T:System.Security.Principal.SecurityIdentifier" />입니다.</param>
      <param name="accessMask">새 ACE에 대한 액세스 규칙입니다.</param>
      <param name="inheritanceFlags">새 ACE의 상속 속성을 지정하는 플래그입니다.</param>
      <param name="propagationFlags">새 ACE의 상속 전파 속성을 지정하는 플래그입니다.</param>
      <param name="objectFlags">
        <paramref name="objectType" /> 및 <paramref name="inheritedObjectType" /> 매개 변수에 null이 아닌 값이 포함되어 있는지 여부를 지정하는 플래그입니다.</param>
      <param name="objectType">새 ACE가 적용되는 개체 클래스의 ID입니다.</param>
      <param name="inheritedObjectType">새 ACE를 상속할 수 있는 자식 개체 클래스의 ID입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.DiscretionaryAcl.SetAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAccessRule)">
      <summary>지정된 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체에 대해 지정된 액세스 제어를 설정합니다.</summary>
      <param name="accessType">설정할 액세스 제어 형식(허용 또는 거부)입니다.</param>
      <param name="sid">ACE를 설정할 <see cref="T:System.Security.Principal.SecurityIdentifier" />입니다.</param>
      <param name="rule">
        <see cref="T:System.Security.AccessControl.ObjectAccessRule" /> 액세스 설정입니다.</param>
    </member>
    <member name="T:System.Security.AccessControl.GenericAce">
      <summary>ACE(액세스 제어 항목)를 나타내며 다른 모든 ACE 클래스의 기본 클래스입니다.</summary>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.AceFlags">
      <summary>이 <see cref="T:System.Security.AccessControl.GenericAce" /> 개체와 관련된 <see cref="T:System.Security.AccessControl.AceFlags" />를 가져오거나 설정합니다.</summary>
      <returns>이 <see cref="T:System.Security.AccessControl.GenericAce" /> 개체와 관련된 <see cref="T:System.Security.AccessControl.AceFlags" />입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.AceType">
      <summary>이 ACE(액세스 제어 항목)의 형식을 가져옵니다.</summary>
      <returns>이 ACE의 형식입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.AuditFlags">
      <summary>이 ACE(액세스 제어 항목)와 관련된 감사 정보를 가져옵니다.</summary>
      <returns>이 ACE(액세스 제어 항목)와 관련된 감사 정보입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.BinaryLength">
      <summary>현재 <see cref="T:System.Security.AccessControl.GenericAce" /> 개체에 대한 이진 표현의 길이(바이트)를 가져옵니다.이 길이는 <see cref="M:System.Security.AccessControl.GenericAce.GetBinaryForm" /> 메서드를 사용하여 ACL을 이진 배열로 마샬링하기 전에 사용해야 합니다.</summary>
      <returns>현재 <see cref="T:System.Security.AccessControl.GenericAce" /> 개체의 이진 표현 길이(바이트)입니다.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.Copy">
      <summary>이 ACE(액세스 제어 항목)의 전체 복사본을 만듭니다.</summary>
      <returns>이 메서드가 만드는 <see cref="T:System.Security.AccessControl.GenericAce" /> 개체를 반환합니다.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.CreateFromBinaryForm(System.Byte[],System.Int32)">
      <summary>지정된 이진 데이터에서 <see cref="T:System.Security.AccessControl.GenericAce" /> 개체를 만듭니다.</summary>
      <returns>이 메서드가 만드는 <see cref="T:System.Security.AccessControl.GenericAce" /> 개체를 반환합니다.</returns>
      <param name="binaryForm">새 <see cref="T:System.Security.AccessControl.GenericAce" /> 개체를 만들 이진 데이터입니다.</param>
      <param name="offset">역마샬링을 시작할 오프셋입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.Equals(System.Object)">
      <summary>지정된 <see cref="T:System.Security.AccessControl.GenericAce" /> 개체가 현재 <see cref="T:System.Security.AccessControl.GenericAce" /> 개체와 같은지 여부를 확인합니다.</summary>
      <returns>지정한 <see cref="T:System.Security.AccessControl.GenericAce" /> 개체가 현재 <see cref="T:System.Security.AccessControl.GenericAce" /> 개체와 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="o">현재 <see cref="T:System.Security.AccessControl.GenericAce" /> 개체와 비교할 <see cref="T:System.Security.AccessControl.GenericAce" /> 개체입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>
        <see cref="T:System.Security.AccessControl.GenericAce" /> 개체의 내용을 특정 오프셋 위치에서 시작하여 지정된 바이트 배열에 마샬링합니다.</summary>
      <param name="binaryForm">
        <see cref="T:System.Security.AccessControl.GenericAce" />의 내용이 마샬링되는 바이트 배열입니다.</param>
      <param name="offset">마샬링을 시작할 오프셋입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 값이 음수이거나, 너무 커서 전체 <see cref="T:System.Security.AccessControl.GenericAcl" />를 <paramref name="array" />에 복사할 수 없는 경우</exception>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.GetHashCode">
      <summary>
        <see cref="T:System.Security.AccessControl.GenericAce" /> 클래스에 대한 해시 함수 역할을 합니다.<see cref="M:System.Security.AccessControl.GenericAce.GetHashCode" /> 메서드는 해시 테이블과 같은 해시 알고리즘 및 데이터 구조에 적합합니다.</summary>
      <returns>현재 <see cref="T:System.Security.AccessControl.GenericAce" /> 개체의 해시 코드입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.InheritanceFlags">
      <summary>이 ACE(액세스 제어 항목)의 상속 속성을 지정하는 플래그를 가져옵니다.</summary>
      <returns>이 ACE(액세스 제어 항목)의 상속 속성을 지정하는 플래그입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.IsInherited">
      <summary>이 ACE(액세스 제어 항목)를 상속할 것인지 또는 명시적으로 설정할 것인지를 지정하는 부울 값을 가져옵니다.</summary>
      <returns>ACE가 상속된 경우 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.op_Equality(System.Security.AccessControl.GenericAce,System.Security.AccessControl.GenericAce)">
      <summary>지정한 <see cref="T:System.Security.AccessControl.GenericAce" /> 개체가 동일한지 여부를 확인합니다.</summary>
      <returns>두 <see cref="T:System.Security.AccessControl.GenericAce" /> 개체가 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="left">비교할 첫 번째 <see cref="T:System.Security.AccessControl.GenericAce" /> 개체입니다.</param>
      <param name="right">비교할 두 번째 <see cref="T:System.Security.AccessControl.GenericAce" />입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.GenericAce.op_Inequality(System.Security.AccessControl.GenericAce,System.Security.AccessControl.GenericAce)">
      <summary>지정한 <see cref="T:System.Security.AccessControl.GenericAce" /> 개체가 같지 않은지 여부를 확인합니다.</summary>
      <returns>두 <see cref="T:System.Security.AccessControl.GenericAce" /> 개체가 같지 않으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="left">비교할 첫 번째 <see cref="T:System.Security.AccessControl.GenericAce" /> 개체입니다.</param>
      <param name="right">비교할 두 번째 <see cref="T:System.Security.AccessControl.GenericAce" />입니다.</param>
    </member>
    <member name="P:System.Security.AccessControl.GenericAce.PropagationFlags">
      <summary>이 ACE(액세스 제어 항목)의 상속 전파 속성을 지정하는 플래그를 가져옵니다.</summary>
      <returns>이 ACE(액세스 제어 항목)의 상속 전파 속성을 지정하는 플래그입니다.</returns>
    </member>
    <member name="T:System.Security.AccessControl.GenericAcl">
      <summary>ACL(액세스 제어 목록)을 나타내며 <see cref="T:System.Security.AccessControl.CommonAcl" />, <see cref="T:System.Security.AccessControl.DiscretionaryAcl" />, <see cref="T:System.Security.AccessControl.RawAcl" /> 및 <see cref="T:System.Security.AccessControl.SystemAcl" /> 클래스의 기본 클래스입니다.</summary>
    </member>
    <member name="M:System.Security.AccessControl.GenericAcl.#ctor">
      <summary>
        <see cref="T:System.Security.AccessControl.GenericAcl" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.GenericAcl.AclRevision">
      <summary>현재 <see cref="T:System.Security.AccessControl.GenericAcl" />의 수정 수준입니다.이 값은 디렉터리 서비스 개체와 관련되지 않은 ACL(액세스 제어 목록)의 <see cref="P:System.Security.AccessControl.GenericAcl.Revision" /> 속성에 의해 반환됩니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.GenericAcl.AclRevisionDS">
      <summary>현재 <see cref="T:System.Security.AccessControl.GenericAcl" />의 수정 수준입니다.이 값은 디렉터리 서비스 개체와 관련된 ACL(액세스 제어 목록)의 <see cref="P:System.Security.AccessControl.GenericAcl.Revision" /> 속성에 의해 반환됩니다.</summary>
    </member>
    <member name="P:System.Security.AccessControl.GenericAcl.BinaryLength">
      <summary>현재 <see cref="T:System.Security.AccessControl.GenericAcl" /> 개체에 대한 이진 표현의 길이(바이트)를 가져옵니다.이 길이는 <see cref="M:System.Security.AccessControl.GenericAcl.GetBinaryForm" /> 메서드를 사용하여 ACL을 이진 배열로 마샬링하기 전에 사용해야 합니다.</summary>
      <returns>현재 <see cref="T:System.Security.AccessControl.GenericAcl" /> 개체의 이진 표현 길이(바이트)입니다.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericAcl.CopyTo(System.Security.AccessControl.GenericAce[],System.Int32)">
      <summary>현재 <see cref="T:System.Security.AccessControl.GenericAcl" />의 각 <see cref="T:System.Security.AccessControl.GenericAce" />를 지정된 배열에 복사합니다.</summary>
      <param name="array">현재 <see cref="T:System.Security.AccessControl.GenericAcl" />에 포함된 <see cref="T:System.Security.AccessControl.GenericAce" /> 개체의 복사본을 넣을 대상 배열입니다.</param>
      <param name="index">복사를 시작하는 <paramref name="array" />의 인덱스(0부터 시작)입니다.</param>
    </member>
    <member name="P:System.Security.AccessControl.GenericAcl.Count">
      <summary>현재 <see cref="T:System.Security.AccessControl.GenericAcl" /> 개체의 ACE(액세스 제어 항목) 수를 가져옵니다.</summary>
      <returns>현재 <see cref="T:System.Security.AccessControl.GenericAcl" /> 개체의 ACE 수입니다.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericAcl.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>
        <see cref="T:System.Security.AccessControl.GenericAcl" /> 개체의 내용을 특정 오프셋 위치에서 시작하여 지정된 바이트 배열에 마샬링합니다.</summary>
      <param name="binaryForm">
        <see cref="T:System.Security.AccessControl.GenericAcl" />의 내용이 마샬링되는 바이트 배열입니다.</param>
      <param name="offset">마샬링을 시작할 오프셋입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 값이 음수이거나, 너무 커서 전체 <see cref="T:System.Security.AccessControl.GenericAcl" />를 <paramref name="array" />에 복사할 수 없는 경우</exception>
    </member>
    <member name="M:System.Security.AccessControl.GenericAcl.GetEnumerator">
      <summary>
        <see cref="T:System.Security.AccessControl.AceEnumerator" /> 클래스의 새 인스턴스를 반환합니다.</summary>
      <returns>이 메서드가 반환하는 <see cref="T:Security.AccessControl.AceEnumerator" />입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAcl.IsSynchronized">
      <summary>이 속성은 항상 false로 설정됩니다.또한 <see cref="T:System.Collections.ICollection" /> 인터페이스의 구현에 사용하기 위해서만 구현됩니다.</summary>
      <returns>항상 false입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAcl.Item(System.Int32)">
      <summary>지정된 인덱스에 있는 <see cref="T:System.Security.AccessControl.GenericAce" />를 가져오거나 설정합니다.</summary>
      <returns>지정된 인덱스에 있는 <see cref="T:System.Security.AccessControl.GenericAce" />입니다.</returns>
      <param name="index">가져오거나 설정할 <see cref="T:System.Security.AccessControl.GenericAce" />의 인덱스(0부터 시작)입니다.</param>
    </member>
    <member name="F:System.Security.AccessControl.GenericAcl.MaxBinaryLength">
      <summary>
        <see cref="T:System.Security.AccessControl.GenericAcl" /> 개체의 최대 허용 이진 길이입니다.</summary>
    </member>
    <member name="P:System.Security.AccessControl.GenericAcl.Revision">
      <summary>
        <see cref="T:System.Security.AccessControl.GenericAcl" />의 수정 수준을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Security.AccessControl.GenericAcl" />의 수정 수준을 지정하는 바이트 값입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericAcl.SyncRoot">
      <summary>이 속성은 항상 null를 반환합니다.또한 <see cref="T:System.Collections.ICollection" /> 인터페이스의 구현에 사용하기 위해서만 구현됩니다.</summary>
      <returns>항상 null를 반환합니다.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericAcl.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>현재 <see cref="T:System.Security.AccessControl.GenericAcl" />의 각 <see cref="T:System.Security.AccessControl.GenericAce" />를 지정된 배열에 복사합니다.</summary>
      <param name="array">현재 <see cref="T:System.Security.AccessControl.GenericAcl" />에 포함된 <see cref="T:System.Security.AccessControl.GenericAce" /> 개체의 복사본을 넣을 대상 배열입니다.</param>
      <param name="index">복사를 시작하는 <paramref name="array" />의 인덱스(0부터 시작)입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.GenericAcl.System#Collections#IEnumerable#GetEnumerator">
      <summary>
        <see cref="T:System.Collections.IEnumerator" /> 인터페이스의 인스턴스로 캐스팅된 <see cref="T:System.Security.AccessControl.AceEnumerator" /> 클래스의 새 인스턴스를 반환합니다.</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" /> 인터페이스의 인스턴스로 캐스팅된 새 <see cref="T:System.Security.AccessControl.AceEnumerator" /> 개체입니다.</returns>
    </member>
    <member name="T:System.Security.AccessControl.GenericSecurityDescriptor">
      <summary>보안 설명자를 나타냅니다.보안 설명자에는 소유자, 주 그룹, DACL(임의 액세스 제어 목록) 및 SACL(시스템 액세스 제어 목록)이 포함됩니다.</summary>
    </member>
    <member name="M:System.Security.AccessControl.GenericSecurityDescriptor.#ctor">
      <summary>
        <see cref="T:System.Security.AccessControl.GenericSecurity" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="P:System.Security.AccessControl.GenericSecurityDescriptor.BinaryLength">
      <summary>현재 <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> 개체에 대한 이진 표현의 길이(바이트)를 가져옵니다.이 길이는 <see cref="M:System.Security.AccessControl.GenericSecurityDescriptor.GetBinaryForm" /> 메서드를 사용하여 ACL을 이진 배열로 마샬링하기 전에 사용해야 합니다.</summary>
      <returns>현재 <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> 개체의 이진 표현 길이(바이트)입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericSecurityDescriptor.ControlFlags">
      <summary>
        <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> 개체의 동작을 지정하는 값을 가져옵니다.</summary>
      <returns>논리 OR 연산으로 조합된 <see cref="T:System.Security.AccessControl.ControlFlags" /> 열거형의 하나 이상의 값입니다.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericSecurityDescriptor.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>이 <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> 개체에 포함된 정보를 나타내는 바이트 값 배열을 반환합니다.</summary>
      <param name="binaryForm">
        <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />의 내용이 마샬링되는 바이트 배열입니다.</param>
      <param name="offset">마샬링을 시작할 오프셋입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 값이 음수이거나, 너무 커서 전체 <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />를 <paramref name="array" />에 복사할 수 없는 경우</exception>
    </member>
    <member name="M:System.Security.AccessControl.GenericSecurityDescriptor.GetSddlForm(System.Security.AccessControl.AccessControlSections)">
      <summary>이 <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> 개체가 나타내는 지정된 보안 설명자 섹션의 SDDL(Security Descriptor Definition Language) 표현을 반환합니다.</summary>
      <returns>이 <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> 개체와 관련된 지정된 보안 설명자 섹션의 SDDL 표현입니다.</returns>
      <param name="includeSections">가져올 보안 설명자 섹션(액세스 규칙, 감사 규칙, 주 그룹 및 소유자)을 지정합니다.</param>
    </member>
    <member name="P:System.Security.AccessControl.GenericSecurityDescriptor.Group">
      <summary>이 <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> 개체의 주 그룹을 가져오거나 설정합니다.</summary>
      <returns>이 <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> 개체의 주 그룹입니다.</returns>
    </member>
    <member name="M:System.Security.AccessControl.GenericSecurityDescriptor.IsSddlConversionSupported">
      <summary>이 <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> 개체와 관련된 보안 설명자를 SDDL(Security Descriptor Definition Language) 형식으로 변환할 수 있는지 여부를 지정하는 부울 값을 반환합니다.</summary>
      <returns>이 <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> 개체와 관련된 보안 설명자를 SDDL(Security Descriptor Definition Language) 형식으로 변환할 수 있으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericSecurityDescriptor.Owner">
      <summary>이 <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> 개체와 관련된 개체의 소유자를 가져오거나 설정합니다.</summary>
      <returns>이 <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> 개체와 관련된 개체의 소유자입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.GenericSecurityDescriptor.Revision">
      <summary>
        <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" /> 개체의 수정 수준을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor" />의 수정 수준을 지정하는 바이트 값입니다.</returns>
    </member>
    <member name="T:System.Security.AccessControl.InheritanceFlags">
      <summary>상속 플래그는 ACE(액세스 제어 항목)에 대한 상속 의미를 지정합니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.InheritanceFlags.ContainerInherit">
      <summary>ACE는 자식 컨테이너 개체에서 상속됩니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.InheritanceFlags.None">
      <summary>ACE는 자식 개체에서 상속되지 않습니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.InheritanceFlags.ObjectInherit">
      <summary>ACE는 자식 리프 개체에서 상속됩니다.</summary>
    </member>
    <member name="T:System.Security.AccessControl.KnownAce">
      <summary>Microsoft Corporation에서 현재 정의한 모든 ACE(액세스 제어 항목) 형식을 캡슐화합니다.모든 <see cref="T:System.Security.AccessControl.KnownAce" /> 개체는 32비트 액세스 마스크와 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체를 포함합니다.</summary>
    </member>
    <member name="P:System.Security.AccessControl.KnownAce.AccessMask">
      <summary>이 <see cref="T:System.Security.AccessControl.KnownAce" /> 개체의 액세스 마스크를 가져오거나 설정합니다.</summary>
      <returns>이 <see cref="T:System.Security.AccessControl.KnownAce" /> 개체의 액세스 마스크입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.KnownAce.SecurityIdentifier">
      <summary>이 <see cref="T:System.Security.AccessControl.KnownAce" /> 개체와 관련된 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체를 가져오거나 설정합니다.</summary>
      <returns>이 <see cref="T:System.Security.AccessControl.KnownAce" /> 개체와 관련된 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체입니다.</returns>
    </member>
    <member name="T:System.Security.AccessControl.NativeObjectSecurity">
      <summary>ACL(액세스 제어 목록)을 직접 조작하지 않고 네이티브 개체에 대한 액세스를 제어하는 기능을 제공합니다.네이티브 개체 형식은 <see cref="T:System.Security.AccessControl.ResourceType" /> 열거형에 의해 정의됩니다.</summary>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType)">
      <summary>지정된 값을 사용하여 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="isContainer">새 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 개체가 컨테이너 개체이면 true입니다.</param>
      <param name="resourceType">새 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 개체가 연결되어 있는 보안 가능한 개체의 형식입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections)">
      <summary>지정된 값을 사용하여 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 클래스의 새 인스턴스를 초기화합니다.생성자와 persist 메서드에는 동일한 <paramref name="includeSections" /> 매개 변수 값을 전달하는 것이 좋습니다.자세한 내용은 설명 부분을 참조하십시오.</summary>
      <param name="isContainer">새 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 개체가 컨테이너 개체이면 true입니다.</param>
      <param name="resourceType">새 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 개체가 연결되어 있는 보안 가능한 개체의 형식입니다.</param>
      <param name="handle">새 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 개체가 연결되어 있는 보안 가능한 개체의 핸들입니다.</param>
      <param name="includeSections">이 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 개체에 포함할 보안 가능한 개체의 보안 설명자 섹션(액세스 규칙, 감사 규칙, 소유자 및 기본 그룹)을 지정하는 <see cref="T:System.Security.AccessControl.AccessControlSections" /> 열거형 값 중 하나입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections,System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode,System.Object)">
      <summary>지정된 값을 사용하여 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 클래스의 새 인스턴스를 초기화합니다.생성자와 persist 메서드에는 동일한 <paramref name="includeSections" /> 매개 변수 값을 전달하는 것이 좋습니다.자세한 내용은 설명 부분을 참조하십시오.</summary>
      <param name="isContainer">새 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 개체가 컨테이너 개체이면 true입니다.</param>
      <param name="resourceType">새 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 개체가 연결되어 있는 보안 가능한 개체의 형식입니다.</param>
      <param name="handle">새 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 개체가 연결되어 있는 보안 가능한 개체의 핸들입니다.</param>
      <param name="includeSections">이 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 개체에 포함할 보안 가능한 개체의 보안 설명자 섹션(액세스 규칙, 감사 규칙, 소유자 및 기본 그룹)을 지정하는 <see cref="T:System.Security.AccessControl.AccessControlSections" /> 열거형 값 중 하나입니다.</param>
      <param name="exceptionFromErrorCode">사용자 지정 예외를 제공하며 통합자가 구현하는 대리자입니다. </param>
      <param name="exceptionContext">예외의 소스 또는 대상에 대한 컨텍스트 정보를 포함하는 개체입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode,System.Object)">
      <summary>지정된 값을 사용하여 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="isContainer">새 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 개체가 컨테이너 개체이면 true입니다.</param>
      <param name="resourceType">새 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 개체가 연결되어 있는 보안 가능한 개체의 형식입니다.</param>
      <param name="exceptionFromErrorCode">사용자 지정 예외를 제공하며 통합자가 구현하는 대리자입니다. </param>
      <param name="exceptionContext">예외의 소스 또는 대상에 대한 컨텍스트 정보를 포함하는 개체입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>지정된 값을 사용하여 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 클래스의 새 인스턴스를 초기화합니다.생성자와 persist 메서드에는 동일한 <paramref name="includeSections" /> 매개 변수 값을 전달하는 것이 좋습니다.자세한 내용은 설명 부분을 참조하십시오.</summary>
      <param name="isContainer">새 <see cref="T:System.Security.AccessControl.NativObjectSecurity" /> 개체가 컨테이너 개체이면 true입니다.</param>
      <param name="resourceType">새 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 개체가 연결되어 있는 보안 가능한 개체의 형식입니다.</param>
      <param name="name">새 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 개체가 연결되어 있는 보안 가능한 개체의 이름입니다.</param>
      <param name="includeSections">이 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 개체에 포함할 보안 가능한 개체의 보안 설명자 섹션(액세스 규칙, 감사 규칙, 소유자 및 기본 그룹)을 지정하는 <see cref="T:System.Security.AccessControl.AccessControlSections" /> 열거형 값 중 하나입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.String,System.Security.AccessControl.AccessControlSections,System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode,System.Object)">
      <summary>지정된 값을 사용하여 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 클래스의 새 인스턴스를 초기화합니다.생성자와 persist 메서드에는 동일한 <paramref name="includeSections" /> 매개 변수 값을 전달하는 것이 좋습니다.자세한 내용은 설명 부분을 참조하십시오.</summary>
      <param name="isContainer">새 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 개체가 컨테이너 개체이면 true입니다.</param>
      <param name="resourceType">새 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 개체가 연결되어 있는 보안 가능한 개체의 형식입니다.</param>
      <param name="name">새 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 개체가 연결되어 있는 보안 가능한 개체의 이름입니다.</param>
      <param name="includeSections">이 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 개체에 포함할 보안 가능한 개체의 보안 설명자 섹션(액세스 규칙, 감사 규칙, 소유자 및 기본 그룹)을 지정하는 <see cref="T:System.Security.AccessControl.AccessControlSections" /> 열거형 값 중 하나입니다.</param>
      <param name="exceptionFromErrorCode">사용자 지정 예외를 제공하며 통합자가 구현하는 대리자입니다. </param>
      <param name="exceptionContext">예외의 소스 또는 대상에 대한 컨텍스트 정보를 포함하는 개체입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.Persist(System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections)">
      <summary>이 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 개체와 연결된 보안 설명자의 지정된 섹션을 영구 저장소에 저장합니다.생성자와 persist 메서드에는 동일한 <paramref name="includeSections" /> 매개 변수 값을 전달하는 것이 좋습니다.자세한 내용은 설명 부분을 참조하십시오.</summary>
      <param name="handle">이 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 개체가 연결되어 있는 보안 가능한 개체의 핸들입니다.</param>
      <param name="includeSections">저장할 보안 가능한 개체의 보안 설명자 섹션(액세스 규칙, 감사 규칙, 소유자 및 기본 그룹)을 지정하는 <see cref="T:System.Security.AccessControl.AccessControlSections" /> 열거형 값 중 하나입니다.</param>
      <exception cref="T:System.IO.FileNotFoundException">이 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 개체가 연결되어 있는 보안 가능한 개체가 디렉터리 또는 파일이며 해당 디렉터리나 파일을 찾을 수 없는 경우</exception>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.Persist(System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections,System.Object)">
      <summary>이 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 개체와 연결된 보안 설명자의 지정된 섹션을 영구 저장소에 저장합니다.생성자와 persist 메서드에는 동일한 <paramref name="includeSections" /> 매개 변수 값을 전달하는 것이 좋습니다.자세한 내용은 설명 부분을 참조하십시오.</summary>
      <param name="handle">이 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 개체가 연결되어 있는 보안 가능한 개체의 핸들입니다.</param>
      <param name="includeSections">저장할 보안 가능한 개체의 보안 설명자 섹션(액세스 규칙, 감사 규칙, 소유자 및 기본 그룹)을 지정하는 <see cref="T:System.Security.AccessControl.AccessControlSections" /> 열거형 값 중 하나입니다.</param>
      <param name="exceptionContext">예외의 소스 또는 대상에 대한 컨텍스트 정보를 포함하는 개체입니다.</param>
      <exception cref="T:System.IO.FileNotFoundException">이 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 개체가 연결되어 있는 보안 가능한 개체가 디렉터리 또는 파일이며 해당 디렉터리나 파일을 찾을 수 없는 경우</exception>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.Persist(System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>이 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 개체와 연결된 보안 설명자의 지정된 섹션을 영구 저장소에 저장합니다.생성자와 persist 메서드에는 동일한 <paramref name="includeSections" /> 매개 변수 값을 전달하는 것이 좋습니다.자세한 내용은 설명 부분을 참조하십시오.</summary>
      <param name="name">이 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 개체가 연결되어 있는 보안 가능한 개체의 이름입니다.</param>
      <param name="includeSections">저장할 보안 가능한 개체의 보안 설명자 섹션(액세스 규칙, 감사 규칙, 소유자 및 기본 그룹)을 지정하는 <see cref="T:System.Security.AccessControl.AccessControlSections" /> 열거형 값 중 하나입니다.</param>
      <exception cref="T:System.IO.FileNotFoundException">이 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 개체가 연결되어 있는 보안 가능한 개체가 디렉터리 또는 파일이며 해당 디렉터리나 파일을 찾을 수 없는 경우</exception>
    </member>
    <member name="M:System.Security.AccessControl.NativeObjectSecurity.Persist(System.String,System.Security.AccessControl.AccessControlSections,System.Object)">
      <summary>이 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 개체와 연결된 보안 설명자의 지정된 섹션을 영구 저장소에 저장합니다.생성자와 persist 메서드에는 동일한 <paramref name="includeSections" /> 매개 변수 값을 전달하는 것이 좋습니다.자세한 내용은 설명 부분을 참조하십시오.</summary>
      <param name="name">이 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 개체가 연결되어 있는 보안 가능한 개체의 이름입니다.</param>
      <param name="includeSections">저장할 보안 가능한 개체의 보안 설명자 섹션(액세스 규칙, 감사 규칙, 소유자 및 기본 그룹)을 지정하는 <see cref="T:System.Security.AccessControl.AccessControlSections" /> 열거형 값 중 하나입니다.</param>
      <param name="exceptionContext">예외의 소스 또는 대상에 대한 컨텍스트 정보를 포함하는 개체입니다.</param>
      <exception cref="T:System.IO.FileNotFoundException">이 <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 개체가 연결되어 있는 보안 가능한 개체가 디렉터리 또는 파일이며 해당 디렉터리나 파일을 찾을 수 없는 경우</exception>
    </member>
    <member name="T:System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode">
      <summary>통합자가 숫자 오류 코드를 직접 만든 특정 예외에 매핑하는 방법을 제공합니다.</summary>
      <returns>이 대리자가 만드는 <see cref="T:System.Exception" />입니다.</returns>
      <param name="errorCode">숫자 오류 코드입니다.</param>
      <param name="name">
        <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 개체가 연결되어 있는 보안 가능한 개체의 이름입니다.</param>
      <param name="handle">
        <see cref="T:System.Security.AccessControl.NativeObjectSecurity" /> 개체가 연결되어 있는 보안 가능한 개체의 핸들입니다.</param>
      <param name="context">예외의 소스 또는 대상에 대한 컨텍스트 정보를 포함하는 개체입니다.</param>
    </member>
    <member name="T:System.Security.AccessControl.ObjectAccessRule">
      <summary>사용자의 ID, 액세스 마스크 및 액세스 제어 형식(허용 또는 거부)의 조합을 나타냅니다.또한 <see cref="T:System.Security.AccessControl.ObjectAccessRule" /> 개체에는 규칙을 적용할 개체의 형식, 규칙을 상속할 수 있는 자식 개체의 형식, 자식 개체에서 규칙을 상속하는 방법 및 상속을 전파하는 방법에 대한 정보가 들어 있습니다.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectAccessRule.#ctor(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Guid,System.Guid,System.Security.AccessControl.AccessControlType)">
      <summary>지정된 값을 사용하여 <see cref="T:System.Security.AccessControl.ObjectAccessRule" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="identity">액세스 규칙이 적용되는 ID입니다.  이 값은 <see cref="T:System.Security.Principal.SecurityIdentifier" />로 캐스팅할 수 있는 개체여야 합니다.</param>
      <param name="accessMask">이 규칙의 액세스 마스크입니다.액세스 마스크는 익명 비트의 32비트 컬렉션으로, 비트의 의미는 개별 통합자가 정의합니다.</param>
      <param name="isInherited">이 규칙이 부모 컨테이너에서 상속되면 true입니다.</param>
      <param name="inheritanceFlags">액세스 규칙의 상속 속성을 지정합니다.</param>
      <param name="propagationFlags">상속된 액세스 규칙을 자동으로 전파할 것인지 여부를 지정합니다.<paramref name="inheritanceFlags" />가 <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />으로 설정되면 전파 플래그는 무시됩니다.</param>
      <param name="objectType">규칙을 적용할 개체의 형식입니다.</param>
      <param name="inheritedObjectType">규칙을 상속할 수 있는 자식 개체의 형식입니다.</param>
      <param name="type">이 규칙의 액세스 허용 또는 거부 여부를 지정합니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="identity" /> 매개 변수 값이 <see cref="T:System.Security.Principal.SecurityIdentifier" />로 캐스팅될 수 없거나 <paramref name="type" /> 매개 변수에 잘못된 값이 포함된 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="accessMask" /> 매개 변수 값이 0이거나 <paramref name="inheritanceFlags" /> 또는 <paramref name="propagationFlags" /> 매개 변수에 인식할 수 없는 플래그 값이 포함되어 있는 경우</exception>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAccessRule.InheritedObjectType">
      <summary>
        <see cref="System.Security.AccessControl.ObjectAccessRule" /> 개체를 상속할 수 있는 자식 개체의 형식을 가져옵니다.</summary>
      <returns>
        <see cref="System.Security.AccessControl.ObjectAccessRule" /> 개체를 상속할 수 있는 자식 개체의 형식입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAccessRule.ObjectFlags">
      <summary>
        <see cref="System.Security.AccessControl.ObjectAccessRule" /> 개체의 <see cref="P:System.Security.AccessControl.ObjectAccessRule.ObjectType" /> 및 <see cref="P:System.Security.AccessControl.ObjectAccessRule.InheritedObjectType" /> 속성에 올바른 값이 포함되어 있는지 여부를 지정하는 플래그를 가져옵니다.</summary>
      <returns>
        <see cref="F:System.Security.AccessControl.ObjectAceFlags.ObjectAceTypePresent" />는 <see cref="P:System.Security.AccessControl.ObjectAccessRule.ObjectType" /> 속성에 올바른 값이 포함되어 있음을 지정하며,<see cref="F:System.Security.AccessControl.ObjectAceFlags.InheritedObjectAceTypePresent" />는 <see cref="P:System.Security.AccessControl.ObjectAccessRule.InheritedObjectType" /> 속성에 올바른 값이 포함되어 있음을 지정합니다.이러한 값은 논리 OR와 함께 사용할 수 있습니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAccessRule.ObjectType">
      <summary>
        <see cref="System.Security.AccessControl.ObjectAccessRule" />을 적용할 개체의 형식을 가져옵니다.</summary>
      <returns>
        <see cref="System.Security.AccessControl.ObjectAccessRule" />을 적용할 개체의 형식입니다.</returns>
    </member>
    <member name="T:System.Security.AccessControl.ObjectAce">
      <summary>디렉터리 서비스 개체에 대한 액세스를 제어합니다.이 클래스는 디렉터리 개체와 관련된 ACE(액세스 제어 항목)를 나타냅니다.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectAce.#ctor(System.Security.AccessControl.AceFlags,System.Security.AccessControl.AceQualifier,System.Int32,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid,System.Boolean,System.Byte[])">
      <summary>
        <see cref="T:System.Security.AccessControl.ObjectAce" /> 클래스의 새 인스턴스를 시작합니다.</summary>
      <param name="aceFlags">새 ACE(액세스 제어 항목)의 상속, 상속 전파 및 감사 조건입니다.</param>
      <param name="qualifier">새 ACE의 사용 방법입니다.</param>
      <param name="accessMask">ACE의 액세스 마스크입니다.</param>
      <param name="sid">새 ACE와 관련된 <see cref="T:System.Security.Principal.SecurityIdentifier" />입니다.</param>
      <param name="flags">
        <paramref name="type" /> 및 <paramref name="inheritedType" /> 매개 변수에 올바른 개체 GUID가 포함되어 있는지 여부를 나타냅니다.</param>
      <param name="type">새 ACE를 적용할 개체 형식을 식별하는 GUID입니다.</param>
      <param name="inheritedType">새 ACE를 상속할 수 있는 개체 형식을 식별하는 GUID입니다.</param>
      <param name="isCallback">새 ACE가 콜백 형식 ACE인 경우 true입니다.</param>
      <param name="opaque">새 ACE와 관련된 불투명 데이터입니다.이 데이터는 콜백 ACE 형식에만 허용됩니다.이 배열의 길이는 <see cref="M:System.Security.AccessControl.ObjectAceMaxOpaqueLength" /> 메서드의 반환 값보다 크지 않아야 합니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">한정자 매개 변수에 잘못된 값이 포함되어 있거나 불투명 매개 변수 값의 길이가 <see cref="M:System.Security.AccessControl.ObjectAceMaxOpaqueLength" /> 메서드의 반환 값보다 큰 경우</exception>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAce.BinaryLength">
      <summary>현재 <see cref="T:System.Security.AccessControl.ObjectAce" /> 개체에 대한 이진 표현의 길이(바이트)를 가져옵니다.이 길이는 <see cref="M:System.Security.AccessControl.ObjectAce.GetBinaryForm" /> 메서드를 사용하여 ACL을 이진 배열로 마샬링하기 전에 사용해야 합니다.</summary>
      <returns>현재 <see cref="T:System.Security.AccessControl.ObjectAce" /> 개체의 이진 표현 길이(바이트)입니다.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectAce.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>
        <see cref="T:System.Security.AccessControl.ObjectAce" /> 개체의 내용을 특정 오프셋 위치에서 시작하여 지정된 바이트 배열에 마샬링합니다.</summary>
      <param name="binaryForm">
        <see cref="T:System.Security.AccessControl.ObjectAce" />의 내용이 마샬링되는 바이트 배열입니다.</param>
      <param name="offset">마샬링을 시작할 오프셋입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 값이 음수이거나, 너무 커서 전체 <see cref="T:System.Security.AccessControl.ObjectAce" />를 <paramref name="array" />에 복사할 수 없는 경우</exception>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAce.InheritedObjectAceType">
      <summary>이 <see cref="T:System.Security.AccessControl.ObjectAce" /> 개체가 나타내는 ACE(액세스 제어 항목)를 상속할 수 있는 개체 형식의 GUID를 가져오거나 설정합니다.</summary>
      <returns>이 <see cref="T:System.Security.AccessControl.ObjectAce" /> 개체가 나타내는 ACE(액세스 제어 항목)를 상속할 수 있는 개체 형식의 GUID입니다.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectAce.MaxOpaqueLength(System.Boolean)">
      <summary>콜백 ACE(액세스 제어 항목)에 대해 불투명 데이터 BLOB의 최대 허용 길이(바이트)를 반환합니다.</summary>
      <returns>콜백 ACE(액세스 제어 항목)에 대해 불투명 데이터 BLOB의 최대 허용 길이(바이트)입니다.</returns>
      <param name="isCallback">
        <see cref="T:System.Security.AccessControl.ObjectAce" />가 콜백 ACE 형식인 경우 true입니다.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAce.ObjectAceFlags">
      <summary>
        <see cref="P:System.Security.AccessControl.ObjectAce.ObjectAceType" /> 및 <see cref="P:System.Security.AccessControl.ObjectAce.InheritedObjectAceType" /> 속성에 올바른 개체 형식을 식별하는 값이 들어 있는지 여부를 지정하는 플래그를 가져오거나 설정합니다.</summary>
      <returns>논리 OR 연산으로 조합된 하나 이상의 <see cref="T:System.Security.AccessControl.ObjectAceFlags" /> 열거형 멤버입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAce.ObjectAceType">
      <summary>이 <see cref="T:System.Security.AccessControl.ObjectAce" /> 개체와 관련된 개체 형식의 GUID를 가져오거나 설정합니다.</summary>
      <returns>이 <see cref="T:System.Security.AccessControl.ObjectAce" /> 개체와 관련된 개체 형식의 GUID입니다.</returns>
    </member>
    <member name="T:System.Security.AccessControl.ObjectAceFlags">
      <summary>ACE(액세스 제어 항목)에 대한 개체 형식의 존재 여부를 지정합니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ObjectAceFlags.InheritedObjectAceTypePresent">
      <summary>ACE를 상속할 수 있는 개체의 형식입니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ObjectAceFlags.None">
      <summary>어떠한 개체 형식도 없습니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ObjectAceFlags.ObjectAceTypePresent">
      <summary>ACE와 관련된 개체의 형식이 있습니다.</summary>
    </member>
    <member name="T:System.Security.AccessControl.ObjectAuditRule">
      <summary>사용자의 ID, 액세스 마스크 및 감사 조건의 조합을 나타냅니다.또한 <see cref="T:System.Security.AccessControl.ObjectAuditRule" /> 개체에는 규칙을 적용할 개체의 형식, 규칙을 상속할 수 있는 자식 개체의 형식, 자식 개체에서 규칙이 상속되는 방법 그리고 상속을 전파하는 방법에 대한 정보가 들어 있습니다.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectAuditRule.#ctor(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Guid,System.Guid,System.Security.AccessControl.AuditFlags)">
      <summary>
        <see cref="T:System.Security.AccessControl.ObjectAuditRule" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="identity">액세스 규칙이 적용되는 ID입니다.  이 값은 <see cref="T:System.Security.Principal.SecurityIdentifier" />로 캐스팅할 수 있는 개체여야 합니다.</param>
      <param name="accessMask">이 규칙의 액세스 마스크입니다.액세스 마스크는 익명 비트의 32비트 컬렉션으로, 비트의 의미는 개별 통합자가 정의합니다.</param>
      <param name="isInherited">이 규칙이 부모 컨테이너에서 상속되면 true입니다.</param>
      <param name="inheritanceFlags">액세스 규칙의 상속 속성을 지정합니다.</param>
      <param name="propagationFlags">상속된 액세스 규칙이 자동으로 전파되는지 여부입니다.<paramref name="inheritanceFlags" />가 <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />으로 설정되면 전파 플래그는 무시됩니다.</param>
      <param name="objectType">규칙을 적용할 개체의 형식입니다.</param>
      <param name="inheritedObjectType">규칙을 상속할 수 있는 자식 개체의 형식입니다.</param>
      <param name="auditFlags">감사 조건입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="identity" /> 매개 변수 값이 <see cref="T:System.Security.Principal.SecurityIdentifier" />로 캐스팅될 수 없거나 <paramref name="type" /> 매개 변수에 잘못된 값이 포함된 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="accessMask" /> 매개 변수 값이 0이거나 <paramref name="inheritanceFlags" /> 또는 <paramref name="propagationFlags" /> 매개 변수에 인식할 수 없는 플래그 값이 포함되어 있는 경우</exception>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAuditRule.InheritedObjectType">
      <summary>
        <see cref="System.Security.AccessControl.ObjectAuditRule" /> 개체를 상속할 수 있는 자식 개체의 형식을 가져옵니다.</summary>
      <returns>
        <see cref="System.Security.AccessControl.ObjectAuditRule" /> 개체를 상속할 수 있는 자식 개체의 형식입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAuditRule.ObjectFlags">
      <summary>
        <see cref="System.Security.AccessControl.ObjectAuditRule" /> 개체의 <see cref="P:System.Security.AccessControl.ObjectAuditRule.ObjectType" /> 및 <see cref="P:System.Security.AccessControl.ObjectAuditRule.InheritedObjectType" /> 속성에 올바른 값이 들어 있습니다.</summary>
      <returns>
        <see cref="F:System.Security.AccessControl.ObjectAceFlags.ObjectAceTypePresent" />는 <see cref="P:System.Security.AccessControl.ObjectAuditRule.ObjectType" /> 속성에 올바른 값이 포함되어 있음을 지정합니다.<see cref="F:System.Security.AccessControl.ObjectAceFlags.InheritedObjectAceTypePresent" />는 <see cref="P:System.Security.AccessControl.ObjectAuditRule.InheritedObjectType" /> 속성에 올바른 값이 포함되어 있음을 지정합니다.이러한 값은 논리 OR와 함께 사용할 수 있습니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectAuditRule.ObjectType">
      <summary>
        <see cref="System.Security.AccessControl.ObjectAuditRule" />을 적용할 개체의 형식을 가져옵니다.</summary>
      <returns>
        <see cref="System.Security.AccessControl.ObjectAuditRule" />을 적용할 개체의 형식입니다.</returns>
    </member>
    <member name="T:System.Security.AccessControl.ObjectSecurity">
      <summary>ACL(액세스 제어 목록)을 직접 조작하지 않고 개체에 대한 액세스를 제어하는 기능을 제공합니다.이 클래스는 <see cref="T:System.Security.AccessControl.CommonObjectSecurity" /> 및 <see cref="T:System.Security.AccessControl.DirectoryObjectSecurity" /> 클래스의 추상 기본 클래스입니다.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.#ctor">
      <summary>
        <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.#ctor(System.Boolean,System.Boolean)">
      <summary>
        <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="isContainer">새 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체가 컨테이너 개체이면 true입니다.</param>
      <param name="isDS">새 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체가 디렉터리 개체이면 true입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.#ctor(System.Security.AccessControl.CommonSecurityDescriptor)">
      <summary>
        <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="securityDescriptor">새 <see cref="T:System.Security.AccessControl.CommonObjectSecurity" /> 인스턴스의 <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor" />입니다.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AccessRightType">
      <summary>Gets the <see cref="T:System.Type" /> of the securable object associated with this <see cref="T:System.Security.AccessControl.ObjectSecurity" /> object.</summary>
      <returns>이 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체와 관련된 보안 가능한 개체의 형식입니다.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.AccessRuleFactory(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
      <summary>지정된 값을 사용하여 <see cref="T:System.Security.AccessControl.AccessRule" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <returns>이 메서드가 만드는 <see cref="T:System.Security.AccessControl.AccessRule" /> 개체를 반환합니다.</returns>
      <param name="identityReference">액세스 규칙이 적용되는 ID입니다.이 값은 <see cref="T:System.Security.Principal.SecurityIdentifier" />로 캐스팅할 수 있는 개체여야 합니다.</param>
      <param name="accessMask">이 규칙의 액세스 마스크입니다.액세스 마스크는 익명 비트의 32비트 컬렉션으로, 비트의 의미는 개별 통합자가 정의합니다.</param>
      <param name="isInherited">이 규칙이 부모 컨테이너에서 상속되면 true입니다.</param>
      <param name="inheritanceFlags">액세스 규칙의 상속 속성을 지정합니다.</param>
      <param name="propagationFlags">상속된 액세스 규칙을 자동으로 전파할 것인지 여부를 지정합니다.<paramref name="inheritanceFlags" />가 <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />으로 설정되면 전파 플래그는 무시됩니다.</param>
      <param name="type">올바른 액세스 제어 형식을 지정합니다.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AccessRulesModified">
      <summary>이 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체와 관련된 액세스 규칙이 수정되었는지 여부를 지정하는 부울 값을 가져오거나 설정합니다.</summary>
      <returns>이 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체와 관련된 액세스 규칙이 수정되었으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AccessRuleType">
      <summary>Gets the <see cref="T:System.Type" /> of the object associated with the access rules of this <see cref="T:System.Security.AccessControl.ObjectSecurity" /> object.<see cref="T:System.Type" /> 개체는 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체로 캐스팅할 수 있는 개체여야 합니다.</summary>
      <returns>이 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체의 액세스 규칙과 관련된 개체의 형식입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AreAccessRulesCanonical">
      <summary>이 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체와 관련된 액세스 규칙이 정식 순서대로 되어 있는지 여부를 지정하는 부울 값을 가져옵니다.</summary>
      <returns>액세스 규칙이 정식 순서대로 되어 있으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AreAccessRulesProtected">
      <summary>이 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체와 관련된 DACL(임의 액세스 제어 목록)이 보호되는지 여부를 지정하는 부울 값을 가져옵니다.</summary>
      <returns>DACL이 보호되면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AreAuditRulesCanonical">
      <summary>이 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체와 관련된 감사 규칙이 정식 순서대로 되어 있는지 여부를 지정하는 부울 값을 가져옵니다.</summary>
      <returns>감사 규칙이 정식 순서대로 되어 있으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AreAuditRulesProtected">
      <summary>이 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체와 관련된 SACL(시스템 액세스 제어 목록)이 보호되는지 여부를 지정하는 부울 값을 가져옵니다.</summary>
      <returns>SACL이 보호되면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.AuditRuleFactory(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
      <summary>지정된 값을 사용하여 <see cref="T:System.Security.AccessControl.AuditRule" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <returns>이 메서드가 만드는 <see cref="T:System.Security.AccessControl.AuditRule" /> 개체를 반환합니다.</returns>
      <param name="identityReference">감사 규칙이 적용되는 ID입니다.이 값은 <see cref="T:System.Security.Principal.SecurityIdentifier" />로 캐스팅할 수 있는 개체여야 합니다.</param>
      <param name="accessMask">이 규칙의 액세스 마스크입니다.액세스 마스크는 익명 비트의 32비트 컬렉션으로, 비트의 의미는 개별 통합자가 정의합니다.</param>
      <param name="isInherited">이 규칙이 부모 컨테이너에서 상속되면 true입니다.</param>
      <param name="inheritanceFlags">감사 규칙의 상속 속성을 지정합니다.</param>
      <param name="propagationFlags">상속된 감사 규칙을 자동으로 전파할 것인지 여부를 지정합니다.<paramref name="inheritanceFlags" />가 <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />으로 설정되면 전파 플래그는 무시됩니다.</param>
      <param name="flags">규칙의 감사 조건을 지정합니다.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AuditRulesModified">
      <summary>이 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체와 관련된 감사 규칙이 수정되었는지 여부를 지정하는 부울 값을 가져오거나 설정합니다.</summary>
      <returns>이 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체와 관련된 감사 규칙이 수정되었으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.AuditRuleType">
      <summary>Gets the <see cref="T:System.Type" /> object associated with the audit rules of this <see cref="T:System.Security.AccessControl.ObjectSecurity" /> object.<see cref="T:System.Type" /> 개체는 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체로 캐스팅할 수 있는 개체여야 합니다.</summary>
      <returns>이 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체의 감사 규칙과 관련된 개체의 형식입니다.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.GetGroup(System.Type)">
      <summary>지정된 소유자와 관련된 주 그룹을 가져옵니다.</summary>
      <returns>지정된 소유자와 관련된 주 그룹입니다.</returns>
      <param name="targetType">주 그룹을 가져올 소유자입니다. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.GetOwner(System.Type)">
      <summary>지정된 주 그룹과 관련된 소유자를 가져옵니다.</summary>
      <returns>지정된 그룹과 관련된 소유자입니다.</returns>
      <param name="targetType">소유자를 가져올 주 그룹입니다.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.GetSecurityDescriptorBinaryForm">
      <summary>이 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체의 보안 설명자 정보를 나타내는 바이트 값 배열을 반환합니다.</summary>
      <returns>이 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체의 보안 설명자를 나타내는 바이트 값 배열입니다.이 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체에 보안 정보가 없는 경우 이 메서드는 null을 반환합니다.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.GetSecurityDescriptorSddlForm(System.Security.AccessControl.AccessControlSections)">
      <summary>이 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체와 관련된 지정된 보안 설명자 섹션의 SDDL(Security Descriptor Definition Language) 표현을 반환합니다.</summary>
      <returns>이 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체와 관련된 지정된 보안 설명자 섹션의 SDDL 표현입니다.</returns>
      <param name="includeSections">가져올 보안 설명자 섹션(액세스 규칙, 감사 규칙, 주 그룹 및 소유자)을 지정합니다.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.GroupModified">
      <summary>보안 가능한 개체와 관련된 그룹이 수정되었는지 여부를 지정하는 부울 값을 가져오거나 설정합니다. </summary>
      <returns>보안 가능한 개체와 관련된 그룹이 수정되었으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.IsContainer">
      <summary>이 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체가 컨테이너 개체인지 여부를 지정하는 부울 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체가 컨테이너 개체이면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.IsDS">
      <summary>이 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체가 디렉터리 개체인지 여부를 지정하는 부울 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Security.AccessControl.ObjectSecurity" />가 디렉터리 개체이면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.IsSddlConversionSupported">
      <summary>이 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체와 관련된 보안 설명자를 SDDL(Security Descriptor Definition Language) 형식으로 변환할 수 있는지 여부를 지정하는 부울 값을 반환합니다.</summary>
      <returns>이 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체와 관련된 보안 설명자를 SDDL(Security Descriptor Definition Language) 형식으로 변환할 수 있으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.ModifyAccess(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AccessRule,System.Boolean@)">
      <summary>지정된 수정 사항을 이 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체와 관련된 DACL(임의 액세스 제어 목록)에 적용합니다.</summary>
      <returns>DACL이 성공적으로 수정되면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="modification">DACL에 적용할 수정 내용입니다.</param>
      <param name="rule">수정할 액세스 규칙입니다.</param>
      <param name="modified">DACL이 성공적으로 수정되면 true이고, 그렇지 않으면 false입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.ModifyAccessRule(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AccessRule,System.Boolean@)">
      <summary>지정된 수정 사항을 이 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체와 관련된 DACL(임의 액세스 제어 목록)에 적용합니다.</summary>
      <returns>DACL이 성공적으로 수정되면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="modification">DACL에 적용할 수정 내용입니다.</param>
      <param name="rule">수정할 액세스 규칙입니다.</param>
      <param name="modified">DACL이 성공적으로 수정되면 true이고, 그렇지 않으면 false입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.ModifyAudit(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AuditRule,System.Boolean@)">
      <summary>지정된 수정 사항을 이 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체와 관련된 SACL(시스템 액세스 제어 목록)에 적용합니다.</summary>
      <returns>SACL이 성공적으로 수정되면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="modification">SACL에 적용할 수정 내용입니다.</param>
      <param name="rule">수정할 감사 규칙입니다.</param>
      <param name="modified">SACL이 성공적으로 수정되면 true이고, 그렇지 않으면 false입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.ModifyAuditRule(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AuditRule,System.Boolean@)">
      <summary>지정된 수정 사항을 이 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체와 관련된 SACL(시스템 액세스 제어 목록)에 적용합니다.</summary>
      <returns>SACL이 성공적으로 수정되면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="modification">SACL에 적용할 수정 내용입니다.</param>
      <param name="rule">수정할 감사 규칙입니다.</param>
      <param name="modified">SACL이 성공적으로 수정되면 true이고, 그렇지 않으면 false입니다.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity.OwnerModified">
      <summary>보안 가능한 개체의 소유자가 수정되었는지 여부를 지정하는 부울 값을 가져오거나 설정합니다.</summary>
      <returns>보안 가능한 개체의 소유자가 수정되었으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.Persist(System.Boolean,System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>이 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체와 연결된 보안 설명자의 지정된 섹션을 영구 저장소에 저장합니다.생성자와 persist 메서드에는 동일한 <paramref name="includeSections" /> 매개 변수 값을 전달하는 것이 좋습니다.자세한 내용은 설명 부분을 참조하세요.</summary>
      <param name="enableOwnershipPrivilege">호출자가 개체의 소유권을 가질 수 있도록 하는 권한을 설정하려면 true입니다.</param>
      <param name="name">유지된 정보를 검색하는 데 사용하는 이름입니다.</param>
      <param name="includeSections">저장할 보안 가능한 개체의 보안 설명자 섹션(액세스 규칙, 감사 규칙, 소유자 및 기본 그룹)을 지정하는 <see cref="T:System.Security.AccessControl.AccessControlSections" /> 열거형 값 중 하나입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.Persist(System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections)">
      <summary>이 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체와 연결된 보안 설명자의 지정된 섹션을 영구 저장소에 저장합니다.생성자와 persist 메서드에는 동일한 <paramref name="includeSections" /> 매개 변수 값을 전달하는 것이 좋습니다.자세한 내용은 설명 부분을 참조하세요.</summary>
      <param name="handle">유지된 정보를 검색하는 데 사용하는 핸들입니다.</param>
      <param name="includeSections">저장할 보안 가능한 개체의 보안 설명자 섹션(액세스 규칙, 감사 규칙, 소유자 및 기본 그룹)을 지정하는 <see cref="T:System.Security.AccessControl.AccessControlSections" /> 열거형 값 중 하나입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.Persist(System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>이 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체와 연결된 보안 설명자의 지정된 섹션을 영구 저장소에 저장합니다.생성자와 persist 메서드에는 동일한 <paramref name="includeSections" /> 매개 변수 값을 전달하는 것이 좋습니다.자세한 내용은 설명 부분을 참조하세요.</summary>
      <param name="name">유지된 정보를 검색하는 데 사용하는 이름입니다.</param>
      <param name="includeSections">저장할 보안 가능한 개체의 보안 설명자 섹션(액세스 규칙, 감사 규칙, 소유자 및 기본 그룹)을 지정하는 <see cref="T:System.Security.AccessControl.AccessControlSections" /> 열거형 값 중 하나입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.PurgeAccessRules(System.Security.Principal.IdentityReference)">
      <summary>지정된 <see cref="T:System.Security.Principal.IdentityReference" />와 관련된 모든 액세스 규칙을 제거합니다.</summary>
      <param name="identity">모든 액세스 규칙을 제거할 <see cref="T:System.Security.Principal.IdentityReference" />입니다.</param>
      <exception cref="T:System.InvalidOperationException">모든 액세스 규칙이 정식 순서대로 되어 있지 않은 경우</exception>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.PurgeAuditRules(System.Security.Principal.IdentityReference)">
      <summary>지정된 <see cref="T:System.Security.Principal.IdentityReference" />와 관련된 모든 감사 규칙을 제거합니다.</summary>
      <param name="identity">모든 감사 규칙을 제거할 <see cref="T:System.Security.Principal.IdentityReference" />입니다.</param>
      <exception cref="T:System.InvalidOperationException">모든 감사 규칙이 정식 순서대로 되어 있지 않은 경우</exception>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.ReadLock">
      <summary>이 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체에 대한 읽기 액세스를 잠급니다.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.ReadUnlock">
      <summary>이 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체에 대한 읽기 액세스의 잠금을 해제합니다.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetAccessRuleProtection(System.Boolean,System.Boolean)">
      <summary>이 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체와 관련된 액세스 규칙의 보호를 설정하거나 제거합니다.보호된 액세스 규칙은 부모 개체에서 상속을 통해 수정할 수 없습니다.</summary>
      <param name="isProtected">이 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체와 관련된 액세스 규칙을 상속으로부터 보호하려면 true이고, 상속을 허용하려면 false입니다.</param>
      <param name="preserveInheritance">상속된 액세스 규칙을 유지하려면 true이고, 상속된 액세스 규칙을 제거하려면 false입니다.<paramref name="isProtected" />가 false이면 이 매개 변수는 무시됩니다.</param>
      <exception cref="T:System.InvalidOperationException">이 메서드는 정식이 아닌 DACL(임의 액세스 제어 목록)에서 상속된 규칙을 제거하려고 시도하는 경우</exception>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetAuditRuleProtection(System.Boolean,System.Boolean)">
      <summary>이 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체와 관련된 감사 규칙의 보호를 설정하거나 제거합니다.보호된 감사 규칙은 부모 개체에서 상속을 통해 수정할 수 없습니다.</summary>
      <param name="isProtected">이 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체와 관련된 감사 규칙을 상속으로부터 보호하려면 true이고, 상속을 허용하려면 false입니다.</param>
      <param name="preserveInheritance">상속된 감사 규칙을 유지하려면 true이고, 상속된 감사 규칙을 제거하려면 false입니다.<paramref name="isProtected" />가 false이면 이 매개 변수는 무시됩니다.</param>
      <exception cref="T:System.InvalidOperationException">이 메서드는 정식이 아닌 SACL(시스템 액세스 제어 목록)에서 상속된 규칙을 제거하려고 시도하는 경우</exception>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetGroup(System.Security.Principal.IdentityReference)">
      <summary>이 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체와 관련된 보안 설명자의 주 그룹을 설정합니다.</summary>
      <param name="identity">설정할 주 그룹입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetOwner(System.Security.Principal.IdentityReference)">
      <summary>이 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체와 관련된 보안 설명자의 소유자를 설정합니다.</summary>
      <param name="identity">설정할 소유자입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetSecurityDescriptorBinaryForm(System.Byte[])">
      <summary>지정한 바이트 값 배열에서 이 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체의 보안 설명자를 설정합니다.</summary>
      <param name="binaryForm">보안 설명자를 설정할 바이트 배열입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetSecurityDescriptorBinaryForm(System.Byte[],System.Security.AccessControl.AccessControlSections)">
      <summary>지정한 바이트 값 배열에서 이 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체에 대해 지정한 보안 설명자 섹션을 설정합니다.</summary>
      <param name="binaryForm">보안 설명자를 설정할 바이트 배열입니다.</param>
      <param name="includeSections">설정할 보안 설명자 섹션(액세스 규칙, 감사 규칙, 소유자 및 주 그룹)입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetSecurityDescriptorSddlForm(System.String)">
      <summary>지정한 SDDL(Security Descriptor Definition Language) 문자열에서 이 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체의 보안 설명자를 설정합니다.</summary>
      <param name="sddlForm">보안 설명자를 설정할 SDDL 문자열입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.SetSecurityDescriptorSddlForm(System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>지정한 SDDL(Security Descriptor Definition Language) 문자열에서 이 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체에 대해 지정한 보안 설명자 섹션을 설정합니다.</summary>
      <param name="sddlForm">보안 설명자를 설정할 SDDL 문자열입니다.</param>
      <param name="includeSections">설정할 보안 설명자 섹션(액세스 규칙, 감사 규칙, 소유자 및 주 그룹)입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.WriteLock">
      <summary>이 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체에 대한 쓰기 액세스를 잠급니다.</summary>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity.WriteUnlock">
      <summary>이 <see cref="T:System.Security.AccessControl.ObjectSecurity" /> 개체에 대한 쓰기 액세스의 잠금을 해제합니다.</summary>
    </member>
    <member name="T:System.Security.AccessControl.ObjectSecurity`1">
      <summary>ACL(액세스 제어 목록)을 직접 조작하지 않고 개체에 대한 액세스를 제어할 수 있는 기능을 제공하며, 액세스 권한을 형식 캐스팅할 수 있는 기능도 부여합니다. </summary>
      <typeparam name="T">개체에 대한 액세스 권한입니다.</typeparam>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.#ctor(System.Boolean,System.Security.AccessControl.ResourceType)">
      <summary>ObjectSecurity’1 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="isContainer">새 <see cref="T:System.Security.AccessControl.ObjectSecurity`1" /> 개체가 컨테이너 개체이면 true입니다.</param>
      <param name="resourceType">리소스의 형식입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections)">
      <summary>ObjectSecurity’1 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="isContainer">새 <see cref="T:System.Security.AccessControl.ObjectSecurity`1" /> 개체가 컨테이너 개체이면 true입니다.</param>
      <param name="resourceType">리소스의 형식입니다.</param>
      <param name="safeHandle">핸들입니다.</param>
      <param name="includeSections">포함할 섹션입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections,System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode,System.Object)">
      <summary>ObjectSecurity’1 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="isContainer">새 <see cref="T:System.Security.AccessControl.ObjectSecurity`1" /> 개체가 컨테이너 개체이면 true입니다.</param>
      <param name="resourceType">리소스의 형식입니다.</param>
      <param name="safeHandle">핸들입니다.</param>
      <param name="includeSections">포함할 섹션입니다.</param>
      <param name="exceptionFromErrorCode">사용자 지정 예외를 제공하며 통합자가 구현하는 대리자입니다.</param>
      <param name="exceptionContext">예외의 소스 또는 대상에 대한 컨텍스트 정보를 포함하는 개체입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>ObjectSecurity’1 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="isContainer">새 <see cref="T:System.Security.AccessControl.ObjectSecurity`1" /> 개체가 컨테이너 개체이면 true입니다.</param>
      <param name="resourceType">리소스의 형식입니다.</param>
      <param name="name">새 <see cref="T:System.Security.AccessControl.ObjectSecurity`1" /> 개체가 연결되어 있는 보안 가능한 개체의 이름입니다.</param>
      <param name="includeSections">포함할 섹션입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.String,System.Security.AccessControl.AccessControlSections,System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode,System.Object)">
      <summary>ObjectSecurity’1 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="isContainer">새 <see cref="T:System.Security.AccessControl.ObjectSecurity`1" /> 개체가 컨테이너 개체이면 true입니다.</param>
      <param name="resourceType">리소스의 형식입니다.</param>
      <param name="name">새 <see cref="T:System.Security.AccessControl.ObjectSecurity`1" /> 개체가 연결되어 있는 보안 가능한 개체의 이름입니다.</param>
      <param name="includeSections">포함할 섹션입니다. </param>
      <param name="exceptionFromErrorCode">사용자 지정 예외를 제공하며 통합자가 구현하는 대리자입니다.</param>
      <param name="exceptionContext">예외의 소스 또는 대상에 대한 컨텍스트 정보를 포함하는 개체입니다.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity`1.AccessRightType">
      <summary>이 ObjectSecurity’1 개체와 관련된 보안 가능한 개체의 형식을 가져옵니다.</summary>
      <returns>현재 인스턴스와 관련된 보안 가능한 개체의 형식입니다.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.AccessRuleFactory(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
      <summary>관련된 보안 개체에 대한 새로운 액세스 제어 규칙을 나타내는 ObjectAccessRule 클래스의 새 인스턴스를 초기화합니다.</summary>
      <returns>지정된 액세스 권한, 액세스 제어 및 플래그로 지정한 사용자의 새 액세스 제어 규칙을 나타냅니다.</returns>
      <param name="identityReference">사용자 계정을 나타냅니다.</param>
      <param name="accessMask">액세스 형식입니다.</param>
      <param name="isInherited">액세스 규칙이 상속된 것이면 true이고, 그렇지 않으면 false입니다.</param>
      <param name="inheritanceFlags">액세스 마스크를 자식 개체로 전파할 방법을 지정합니다.</param>
      <param name="propagationFlags">ACE(액세스 제어 항목)가 자식 개체로 전파되는 방법을 지정합니다.</param>
      <param name="type">액세스가 허용 또는 거부되는지를 지정합니다.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity`1.AccessRuleType">
      <summary>이 ObjectSecurity’1 개체의 액세스 규칙과 관련된 개체의 형식을 가져옵니다. </summary>
      <returns>현재 인스턴스의 액세스 규칙과 관련된 개체의 형식입니다.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.AddAccessRule(System.Security.AccessControl.AccessRule{`0})">
      <summary>지정한 액세스 규칙을 이 ObjectSecurity`1 개체와 관련된 DACL(임의 액세스 제어 목록)에 추가합니다.</summary>
      <param name="rule">추가할 규칙입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.AddAuditRule(System.Security.AccessControl.AuditRule{`0})">
      <summary>이 ObjectSecurity`1 개체와 관련된 SACL(시스템 액세스 제어 목록)에 지정된 감사 규칙을 추가합니다.</summary>
      <param name="rule">추가할 감사 규칙입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.AuditRuleFactory(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
      <summary>지정된 사용자에 대한 지정된 감사 규칙을 나타내는 <see cref="T:System.Security.AccessControl.AuditRule" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <returns>지정된 사용자에 대한 지정된 감사 규칙을 반환합니다.</returns>
      <param name="identityReference">사용자 계정을 나타냅니다. </param>
      <param name="accessMask">액세스 형식을 지정하는 정수입니다.</param>
      <param name="isInherited">액세스 규칙이 상속된 것이면 true이고, 그렇지 않으면 false입니다.</param>
      <param name="inheritanceFlags">액세스 마스크를 자식 개체로 전파할 방법을 지정합니다.</param>
      <param name="propagationFlags">ACE(액세스 제어 항목)가 자식 개체로 전파되는 방법을 지정합니다.</param>
      <param name="flags">수행할 감사 형식을 설명합니다.</param>
    </member>
    <member name="P:System.Security.AccessControl.ObjectSecurity`1.AuditRuleType">
      <summary>이 ObjectSecurity’1 개체의 감사 규칙과 관련된 개체의 형식을 가져옵니다.</summary>
      <returns>현재 인스턴스의 감사 규칙과 관련된 형식 개체입니다.</returns>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.Persist(System.Runtime.InteropServices.SafeHandle)">
      <summary>지정된 핸들을 사용하여 이 ObjectSecurity`1 개체와 연결된 보안 설명자를 영구 저장소에 저장합니다.</summary>
      <param name="handle">이 ObjectSecurity`1 개체가 연결되어 있는 보안 가능한 개체의 핸들입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.Persist(System.String)">
      <summary>지정된 이름을 사용하여 이 ObjectSecurity`1 개체와 연결된 보안 설명자를 영구 저장소에 저장합니다.</summary>
      <param name="name">이 ObjectSecurity`1 개체가 연결되어 있는 보안 가능한 개체의 이름입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAccessRule(System.Security.AccessControl.AccessRule{`0})">
      <summary>이 ObjectSecurity`1 개체와 관련된 DACL(임의 액세스 제어 목록)에서 지정한 액세스 규칙과 동일한 보안 식별자 및 액세스 마스크가 들어 있는 액세스 규칙을 제거합니다.</summary>
      <returns>액세스 규칙이 성공적으로 제거되었으면 true, 그렇지 않으면 false를 반환합니다.</returns>
      <param name="rule">제거할 규칙입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAccessRuleAll(System.Security.AccessControl.AccessRule{`0})">
      <summary>이 ObjectSecurity`1 개체와 관련된 DACL(임의 액세스 제어 목록)에서 지정한 액세스 규칙과 동일한 보안 식별자가 있는 모든 액세스 규칙을 제거합니다.</summary>
      <param name="rule">제거할 액세스 규칙입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAccessRuleSpecific(System.Security.AccessControl.AccessRule{`0})">
      <summary>이 ObjectSecurity`1 개체와 관련된 DACL(임의 액세스 제어 목록)에서 지정한 액세스 규칙과 정확히 일치하는 모든 액세스 규칙을 제거합니다.</summary>
      <param name="rule">제거할 액세스 규칙입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAuditRule(System.Security.AccessControl.AuditRule{`0})">
      <summary>이 ObjectSecurity`1 개체와 관련된 SACL(시스템 액세스 제어 목록)에서 지정된 감사 규칙과 동일한 보안 식별자 및 액세스 마스크를 포함하는 액세스 규칙을 제거합니다.</summary>
      <returns>개체가 제거되었으면 true, 그렇지 않으면 false를 반환합니다.</returns>
      <param name="rule">제거할 감사 규칙입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAuditRuleAll(System.Security.AccessControl.AuditRule{`0})">
      <summary>이 ObjectSecurity`1 개체와 관련된 SACL(시스템 액세스 제어 목록)에서 지정된 감사 규칙과 동일한 보안 식별자를 갖는 모든 감사 규칙을 제거합니다.</summary>
      <param name="rule">제거할 감사 규칙입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAuditRuleSpecific(System.Security.AccessControl.AuditRule{`0})">
      <summary>이 ObjectSecurity`1 개체와 관련된 SACL(시스템 액세스 제어 목록)에서 지정된 감사 규칙과 정확히 일치하는 모든 감사 규칙을 제거합니다.</summary>
      <param name="rule">제거할 감사 규칙입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.ResetAccessRule(System.Security.AccessControl.AccessRule{`0})">
      <summary>이 ObjectSecurity`1 개체와 관련된 DACL(임의 액세스 제어 목록)에서 모든 액세스 규칙을 제거한 다음 지정된 액세스 규칙을 추가합니다.</summary>
      <param name="rule">재설정할 액세스 규칙입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.SetAccessRule(System.Security.AccessControl.AccessRule{`0})">
      <summary>이 ObjectSecurity`1 개체와 관련된 DACL(임의 액세스 제어 목록)에서 지정한 액세스 규칙과 동일한 보안 식별자 및 한정자가 들어 있는 모든 액세스 규칙을 제거한 다음 지정한 액세스 규칙을 추가합니다.</summary>
      <param name="rule">설정할 액세스 규칙입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.ObjectSecurity`1.SetAuditRule(System.Security.AccessControl.AuditRule{`0})">
      <summary>이 ObjectSecurity`1 개체와 관련된 SACL(시스템 액세스 제어 목록)에서 지정된 감사 규칙과 동일한 보안 식별자 및 한정자를 포함하는 모든 감사 규칙을 제거한 후 지정한 감사 규칙을 추가합니다.</summary>
      <param name="rule">설정할 감사 규칙입니다.</param>
    </member>
    <member name="T:System.Security.AccessControl.PrivilegeNotHeldException">
      <summary>
        <see cref="N:System.Security.AccessControl" /> 네임스페이스의 메서드가 부여되지 않은 권한을 활성화하려고 할 때 throw되는 예외입니다.</summary>
    </member>
    <member name="M:System.Security.AccessControl.PrivilegeNotHeldException.#ctor">
      <summary>
        <see cref="T:System.Security.AccessControl.PrivilegeNotHeldException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Security.AccessControl.PrivilegeNotHeldException.#ctor(System.String)">
      <summary>지정된 권한을 사용하여 <see cref="T:System.Security.AccessControl.PrivilegeNotHeldException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="privilege">활성화되지 않은 권한입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.PrivilegeNotHeldException.#ctor(System.String,System.Exception)">
      <summary>지정된 예외를 사용하여 <see cref="T:System.Security.AccessControl.PrivilegeNotHeldException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="privilege">활성화되지 않은 권한입니다.</param>
      <param name="inner">현재 예외의 원인이 되는 예외입니다.<paramref name="innerException" /> 매개 변수가 null 참조(Visual Basic에서는 Nothing)가 아니면, 현재 예외는 내부 예외를 처리하는 catch 블록에서 발생합니다.</param>
    </member>
    <member name="P:System.Security.AccessControl.PrivilegeNotHeldException.PrivilegeName">
      <summary>활성화되지 않은 권한의 이름을 가져옵니다.</summary>
      <returns>메서드가 활성화하지 못한 권한의 이름입니다.</returns>
    </member>
    <member name="T:System.Security.AccessControl.PropagationFlags">
      <summary>ACE(액세스 제어 항목)가 자식 개체로 전파되는 방법을 지정합니다.  이 플래그는 상속 플래그가 있는 경우에만 의미를 가집니다. </summary>
    </member>
    <member name="F:System.Security.AccessControl.PropagationFlags.InheritOnly">
      <summary>ACE가 자식 개체로만 전파되도록 지정합니다.여기에는 컨테이너와 리프 자식 개체도 모두 포함됩니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.PropagationFlags.None">
      <summary>상속 플래그가 설정되지 않도록 지정합니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.PropagationFlags.NoPropagateInherit">
      <summary>ACE가 자식 개체로 전파되지 않도록 지정합니다.</summary>
    </member>
    <member name="T:System.Security.AccessControl.QualifiedAce">
      <summary>한정자가 포함된 ACE(액세스 제어 항목)를 나타냅니다.<see cref="T:System.Security.AccessControl.AceQualifier" /> 개체로 표시되는 한정자는 ACE의 액세스 허용, 액세스 거부, 시스템 감사 수행 또는 시스템 경보 발생 여부를 지정합니다.<see cref="T:System.Security.AccessControl.QualifiedAce" /> 클래스는 <see cref="T:System.Security.AccessControl.CommonAce" /> 및 <see cref="T:System.Security.AccessControl.ObjectAce" /> 클래스의 추상 기본 클래스입니다.</summary>
    </member>
    <member name="P:System.Security.AccessControl.QualifiedAce.AceQualifier">
      <summary>ACE의 액세스 허용, 액세스 거부, 시스템 감사 수행 또는 시스템 경보 발생 여부를 지정하는 값을 가져옵니다.</summary>
      <returns>ACE의 액세스 허용, 액세스 거부, 시스템 감사 수행 또는 시스템 경보 발생 여부를 지정하는 값입니다.</returns>
    </member>
    <member name="M:System.Security.AccessControl.QualifiedAce.GetOpaque">
      <summary>이 <see cref="T:System.Security.AccessControl.QualifiedAce" /> 개체와 관련된 불투명 콜백 데이터를 반환합니다. </summary>
      <returns>이 <see cref="T:System.Security.AccessControl.QualifiedAce" /> 개체와 관련된 불투명 콜백 데이터를 나타내는 바이트 값의 배열입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.QualifiedAce.IsCallback">
      <summary>이 <see cref="T:System.Security.AccessControl.QualifiedAce" /> 개체에 콜백 데이터가 들어 있는지 여부를 지정합니다.</summary>
      <returns>이 <see cref="T:System.Security.AccessControl.QualifiedAce" /> 개체에 콜백 데이터가 들어 있으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.QualifiedAce.OpaqueLength">
      <summary>이 <see cref="T:System.Security.AccessControl.QualifiedAce" /> 개체와 관련된 불투명 콜백 데이터의 길이를 가져옵니다.이 속성은 콜백 ACE(액세스 제어 항목)에 대해서만 유효합니다.</summary>
      <returns>불투명 콜백 데이터의 길이입니다.</returns>
    </member>
    <member name="M:System.Security.AccessControl.QualifiedAce.SetOpaque(System.Byte[])">
      <summary>이 <see cref="T:System.Security.AccessControl.QualifiedAce" /> 개체와 관련된 불투명 콜백 데이터를 설정합니다.</summary>
      <param name="opaque">이 <see cref="T:System.Security.AccessControl.QualifiedAce" /> 개체의 불투명 콜백 데이터를 나타내는 바이트 값의 배열입니다.</param>
    </member>
    <member name="T:System.Security.AccessControl.RawAcl">
      <summary>ACL(액세스 제어 목록)을 나타냅니다.</summary>
    </member>
    <member name="M:System.Security.AccessControl.RawAcl.#ctor(System.Byte,System.Int32)">
      <summary>지정된 수정 수준을 사용하여 <see cref="T:System.Security.AccessControl.RawAcl" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="revision">새 ACL(액세스 제어 목록)의 수정 수준입니다.</param>
      <param name="capacity">이 <see cref="T:System.Security.AccessControl.RawAcl" /> 개체에 포함될 수 있는 ACE(액세스 제어 항목)의 수입니다.이 번호는 힌트로만 사용됩니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.RawAcl.#ctor(System.Byte[],System.Int32)">
      <summary>지정된 이진 형식에서 <see cref="T:System.Security.AccessControl.RawAcl" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="binaryForm">ACL(액세스 제어 목록)을 나타내는 바이트 값 배열입니다.</param>
      <param name="offset">데이터 역마샬링을 시작할 <paramref name="binaryForm" /> 매개 변수의 오프셋입니다.</param>
    </member>
    <member name="P:System.Security.AccessControl.RawAcl.BinaryLength">
      <summary>현재 <see cref="T:System.Security.AccessControl.RawAcl" /> 개체에 대한 이진 표현의 길이(바이트)를 가져옵니다.이 길이는 <see cref="M:System.Security.AccessControl.RawAcl.GetBinaryForm" /> 메서드를 사용하여 ACL을 이진 배열로 마샬링하기 전에 사용해야 합니다.</summary>
      <returns>현재 <see cref="T:System.Security.AccessControl.RawAcl" /> 개체의 이진 표현 길이(바이트)입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.RawAcl.Count">
      <summary>현재 <see cref="T:System.Security.AccessControl.RawAcl" /> 개체에 있는 ACE(액세스 제어 항목) 수를 가져옵니다.</summary>
      <returns>현재 <see cref="T:System.Security.AccessControl.RawAcl" /> 개체에 있는 ACE의 수입니다.</returns>
    </member>
    <member name="M:System.Security.AccessControl.RawAcl.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>
        <see cref="T:System.Security.AccessControl.RawAcl" /> 개체의 내용을 특정 오프셋 위치에서 시작하여 지정된 바이트 배열에 마샬링합니다.</summary>
      <param name="binaryForm">
        <see cref="T:System.Security.AccessControl.RawAcl" />의 내용이 마샬링되는 바이트 배열입니다.</param>
      <param name="offset">마샬링을 시작할 오프셋입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 값이 음수이거나, 너무 커서 전체 <see cref="T:System.Security.AccessControl.RawAcl" />를 <paramref name="array" />에 복사할 수 없는 경우</exception>
    </member>
    <member name="M:System.Security.AccessControl.RawAcl.InsertAce(System.Int32,System.Security.AccessControl.GenericAce)">
      <summary>지정한 인덱스에 지정한 ACE(액세스 제어 항목)를 삽입합니다.</summary>
      <param name="index">새 ACE를 추가할 위치입니다.<see cref="P:System.Security.AccessControl.RawAcl.Count" /> 속성 값을 지정하여 <see cref="T:System.Security.AccessControl.RawAcl" /> 개체의 끝에 ACE를 삽입합니다.</param>
      <param name="ace">삽입할 ACE입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 값이 음수이거나, 너무 커서 전체 <see cref="T:System.Security.AccessControl.GenericAcl" />를 <paramref name="array" />에 복사할 수 없는 경우</exception>
    </member>
    <member name="P:System.Security.AccessControl.RawAcl.Item(System.Int32)">
      <summary>지정한 인덱스의 ACE(액세스 제어 항목)를 가져오거나 설정합니다.</summary>
      <returns>지정된 인덱스의 ACE입니다.</returns>
      <param name="index">가져오거나 설정할 ACE의 인덱스(0부터 시작)입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.RawAcl.RemoveAce(System.Int32)">
      <summary>지정한 위치의 ACE(액세스 제어 항목)를 제거합니다.</summary>
      <param name="index">제거할 의 ACE의 인덱스(0부터 시작)입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 매개 변수 값이 <see cref="P:System.Security.AccessControl.RawAcl.Count" /> 속성 값에서 1을 뺀 값보다 크거나 음수인 경우</exception>
    </member>
    <member name="P:System.Security.AccessControl.RawAcl.Revision">
      <summary>
        <see cref="T:System.Security.AccessControl.RawAcl" />의 수정 수준을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Security.AccessControl.RawAcl" />의 수정 수준을 지정하는 바이트 값입니다.</returns>
    </member>
    <member name="T:System.Security.AccessControl.RawSecurityDescriptor">
      <summary>보안 설명자를 나타냅니다.보안 설명자에는 소유자, 주 그룹, DACL(임의 액세스 제어 목록) 및 SACL(시스템 액세스 제어 목록)이 포함됩니다.</summary>
    </member>
    <member name="M:System.Security.AccessControl.RawSecurityDescriptor.#ctor(System.Byte[],System.Int32)">
      <summary>지정된 바이트 값 배열을 사용하여 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="binaryForm">새 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 개체를 만들 바이트 값 배열입니다.</param>
      <param name="offset">
        <paramref name="binaryForm" /> 배열에서 복사를 시작할 오프셋입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.RawSecurityDescriptor.#ctor(System.Security.AccessControl.ControlFlags,System.Security.Principal.SecurityIdentifier,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.RawAcl,System.Security.AccessControl.RawAcl)">
      <summary>지정된 값을 사용하여 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="flags">새 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 개체의 동작을 지정하는 플래그입니다.</param>
      <param name="owner">새 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 개체의 소유자입니다.</param>
      <param name="group">새 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 개체의 기본 그룹입니다.</param>
      <param name="systemAcl">새 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 개체의 SACL(시스템 액세스 제어 목록)입니다.</param>
      <param name="discretionaryAcl">새 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 개체의 DACL(임의 액세스 제어 목록)입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.RawSecurityDescriptor.#ctor(System.String)">
      <summary>지정한 SDDL(Security Descriptor Definition Language) 문자열에서 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="sddlForm">새 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 개체를 만들 SDDL 문자열입니다.</param>
    </member>
    <member name="P:System.Security.AccessControl.RawSecurityDescriptor.ControlFlags">
      <summary>
        <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 개체의 동작을 지정하는 값을 가져옵니다.</summary>
      <returns>논리 OR 연산으로 조합된 <see cref="T:System.Security.AccessControl.ControlFlags" /> 열거형의 하나 이상의 값입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.RawSecurityDescriptor.DiscretionaryAcl">
      <summary>이 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 개체의 DACL(임의 액세스 제어 목록)을 가져오거나 설정합니다.DACL에는 액세스 규칙이 포함되어 있습니다.</summary>
      <returns>이 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 개체의 DACL입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.RawSecurityDescriptor.Group">
      <summary>이 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 개체의 기본 그룹을 가져오거나 설정합니다.</summary>
      <returns>이 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 개체의 기본 그룹입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.RawSecurityDescriptor.Owner">
      <summary>이 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 개체와 관련된 개체의 소유자를 가져오거나 설정합니다.</summary>
      <returns>이 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 개체와 관련된 개체의 소유자입니다.</returns>
    </member>
    <member name="P:System.Security.AccessControl.RawSecurityDescriptor.ResourceManagerControl">
      <summary>이 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 개체와 관련된 리소스 관리자 제어 비트를 나타내는 바이트 값을 가져오거나 설정합니다.</summary>
      <returns>이 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 개체와 관련된 리소스 관리자 제어 비트를 나타내는 바이트 값입니다.</returns>
    </member>
    <member name="M:System.Security.AccessControl.RawSecurityDescriptor.SetFlags(System.Security.AccessControl.ControlFlags)">
      <summary>이 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 개체의 <see cref="P:System.Security.AccessControl.RawSecurityDescriptor.ControlFlags" /> 속성을 지정된 값으로 설정합니다.</summary>
      <param name="flags">논리 OR 연산으로 조합된 <see cref="T:System.Security.AccessControl.ControlFlags" /> 열거형의 하나 이상의 값입니다.</param>
    </member>
    <member name="P:System.Security.AccessControl.RawSecurityDescriptor.SystemAcl">
      <summary>이 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 개체의 SACL(시스템 액세스 제어 목록)을 가져오거나 설정합니다.SACL에는 감사 규칙이 포함되어 있습니다.</summary>
      <returns>이 <see cref="T:System.Security.AccessControl.RawSecurityDescriptor" /> 개체의 SACL입니다.</returns>
    </member>
    <member name="T:System.Security.AccessControl.ResourceType">
      <summary>정의된 네이티브 개체 형식을 지정합니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.DSObject">
      <summary>DS(디렉터리 서비스) 개체이거나 디렉터리 서비스 개체의 속성 집합 또는 속성입니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.DSObjectAll">
      <summary>디렉터리 서비스 개체와 개체의 모든 속성 집합 및 속성입니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.FileObject">
      <summary>파일 또는 디렉터리입니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.KernelObject">
      <summary>로컬 커널 개체입니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.LMShare">
      <summary>네트워크 공유입니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.Printer">
      <summary>프린터입니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.ProviderDefined">
      <summary>공급자가 정의한 개체입니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.RegistryKey">
      <summary>레지스트리 키입니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.RegistryWow6432Key">
      <summary>WOW64에 속해 있는 레지스트리 항목의 개체입니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.Service">
      <summary>Windows 서비스입니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.Unknown">
      <summary>알 수 없는 개체 형식입니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.WindowObject">
      <summary>로컬 컴퓨터의 윈도우 스테이션 또는 바탕 화면 개체입니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.ResourceType.WmiGuidObject">
      <summary>WMI(Windows Management Instrumentation) 개체입니다.</summary>
    </member>
    <member name="T:System.Security.AccessControl.SecurityInfos">
      <summary>쿼리하거나 설정할 보안 설명자의 섹션을 지정합니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.SecurityInfos.DiscretionaryAcl">
      <summary>DACL(임의 액세스 제어 목록)을 지정합니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.SecurityInfos.Group">
      <summary>기본 그룹 식별자를 지정합니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.SecurityInfos.Owner">
      <summary>소유자 식별자를 지정합니다.</summary>
    </member>
    <member name="F:System.Security.AccessControl.SecurityInfos.SystemAcl">
      <summary>SACL(시스템 액세스 제어 목록)을 지정합니다.</summary>
    </member>
    <member name="T:System.Security.AccessControl.SystemAcl">
      <summary>SACL(시스템 액세스 제어 목록)을 나타냅니다.</summary>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.#ctor(System.Boolean,System.Boolean,System.Byte,System.Int32)">
      <summary>지정된 값을 사용하여 <see cref="T:System.Security.AccessControl.SystemAcl" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="isContainer">새 <see cref="T:System.Security.AccessControl.SystemAcl" /> 개체가 컨테이너이면 true입니다.</param>
      <param name="isDS">새 <see cref="T:System.Security.AccessControl.SystemAcl" /> 개체가 디렉터리 개체 ACL(액세스 제어 목록)이면 true입니다.</param>
      <param name="revision">새 <see cref="T:System.Security.AccessControl.SystemAcl" /> 개체의 수정 수준입니다.</param>
      <param name="capacity">이 <see cref="T:System.Security.AccessControl.SystemAcl" /> 개체에 포함될 수 있는 ACE(액세스 제어 항목)의 수입니다.이 번호는 힌트로만 사용됩니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.#ctor(System.Boolean,System.Boolean,System.Int32)">
      <summary>지정된 값을 사용하여 <see cref="T:System.Security.AccessControl.SystemAcl" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="isContainer">새 <see cref="T:System.Security.AccessControl.SystemAcl" /> 개체가 컨테이너이면 true입니다.</param>
      <param name="isDS">새 <see cref="T:System.Security.AccessControl.SystemAcl" /> 개체가 디렉터리 개체 ACL(액세스 제어 목록)이면 true입니다.</param>
      <param name="capacity">이 <see cref="T:System.Security.AccessControl.SystemAcl" /> 개체에 포함될 수 있는 ACE(액세스 제어 항목)의 수입니다.이 번호는 힌트로만 사용됩니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.#ctor(System.Boolean,System.Boolean,System.Security.AccessControl.RawAcl)">
      <summary>지정된 <see cref="T:System.Security.AccessControl.RawAcl" /> 개체의 지정된 값을 사용하여 <see cref="T:System.Security.AccessControl.SystemAcl" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="isContainer">새 <see cref="T:System.Security.AccessControl.SystemAcl" /> 개체가 컨테이너이면 true입니다.</param>
      <param name="isDS">새 <see cref="T:System.Security.AccessControl.SystemAcl" /> 개체가 디렉터리 개체 ACL(액세스 제어 목록)이면 true입니다.</param>
      <param name="rawAcl">새 <see cref="T:System.Security.AccessControl.SystemAcl" /> 개체의 내부 <see cref="T:System.Security.AccessControl.RawAcl" /> 개체입니다.null을 지정하여 빈 ACL을 만듭니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.AddAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>현재 <see cref="T:System.Security.AccessControl.SystemAcl" /> 개체에 감사 규칙을 추가합니다.</summary>
      <param name="auditFlags">추가할 감사 규칙의 형식입니다.</param>
      <param name="sid">감사 규칙을 추가할 <see cref="T:System.Security.Principal.SecurityIdentifier" />입니다.</param>
      <param name="accessMask">새 감사 규칙의 액세스 마스크입니다.</param>
      <param name="inheritanceFlags">새 감사 규칙의 상속 속성을 지정하는 플래그입니다.</param>
      <param name="propagationFlags">새 감사 규칙의 상속 전파 속성을 지정하는 플래그입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.AddAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>현재 <see cref="T:System.Security.AccessControl.SystemAcl" /> 개체에 지정된 설정이 있는 감사 규칙을 추가합니다.이 메서드는 새 감사 규칙의 개체 형식 또는 상속된 개체 형식을 지정할 때 디렉터리 개체 ACL(액세스 제어 목록)에 사용합니다.</summary>
      <param name="auditFlags">추가할 감사 규칙의 형식입니다.</param>
      <param name="sid">감사 규칙을 추가할 <see cref="T:System.Security.Principal.SecurityIdentifier" />입니다.</param>
      <param name="accessMask">새 감사 규칙의 액세스 마스크입니다.</param>
      <param name="inheritanceFlags">새 감사 규칙의 상속 속성을 지정하는 플래그입니다.</param>
      <param name="propagationFlags">새 감사 규칙의 상속 전파 속성을 지정하는 플래그입니다.</param>
      <param name="objectFlags">
        <paramref name="objectType" /> 및 <paramref name="inheritedObjectType" /> 매개 변수에 null이 아닌 값이 포함되어 있는지 여부를 지정하는 플래그입니다.</param>
      <param name="objectType">새 감사 규칙을 적용할 개체 클래스의 ID입니다.</param>
      <param name="inheritedObjectType">새 감사 규칙을 상속할 수 있는 자식 개체 클래스의 ID입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.AddAudit(System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAuditRule)">
      <summary>현재 <see cref="T:System.Security.AccessControl.SystemAcl" /> 개체에 감사 규칙을 추가합니다.</summary>
      <param name="sid">감사 규칙을 추가할 <see cref="T:System.Security.Principal.SecurityIdentifier" />입니다.</param>
      <param name="rule">
        <see cref="T:System.Security.AccessControl.ObjectAuditRule" />새 감사 규칙에 대 한 합니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.RemoveAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>지정한 감사 규칙을 현재 <see cref="T:System.Security.AccessControl.SystemAcl" /> 개체에서 제거합니다.</summary>
      <returns>지정한 감사 규칙이 이 메서드로 제거되면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="auditFlags">제거할 감사 규칙의 형식입니다.</param>
      <param name="sid">감사 규칙을 제거할 <see cref="T:System.Security.Principal.SecurityIdentifier" />입니다.</param>
      <param name="accessMask">제거할 규칙에 대한 액세스 마스크입니다.</param>
      <param name="inheritanceFlags">제거할 규칙의 상속 속성을 지정하는 플래그입니다.</param>
      <param name="propagationFlags">제거할 규칙의 상속 전파 속성을 지정하는 플래그입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.RemoveAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>지정한 감사 규칙을 현재 <see cref="T:System.Security.AccessControl.SystemAcl" /> 개체에서 제거합니다.이 메서드는 개체 형식 또는 상속된 개체 형식을 지정할 때 디렉터리 개체 ACL(액세스 제어 목록)에 사용합니다.</summary>
      <returns>지정한 감사 규칙이 이 메서드로 제거되면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="auditFlags">제거할 감사 규칙의 형식입니다.</param>
      <param name="sid">감사 규칙을 제거할 <see cref="T:System.Security.Principal.SecurityIdentifier" />입니다.</param>
      <param name="accessMask">제거할 규칙에 대한 액세스 마스크입니다.</param>
      <param name="inheritanceFlags">제거할 규칙의 상속 속성을 지정하는 플래그입니다.</param>
      <param name="propagationFlags">제거할 규칙의 상속 전파 속성을 지정하는 플래그입니다.</param>
      <param name="objectFlags">
        <paramref name="objectType" /> 및 <paramref name="inheritedObjectType" /> 매개 변수에 null이 아닌 값이 포함되어 있는지 여부를 지정하는 플래그입니다.</param>
      <param name="objectType">제거한 감사 제어 규칙을 적용할 개체 클래스의 ID입니다.</param>
      <param name="inheritedObjectType">제거한 감사 규칙을 상속할 수 있는 자식 개체 클래스의 ID입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.RemoveAudit(System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAuditRule)">
      <summary>지정한 감사 규칙을 현재 <see cref="T:System.Security.AccessControl.SystemAcl" /> 개체에서 제거합니다.</summary>
      <returns>지정한 감사 규칙이 이 메서드로 제거되면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="sid">감사 규칙을 제거할 <see cref="T:System.Security.Principal.SecurityIdentifier" />입니다.</param>
      <param name="rule">감사 규칙을 제거할 <see cref="T:System.Security.AccessControl.ObjectAuditRule" />입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.RemoveAuditSpecific(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>지정한 감사 규칙을 현재 <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> 개체에서 제거합니다.</summary>
      <param name="auditFlags">제거할 감사 규칙의 형식입니다.</param>
      <param name="sid">감사 규칙을 제거할 <see cref="T:System.Security.Principal.SecurityIdentifier" />입니다.</param>
      <param name="accessMask">제거할 규칙에 대한 액세스 마스크입니다.</param>
      <param name="inheritanceFlags">제거할 규칙의 상속 속성을 지정하는 플래그입니다.</param>
      <param name="propagationFlags">제거할 규칙의 상속 전파 속성을 지정하는 플래그입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.RemoveAuditSpecific(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>지정한 감사 규칙을 현재 <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> 개체에서 제거합니다.이 메서드는 개체 형식 또는 상속된 개체 형식을 지정할 때 디렉터리 개체 ACL(액세스 제어 목록)에 사용합니다.</summary>
      <param name="auditFlags">제거할 감사 규칙의 형식입니다.</param>
      <param name="sid">감사 규칙을 제거할 <see cref="T:System.Security.Principal.SecurityIdentifier" />입니다.</param>
      <param name="accessMask">제거할 규칙에 대한 액세스 마스크입니다.</param>
      <param name="inheritanceFlags">제거할 규칙의 상속 속성을 지정하는 플래그입니다.</param>
      <param name="propagationFlags">제거할 규칙의 상속 전파 속성을 지정하는 플래그입니다.</param>
      <param name="objectFlags">
        <paramref name="objectType" /> 및 <paramref name="inheritedObjectType" /> 매개 변수에 null이 아닌 값이 포함되어 있는지 여부를 지정하는 플래그입니다.</param>
      <param name="objectType">제거한 감사 제어 규칙을 적용할 개체 클래스의 ID입니다.</param>
      <param name="inheritedObjectType">제거한 감사 규칙을 상속할 수 있는 자식 개체 클래스의 ID입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.RemoveAuditSpecific(System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAuditRule)">
      <summary>지정한 감사 규칙을 현재 <see cref="T:System.Security.AccessControl.DiscretionaryAcl" /> 개체에서 제거합니다.</summary>
      <param name="sid">감사 규칙을 제거할 <see cref="T:System.Security.Principal.SecurityIdentifier" />입니다.</param>
      <param name="rule">
        <see cref="T:System.Security.AccessControl.ObjectAuditRule" /> 제거할 규칙에 대 한 합니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.SetAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
      <summary>지정한 감사 규칙을 지정한 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체에 설정합니다.</summary>
      <param name="auditFlags">설정할 감사 조건입니다.</param>
      <param name="sid">감사 규칙을 설정할 <see cref="T:System.Security.Principal.SecurityIdentifier" />입니다.</param>
      <param name="accessMask">새 감사 규칙의 액세스 마스크입니다.</param>
      <param name="inheritanceFlags">새 감사 규칙의 상속 속성을 지정하는 플래그입니다.</param>
      <param name="propagationFlags">새 감사 규칙의 상속 전파 속성을 지정하는 플래그입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.SetAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
      <summary>지정한 감사 규칙을 지정한 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체에 설정합니다.이 메서드는 개체 형식 또는 상속된 개체 형식을 지정할 때 디렉터리 개체 ACL(액세스 제어 목록)에 사용합니다.</summary>
      <param name="auditFlags">설정할 감사 조건입니다.</param>
      <param name="sid">감사 규칙을 설정할 <see cref="T:System.Security.Principal.SecurityIdentifier" />입니다.</param>
      <param name="accessMask">새 감사 규칙의 액세스 마스크입니다.</param>
      <param name="inheritanceFlags">새 감사 규칙의 상속 속성을 지정하는 플래그입니다.</param>
      <param name="propagationFlags">새 감사 규칙의 상속 전파 속성을 지정하는 플래그입니다.</param>
      <param name="objectFlags">
        <paramref name="objectType" /> 및 <paramref name="inheritedObjectType" /> 매개 변수에 null이 아닌 값이 포함되어 있는지 여부를 지정하는 플래그입니다.</param>
      <param name="objectType">새 감사 규칙을 적용할 개체 클래스의 ID입니다.</param>
      <param name="inheritedObjectType">새 감사 규칙을 상속할 수 있는 자식 개체 클래스의 ID입니다.</param>
    </member>
    <member name="M:System.Security.AccessControl.SystemAcl.SetAudit(System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAuditRule)">
      <summary>지정한 감사 규칙을 지정한 <see cref="T:System.Security.Principal.SecurityIdentifier" /> 개체에 설정합니다.</summary>
      <param name="sid">감사 규칙을 설정할 <see cref="T:System.Security.Principal.SecurityIdentifier" />입니다.</param>
      <param name="rule">감사 규칙을 설정할 <see cref="T:System.Security.AccessControl.ObjectAuditRule" />입니다.</param>
    </member>
  </members>
</doc>