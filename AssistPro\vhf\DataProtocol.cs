﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO.Ports;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Security.Policy;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Timers;
using System.Windows.Forms;
using static AssistPro.vhf.DataProtocol;

namespace AssistPro.vhf
{
    class DataProtocol
    {

        private const int FRAME_LEN_SIZE = 2;
        private const int FRAME_SEQ_NO_SIZE = 2;
        private const int FRAME_CMD1_SIZE = 2;
        private const int FRAME_CMD2_SIZE = 2;

        public const int PAYLOAD_OFFSET = 9;
        public const int FRAME_CRC_SIZE = 2;

        public const int FRAME_HEADER_SIZE = FRAME_LEN_SIZE + FRAME_SEQ_NO_SIZE + FRAME_CMD1_SIZE + FRAME_CMD2_SIZE;
        public const int MAX_FRAME_LEN = 300;

        public const int DSC_EXP_CHR_SIZE = 6;
        public const int DSC_FRQ_CHR_SIZE = 8;
        public const int DSC_TEL_CHR_SIZE = 9;
        public const int DSC_REAL_DATA_ST_IDX = 16;
        
        public const int DSC_MSG_SIZE = (60 - 8); // without header

        public const int TRACK_INFO_SIZE = 16;

        public delegate void NotiDsdMsgReceived();
        public static event NotiDsdMsgReceived evtNotiDscMsgReceived;

        public delegate void NotiFullExtCommReceived();
        public static event NotiFullExtCommReceived evtNotiFullExtCommReceived;

        public delegate void NotiSelfTestReceived();
        public static event NotiSelfTestReceived evtNotiSelfTestReceived;

        public delegate void NotiPosTmReceived();
        public static event NotiPosTmReceived evtNotiPosTmReceived;

        public delegate void NotiAudioTrackInfoReceived();
        public static event NotiAudioTrackInfoReceived evtNotiAudioTrackInfoReceived;

        public delegate void NotiRecordDoneReceived();
        public static event NotiRecordDoneReceived evtNotiRecordDoneReceived;

       public enum PosStrFmt
        {
            Normal,
            Unknown
        }

        public enum ERROR
        {
            ERR_OK = 0,
            ERR_SFD = -1,
            ERR_LEN = -2,
            ERR_CRC = -3,
        }

        public enum STATE
        {
            REQ_READY,
            READ_FILELINE,
            SEND_HEXDATA,
            READ_STATUS,
            STOP_UPDATE,
            LAST_UPDATE,
            UPDATE_DONE,
        }

        public enum PROTO_STAT
        {
            IDLE_STAT,
            DATA_BAD,
            DATA_OK,
            WAIT_FOR_ACK,
            WAIT_FOR_RESPONSE,
        }

        // CMD TYPE
        public const byte CMD_REQ = 0x00;
        public const byte CMD_RSP = 0x01;
        public const byte CMD_SET_NOTI = 0x02;
        public const byte CMD_ACK = 0x03;

        // TARGET TYPE
        public const byte TRANSCEIVER = 0x00;
        public const byte CONTROLLER = 0x01;
        public const byte ALARM_UNIT = 0x02;
        public const byte ALARM_BOX = 0x03;
        public const byte BLOADCAST = 0x04;

        // COMMAND

        // Self Diagnosis Group (0x01 ~ 0x1F)
        public const byte CMD_COM_TEST = 0x01;
        public const byte CMD_SET_SELF_TEST = 0x02;

        public const byte CMD_NOTI_SELF_TEST= 0x03;

        // System Group (0x2 ~ 0x2F)
        public const byte CMD_TRANS_CONNECT = 0x20;
        public const byte CMD_REQ_MOD_INFO = 0x21;
        public const byte CMD_CONTROLLER_SET = 0x22;

        // Audio Group (0x30 ~ 0x3F)
        public const byte CMD_BB_SET = 0x30;
        public const byte CMD_CODEC_SET = 0x31;
        public const byte CMD_VOL_SET = 0x32;
        public const byte CMD_RF_SET = 0x3A;

        public const byte CMD_REQ_TRACK_INFO = 0x3B;
        public const byte CMD_AUDIO_TRACK_SET = 0x3C;

        public const byte CMD_READY_TRACK_SET = 0x3D;

        public const byte CMD_DELETE_TRACK_SET = 0x3E;

        public const byte CMD_RECORD_DONE_NOTI = 0x3F;

        // DSC Group (0x40 ~ 0x4F)
        public const byte CMD_NOTI_RX_DSC = 0x40;
        public const byte CMD_REQ_TX_DSC = 0x41;

        // TU ALERT Group (0x50 ~ 0x5F)
        public const byte CMD_NOTI_TU_ALERT = 0x50;

        public const byte CMD_NOTI_FULL_EXT_COMM = 0xE0;        
        public const byte CMD_NOTI_POS_TM = 0xE1;        

        // COMMAND PARAM
        public const byte FRAMEINFO = 3;
        public const byte FRM_SFD = 0x24;

        // Define DataProtocol
        public enum COMM_TEST
        {
            TEST_VALUE_1,
            TEST_VALUE_2,
            TEST_VALUE_3,
            TEST_VALUE_4,
            MAX_COMM_TEST,
        }
        public byte[] m_CommTestData;

        public enum DSC_LOOP_TEST
        {
            TEST_RESULT,
            RESERVED_1,
            RESERVED_2,
            RESERVED_3,
            MAX_DSC_LOOP_TEST,
        }
        public byte[] m_DscLoopTestData;


        public enum TRANS_VER
        {
            MAJOR,
            MINOR,
            REVISION,
            RELEASE,
            MAX_TRANS_VER,
        }
        public byte[] m_TransVerData;


        public enum BB_SET
        {
            OPMODE,
            GAIN,
            ATT,
            LIMITER,
            MAX_BB_SET,
        }
        public byte[] m_BaseBandSetData;

        public enum CODEC_SET
        {
            IN_AMP_FRONT_MIC,
            IN_AMP_REAR_MIC,
            IN_AMP_WING_MIC,
            IN_AMP_RX_BB,
            MAX_CODEC_SET,
        }
        public byte[] m_CodecSetData;

        public enum VOL_CH
        {
            FRONT_H_SPK,
            REAR_WING_H_SPK,
            FRONT_EXT_SPK,
            OUT_RX_BB,
            MAX_VOL_CH,
        }

        public enum VOL_SET
        {
            SELECT_CH,
            OUT_FRONT_H_SPK,
            OUT_REAR_WING_SPK,
            OUT_FRONT_EXT_SPK,
            OUT_RX_BB,
            RESERVED_1,
            RESERVED_2,
            RESERVED_3,
            MAX_VOL_SET,
        }
        public byte[] m_VolSetData;

        public enum TU_ALERT
        {
            ALERT_ID_L,
            ALERT_ID_H,
            ALERT_INSTANCE,
            ALERT_STATE,
            MAX_TU_ALERT,
        }
        public byte[] m_TuAlertData;

        public enum MSG_TYPE
        {
            DSC_MSG_TYPE_NONE = 0,                            // None
            DSC_MSG_TYPE_DST_ALT,                             // Distress-Alert  (tDstALT)
            DSC_MSG_TYPE_DST_ACK,                             // Distress-ACK    (tDstACK)
            DSC_MSG_TYPE_DST_RLY,                             // Distress-Relay  (tDstRLY)
            DSC_MSG_TYPE_RLY_ACK,                             // Dst-Rly-ACK     (tRlyACK)
            DSC_MSG_TYPE_ALL_SHIP,                            // All-Ship        (tAllSHIP)
            DSC_MSG_TYPE_GRP_CALL,                            // Group-Cal       (tGrpCALL)
            DSC_MSG_TYPE_GEO_AREA,                            // Geo-Call        (tGeoAREA)
            DSC_MSG_TYPE_INDI_CALL,                           // Individual-Call (tIndiCALL)
            DSC_MSG_TYPE_SEMI_AUTO,                           // Semi-Auto       (tSemiAUTO)
        }


        public enum ECC_ERR
        {
            DSC_ECC_MODE_ERROR = 0,                           // ECC Error
            DSC_ECC_MODE_GOOD,                                // ECC OK
        }

        public struct TrackInfo
        {
            public uint[] MagicCode;
            public uint[] TrackNum;
            public uint[] RecordMS;
            public uint[] TimeStamp;
        }

        public struct PlaybackSet
        {
            public byte status;
            public byte track_number;
            public byte Reserved1;
            public byte Reserved2;
            public uint playback_pos;
        }

        public enum READY_TRACK_SET
        {
            TRACK_NUMBER,
            RESERVED_1,
            RESERVED_2,
            RESERVED_3,
            MAX_READY_TRACK_SET,
        }
        public byte[] m_ReadyTrackSetData;

        public enum CONTROLLER_SET
        {
            SOURCE,
            RESERVED_1,
            RESERVED_2,
            RESERVED_3,
            MAX_CONTROLLER_SET,
        }
        public byte[] m_ControllerSetData;

        public enum DELETE_TRACK_SET
        {
            TRACK_NUMBER,
            RESERVED_1,
            RESERVED_2,
            RESERVED_3,
            MAX_DELETE_TRACK_SET,
        }
        public byte[] m_DeleteTrackSetData;

        public struct SetRfDAta
        {
            public byte PwrCnt;
            public byte DdsAdj;
            public byte DcOffset;
            public byte Reserved1;
            public ushort MainRxPower;
            public ushort MainTxPower;
            public ushort WkrRxPower;
            public ushort Reserved3;
            public uint MainDdsFreq;
            public uint WkrDdsFreq;
        }


        public struct PosTime
        {
            public int lat;
            public int lon;
            public int date;
            public int time;
            public int lz_hours;
            public int lz_mins;
        }


        public struct SelfTest
        {
            public byte test_item;
            public byte test_status;
            public byte Reserved2;
            public byte Reserved3;
        }

        public struct DscMsgHead
        {
            public MSG_TYPE msg_type;
            public ECC_ERR ecc_err;

            public uint date;
            public uint time;
            public uint freq;
        }

        public struct DstAlert
        {
            public DscMsgHead Head;
            public byte Format;
            public byte[] SelfID;
            public byte Nature;
            public byte[] DstPos;
            public byte[] UTC;
            public byte SubCom;
            public byte EOS;
            public byte[] ExpPos;
        }

        public struct DstAlertAck
        {
            public DscMsgHead Head;
            public byte Format;
            public byte Category;
            public byte[] SelfID;
            public byte TelCmd0;
            public byte[] DistID;
            public byte Nature;
            public byte[] DstPos;
            public byte[] UTC;
            public byte SubCom;
            public byte EOS;
            public byte[] ExpPos;
        }

        public struct DstAlertRelay
        {
            public DscMsgHead Head;
            public byte Format;
            public byte[] AddrID;
            public byte Category;
            public byte[] SelfID;
            public byte TelCmd0;
            public byte[] DistID;
            public byte Nature;
            public byte[] DstPos;
            public byte[] UTC;
            public byte SubCom;
            public byte EOS;
            public byte[] ExpPos;
        }

        public struct DstAlertRelayAck
        {
            public DscMsgHead Head;
            public byte Format;
            public byte[] AddrID;
            public byte Category;
            public byte[] SelfID;
            public byte TelCmd0;
            public byte[] DistID;
            public byte Nature;
            public byte[] DstPos;
            public byte[] UTC;
            public byte SubCom;
            public byte EOS;
            public byte[] ExpPos;
        }

        public struct AllShip
        {
            public DscMsgHead Head;
            public byte Format;
            public byte Category;
            public byte[] SelfID;
            public byte TelCmd0;
            public byte TelCmd1;
            public byte[] Freq;
            public byte EOS;
        }

        public struct GeoAREA
        {
            public DscMsgHead Head;
            public byte Format;
            public byte[] AddrID;
            public byte Category;
            public byte[] SelfID;
            public byte TelCmd0;
            public byte TelCmd1;
            public byte[] Freq;
            public byte EOS;
        }

        public struct GrpCall
        {
            public DscMsgHead Head;
            public byte Format;
            public byte[] AddrID;
            public byte Category;
            public byte[] SelfID;
            public byte TelCmd0;
            public byte TelCmd1;
            public byte[] Freq;
            public byte EOS;
        }

        public struct IndiCall
        {
            public DscMsgHead Head;
            public byte Format;
            public byte[] AddrID;
            public byte Category;
            public byte[] SelfID;
            public byte TelCmd0;
            public byte TelCmd1;
            public byte[] Freq;
            public byte[] UTC;
            public byte EOS;
            public byte[] ExpPos;
        }

        public struct SemiAUTO
        {
            public DscMsgHead Head;
            public byte Format;
            public byte[] AddrID;
            public byte Category;
            public byte[] SelfID;
            public byte TelCmd0;
            public byte TelCmd1;
            public byte[] Freq;
            public byte[] TelNo;
            public byte EOS;
        }

        public struct DataFrame
        {
            public byte RxFrameIdx;
            public ushort SFD;
            public ushort Length;
            public ushort SeqNum;
            public ushort CMD;
            public ushort SubCmd;
            public byte[] data_buf;
            public byte CRC_L;
            public byte CRC_H;
            public ERROR ErrorStat;
        }

        public struct RcvFrame
        {
            public bool bReceiving;
            public uint rcv_cnt;
            public ushort frame_len;
            public ushort seq_no;
            public byte[] rx_data;
        }

        public struct DataStatusCnt
        {
            public uint pass_cnt;
            public uint fail_cnt;
        }

        // 구조체
        public PROTO_STAT m_protocol_status;
        public DataFrame m_DataFrame;
        public RcvFrame m_RcvFrame; // serial raw data;
        public DataStatusCnt m_DataStatCnt;
        public bool m_ConnectStat;

        public SetRfDAta m_SetRfData;
        public PosTime m_PosTime;

        public SelfTest m_SelfTestData;

        public TrackInfo m_TrackInfoData;

        public PlaybackSet m_PlaybackSetData;

        public DstAlert m_DstAlert;
        public DstAlertAck m_DstAlertAck;
        public DstAlertRelay m_DstAlertRelay;
        public DstAlertRelayAck m_DstAlertRelayAck;
        public AllShip m_AllShip;
        public GeoAREA m_GeoAREA;
        public GrpCall m_GrpCall;
        public IndiCall m_IndiCall;
        public SemiAUTO m_SemiAUTO;
    
        private byte[] m_ComTxBuf;
        private ushort m_SeqNum;

        private int m_DataIdx;


        private SerialPort m_SerialPort;
        

        private void AllocateData()
        {
            m_CommTestData = new byte[(int)COMM_TEST.MAX_COMM_TEST];
            m_TransVerData = new byte[(int)TRANS_VER.MAX_TRANS_VER];
            m_BaseBandSetData = new byte[(int)BB_SET.MAX_BB_SET];
            m_TuAlertData = new byte[(int)TU_ALERT.MAX_TU_ALERT];
            m_CodecSetData = new byte[(int)CODEC_SET.MAX_CODEC_SET];
            m_VolSetData = new byte[(int)VOL_SET.MAX_VOL_SET];

            m_DscLoopTestData = new byte[(int)DSC_LOOP_TEST.MAX_DSC_LOOP_TEST];

            m_CodecSetData[(int)CODEC_SET.IN_AMP_FRONT_MIC] = 2;
            m_CodecSetData[(int)CODEC_SET.IN_AMP_REAR_MIC] = 2;
            m_CodecSetData[(int)CODEC_SET.IN_AMP_WING_MIC] = 2;
            m_CodecSetData[(int)CODEC_SET.IN_AMP_RX_BB] = 2;

            m_SetRfData.MainDdsFreq = 0;
            m_SetRfData.WkrDdsFreq = 0;
            m_SetRfData.MainRxPower = 16;
            m_SetRfData.MainTxPower = 16;
            m_SetRfData.WkrRxPower = 16;

            m_DstAlert.SelfID = new byte[5];
            m_DstAlert.DstPos = new byte[5];
            m_DstAlert.UTC = new byte[2];
            m_DstAlert.ExpPos = new byte[DSC_EXP_CHR_SIZE];

            m_DstAlertAck.SelfID = new byte[5];
            m_DstAlertAck.DistID = new byte[5];
            m_DstAlertAck.DstPos = new byte[5];
            m_DstAlertAck.UTC = new byte[2];
            m_DstAlertAck.ExpPos = new byte[DSC_EXP_CHR_SIZE];

            m_DstAlertRelay.AddrID = new byte[5];
            m_DstAlertRelay.SelfID = new byte[5];
            m_DstAlertRelay.DistID = new byte[5];
            m_DstAlertRelay.DstPos = new byte[5];
            m_DstAlertRelay.UTC = new byte[2];
            m_DstAlertRelay.ExpPos = new byte[DSC_EXP_CHR_SIZE];

            m_DstAlertRelayAck.AddrID = new byte[5];
            m_DstAlertRelayAck.SelfID = new byte[5];
            m_DstAlertRelayAck.DistID = new byte[5];
            m_DstAlertRelayAck.DstPos = new byte[5];
            m_DstAlertRelayAck.UTC = new byte[2];
            m_DstAlertRelayAck.ExpPos = new byte[DSC_EXP_CHR_SIZE];


            m_AllShip.SelfID = new byte[5];
            m_AllShip.Freq = new byte[DSC_FRQ_CHR_SIZE];

            m_GeoAREA.AddrID = new byte[5];
            m_GeoAREA.SelfID = new byte[5];
            m_GeoAREA.Freq = new byte[DSC_FRQ_CHR_SIZE];

            m_GrpCall.AddrID = new byte[5];
            m_GrpCall.SelfID = new byte[5];
            m_GrpCall.Freq = new byte[DSC_FRQ_CHR_SIZE];

            m_IndiCall.AddrID = new byte[5];
            m_IndiCall.SelfID = new byte[5];
            m_IndiCall.Freq = new byte[DSC_FRQ_CHR_SIZE];
            m_IndiCall.UTC = new byte[2];
            m_IndiCall.ExpPos = new byte[DSC_EXP_CHR_SIZE];

            m_SemiAUTO.AddrID = new byte[5];
            m_SemiAUTO.SelfID = new byte[5];
            m_SemiAUTO.Freq = new byte[DSC_FRQ_CHR_SIZE];
            m_SemiAUTO.TelNo = new byte[DSC_TEL_CHR_SIZE];

            m_TrackInfoData.MagicCode = new uint[TRACK_INFO_SIZE];
            m_TrackInfoData.TrackNum = new uint[TRACK_INFO_SIZE];
            m_TrackInfoData.RecordMS = new uint[TRACK_INFO_SIZE];
            m_TrackInfoData.TimeStamp = new uint[TRACK_INFO_SIZE];

            m_ReadyTrackSetData = new byte[(int)READY_TRACK_SET.MAX_READY_TRACK_SET];
            m_ControllerSetData = new byte[(int)CONTROLLER_SET.MAX_CONTROLLER_SET];
            m_DeleteTrackSetData = new byte[(int)DELETE_TRACK_SET.MAX_DELETE_TRACK_SET];
        }

        public DataProtocol(SerialPort port)
        {

            m_ComTxBuf = new byte[1024];
            m_DataFrame.data_buf = new byte[256];
            m_DataFrame.SeqNum = 0;

            m_RcvFrame.rx_data = new byte[MAX_FRAME_LEN + FRAME_CRC_SIZE];
            m_SerialPort = port;

            AllocateData();
            Init();
        }

        public void SendFullFrameData(byte[] frame, byte length)
        {
            int idx = 5;
            ushort crc16;

            int len = length / 2 + 1;

            m_ComTxBuf[0] = frame[0];
            m_ComTxBuf[1] = frame[1];
            m_ComTxBuf[2] = frame[2];
            m_ComTxBuf[3] = frame[3];
            m_ComTxBuf[4] = frame[4];

            for (byte i = 5; i < len; i++)
            {
                m_ComTxBuf[i + PAYLOAD_OFFSET] = frame[i];
                idx++;
            }

            crc16 = crc16_calc(0xFFFF, m_ComTxBuf, (uint)(idx - 1));

            m_ComTxBuf[idx++] = (byte)(crc16);
            m_ComTxBuf[idx++] = (byte)(crc16 >> 8);

            m_SerialPort.Write(m_ComTxBuf, 0, idx);
        }

        //Target type
        // 0 : Transceiver
        // 1 : Controller
        // 2 : Alarm unit
        // 3 : Alarm box
        // 4 : All

        //Command type
        // 0 : Request
        // 1 : Response
        // 2 : Set or Noti
        // 3 : Ack

        public ushort GenerateCMD(byte cmd_type, byte sender, byte receiver)
        {
            ushort cmd;

            cmd = (ushort)((cmd_type << 6) | (sender << 3) | (receiver));

            return cmd;
        }

        //public PROTO_STAT ValidateHeadData()
        //{
        //    if ((m_SendCmdParam != m_RcvCmdParam) || (m_SendSeqNum != m_RcvSeqNum))
        //    {
        //        return PROTO_STAT.DATA_BAD;
        //    }

        //    return PROTO_STAT.DATA_OK;
        //}

        public PROTO_STAT CheckStatus()
        {
            return m_protocol_status;
        }

        public void ResetReceiveFrame()
        {
            m_RcvFrame.bReceiving = false;
            m_RcvFrame.frame_len = 0;
            m_RcvFrame.rcv_cnt = 0;
            m_RcvFrame.seq_no = 0;
            Array.Clear(m_RcvFrame.rx_data, 0, m_RcvFrame.rx_data.Length);
        }

        public void Init()
        {
            m_SeqNum = 0;
            m_protocol_status = PROTO_STAT.IDLE_STAT;
            m_ConnectStat = false;

            ResetReceiveFrame();
        }

        public void RetrySendFrame()
        {
            m_SerialPort.Write(m_ComTxBuf, 0, m_DataIdx);
        }

        public string UShortToHex(ushort number)
        {
            return number.ToString("X4");
        }

        public string ConvertRawDataToString(byte[] convertArr, int cnt, bool mode)
        {
            StringBuilder hex = new StringBuilder(convertArr.Length * 2);
            for (int i = 0; i < cnt; i++)
            {
                if (mode == true)
                {
                    hex.Append(convertArr[i].ToString("X2"));
                }
                else
                {
                    if (convertArr[i].ToString().Length > 2)
                        hex.Append(convertArr[i].ToString("D3"));
                    else
                        hex.Append(convertArr[i].ToString("D2"));
                }
                hex.Append(" ");
            }
            //hex.Append("\r\n");

            return hex.ToString();
        }

        public void ClearStatCnt()
        {
            m_DataStatCnt.pass_cnt = 0;
            m_DataStatCnt.fail_cnt = 0;
        }

        public void EncodeDistressAlertRelay(ref byte[] data)
        {
            uint idx = 0;
            data[idx++] = (byte)m_DstAlertRelay.Head.msg_type;
            data[idx++] = (byte)m_DstAlertRelay.Head.ecc_err;
            data[idx++] = 0; // padding
            data[idx++] = 0; // padding

            for (uint i = 0; i < 4; i++) // date
                data[idx++] = 0;

            for (uint i = 0; i < 4; i++) // time
                data[idx++] = 0;

            for (uint i = 0; i < 4; i++) // freq
                data[idx++] = 0;

            data[idx++] = m_DstAlertRelay.Format; //format

            for (uint i = 0; i < 5; i++)
                data[idx++] = m_DstAlertRelay.AddrID[i]; //Addr

            data[idx++] = m_DstAlertRelay.Category; //Category

            for (uint i = 0; i < 5; i++)
                data[idx++] = m_DstAlertRelay.SelfID[i]; //self id

            data[idx++] = m_DstAlertRelay.TelCmd0; //TeleCom

            for (uint i = 0; i < 5; i++)
                data[idx++] = m_DstAlertRelay.DistID[i]; //Distress ID

            data[idx++] = m_DstAlertRelay.Nature; // Nature

            for (uint i = 0; i < 5; i++)
                data[idx++] = m_DstAlertRelay.DstPos[i];

            for (uint i = 0; i < 2; i++)
                data[idx++] = m_DstAlertRelay.UTC[i];

            data[idx++] = m_DstAlertRelay.SubCom;
            data[idx++] = m_DstAlertRelay.EOS;

            for (uint i = 0; i < DSC_EXP_CHR_SIZE; i++)
                data[idx++] = m_DstAlertRelay.ExpPos[i];
        }

        public void EncodeDistressAlertRelayAck(ref byte[] data)
        {
            uint idx = 0;
            data[idx++] = (byte)m_DstAlertRelayAck.Head.msg_type;
            data[idx++] = (byte)m_DstAlertRelayAck.Head.ecc_err;
            data[idx++] = 0; // padding
            data[idx++] = 0; // padding

            for (uint i = 0; i < 4; i++) // date
                data[idx++] = 0;

            for (uint i = 0; i < 4; i++) // time
                data[idx++] = 0;

            for (uint i = 0; i < 4; i++) // freq
                data[idx++] = 0;

            data[idx++] = m_DstAlertRelayAck.Format; //format

            for (uint i = 0; i < 5; i++)
                data[idx++] = m_DstAlertRelayAck.AddrID[i]; //Addr

            data[idx++] = m_DstAlertRelayAck.Category; //Category

            for (uint i = 0; i < 5; i++)
                data[idx++] = m_DstAlertRelayAck.SelfID[i]; //self id

            data[idx++] = m_DstAlertRelayAck.TelCmd0; //TeleCom

            for (uint i = 0; i < 5; i++)
                data[idx++] = m_DstAlertRelayAck.DistID[i]; //Distress ID

            data[idx++] = m_DstAlertRelayAck.Nature; // Nature

            for (uint i = 0; i < 5; i++)
                data[idx++] = m_DstAlertRelayAck.DstPos[i];

            for (uint i = 0; i < 2; i++)
                data[idx++] = m_DstAlertRelayAck.UTC[i];

            data[idx++] = m_DstAlertRelayAck.SubCom;
            data[idx++] = m_DstAlertRelayAck.EOS;

            for (uint i = 0; i < DSC_EXP_CHR_SIZE; i++)
                data[idx++] = m_DstAlertRelayAck.ExpPos[i];
        }

        public void EncodeIndividual(ref byte[] data)
        {
            uint idx = 0;
            data[idx++] = (byte)m_IndiCall.Head.msg_type;
            data[idx++] = (byte)m_IndiCall.Head.ecc_err;
            data[idx++] = 0; // padding
            data[idx++] = 0; // padding

            for (uint i = 0; i < 4; i++) // date
                data[idx++] = 0;

            for (uint i = 0; i < 4; i++) // time
                data[idx++] = 0;

            for (uint i = 0; i < 4; i++) // freq
                data[idx++] = 0;

            data[idx++] = m_IndiCall.Format; //format

            for (uint i = 0; i < 5; i++)
                data[idx++] = m_IndiCall.AddrID[i]; //Addr

            data[idx++] = m_IndiCall.Category; //Category

            for (uint i = 0; i < 5; i++)
                data[idx++] = m_IndiCall.SelfID[i]; //self id

            data[idx++] = m_IndiCall.TelCmd0; //TeleCom1 
            data[idx++] = m_IndiCall.TelCmd1; //TeleCom2 

            for (uint i = 0; i < DSC_FRQ_CHR_SIZE; i++)
                data[idx++] = m_IndiCall.Freq[i]; //Freq

            for (uint i = 0; i < 2; i++)
                data[idx++] = m_IndiCall.UTC[i];

            data[idx++] = m_IndiCall.EOS;

            for (uint i = 0; i < DSC_EXP_CHR_SIZE; i++)
                data[idx++] = m_IndiCall.ExpPos[i];
        }

        public void EncodeGroupCall(ref byte[] data)
        {
            uint idx = 0;
            data[idx++] = (byte)m_GrpCall.Head.msg_type;
            data[idx++] = (byte)m_GrpCall.Head.ecc_err;
            data[idx++] = 0; // padding
            data[idx++] = 0; // padding

            for (uint i = 0; i < 4; i++) // date
                data[idx++] = 0;

            for (uint i = 0; i < 4; i++) // time
                data[idx++] = 0;

            for (uint i = 0; i < 4; i++) // freq
                data[idx++] = 0;

            data[idx++] = m_GrpCall.Format; //format

            for (uint i = 0; i < 5; i++)
                data[idx++] = m_GrpCall.AddrID[i]; //Addr

            data[idx++] = m_GrpCall.Category; //Category

            for (uint i = 0; i < 5; i++)
                data[idx++] = m_GrpCall.SelfID[i]; //self id

            data[idx++] = m_GrpCall.TelCmd0; //TeleCom1 
            data[idx++] = m_GrpCall.TelCmd1; //TeleCom2 

            for (uint i = 0; i < DSC_FRQ_CHR_SIZE; i++)
                data[idx++] = m_GrpCall.Freq[i]; //Freq

            data[idx++] = m_GrpCall.EOS;
        }

        public void EncodeSemiAuto(ref byte[] data)
        {
            uint idx = 0;
            data[idx++] = (byte)m_SemiAUTO.Head.msg_type;
            data[idx++] = (byte)m_SemiAUTO.Head.ecc_err;
            data[idx++] = 0; // padding
            data[idx++] = 0; // padding

            for (uint i = 0; i < 4; i++) // date
                data[idx++] = 0;

            for (uint i = 0; i < 4; i++) // time
                data[idx++] = 0;

            for (uint i = 0; i < 4; i++) // freq
                data[idx++] = 0;

            data[idx++] = m_SemiAUTO.Format; //format

            for (uint i = 0; i < 5; i++)
                data[idx++] = m_SemiAUTO.AddrID[i]; //Addr

            data[idx++] = m_SemiAUTO.Category; //Category

            for (uint i = 0; i < 5; i++)
                data[idx++] = m_SemiAUTO.SelfID[i]; //self id

            data[idx++] = m_SemiAUTO.TelCmd0; //TeleCom1 
            data[idx++] = m_SemiAUTO.TelCmd1; //TeleCom2 

            for (uint i = 0; i < DSC_FRQ_CHR_SIZE; i++)
                data[idx++] = m_SemiAUTO.Freq[i]; //Freq

            for (uint i = 0; i < DSC_TEL_CHR_SIZE; i++)
                data[idx++] = m_SemiAUTO.TelNo[i]; //Tell No

            data[idx++] = m_SemiAUTO.EOS;
        }

        public void EncodeAllShips(ref byte[] data)
        {
            uint idx = 0;
            data[idx++] = (byte)m_AllShip.Head.msg_type;
            data[idx++] = (byte)m_AllShip.Head.ecc_err;
            data[idx++] = 0; // padding
            data[idx++] = 0; // padding

            for (uint i = 0; i < 4; i++) // date
                data[idx++] = 0;

            for (uint i = 0; i < 4; i++) // time
                data[idx++] = 0;

            for (uint i = 0; i < 4; i++) // freq
                data[idx++] = 0;

            data[idx++] = m_AllShip.Format; //format

            data[idx++] = m_AllShip.Category; //Category

            for (uint i = 0; i < 5; i++)
                data[idx++] = m_AllShip.SelfID[i]; //self id

            data[idx++] = m_AllShip.TelCmd0; //TeleCom1
            data[idx++] = m_AllShip.TelCmd1; //TeleCom2

            for (uint i = 0; i < DSC_FRQ_CHR_SIZE; i++)
                data[idx++] = m_AllShip.Freq[i]; //Freq

            data[idx++] = m_AllShip.EOS;
        }

        public void EncodeDistressAlertAck(ref byte[] data)
        {
            uint idx = 0;
            data[idx++] = (byte)m_DstAlertAck.Head.msg_type;
            data[idx++] = (byte)m_DstAlertAck.Head.ecc_err;
            data[idx++] = 0; // padding
            data[idx++] = 0; // padding

            for (uint i = 0; i < 4; i++) // date
                data[idx++] = 0;

            for (uint i = 0; i < 4; i++) // time
                data[idx++] = 0;

            for (uint i = 0; i < 4; i++) // freq
                data[idx++] = 0;

            data[idx++] = m_DstAlertAck.Format; //format

            data[idx++] = m_DstAlertAck.Category; //Category

            for (uint i = 0; i < 5; i++)
                data[idx++] = m_DstAlertAck.SelfID[i]; //self id

            data[idx++] = m_DstAlertAck.TelCmd0; //TeleCom

            for (uint i = 0; i < 5; i++)
                data[idx++] = m_DstAlertAck.DistID[i]; //Distress ID

            data[idx++] = m_DstAlertAck.Nature; // Nature

            for (uint i = 0; i < 5; i++)
                data[idx++] = m_DstAlertAck.DstPos[i];

            for (uint i = 0; i < 2; i++)
                data[idx++] = m_DstAlertAck.UTC[i];

            data[idx++] = m_DstAlertAck.SubCom;
            data[idx++] = m_DstAlertAck.EOS;

            for (uint i = 0; i < DSC_EXP_CHR_SIZE; i++)
                data[idx++] = m_DstAlertAck.ExpPos[i];
        }

        public void EncodeDistressAlert(ref byte[] data)
        {
            uint idx = 0;
            data[idx++] = (byte)m_DstAlert.Head.msg_type;
            data[idx++] = (byte)m_DstAlert.Head.ecc_err;
            data[idx++] = 0; // padding
            data[idx++] = 0; // padding

            for (uint i = 0; i < 4; i++) // date
                data[idx++] = 0;

            for (uint i = 0; i < 4; i++) // time
                data[idx++] = 0;

            for (uint i = 0; i < 4; i++) // freq
                data[idx++] = 0;

            data[idx++] = m_DstAlert.Format; //format

            for (uint i = 0; i < 5; i++)
                data[idx++] = m_DstAlert.SelfID[i]; //self id

            data[idx++] = m_DstAlert.Nature; // nature

            for (uint i = 0; i < 5; i++)
                data[idx++] = m_DstAlert.DstPos[i];

            for (uint i = 0; i < 2; i++)
                data[idx++] = m_DstAlert.UTC[i];

            data[idx++] = m_DstAlert.SubCom;
            data[idx++] = m_DstAlert.EOS;

            for (uint i = 0; i < DSC_EXP_CHR_SIZE; i++)
                data[idx++] = m_DstAlert.ExpPos[i];
        }

        private void RsvDistressAlert(byte[] data)
        {
            uint idx = DSC_REAL_DATA_ST_IDX;
            m_DstAlert.Format = data[idx++];

            for (uint i = 0; i < 5; i++)
                m_DstAlert.SelfID[i] = data[idx++];

            m_DstAlert.Nature = data[idx++];

            for (uint i = 0; i < 5; i++)
                m_DstAlert.DstPos[i] = data[idx++];

            for (uint i = 0; i < 2; i++)
                m_DstAlert.UTC[i] = data[idx++];

            m_DstAlert.SubCom = data[idx++];
            m_DstAlert.EOS = data[idx++];

            for (uint i = 0; i < DSC_EXP_CHR_SIZE; i++)
                m_DstAlert.ExpPos[i] = data[idx++];
        }

        private void RsvDistressAlertAck(byte[] data)
        {
            uint idx = DSC_REAL_DATA_ST_IDX;
            m_DstAlertAck.Format = data[idx++];

            m_DstAlertAck.Category = data[idx++];

            for (uint i = 0; i < 5; i++)
                m_DstAlertAck.SelfID[i] = data[idx++];

            m_DstAlertAck.TelCmd0 = data[idx++];

            for (uint i = 0; i < 5; i++)
                m_DstAlertAck.DistID[i] = data[idx++];

            m_DstAlertAck.Nature = data[idx++];



            for (uint i = 0; i < 5; i++)
                m_DstAlertAck.DstPos[i] = data[idx++];

            for (uint i = 0; i < 2; i++)
                m_DstAlertAck.UTC[i] = data[idx++];

            m_DstAlertAck.SubCom = data[idx++];
            m_DstAlertAck.EOS = data[idx++];

            for (uint i = 0; i < DSC_EXP_CHR_SIZE; i++)
                m_DstAlertAck.ExpPos[i] = data[idx++];
        }

        private void RsvDistressAlertRelay(byte[] data)
        {
            uint idx = DSC_REAL_DATA_ST_IDX;
            m_DstAlertRelay.Format = data[idx++];

            for (uint i = 0; i < 5; i++)
                m_DstAlertRelay.AddrID[i] = data[idx++];

            m_DstAlertRelay.Category = data[idx++];

            for (uint i = 0; i < 5; i++)
                m_DstAlertRelay.SelfID[i] = data[idx++];

            m_DstAlertRelay.TelCmd0 = data[idx++];

            for (uint i = 0; i < 5; i++)
                m_DstAlertRelay.DistID[i] = data[idx++];

            m_DstAlertRelay.Nature = data[idx++];

            for (uint i = 0; i < 5; i++)
                m_DstAlertRelay.DstPos[i] = data[idx++];

            for (uint i = 0; i < 2; i++)
                m_DstAlertRelay.UTC[i] = data[idx++];

            m_DstAlertRelay.SubCom = data[idx++];
            m_DstAlertRelay.EOS = data[idx++];

            for (uint i = 0; i < DSC_EXP_CHR_SIZE; i++)
                m_DstAlertRelay.ExpPos[i] = data[idx++];
        }

        private void RsvDistressAlertRelayAck(byte[] data)
        {
            uint idx = DSC_REAL_DATA_ST_IDX;
            m_DstAlertRelayAck.Format = data[idx++];

            for (uint i = 0; i < 5; i++)
                m_DstAlertRelayAck.AddrID[i] = data[idx++];

            m_DstAlertRelayAck.Category = data[idx++];

            for (uint i = 0; i < 5; i++)
                m_DstAlertRelayAck.SelfID[i] = data[idx++];

            m_DstAlertRelayAck.TelCmd0 = data[idx++];

            for (uint i = 0; i < 5; i++)
                m_DstAlertRelayAck.DistID[i] = data[idx++];

            m_DstAlertRelayAck.Nature = data[idx++];

            for (uint i = 0; i < 5; i++)
                m_DstAlertRelayAck.DstPos[i] = data[idx++];

            for (uint i = 0; i < 2; i++)
                m_DstAlertRelayAck.UTC[i] = data[idx++];

            m_DstAlertRelayAck.SubCom = data[idx++];
            m_DstAlertRelayAck.EOS = data[idx++];

            for (uint i = 0; i < DSC_EXP_CHR_SIZE; i++)
                m_DstAlertRelayAck.ExpPos[i] = data[idx++];
        }

        private void RsvAllShip(byte[] data)
        {
            uint idx = DSC_REAL_DATA_ST_IDX;
            m_AllShip.Format = data[idx++];

            m_AllShip.Category = data[idx++];

            for (uint i = 0; i < 5; i++)
                m_AllShip.SelfID[i] = data[idx++];

            m_AllShip.TelCmd0 = data[idx++];
            m_AllShip.TelCmd1 = data[idx++];

            for (uint i = 0; i < DSC_FRQ_CHR_SIZE; i++)
                m_AllShip.Freq[i] = data[idx++];

            m_AllShip.EOS = data[idx++];
        }

        private void RsvGrpCall(byte[] data)
        {
            uint idx = DSC_REAL_DATA_ST_IDX;

            m_GrpCall.Format = data[idx++];

            for (uint i = 0; i < 5; i++)
                m_GrpCall.AddrID[i] = data[idx++];

            m_GrpCall.Category = data[idx++];

            for (uint i = 0; i < 5; i++)
                m_GrpCall.SelfID[i] = data[idx++];

            m_GrpCall.TelCmd0 = data[idx++];
            m_GrpCall.TelCmd1 = data[idx++];

            for (uint i = 0; i < DSC_FRQ_CHR_SIZE; i++)
                m_GrpCall.Freq[i] = data[idx++];

            m_GrpCall.EOS = data[idx++];
        }

        private void RsvGeoAREA(byte[] data)
        {
            uint idx = DSC_REAL_DATA_ST_IDX;

            m_GeoAREA.Format = data[idx++];

            for (uint i = 0; i < 5; i++)
                m_GeoAREA.AddrID[i] = data[idx++];

            m_GeoAREA.Category = data[idx++];

            for (uint i = 0; i < 5; i++)
                m_GeoAREA.SelfID[i] = data[idx++];

            m_GeoAREA.TelCmd0 = data[idx++];
            m_GeoAREA.TelCmd1 = data[idx++];

            for (uint i = 0; i < DSC_FRQ_CHR_SIZE; i++)
                m_GeoAREA.Freq[i] = data[idx++];

            m_GeoAREA.EOS = data[idx++];
        }

        private void RsvIndiCall(byte[] data)
        {
            uint idx = DSC_REAL_DATA_ST_IDX;
            m_IndiCall.Format = data[idx++];

            for (uint i = 0; i < 5; i++)
                m_IndiCall.AddrID[i] = data[idx++];

            m_IndiCall.Category = data[idx++];

            for (uint i = 0; i < 5; i++)
                m_IndiCall.SelfID[i] = data[idx++];

            m_IndiCall.TelCmd0 = data[idx++];
            m_IndiCall.TelCmd1 = data[idx++];

            for (uint i = 0; i < DSC_FRQ_CHR_SIZE; i++)
                m_IndiCall.Freq[i] = data[idx++];

            for (uint i = 0; i < 2; i++)
                m_IndiCall.UTC[i] = data[idx++];

            m_IndiCall.EOS = data[idx++];

            for (uint i = 0; i < DSC_EXP_CHR_SIZE; i++)
                m_IndiCall.ExpPos[i] = data[idx++];
        }

        private void RsvSemiAUTO(byte[] data)
        {
            uint idx = DSC_REAL_DATA_ST_IDX;
            m_SemiAUTO.Format = data[idx++];

            for (uint i = 0; i < 5; i++)
                m_SemiAUTO.AddrID[i] = data[idx++];

            m_SemiAUTO.Category = data[idx++];

            for (uint i = 0; i < 5; i++)
                m_SemiAUTO.SelfID[i] = data[idx++];

            m_SemiAUTO.TelCmd0 = data[idx++];
            m_SemiAUTO.TelCmd1 = data[idx++];

            for (uint i = 0; i < DSC_FRQ_CHR_SIZE; i++)
                m_SemiAUTO.Freq[i] = data[idx++];

            for (uint i = 0; i < DSC_TEL_CHR_SIZE; i++)
                m_SemiAUTO.TelNo[i] = data[idx++];

            m_SemiAUTO.EOS = data[idx++];
        }

        public PROTO_STAT ProcCommandHandler()
        {
            byte cmd_type = (byte)((m_DataFrame.CMD & 0xC0) >> 6);
            ushort cmd_param = m_DataFrame.SubCmd;

            switch (cmd_type)
            {
                case DataProtocol.CMD_ACK:
                    switch (cmd_param)
                    {
                        case DataProtocol.CMD_SET_SELF_TEST:
                            Array.Copy(m_DataFrame.data_buf, m_DscLoopTestData, (int)DSC_LOOP_TEST.MAX_DSC_LOOP_TEST);
                            break;
                        default:
                            break;
                    }
                     break;
                case DataProtocol.CMD_RSP:
                    switch (cmd_param)
                    {
                        case DataProtocol.CMD_TRANS_CONNECT:
                            m_ConnectStat = true;
                            break;
                        case DataProtocol.CMD_REQ_MOD_INFO:
                            Array.Copy(m_DataFrame.data_buf, 4, m_TransVerData, 0, (int)TRANS_VER.MAX_TRANS_VER);
                            break;
                        case DataProtocol.CMD_REQ_TRACK_INFO:
                            for (int i = 0; i < TRACK_INFO_SIZE; i++)
                            {
                                m_TrackInfoData.MagicCode[i] = BitConverter.ToUInt32(m_DataFrame.data_buf, i * 16);
                                m_TrackInfoData.TrackNum[i] = BitConverter.ToUInt32(m_DataFrame.data_buf, (i * 16) + 4);
                                m_TrackInfoData.RecordMS[i] = BitConverter.ToUInt32(m_DataFrame.data_buf, (i * 16) + 8);
                                m_TrackInfoData.TimeStamp[i] = BitConverter.ToUInt32(m_DataFrame.data_buf, (i * 16) + 12);
                            }
                            evtNotiAudioTrackInfoReceived();
                            break;
                        default:
                            break;
                    }
                    break;
                case DataProtocol.CMD_SET_NOTI:
                    switch (cmd_param)
                    {
                        case DataProtocol.CMD_NOTI_RX_DSC:

                            if (m_DataFrame.data_buf[0] == (byte)MSG_TYPE.DSC_MSG_TYPE_DST_ALT)
                                RsvDistressAlert(m_DataFrame.data_buf);
                            else if (m_DataFrame.data_buf[0] == (byte)MSG_TYPE.DSC_MSG_TYPE_DST_ACK)
                                RsvDistressAlertAck(m_DataFrame.data_buf);
                            else if (m_DataFrame.data_buf[0] == (byte)MSG_TYPE.DSC_MSG_TYPE_DST_RLY)
                                RsvDistressAlertRelay(m_DataFrame.data_buf);
                            else if (m_DataFrame.data_buf[0] == (byte)MSG_TYPE.DSC_MSG_TYPE_RLY_ACK)
                                RsvDistressAlertRelayAck(m_DataFrame.data_buf);
                            else if (m_DataFrame.data_buf[0] == (byte)MSG_TYPE.DSC_MSG_TYPE_ALL_SHIP)
                                RsvAllShip(m_DataFrame.data_buf);
                            else if (m_DataFrame.data_buf[0] == (byte)MSG_TYPE.DSC_MSG_TYPE_GRP_CALL)
                                RsvGrpCall(m_DataFrame.data_buf);
                            else if (m_DataFrame.data_buf[0] == (byte)MSG_TYPE.DSC_MSG_TYPE_GEO_AREA)
                                RsvGeoAREA(m_DataFrame.data_buf);
                            else if (m_DataFrame.data_buf[0] == (byte)MSG_TYPE.DSC_MSG_TYPE_INDI_CALL)
                                RsvIndiCall(m_DataFrame.data_buf);
                            else if (m_DataFrame.data_buf[0] == (byte)MSG_TYPE.DSC_MSG_TYPE_SEMI_AUTO)
                                RsvSemiAUTO(m_DataFrame.data_buf);

                            evtNotiDscMsgReceived();
                            break;

                        case DataProtocol.CMD_NOTI_FULL_EXT_COMM:
                            evtNotiFullExtCommReceived();
                            break;
                        case DataProtocol.CMD_NOTI_POS_TM:
                            m_PosTime.lat = BitConverter.ToInt32(m_DataFrame.data_buf, 0);
                            m_PosTime.lon = BitConverter.ToInt32(m_DataFrame.data_buf, 4);
                            m_PosTime.date = BitConverter.ToInt32(m_DataFrame.data_buf, 8);
                            m_PosTime.time = BitConverter.ToInt32(m_DataFrame.data_buf, 12);
                            m_PosTime.lz_hours = BitConverter.ToInt32(m_DataFrame.data_buf, 16);
                            m_PosTime.lz_mins = BitConverter.ToInt32(m_DataFrame.data_buf, 20);
                            evtNotiPosTmReceived();
                            break;

                        case DataProtocol.CMD_NOTI_SELF_TEST:
                            m_SelfTestData.test_item = m_DataFrame.data_buf[0];
                            m_SelfTestData.test_status = m_DataFrame.data_buf[1];
                            evtNotiSelfTestReceived();
                            break;

                        case DataProtocol.CMD_RECORD_DONE_NOTI:
                            evtNotiRecordDoneReceived();
                            break;
                    }
                    break;

                default:
                    break;

            }
            m_protocol_status = PROTO_STAT.IDLE_STAT;
            return m_protocol_status;
        }
        
        public void SetProtocoStatus(PROTO_STAT status)
        {
            m_protocol_status = status;
        }

        public void SendDataFrame(ushort cmd, ushort param, byte[] payload, int length)
        {
            ushort crc16;
            ushort len;

            m_DataIdx = PAYLOAD_OFFSET;

            len = (ushort)(length + (4 * 2));

            m_ComTxBuf[0] = DataProtocol.FRM_SFD;

            //frame length 4*2byte (length(2), Seq Num(2), Cmd(2), Sub Cmd(2))
            m_ComTxBuf[1] = (byte)(len & 0xFF);        
            m_ComTxBuf[2] = (byte)((len >> 8) & 0xFF); 


            m_ComTxBuf[3] = (byte)(m_SeqNum & 0xFF);       
            m_ComTxBuf[4] = (byte)((m_SeqNum >> 8) & 0xFF);

            m_ComTxBuf[5] = (byte)(cmd & 0xFF);
            m_ComTxBuf[6] = (byte)((cmd >> 8) & 0xFF);

            //m_ComTxBuf[3] = cmd;

            if (param != 0xFFFF)
            {
                m_ComTxBuf[7] = (byte)(param & 0xFF);       // 하위 8비트
                m_ComTxBuf[8] = (byte)((param >> 8) & 0xFF); // 상위 8비트

                //m_ComTxBuf[4] = param;
            }
            else
            {
                m_ComTxBuf[7] = 0; // 하위 8비트
                m_ComTxBuf[8] = 0; // 상위 8비트
                //m_ComTxBuf[4] = 0;
            }

            for (ushort i = 0; i < length; i++)
            {
                m_ComTxBuf[i + PAYLOAD_OFFSET] = payload[i];
                m_DataIdx++;
            }

            crc16 = crc16_calc(0xFFFF, m_ComTxBuf, (uint)(m_DataIdx - 1));

            m_ComTxBuf[m_DataIdx++] = (byte)(crc16);
            m_ComTxBuf[m_DataIdx++] = (byte)(crc16 >> 8);

            m_SerialPort.Write(m_ComTxBuf, 0, m_DataIdx);

            m_SeqNum++;
        }

        public ERROR ParseCommand(byte[] buffer, ushort frame_len, uint rcv_cnt)
        {
            ushort calc_CRC16;
            ushort rcv_CRC16;

            m_DataFrame.SFD = buffer[0];
            if (m_DataFrame.SFD != FRM_SFD)
            {
                return ERROR.ERR_SFD;
            }

            m_DataFrame.Length = (ushort)((buffer[2] << 8) | buffer[1]);
            if (m_DataFrame.Length > frame_len + 3) // SFD(1), CRC(2)
            {
                return ERROR.ERR_LEN;
            }

            calc_CRC16 = crc16_calc(0xFFFF, buffer, frame_len);
            rcv_CRC16 = (ushort)((buffer[rcv_cnt - 1] << 8) | buffer[rcv_cnt - 2]);

            if (calc_CRC16 != rcv_CRC16)
            {
                return ERROR.ERR_CRC;
            }

            m_DataFrame.SeqNum = (ushort)((buffer[4] << 8) | buffer[3]);
            m_DataFrame.CMD = (ushort)((buffer[6] << 8) | buffer[5]);
            m_DataFrame.SubCmd = (ushort)((buffer[8] << 8) | buffer[7]);
            m_DataFrame.CRC_L = buffer[frame_len - 2];
            m_DataFrame.CRC_H = buffer[frame_len - 1];

            if (m_DataFrame.Length > 8)
            {
                Array.Copy(buffer, PAYLOAD_OFFSET, m_DataFrame.data_buf, 0, m_DataFrame.Length - 8);
            }
            m_DataFrame.RxFrameIdx++;

            return ERROR.ERR_OK;
        }

        private  ushort crc16_calc(ushort init_crc, byte[] p, uint len)
        {
            byte[] data = p;
            ushort crc = init_crc;

            for (int i = 1; i <= len; i++) // except for SFD
            {
                crc = (ushort)((crc >> 8) | (crc << 8));
                crc ^= data[i];
                crc ^= (ushort)((crc & 0xFF) >> 4);
                crc ^= (ushort)((crc << 8) << 4);
                crc ^= (ushort)(((crc & 0xFF) << 4) << 1);
            }

            return crc;
        }

        public string GetLonStringEx(int nLon, PosStrFmt nFormat)
        {
            char chEW = 'E';
            int nDeg = 0, nMin = 0, nMin10000 = 0;
            int nTmp = 0;

            if ((nLon >= -108000000) && (nLon <= 108000000) && nLon != -1)
            {
                if (nLon < 0) chEW = 'W';
                else chEW = 'E';

                nDeg = (nLon / (60 * 10000));
                nTmp = nLon - (nDeg * 60 * 10000);
                nMin = nTmp / 10000;
                nMin10000 = nTmp % 10000;

                if (nDeg < 0) nDeg *= -1;
                if (nMin < 0) nMin *= -1;
                if (nMin10000 < 0) nMin10000 *= -1;

                return $"{nDeg}°{nMin:00}.{nMin10000:0000}' {chEW}";
            }
            else
            {
                if (nFormat == PosStrFmt.Unknown)
                {
                    return "Unknown";
                }
                else
                {
                    return "---°--.----' E";
                }
            }
        }


        public string GetLatStringEx(int nLat, PosStrFmt nFormat)
        {
            char chNS = 'N';
            int nDeg = 0, nMin = 0, nMin10000 = 0;
            int nTmp = 0;

            if ((nLat >= -54000000) && (nLat <= 54000000) && nLat != -1)
            {
                if (nLat < 0) chNS = 'S';
                else chNS = 'N';

                nDeg = (nLat / (60 * 10000));
                nTmp = nLat - (nDeg * 60 * 10000);
                nMin = nTmp / 10000;
                nMin10000 = nTmp % 10000;

                if (nDeg < 0) nDeg *= -1;
                if (nMin < 0) nMin *= -1;
                if (nMin10000 < 0) nMin10000 *= -1;

                return $"{nDeg}°{nMin:00}.{nMin10000:0000}' {chNS}";
            }
            else
            {
                if (nFormat == PosStrFmt.Unknown)
                {
                    return "Unknown";
                }
                else
                {
                    return "--°--.----' N";
                }
            }
        }

    }
}
